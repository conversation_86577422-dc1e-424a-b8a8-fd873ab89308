[tool.poetry]
name = "operation"
version = "0.1.0"
description = "GT Operation Service"
authors = ["Vingen <<EMAIL>>"]
license = "Apache-2.0"
readme = "README.md"
repository = "https://bitbucket.org/tysud/operation.git"

[tool.poetry.dependencies]
python = "^3.7"
alembic = "^1.4.2"
cdislogging = "^1.0.0"
fastapi = "^0.61.0"
gen3authz = "^1.0.0"
gen3config = "^0.1.8"
httpx = "^0.12.1"
psycopg2-binary = "^2.8.5"
uvicorn = "^0.11.8"
gino = {version = "^1.0.1", extras = ["starlette"]}
importlib-metadata = {version = "^1.7.0", python = "<3.8"}
gunicorn = "^20.0.4"
requests = "^2.26.0"
fastapi-mail = "*******"
python-dotenv = "^0.19.2"
boto3 = "^1.20.16"
test-tools = "^0.0.3"
fixtures = "^4.0.0"
pytest-asyncio = "^0.18.3"
celery = "v5.1.2"
pycurl = "^7.45.1"
PyYAML = "5.3.1"
moto = "^3.1.16"
pytest-env = "^0.6.2"
asgiref = "^3.5.2"
PyJWT = {extras = ["crypto"], version = "^2.4.0"}
markupsafe = "2.0.0"
[tool.poetry.dev-dependencies]
pytest = "^6.0.1"
pytest-cov = "^2.8"
requests = "^2.24.0"

[tool.poetry.plugins."operation.modules"]
"operator_code" = "operation.routes.operator.code"
"operator_sample" = "operation.routes.operator.sample"
"operator_promotion" = "operation.routes.operator.promotion"
"lims_dna_extraction" = "operation.routes.lims.dna_extraction"
"lims_dna_box" = "operation.routes.lims.dna_box"
"lims_chip" = "operation.routes.lims.chip"
"user_kit" = "operation.routes.user.kit"
"partner_account" = "operation.routes.partner.account"
"partner_staff" = "operation.routes.partner.staff"
"partner_history" = "operation.routes.partner.sale_account_history"
"lims_lab_sample" = "operation.routes.lims.lab_sample"
"lims_sample_management" = "operation.routes.lims.sample_management"
"lims_sample_mapping" = "operation.routes.lims.sample_mapping"
"lims_batch" = "operation.routes.lims.batch"
"lims_raw" = "operation.routes.lims.raw"
"lims_plate" = "operation.routes.lims.plate"
"lims_card" = "operation.routes.lims.card"
"partner_booking" = "operation.routes.partner.partner_booking"
"samplecode" = "operation.routes.operator.samplecode"
"user_identity_card" = "operation.routes.user.identity_card"
"platform_agency" = "operation.routes.platform.agency"
"request_agency" = "operation.routes.platform.request"
"adn_integration_agency" = "operation.routes.platform.adn_integration"
"adn_integration_type_agency" = "operation.routes.platform.adn_integration_type"
"tracking_unit" = "operation.routes.tracking.unit"
"tracking_employee" = "operation.routes.tracking.employee"
"tracking_collection" = "operation.routes.tracking.collection"
"tracking_delivery" = "operation.routes.tracking.delivery"
"tracking_template" = "operation.routes.tracking.template"
"tracking_step" = "operation.routes.tracking.step"
"tracking_procedure" = "operation.routes.tracking.procedure"
"tracking_sample" = "operation.routes.tracking.sample"
"adn_result" = "operation.routes.platform.result"
"billcode" = "operation.routes.operator.billcode"
"tracking_unit_location" = "operation.routes.tracking.unit_location"


[build-system]
requires = ["poetry>=1.0.0"]
build-backend = "poetry.masonry.api"
