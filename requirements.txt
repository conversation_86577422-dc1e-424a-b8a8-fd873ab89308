aiosmtplib==1.1.6; python_full_version >= "3.5.2" and python_full_version < "4.0.0"
alembic==1.6.5; (python_version >= "2.7" and python_full_version < "3.0.0") or (python_full_version >= "3.6.0")
amqp==5.1.1; python_version >= "3.7"
asgiref==3.5.2; python_version >= "3.7"
asyncpg==0.23.0; python_version >= "3.6" and python_version < "4.0" and python_full_version >= "3.5.0"
atomicwrites==1.4.0; python_version >= "3.7" and python_full_version < "3.0.0" and sys_platform == "win32" or sys_platform == "win32" and python_version >= "3.7" and python_full_version >= "3.4.0"
attrs==21.2.0; python_version >= "3.7" and python_full_version < "3.0.0" or python_full_version >= "3.5.0" and python_version >= "3.7"
backoff==1.11.1; python_version >= "3.6" and python_full_version < "3.0.0" and python_version < "4.0" or python_version >= "3.6" and python_version < "4.0" and python_full_version >= "3.5.0"
billiard==3.6.4.0; python_version >= "3.6"
boto3==1.20.16; python_version >= "3.6"
botocore==1.23.16; python_version >= "3.6"
cached-property==1.5.2; python_version < "3.8" and python_version >= "3.7"
cdiserrors==1.0.0; python_version >= "3.6" and python_version < "4.0"
cdislogging==1.0.0
celery==5.1.2; python_version >= "3.6"
certifi==2021.5.30; python_version >= "3.6" and python_version < "4.0" and (python_version >= "2.7" and python_full_version < "3.0.0" or python_full_version >= "3.6.0") and (python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.6.0" and python_version >= "3.6")
cffi==1.14.6; python_version >= "3.6"
chardet==3.0.4; python_version >= "3.6" and python_version < "4.0"
charset-normalizer==2.0.4; python_full_version >= "3.6.0" and python_version >= "3.6"
click-didyoumean==0.3.0; python_full_version >= "3.6.2" and python_full_version < "4.0.0" and python_version >= "3.6"
click-plugins==1.1.1; python_version >= "3.6"
click-repl==0.2.0; python_version >= "3.6"
click==7.1.2; python_full_version >= "3.6.2" and python_version >= "3.6" and python_full_version < "4.0.0" and (python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.5.0" and python_version >= "3.6")
colorama==0.4.4; python_version >= "3.7" and python_full_version < "3.0.0" and sys_platform == "win32" or sys_platform == "win32" and python_version >= "3.7" and python_full_version >= "3.5.0"
cryptography==3.4.7; python_version >= "3.6"
dnspython==2.1.0; python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.5.0" and python_version >= "3.6"
email-validator==1.1.3; python_version >= "2.7" and python_full_version < "3.0.0" or python_full_version >= "3.5.0"
extras==1.0.0; python_version >= "3.6"
fastapi-mail==0.2.8.0
fastapi==0.61.2; python_version >= "3.6"
fixtures==4.0.0; python_version >= "3.6"
gen3authz==1.1.0; python_version >= "3.6" and python_version < "4.0"
gen3config==0.1.9
gino-starlette==0.1.2; python_version >= "3.6" and python_version < "4.0"
gino==1.0.1; python_version >= "3.5" and python_version < "4.0"
gunicorn==20.1.0; python_version >= "3.5"
h11==0.9.0; python_version >= "3.6" and python_version < "4.0"
h2==3.2.0; python_version >= "3.6" and python_version < "4.0"
hpack==3.0.0; python_version >= "3.6" and python_version < "4.0"
hstspreload==2021.11.1; python_version >= "3.6" and python_version < "4.0"
httptools==0.1.2; sys_platform != "win32" and sys_platform != "cygwin" and platform_python_implementation != "PyPy"
httpx==0.12.1; python_version >= "3.6"
hyperframe==5.2.0; python_version >= "3.6" and python_version < "4.0"
idna==2.10; python_version >= "3.6" and python_full_version < "3.0.0" and python_version < "4.0" or python_version >= "3.6" and python_version < "4.0" and python_full_version >= "3.6.0"
importlib-metadata==1.7.0; python_version >= "3.7" and python_full_version < "3.0.0" and python_version < "3.8" or python_version >= "3.7" and python_version < "3.8" and python_full_version >= "3.5.0" or python_version < "3.8"
iniconfig==1.1.1; python_version >= "3.7"
jinja2==2.10.3; python_version >= "3.6"
jmespath==0.10.0; python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.3.0" and python_version >= "3.6"
kombu==5.2.4; python_version >= "3.7"
mako==1.1.4; python_version >= "2.7" and python_full_version < "3.0.0" or python_full_version >= "3.6.0"
markupsafe==2.0.0; python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.6.0" and python_version >= "3.6"
moto==3.1.16; python_version >= "3.6"
packaging==21.0; python_version >= "3.7"
pbr==5.9.0; python_version >= "3.6"
pluggy==0.13.1; python_version >= "3.7" and python_full_version < "3.0.0" or python_full_version >= "3.4.0" and python_version >= "3.7"
prompt-toolkit==3.0.30; python_full_version >= "3.6.2" and python_version >= "3.6"
psycopg2-binary==2.9.1; python_version >= "3.6"
py==1.10.0; python_version >= "3.7" and python_full_version < "3.0.0" or python_full_version >= "3.4.0" and python_version >= "3.7"
pycparser==2.20; python_version >= "3.6" and python_full_version < "3.0.0" or python_version >= "3.6" and python_full_version >= "3.4.0"
pycurl==7.45.1; python_version >= "3.5"
pydantic==1.8.2; python_full_version >= "3.6.1" and python_version >= "3.6"
pyjwt==2.4.0; python_version >= "3.6"
pyparsing==2.4.7; python_version >= "3.7" and python_full_version < "3.0.0" or python_full_version >= "3.3.0" and python_version >= "3.7"
pytest-asyncio==0.18.3; python_version >= "3.7"
pytest-env==0.6.2
pytest==6.2.4; python_version >= "3.6"
python-dateutil==2.8.2; python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.6.0" and python_version >= "3.6"
python-dotenv==0.19.2; python_version >= "3.5"
python-editor==1.0.4; python_version >= "2.7" and python_full_version < "3.0.0" or python_full_version >= "3.6.0"
python-multipart==0.0.5
pytz==2022.1; python_version >= "3.6"
pyyaml~=5.1; python_version >= "2.7" and python_full_version < "3.0.0" or python_full_version >= "3.6.0"
requests==2.26.0; (python_version >= "2.7" and python_full_version < "3.0.0") or (python_full_version >= "3.6.0")
responses==0.21.0; python_version >= "3.7"
rfc3986==1.5.0; python_version >= "3.6" and python_version < "4.0"
s3transfer==0.5.0; python_version >= "3.6"
six==1.16.0; python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.6.0" and python_version >= "3.6"
sniffio==1.2.0; python_version >= "3.6" and python_version < "4.0"
sqlalchemy==1.3.24; python_version >= "3.6" and python_full_version < "3.0.0" and python_version < "4.0" or python_version >= "3.6" and python_version < "4.0" and python_full_version >= "3.6.0"
starlette==0.13.6; python_version >= "3.6" and python_version < "4.0"
test-tools==0.0.3
testtools==2.5.0; python_version >= "3.6"
toml==0.10.2; python_version >= "3.7" and python_full_version < "3.0.0" or python_full_version >= "3.3.0" and python_version >= "3.7"
typing-extensions==3.10.0.0; python_version < "3.8" and python_version >= "3.7" and python_full_version >= "3.6.1"
urllib3==1.26.6; python_version >= "3.7" and python_full_version < "3.0.0" and python_version < "4.0" or python_full_version >= "3.6.0" and python_version < "4" and python_version >= "3.7"
uvicorn==0.11.8
uvloop==0.17.0; sys_platform != "win32" and sys_platform != "cygwin" and platform_python_implementation != "PyPy" and python_version >= "3.8"
vine==5.0.0; python_version >= "3.7"
wcwidth==0.2.5; python_full_version >= "3.6.2" and python_version >= "3.6"
websockets==8.1; python_full_version >= "3.6.1"
werkzeug==2.1.2; python_version >= "3.7"
xmltodict==0.12.0; python_version >= "3.6" and python_full_version < "3.0.0" or python_full_version >= "3.4.0" and python_version >= "3.6"
zipp==3.5.0; python_version >= "3.7" and python_full_version < "3.0.0" and python_version < "3.8" or python_full_version >= "3.5.0" and python_version < "3.8" and python_version >= "3.7"
