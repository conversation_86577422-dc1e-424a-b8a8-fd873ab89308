FROM python:3.8-alpine3.16 as base

FROM base as builder
ENV CRYPTOGRAPHY_DONT_BUILD_RUST=1
RUN apk add --no-cache --virtual .build-deps gcc musl-dev libffi-dev openssl-dev make postgresql-dev git curl curl-dev python3-dev libressl-dev

# Copy only requirements to leverage Docker cache
COPY requirements.txt /tmp/
RUN python -m venv /env && \
    . /env/bin/activate \
    && pip install psycopg2 openpyxl==3.1.5  docxtpl==0.20.0 \
    && pip install -r /tmp/requirements.txt

FROM base
RUN apk add --no-cache postgresql-libs curl
RUN mkdir -p $HOME/.gt/operation/

# Copy only the virtual environment from builder
COPY --from=builder /env /env

# Copy scripts first (better caching for unchanged scripts)
COPY ./scripts/entrypoint /entrypoint
RUN sed -i 's/\r$//g' /entrypoint && chmod +x /entrypoint

COPY ./scripts/start /start
RUN sed -i 's/\r$//g' /start && chmod +x /start

COPY ./scripts/consumer/start /start-consumer
RUN sed -i 's/\r$//g' /start-consumer && chmod +x /start-consumer

# Copy source code last (changes most frequently)
COPY . /src/
WORKDIR /src

# Uncomment if needed
#ENTRYPOINT ["/entrypoint"]