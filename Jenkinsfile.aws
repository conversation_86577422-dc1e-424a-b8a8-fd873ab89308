import java.text.SimpleDateFormat

pipeline {
    environment {
        registryCredential  = 'tys-dockerhub'
        imageTag            = ''
        customImage         = ''
        svc_name            = ''
        svc_port            = ''
        svc_prefix          = ''
        svc_dockerfile      = ''
        svc_replica         = ''
        eks_svc_name        = ''
        eks_namespace       = ''
        BRANCH              = ''
    }
    agent { label 'python' }
    stages {
        stage('Read deployment configurations') {
            steps {
                script {            
                    def props       = readProperties file: 'deploy/eks.properties'
                    svc_dockerfile  = props['DOCKERFILE']
                    svc_dockerfile_consumer = props['DOCKERFILE_CONSUMER']
                    svc_replica     = props['NREPLICA']
                    svc_name        = props['SVCNAME']
                    svc_port        = props['SVCPORT']
                    svc_prefix      = props['PREFIX']
                    eks_svc_name    = props['EKS_SVCNAME']
                    eks_namespace   = props['NAMESPACE']
                }
            }
        }
        stage('Checkout') {
            steps {
                checkout scm
                sh "git rev-parse --short HEAD > .git/commit-id"
                script {
                    imageTag = readFile('.git/commit-id').trim()
                    BRANCH = "${GIT_BRANCH.split('/').size() > 1 ? GIT_BRANCH.split('/')[1..-1].join('/') : GIT_BRANCH}"
                }                
                //BRANCH = sh(returnStdout: true, script:"echo ${env.BRANCH_NAME} | sed 's/[^a-zA-Z0-9]/-/g'").trim().toLowerCase()
            }
        }
        // stage('Pytest') {
        //     when {
        //         expression {BRANCH == 'develop'}
        //     }
        //     steps {
        //         script {   
        //             sh """      
        //             pwd
        //             #mkdir -p /tmp/operation
        //             #cp -r * /tmp/operation
        //             #cd /tmp/operation
        //             export CRYPTOGRAPHY_DONT_BUILD_RUST=1
        //             python3 -m venv /tmp/env && . /tmp/env/bin/activate && /home/<USER>/.poetry/bin/poetry install --no-dev --no-interaction
        //             pip3 install pytest
        //             pip3 install pytest-cov
        //             sed -i 's|/alembic/migrations|./migrations|g' alembic.ini
        //             /tmp/env/bin/alembic init migrations && cp alembic-env/env.py migrations
        //             mkdir -p ~/.gt/operation
        //             cp tests/test-operation-config.yaml ~/.gt/operation
        //             alembic revision -m "migrate" --autogenerate --head head && alembic upgrade head
        //             pytest --cov-report term-missing --cov=src/operation tests/ --cov-report xml
        //             #cat coverage.xml
        //             """
        //         }
        //     }
        // }
        // stage('Sonarqube') {
        //     when {
        //         expression {BRANCH == 'develop'}
        //     }
        //     steps {
        //         script {   
        //             sh """
        //             #cd -      
        //             sonar-scanner \
        //                 -Dsonar.projectKey=operation \
        //                 -Dsonar.sources=. \
        //                 -Dsonar.host.url=https://sonarqube.genestory.ai \
        //                 -Dsonar.login=****************************************
        //             """
        //         }
        //     }
        // }
        stage('Build and Test') {
            steps {
                echo 'Starting to build docker image'
                script {
                    docker.withRegistry('', registryCredential) {
                        customImage = docker.build("tysud/$svc_name:${BRANCH}-${imageTag}","-f ${svc_dockerfile} .")
                    }
                    
                }
            }
            //image = docker.build("${registry_url}:${imageTag}","-f ${svc_dockerfile} .")
        }
        stage('Push to Registry') {
            steps {
                echo 'Starting to push docker image'

                script {
                    docker.withRegistry('', registryCredential) {
                        customImage.push()  
                    }
                }
            }
            //docker.withRegistry("https://${registry_url}", "ecr:ap-southeast-1:ec2-jenkins") {
              //  image.push()
            //}
        }
        stage('Update helm chart') {
            steps {
            withAWS(credentials: 'aws-apse1', region: 'us-east-2') {
                sh """
                    cd /tmp
                    rm -rf operation
                    helm repo add tysud-charts s3://tysud-helm-charts/charts
                    helm repo update
                    helm pull tysud-charts/$svc_name --untar=true
                    CURRENT_CHART_VERSION=`cat $svc_name/Chart.yaml | grep "^version" | cut -d':' -f2 | sed 's/ //g'`
                    NEW_CHART_VERSION=`echo "\$CURRENT_CHART_VERSION" | awk -F. '{\$NF = \$NF + 1;} 1' | sed 's/ /./g'`
                    sed -i  "s/\$CURRENT_CHART_VERSION/\$NEW_CHART_VERSION/g" $svc_name/Chart.yaml
                    sed -i 's/tag.*/tag: "${BRANCH}-${imageTag}"/' $svc_name/values.yaml
                    helm package $svc_name                                                                              
                    helm s3 push --force $svc_name-\$NEW_CHART_VERSION.tgz tysud-charts
                 """
                sh """
                    cd /tmp
                    helm pull tysud-charts/operation-consumer --untar=true
                    CURRENT_CHART_VERSION=`cat operation-consumer/Chart.yaml | grep "^version" | cut -d':' -f2 | sed 's/ //g'`
                    NEW_CHART_VERSION=`echo "\$CURRENT_CHART_VERSION" | awk -F. '{\$NF = \$NF + 1;} 1' | sed 's/ /./g'`
                    sed -i  "s/\$CURRENT_CHART_VERSION/\$NEW_CHART_VERSION/g" operation-consumer/Chart.yaml
                    sed -i 's/tag.*/tag: "${BRANCH}-${imageTag}"/' operation-consumer/values.yaml
                    helm package operation-consumer                                                                              
                    helm s3 push --force operation-consumer-\$NEW_CHART_VERSION.tgz tysud-charts
                 """

                sh """
                    cd /tmp
                    helm pull tysud-charts/celery-beat --untar=true
                    CURRENT_CHART_VERSION=`cat celery-beat/Chart.yaml | grep "^version" | cut -d':' -f2 | sed 's/ //g'`
                    NEW_CHART_VERSION=`echo "\$CURRENT_CHART_VERSION" | awk -F. '{\$NF = \$NF + 1;} 1' | sed 's/ /./g'`
                    sed -i  "s/\$CURRENT_CHART_VERSION/\$NEW_CHART_VERSION/g" celery-beat/Chart.yaml
                    sed -i 's/tag.*/tag: "${BRANCH}-${imageTag}"/' celery-beat/values.yaml
                    helm package celery-beat                                                                              
                    helm s3 push --force celery-beat-\$NEW_CHART_VERSION.tgz tysud-charts
                 """
                
                sh """
                    cd /tmp
                    helm pull tysud-charts/celery-worker --untar=true
                    CURRENT_CHART_VERSION=`cat celery-worker/Chart.yaml | grep "^version" | cut -d':' -f2 | sed 's/ //g'`
                    NEW_CHART_VERSION=`echo "\$CURRENT_CHART_VERSION" | awk -F. '{\$NF = \$NF + 1;} 1' | sed 's/ /./g'`
                    sed -i  "s/\$CURRENT_CHART_VERSION/\$NEW_CHART_VERSION/g" celery-worker/Chart.yaml
                    sed -i 's/tag.*/tag: "${BRANCH}-${imageTag}"/' celery-worker/values.yaml
                    helm package celery-worker                                                                              
                    helm s3 push --force celery-worker-\$NEW_CHART_VERSION.tgz tysud-charts
                 """

                 }
             }
        }

        // stage('Countdown to Dev') {
        //     when {
        //         expression {BRANCH == 'develop' && "${DEV_ALARM_ON}" == 'true' }
        //     }
        //     steps {
        //         script {
        //             now = new Date()
        //             println(now.seconds)
        //             println(now.minutes)
        //             println(now.hours+7)
        //             current_sec = (now.hours+7)*3600 + (now.minutes)*60 + (now.seconds)
        //             println "${current_sec}"
        //             def fix_time = "${DEV_DEPLOY_TIME}"
        //             // Date fix_time_parsed = Date.parse('HH:mm:ss', fix_time)
        //             def fix_time_parsed = new SimpleDateFormat('HH:mm:ss').parse(fix_time)
        //             long fix_sec = fix_time_parsed.getTime()/1000
        //             println "${fix_sec}"
        //             long awaiting_duration=fix_sec-current_sec
        //             println "awaiting_duration: ${awaiting_duration}"
        //             if (awaiting_duration > 0){
        //                 sleep "${awaiting_duration}"
        //             } else {
        //                 sleep "0"
        //             }
        //         }
        //      }
        // }

        stage('Deploy to Dev') {
            when {
                expression {BRANCH == 'develop'}
            }
            steps {
            withAWS(credentials: "${DEV_CREDENTIAL}", region: "${DEV_REGION}") {
                sh """
                    aws eks --region ap-southeast-1 update-kubeconfig --name gt-eks-dev
                    helm upgrade --install $eks_svc_name -n $eks_namespace /tmp/$svc_name
                    helm upgrade --install operation-consumer -n $eks_namespace /tmp/operation-consumer
                    helm upgrade --install celery-beat -n $eks_namespace /tmp/celery-beat
                    helm upgrade --install celery-worker -n $eks_namespace /tmp/celery-worker
                 """
                 }
             }
        }

        // stage('Countdown to QA') {
        //     when {
        //         expression {BRANCH == 'qa' && "${QA_ALARM_ON}" == 'true' }
        //     }
        //     steps {
        //         script {
        //             now = new Date()
        //             println(now.seconds)
        //             println(now.minutes)
        //             println(now.hours+7)
        //             current_sec = (now.hours+7)*3600 + (now.minutes)*60 + (now.seconds)
        //             println "${current_sec}"
        //             def fix_time = "${QA_DEPLOY_TIME}"
        //             // Date fix_time_parsed = Date.parse('HH:mm:ss', fix_time)
        //             def fix_time_parsed = new SimpleDateFormat('HH:mm:ss').parse(fix_time)
        //             long fix_sec = fix_time_parsed.getTime()/1000
        //             println "${fix_sec}"
        //             long awaiting_duration=fix_sec-current_sec
        //             println "awaiting_duration: ${awaiting_duration}"
        //             if (awaiting_duration > 0){
        //                 sleep "${awaiting_duration}"
        //             } else {
        //                 sleep "0"
        //             }
                    
        //         }
        //      }
        // }

        stage('Deploy to QA') {
            when {
                expression {BRANCH == 'qa'}
            }
            steps {
            withAWS(credentials: "${QA_CREDENTIAL}", region: "${QA_REGION}") {
                script {
                    deploy = sh (
                    script: """
                        aws eks --region ap-southeast-1 update-kubeconfig --name gt-eks-qa
                        helm upgrade --install $eks_svc_name -n $eks_namespace /tmp/$svc_name
                        helm upgrade --install operation-consumer -n $eks_namespace /tmp/operation-consumer
                        helm upgrade --install celery-beat -n $eks_namespace /tmp/celery-beat
                        helm upgrade --install celery-worker -n $eks_namespace /tmp/celery-worker
                        """,
                    returnStdout: true
                    ).trim()
                }
                
                // script {
                //     POD_NAME = sh (
                //     script: """
                //         kubectl get pods --namespace test -l "app.kubernetes.io/name=api-testing,app.kubernetes.io/instance=api-testing" -o "jsonpath={.items[0].metadata.name}"                     
                //         """,
                //     returnStdout: true
                //     ).trim()
                //     echo "Pod name: ${POD_NAME}"
                // }

               // sh """
               //     sleep 60
               //     kubectl exec -n test ${POD_NAME} -- ginkgo -r --keep-going ./gt-mvp/operations
               // """
                }
             }
        }

        stage('Deploy to Staging') {
            when {
                expression {BRANCH == 'staging'}
            }
            steps {
            withAWS(credentials: "${QA_CREDENTIAL}", region: "${QA_REGION}") {
                script {
                    deploy = sh (
                    script: """
                        aws eks --region ap-southeast-1 update-kubeconfig --name gt-eks-qa
                        helm upgrade --install $eks_svc_name -n $eks_namespace /tmp/$svc_name
                        helm upgrade --install operation-consumer -n $eks_namespace /tmp/operation-consumer
                        helm upgrade --install celery-beat -n $eks_namespace /tmp/celery-beat
                        helm upgrade --install celery-worker -n $eks_namespace /tmp/celery-worker
                        """,
                    returnStdout: true
                    ).trim()
                }
                
                }
             }
        }

        // stage('Countdown to Prod') {
        //     when {
        //         expression {BRANCH == 'release' && "${PROD_ALARM_ON}" == 'true' && "${NOT_HOTFIX}" == 'true' }
        //     }
        //     steps {
        //         script {
        //             now = new Date()
        //             println(now.seconds)
        //             println(now.minutes)
        //             println(now.hours+7)
        //             current_sec = (now.hours+7)*3600 + (now.minutes)*60 + (now.seconds)
        //             println "${current_sec}"
        //             def fix_time = "${PROD_DEPLOY_TIME}"
        //             // Date fix_time_parsed = Date.parse('HH:mm:ss', fix_time)
        //             def fix_time_parsed = new SimpleDateFormat('HH:mm:ss').parse(fix_time)
        //             long fix_sec = fix_time_parsed.getTime()/1000
        //             println "${fix_sec}"
        //             long awaiting_duration=fix_sec-current_sec
        //             println "awaiting_duration: ${awaiting_duration}"
        //             if (awaiting_duration > 0){
        //                 sleep "${awaiting_duration}"
        //             } else {
        //                 sleep "0"
        //             }
        //         }
        //      }
        // }

        stage('Deploy to Prod') {
            when {
                expression {BRANCH == 'release'}
            }
            steps {
            withAWS(credentials: "${PROD_CREDENTIAL}", region: "${PROD_REGION}") {
                sh """
                    aws eks --region ap-southeast-1 update-kubeconfig --name gt-eks-prod
                    helm upgrade --install $eks_svc_name -n $eks_namespace /tmp/$svc_name
                    helm upgrade --install operation-consumer -n $eks_namespace /tmp/operation-consumer
                    helm upgrade --install celery-beat -n $eks_namespace /tmp/celery-beat
                    helm upgrade --install celery-worker -n $eks_namespace /tmp/celery-worker
                 """
                }
            }
            
        }
    }

    // post {
    //     success {
    //         emailext (
    //             subject: "SUCCESS: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]'",
    //             from: "<EMAIL>",
    //             body: """SUCCESS: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]
    //             Check console output at ${env.BUILD_URL}console""",
    //             to: "<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
    //             )
    //     }
    //     failure {
    //         emailext (
    //             subject: "FAILED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]'",
    //             from: "<EMAIL>",
    //             body: """FAILED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]
    //             Check console output at ${env.BUILD_URL}console""",
    //             to: "<EMAIL>, <EMAIL>, <EMAIL>"
    //             )
    //     }
    // }
}
//
