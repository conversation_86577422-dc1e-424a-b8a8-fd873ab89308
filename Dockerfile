FROM python:3.8-alpine3.16 as base
# FROM tysud/python:3.7-alpine-modified as base

FROM base as builder
ENV CRYPTOGRAPHY_DONT_BUILD_RUST=1
RUN apk add --no-cache --virtual .build-deps gcc musl-dev libffi-dev openssl-dev make postgresql-dev git curl curl-dev python3-dev libressl-dev
#RUN curl -sSL https://raw.githubusercontent.com/python-poetry/poetry/master/get-poetry.py | python
RUN GET_POETRY_URL=https://install.python-poetry.org && curl -sSL $GET_POETRY_URL | POETRY_VERSION=1.1.15 python3 -
RUN mkdir -p $HOME/.gt/operation/
COPY pyproject.toml poetry.lock /src/
WORKDIR /src
# RUN python -m venv /env && . /env/bin/activate \
# 	&& $HOME/.poetry/bin/poetry config experimental.new-installer false \
# 	&& $HOME/.poetry/bin/poetry install --no-dev --no-interaction
RUN python -m venv /env && . /env/bin/activate \
    && pip install psycopg2 openpyxl==3.1.5  docxtpl==0.20.0 aiofiles==24.1.0 \
	&& $HOME/.local/bin/poetry config experimental.new-installer false \
	&& $HOME/.local/bin/poetry install --no-dev --no-interaction

FROM base
RUN apk add --no-cache postgresql-libs curl
RUN mkdir -p $HOME/.gt/operation/

COPY --from=builder /env /env
COPY --from=builder /src /src
COPY . /src

COPY ./scripts/entrypoint /entrypoint
RUN sed -i 's/\r$//g' /entrypoint
RUN chmod +x /entrypoint

COPY ./scripts/start /start
RUN sed -i 's/\r$//g' /start
RUN chmod +x /start

# COPY ./scripts/celery/worker/start /start-celeryworker
# RUN sed -i 's/\r$//g' /start-celeryworker
# RUN chmod +x /start-celeryworker

# COPY ./scripts/celery/beat/start /start-celerybeat
# RUN sed -i 's/\r$//g' /start-celerybeat
# RUN chmod +x /start-celerybeat

COPY ./scripts/consumer/start /start-consumer
RUN sed -i 's/\r$//g' /start-consumer
RUN chmod +x /start-consumer

WORKDIR /src
EXPOSE 8000
ENTRYPOINT ["/start"]
