"""Extending Address field of Account tbl

Revision ID: 7dc1f5bded00
Revises: 31c510235d7d
Create Date: 2023-01-06 03:12:52.445438

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7dc1f5bded00'
down_revision = '31c510235d7d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('account', 'address',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=255),
               nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('account', 'address',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=50),
               nullable=False)
    # ### end Alembic commands ###
