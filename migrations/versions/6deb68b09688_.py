"""Auto generated version

Revision ID: 6deb68b09688
Revises: 
Create Date: 2021-12-07 05:28:26.299391

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB

# revision identifiers, used by Alembic.
revision = '6deb68b09688'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('status',
    sa.Column('id', sa.Integer(), nullable=False), #
    sa.Column('barcode', sa.String(), nullable=True), #
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('note', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )

    op.create_table('code',
    sa.Column('barcode', sa.String(), nullable=False), #
    sa.Column('qrcode', sa.String(), nullable=False), #
    sa.Column('state', sa.String(), nullable=True),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=False),
    # sa.Column('printed', sa.Boolean(), nullable=True), # ADDED at 3c627e39556f
    sa.PrimaryKeyConstraint('barcode')
    )

    op.create_table('attempt',
    sa.Column('email', sa.String(), nullable=False), #
    sa.Column('block', sa.Boolean(), nullable=False), #
    sa.Column('attempt', sa.Integer(), nullable=False),
    sa.Column('note', sa.String(500), nullable=True),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=False),
    # sa.Column('userid', sa.String(50), nullable=True), # ADDED at c1dd8908eb52
    sa.PrimaryKeyConstraint('email')
    )
    # op.create_primary_key("attempt_pkey", "attempt", ["email", ])

    op.create_table('kit',
    sa.Column('barcode', sa.String(), nullable=False), #
    sa.Column('nickname', sa.String(), nullable=True), #
    sa.Column('userid', sa.String(), nullable=True), #
    sa.Column('email', sa.String(), nullable=False), #
    sa.Column('myself', sa.Boolean(), nullable=False), #
    sa.Column('name', sa.String(), nullable=False), #
    sa.Column('gender', sa.String(), nullable=False), #
    sa.Column('current_status', sa.String(), nullable=True), #
    sa.Column('current_status_id', sa.Integer(), nullable=False), #
    # sa.Column('info', JSONB(), nullable=False,server_default={}), #
    sa.Column('dob', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('version', sa.String(50), nullable=False), #
    # sa.Column('phone_number', sa.String(50), nullable=True), # ADDED at c1dd8908eb52
    sa.PrimaryKeyConstraint('barcode')
    )


    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('status')
    op.drop_table('code')
    op.drop_table('attempt')
    op.drop_table('kit')
    # ### end Alembic commands ###