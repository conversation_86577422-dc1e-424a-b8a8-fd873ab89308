"""Change ke

Revision ID: c1dd8908eb52
Revises: 3c627
Create Date: 2022-11-03 05:11:34.435498

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c1dd8908eb52'
down_revision = '3c627e39556f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE attempt DROP CONSTRAINT attempt_pkey') # FOR Test Only
    op.execute("UPDATE kit SET userid='null' WHERE userid IS NULL")
    op.add_column('attempt', sa.Column('userid', sa.String(length=50)))
    op.execute("UPDATE attempt SET userid=public.attempt.email WHERE userid IS NULL") # FOR Test Only
    op.add_column('kit', sa.Column('phone_number', sa.String(length=50), nullable=True))
    op.alter_column('attempt', 'email',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('kit', 'email',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.create_primary_key("attempt_pkey", "attempt", ["userid", ]) # FOR Test Only
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute('ALTER TABLE attempt DROP CONSTRAINT attempt_pkey') # FOR Test Only
    op.execute("UPDATE attempt SET email='<EMAIL>' WHERE email IS NULL")
    op.execute("UPDATE kit SET email='<EMAIL>' WHERE email IS NULL")
    op.alter_column('kit', 'userid',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('kit', 'email',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('attempt', 'email',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.drop_column('attempt', 'userid')
    op.drop_column('kit', 'phone_number')
    op.create_primary_key("attempt_pkey", "attempt", ["email", ]) # FOR Test Only
    # ### end Alembic commands ###