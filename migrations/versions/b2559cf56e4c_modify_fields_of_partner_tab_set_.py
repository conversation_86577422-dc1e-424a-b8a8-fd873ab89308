"""Modify fields of PARTNER tab & Set DEFAULT value

Revision ID: b2559cf56e4c
Revises: 152933eb6cf2
Create Date: 2023-01-05 04:32:07.524419

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b2559cf56e4c'
down_revision = '152933eb6cf2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account', sa.Column('type', sa.String(length=50), nullable=False, server_default=str("CLINIC")))
    op.alter_column('staff', 'role',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=50),
               nullable=False,
               server_default=str("DOCTOR"))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account', 'type')
    op.alter_column('staff', 'role',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=50),
               nullable=False)
    # ### end Alembic commands ###