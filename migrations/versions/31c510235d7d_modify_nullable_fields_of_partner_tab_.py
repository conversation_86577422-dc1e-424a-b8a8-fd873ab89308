"""Modify NULLABLE fields of PARTNER tab & Set DEFAULT value

Revision ID: 31c510235d7d
Revises: b2559cf56e4c
Create Date: 2023-01-05 04:49:29.727228

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '31c510235d7d'
down_revision = 'b2559cf56e4c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('staff', 'email',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('staff', 'phone_number',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=50),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('staff', 'email',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=50),
               nullable=False)
    op.alter_column('staff', 'phone_number',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.String(length=50),
               nullable=False)
    # ### end Alembic commands ###
