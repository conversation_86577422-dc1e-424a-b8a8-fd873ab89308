"""empty message

Revision ID: 55da5b16387d
Revises: 
Create Date: 2022-12-30 14:30:25.955358

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = '55da5b16387d'
down_revision = 'c1dd8908eb52'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("kit", sa.Column('product_code', sa.String(50), nullable=False, server_default=""))
    op.add_column("kit", sa.Column("sample_collection_date", sa.DateTime(), nullable=True))
    # op.add_column("kit", sa.Column('sample_collection_time', sa.Integer(), nullable=False, server_default=1))
    op.add_column("kit", sa.Column('sample_receipt_date', sa.DateTime(), nullable=True))
    op.add_column("kit", sa.Column('expected_report_release_date', sa.DateTime(), nullable=True))
    op.add_column("kit", sa.Column('source_id', sa.Integer(), nullable=True))
    op.add_column("kit", sa.Column('deleted_at', sa.DateTime(), nullable=True))
    op.add_column("kit", sa.Column('validate_account', sa.Boolean(), nullable=True))
    op.add_column("kit", sa.Column('note', sa.String(100), nullable=True))
    op.add_column("kit", sa.Column('diagnosis', sa.String(120), nullable=True))
    # op.add_column("kit", sa.Column('free_sample', sa.Boolean(), nullable=False, server_default=False))
    op.add_column("kit", sa.Column('discount_campain', sa.String(120), nullable=True))
    op.add_column("kit", sa.Column('customer_support_id', UUID(), nullable=True))
    
    op.create_table(
        "source",
        sa.Column("id", UUID()),
        sa.Column("account_history_id", UUID()),
        sa.Column("nominator_id", UUID()),
        sa.PrimaryKeyConstraint("id")
    )
    
    op.create_table(
        "sale_account_history",
        sa.Column("id", UUID()),
        sa.Column("account_id", UUID(), nullable=False),
        sa.Column("pic_id", UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id")
    )
    
    op.create_table(
        "staff",
        sa.Column("id", UUID()),
        sa.Column("name", sa.String(50), nullable=False),
        sa.Column("email", sa.String(50), nullable=False),
        sa.Column("userid", UUID(), nullable=False),
        sa.Column("account_id", UUID(), nullable=False),
        sa.Column("type", sa.String(50), nullable=False),
        sa.Column("phone_number", sa.String(50), nullable=False),
        sa.Column("role", sa.String(50), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id")
    )
    
    op.create_table(
        "account",
        sa.Column("id", UUID()),
        sa.Column("name", sa.String(50), nullable=False),
        sa.Column("address", sa.String(50), nullable=False),
        sa.Column("description", sa.String(120), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id")
    )

def downgrade():
    op.drop_table('account')
    op.drop_table('staff')
    op.drop_table('sale_account_history')
    op.drop_table('source')
    op.drop_column('kit', 'customer_support_id')
    op.drop_column('kit', 'discount_campain')
    op.drop_column('kit', 'free_sample')
    op.drop_column('kit', 'diagnosis')
    op.drop_column('kit', 'note')
    op.drop_column('kit', 'validate_account')
    op.drop_column('kit', 'deleted_at')
    op.drop_column('kit', 'source_id')
    op.drop_column('kit', 'expected_report_release_date')
    op.drop_column('kit', 'sample_receipt_date')
    op.drop_column('kit', 'sample_collection_time')
    op.drop_column('kit', 'sample_collection_date')
    op.drop_column('kit', 'product_code')
    pass
