"""empty message

Revision ID: ea92f2fbe2c2
Revises: 8dc2f6bded68
Create Date: 2023-01-09 15:49:50.150699

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ea92f2fbe2c2'
down_revision = '8dc2f6bded68'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('account', 'name',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('account', 'address',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('account', 'area',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('account', 'description',
               existing_type=sa.VARCHAR(length=120),
               nullable=True)
    op.alter_column('account', 'type',
               existing_type=sa.VARCHAR(length=50),
               nullable=True,
               existing_server_default=sa.text("'CLINIC'::character varying"))
    op.alter_column('account', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('account', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('account', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('kit', 'sample_meta_id',
               existing_type=postgresql.UUID(),
               nullable=False,
               existing_server_default=sa.text('uuid_generate_v4()'))
    op.alter_column('kit', 'version',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.alter_column('kit', 'sample_receipt_date',
               existing_type=sa.DATE(),
               nullable=False)
    op.alter_column('kit', 'expected_report_release_date',
               existing_type=sa.DATE(),
               nullable=False)
    op.alter_column('kit', 'source_id',
               existing_type=postgresql.UUID(),
               nullable=False)
    op.alter_column('kit', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('kit', 'free_of_charge',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.drop_column('kit', 'diagnosis')
    op.drop_column('kit', 'validate_account')
    op.drop_column('kit', 'discount_campain')
    op.alter_column('sale_account_history', 'pic_id',
               existing_type=postgresql.UUID(),
               nullable=True)
    op.alter_column('sale_account_history', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('sale_account_history', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('sample_meta', 'userid',
               existing_type=postgresql.UUID(),
               type_=sa.String(length=50),
               existing_nullable=True)
    op.alter_column('sample_meta', 'address',
               existing_type=sa.VARCHAR(length=250),
               type_=sa.String(length=100),
               nullable=True)
    op.alter_column('sample_meta', 'diagnosis',
               existing_type=sa.VARCHAR(length=100),
               type_=sa.String(length=120),
               existing_nullable=True)
    op.alter_column('sample_meta', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=False)
    op.alter_column('sample_meta', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('sample_meta', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('source', 'account_history_id',
               existing_type=postgresql.UUID(),
               nullable=False)
    op.alter_column('staff', 'userid',
               existing_type=postgresql.UUID(),
               type_=sa.String(length=50),
               existing_nullable=True)
    op.alter_column('staff', 'account_id',
               existing_type=postgresql.UUID(),
               nullable=True)
    op.alter_column('staff', 'role',
               existing_type=sa.VARCHAR(length=50),
               nullable=True,
               existing_server_default=sa.text("'DOCTOR'::character varying"))
    op.alter_column('staff', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('staff', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('staff', 'deleted_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
