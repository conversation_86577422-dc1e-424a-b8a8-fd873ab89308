"""Auto generated version

Revision ID: 3c627e39556f
Revises: 9deb68b096ef
Create Date: 2021-12-30 02:28:21.286376

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3c627e39556f'
down_revision = '9deb68b096ef'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # add nullable columns
    op.add_column('code', sa.Column('printed', sa.<PERSON>(), nullable=True))
    op.add_column('sample', sa.Column('physical_position', sa.String(length=50), nullable=True))
    op.add_column('sample', sa.Column('chip_type', sa.String(length=50), nullable=True))
    op.add_column('sample', sa.Column('assembly', sa.String(length=50), nullable=True))
    op.add_column('sample', sa.Column('positive_tested', sa.<PERSON>(), nullable=True))
    # set existing data value to not null
    op.execute("UPDATE code SET printed = false")
    op.execute("UPDATE sample SET physical_position = 'R01C01'")
    op.execute("UPDATE sample SET chip_type = 'GSAv3'")
    op.execute("UPDATE sample SET assembly = 'hg38'")
    op.execute("UPDATE sample SET positive_tested = false")
    # set column to not null
    op.alter_column('code', 'printed', nullable=False)
    op.alter_column('sample', 'physical_position', nullable=False)
    op.alter_column('sample', 'chip_type', nullable=False)
    op.alter_column('sample', 'assembly', nullable=False)
    op.alter_column('sample', 'positive_tested', nullable=False)
    # ### end Alembic commands ###

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('sample', 'positive_tested')
    op.drop_column('sample', 'assembly')
    op.drop_column('sample', 'chip_type')
    op.drop_column('sample', 'physical_position')
    op.drop_column('code', 'printed')
    # ### end Alembic commands ###