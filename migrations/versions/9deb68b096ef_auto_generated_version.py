"""Auto generated version

Revision ID: 9deb68b096ef
Revises: 
Create Date: 2021-12-07 05:28:26.299391

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9deb68b096ef'
down_revision = '6deb68b09688'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sample',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('operation_id', sa.String(length=50), nullable=False),
    sa.Column('num', sa.Integer(), nullable=False),
    sa.Column('position', sa.String(length=50), nullable=False),
    sa.Column('barcode', sa.String(length=50), nullable=False),
    sa.Column('batch_barcode', sa.String(length=50), nullable=False),
    sa.Column('vinmec_id', sa.String(length=50), nullable=True),
    sa.Column('chip_id', sa.String(length=50), nullable=False),
    sa.Column('gender', sa.String(length=50), nullable=False),
    sa.Column('technician_name', sa.String(length=50), nullable=False),
    sa.Column('qc_status', sa.String(length=50), nullable=False),
    sa.Column('created_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_time', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sample')
    # ### end Alembic commands ###