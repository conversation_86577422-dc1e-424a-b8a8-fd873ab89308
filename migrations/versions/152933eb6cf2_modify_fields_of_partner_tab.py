"""Modify fields of PARTNER tab

Revision ID: 152933eb6cf2
Revises: 55da5b16387d
Create Date: 2023-01-05 02:37:21.296744

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '152933eb6cf2'
down_revision = '55da5b16387d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('account', sa.Column('area', sa.String(length=50), nullable=False))
    op.alter_column('staff', 'userid',
               existing_type=sa.String(length=50),
               type_=postgresql.UUID(),
               nullable=True)
    op.drop_column('staff', 'type')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('account', 'area')
    op.alter_column('staff', 'userid',
               existing_type=postgresql.UUID(),
               type_=sa.String(length=50),
               nullable=False)
    op.add_column('account', sa.Column('type', sa.String(length=50), nullable=False))
    # ### end Alembic commands ###
