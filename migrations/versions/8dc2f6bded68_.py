"""Extending Address field of Account tbl

Revision ID: 8dc2f6bded68
Revises: 31c510235d7d
Create Date: 2023-01-06 03:12:52.445438

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = '8dc2f6bded68'
down_revision = '7dc1f5bded00'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")	 # RUN MANNUAL WITH ROOT/SUPER USER e.g sonarqube
    # op.execute("ALTER TABLE kit DROP COLUMN myself")	
    # op.execute("ALTER TABLE kit DROP COLUMN info")	
    # op.execute("ALTER TABLE kit DROP COLUMN userid")	
    # op.execute("ALTER TABLE kit DROP COLUMN email")	
    # op.execute("ALTER TABLE kit DROP COLUMN name")	
    # op.execute("ALTER TABLE kit DROP COLUMN gender")	
    # op.execute("ALTER TABLE kit DROP COLUMN dob")	
    # op.execute("ALTER TABLE kit DROP COLUMN phone_number")
    op.alter_column('staff', 'userid',
               existing_type=sa.Integer(),
               type_=postgresql.UUID(),
               nullable=True)
    op.add_column("kit", sa.Column('product_name', sa.String(50), nullable=False, server_default=""))
    op.add_column("kit", sa.Column('sample_collection_time', sa.Integer(), nullable=False, server_default='1'))
    op.add_column("kit", sa.Column('free_of_charge', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column("kit", sa.Column('is_priority', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column("kit", sa.Column('promotion', sa.String(120), nullable=True))
    op.add_column("kit", sa.Column('customer_support_name', sa.String(120), nullable=True))
    op.add_column("kit", sa.Column('sample_meta_id', UUID(), nullable=True))

    op.create_table(	
        "sample_meta",	
        sa.Column("id", UUID(), server_default=sa.text("uuid_generate_v4()")), #	
        sa.Column("userid", UUID(), nullable=True),	
        sa.Column("full_name", sa.String(50), nullable=False),	
        sa.Column("address", sa.String(250), nullable=False),	#Modified
        sa.Column("email", sa.String(50), nullable=True),	
        sa.Column("phone_number", sa.String(50), nullable=True),	
        sa.Column("validate_account", sa.Boolean(), nullable=False),	
        sa.Column("gender", sa.String(50), nullable=False),	
        sa.Column("dob", sa.DateTime(), nullable=False),	
        sa.Column("diagnosis", sa.String(100), nullable=True),	
        sa.Column("created_at", sa.DateTime(), nullable=False),	
        sa.Column("updated_at", sa.DateTime(), nullable=True),	
        sa.Column("deleted_at", sa.DateTime(), nullable=True),	
        sa.PrimaryKeyConstraint("id")	
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # op.alter_column('staff', 'userid',
    #            existing_type=postgresql.UUID(),
    #            type_=sa.Integer(), # cannot be cast automatically
    #            nullable=False)

    op.drop_column('kit', 'product_name')
    op.drop_column('kit', 'sample_collection_time')
    op.drop_column('kit', 'free_of_charge')
    op.drop_column('kit', 'is_priority')
    op.drop_column('kit', 'promotion')
    op.drop_column('kit', 'customer_support_name')
    op.drop_column('kit', 'sample_meta_id')
    op.drop_table('sample_meta')
    # ### end Alembic commands ###
