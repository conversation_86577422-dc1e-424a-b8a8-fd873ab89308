import os
from logging.config import fileConfig

from sqlalchemy import engine_from_config, pool

from alembic import context
from src.operation.app import db, load_modules
from src.operation.config import config

conf = context.config
fileConfig(conf.config_file_name)

load_modules()
target_metadata = db
conf.set_main_option("sqlalchemy.url", str(config["DB_URL"]))


def get_url():
    if os.environ.get("OPERATION_CONFIG_PATH"):
        user = os.getenv("POSTGRES_USER", "operation_user")
        password = os.getenv("POSTGRES_PASSWORD", "test_operation_pass")
        server = os.getenv("POSTGRES_SERVER", "localhost:8540")
        db = os.getenv("POSTGRES_DB", "test_operation_v3")
    else:
        user = os.getenv("POSTGRES_USER", "operation_user")
        password = os.getenv("POSTGRES_PASSWORD", "operation_pass")
        server = os.getenv(
            "POSTGRES_SERVER", "gt-qa-rds.cntsuccfkujx.ap-southeast-1.rds.amazonaws.com"
        )
        db = os.getenv("POSTGRES_DB", "operation_v3")
        pass
    return f"postgresql://{user}:{password}@{server}/{db}"


def run_migrations_offline():
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = get_url()
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        compare_type=True,
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    configuration = conf.get_section(conf.config_ini_section)
    configuration["sqlalchemy.url"] = get_url()
    connectable = engine_from_config(
        configuration,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata, compare_type=True
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
