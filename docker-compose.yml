version: '3.8'

services:
  operation:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: operation
    # '/start' is the shell script used to run the service
    command: /start
    # this volume is used to map the files and folders on the host to the container
    # so if we change code on the host, code in the docker container will also be changed
    volumes:
      - .:/src
      - ./scripts/start:/start
    restart: on-failure
    ports:
      - 8010:8000
    network_mode: host

  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: celery_worker
    command: /start-celeryworker
    volumes:
      - .:/src
    restart: on-failure
    network_mode: host

  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: celery_beat
    command: /start-celerybeat
    volumes:
      - .:/src
    restart: on-failure
    network_mode: host

  operation_consumer:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: operation_consumer
    command: /start-consumer
    volumes:
      - .:/src
    restart: on-failure
    network_mode: host
