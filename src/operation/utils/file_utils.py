import io
from datetime import datetime
from typing import List
from openpyxl import load_workbook


class ExcelValidationError(Exception):
    pass


def parse_cell(value, expected_type):
    if expected_type == str:
        return str(value).strip() if value is not None else None
    if expected_type == int:
        try:
            return int(value)
        except:
            raise ValueError("Expected integer")
    if expected_type == float:
        try:
            return float(value)
        except:
            raise ValueError("Expected float")
    if expected_type == "date":
        try:
            if isinstance(value, datetime):
                return value.date()
            return datetime.strptime(str(value), "%d/%m/%Y").date()
        except:
            raise ValueError("Invalid date format")
    return value


def read_and_validate_excel_schema(file_bytes: bytes, template_schema: List[dict]) -> List[dict]:
    workbook = load_workbook(io.BytesIO(file_bytes), data_only=True)
    sheet = workbook.active

    headers = [cell.value for cell in sheet[1]]
    expected_columns = [col["display_name"] for col in template_schema]

    # Check missing columns
    missing = [col for col in expected_columns if col not in headers]
    if missing:
        raise ExcelValidationError(f"Missing required columns: {', '.join(missing)}")

    header_index = {header: idx for idx, header in enumerate(headers)}
    seen_unique_values = {col["display_name"]: set() for col in template_schema if col.get("unique")}

    results = []
    for idx, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
        if all(cell is None for cell in row):
            continue  # skip empty rows

        row_data = {}
        for col in template_schema:
            display_name = col["display_name"]
            field = col["field"]
            required = col.get("required", False)
            expected_type = col.get("type", str)
            unique = col.get("unique", False)

            col_idx = header_index[display_name]
            value = row[col_idx] if col_idx < len(row) else None

            if required and (value is None or str(value).strip() == ""):
                raise ExcelValidationError(f"Row {idx}: '{display_name}' is required")

            if value is not None:
                try:
                    parsed_value = parse_cell(value, expected_type)
                    row_data[field] = parsed_value
                except ValueError as e:
                    raise ExcelValidationError(f"Row {idx}: '{display_name}' {str(e)}")
            else:
                row_data[field] = None

            if unique:
                if parsed_value in seen_unique_values[display_name]:
                    raise ExcelValidationError(
                        f"Duplicate value '{parsed_value}' found in column '{display_name}' at row {idx}")
                seen_unique_values[display_name].add(parsed_value)

        results.append(row_data)

    if len(results) == 0:
        raise ExcelValidationError("File is empty")

    return results
