from ..config import config
import boto3
import json

QUEUE_URL = config['AUTHZ']['SQS'][config['ENV']]

sqs=boto3.client('sqs',
        aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"], 
        aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"], 
        region_name=config["AWS"]["CONSUMER_REGION_NAME"])

def add_group_new_user(sub):
    mess_body = {"action":"ADD_USER_TO_GROUP","body":{"user_id":f"{sub}","group_id":"user"}}
    return json.dumps(mess_body)

def add_group_cccd_new_user(sub):
    mess_body = {"action":"ADD_USER_TO_GROUP","body":{"user_id":f"{sub}","group_id":"cccd"}}
    return json.dumps(mess_body)

def send_mess_queue(mess_body):
    
    response = sqs.send_message(
        QueueUrl=QUEUE_URL,
        MessageBody=mess_body
    )
    return response