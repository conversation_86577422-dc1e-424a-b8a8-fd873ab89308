import base64
import csv
import json
import logging
import math
import os
import re
import secrets
import string
import time
from datetime import date, datetime, timedelta
from functools import wraps
from http.client import HTTPResponse
from urllib import request
from urllib.error import HTTPError

import boto3
import jwt
import pytz
import requests
import urllib3
from botocore.client import Config
from botocore.exceptions import ClientError
from fastapi import HTTPException
from jwt import PyJWKClient
from requests import RequestException
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ..config import config

DEFAULT_DATE_STR = "%Y-%m-%dT%H:%M:%S.%fZ"
DEFAULT_DATE_NO_TIME_ZONE = "%Y-%m-%d"
DEFAULT_DATE_NO_TIME_ZONE_W_HOUR = "%Y-%m-%dT%H:%M:%S"
DEFAULT_YEAR = "%Y"


import random
import unicodedata

from .. import logger
from ..schemas.staff import *


def add_business_days_range(start_date, days):
    current_date = start_date
    while days != 0:
        current_date += timedelta(days=1 if days > 0 else -1)
        if current_date.weekday() < 5:  # Monday to Friday are business days
            days += 1 if days < 0 else -1
    return current_date


def calculate_business_days(date_str, n):
    if not n:
        n = 0
    else:
        n = int(n)

    if not date_str:
        return None, None
    # Convert the input date string to a datetime object
    date_str = str(date_str).split(" ")[0]
    start_date = datetime.strptime(str(date_str), "%Y-%m-%d")

    # Calculate the date minus n business days
    minus_n_days = add_business_days_range(start_date, -n)
    # Calculate the date plus n business days
    plus_n_days = add_business_days_range(start_date, n)

    # Convert the resulting datetime objects back to strings
    minus_n_days_str = minus_n_days.strftime("%Y-%m-%d")
    plus_n_days_str = plus_n_days.strftime("%Y-%m-%d")

    return minus_n_days_str, plus_n_days_str


def is_wsq(base64_string):
    try:
        # Decode the base64 string
        binary_data = base64.b64decode(base64_string)

        # WSQ files start with the bytes 0xFF 0xA0
        wsq_signature = b"\xff\xa0"

        # Check if the binary data starts with the WSQ signature
        if binary_data.startswith(wsq_signature):
            return True
        else:
            return False
    except Exception as e:
        # If there's an error in decoding or processing, it's not a valid WSQ
        print(f"Error: {e}")
        return False


def contains_no_digits(s):
    """
    Returns True if the string does not contain any numeric characters [0-9], False otherwise.
    """
    # Check for the presence of any digit in the string
    return not bool(re.search(r"\d", s))


def convert_vnese_full_name_to_print_text(input_name):
    input_name_stripped = input_name.strip()
    name_normalized = "".join(
        c
        for c in unicodedata.normalize("NFD", input_name_stripped)
        if unicodedata.category(c) != "Mn"
    )

    # Replace spaces with underscores and convert to lowercase
    name_normalized = name_normalized.replace(" ", "_").lower()
    return name_normalized


def calculate_estimated_gender(sample: dict):
    if sample["estimated_gender"] == "True":
        sample["estimated_gender"] = sample["gender"]
    elif sample["estimated_gender"] == "False":
        sample["estimated_gender"] = "N/A"
    else:
        sample["estimated_gender"] = None

    return sample


def calculate_weight_of_well_position(well_position):
    row, col = split_text_n_number(well_position)
    return ord(row) + int(col) * 10
    pass


def convert_rowproxy_to_dict(row):
    res = {}
    for column in row.keys():
        res[column] = (
            str(getattr(row, column)) if getattr(row, column) is not None else None
        )

    return res


def convert_rowproxy_to_dict_w_original_type(row):
    res = {}
    for column in row.keys():
        res[column] = getattr(row, column) if getattr(row, column) is not None else None

    return res


def parse_adn_result_on_s3_bucket(payload: dict, agency_name: str = "vneid"):
    request_entity = request.Request(
        method="POST",
        url=f"{config.get('AGENCY_STORAGE').get('PARSING_API')}/{agency_name}",
        data=json.dumps(payload).encode("utf-8"),
        headers={"Content-Type": "application/json"},
    )
    response_entity: HTTPResponse = request.urlopen(request_entity)
    # logger.info(f"Response: code: {response_entity.status}, body: {response_entity.read()}")

    # Read and decode the response
    response_body = response_entity.read().decode("utf-8")

    # Parse the response body as JSON
    response_json = json.loads(response_body)

    return response_json

    pass


async def send_adn_result_to_vneid_agency(payload: dict):
    # headers = {'api_key': config.get('AGENCY_CONNECTION').get('GOVERNMENT').get('AGENCY_API_KEY')}
    api_key = config.get("AGENCY_CONNECTION").get("GOVERNMENT").get("AGENCY_API_KEY")
    authorization_token = (
        config.get("AGENCY_CONNECTION").get("GOVERNMENT").get("BEARER_TOKEN")
    )
    headers = dict()
    if api_key:
        headers["api_key"] = api_key
    if authorization_token:
        headers["Authorization"] = f"Bearer {authorization_token}"
    print("headers: ", headers)
    count_retry = 0
    response = dict()

    while count_retry < 5:
        logger.info(f"Retry number: {count_retry}")
        try:
            request_entity = request.Request(
                method="POST",
                url=f"{config.get('AGENCY_CONNECTION').get('GOVERNMENT').get('AGENCY_GATEWAY')}",
                data=json.dumps(payload).encode("utf-8"),
                headers=headers,
            )
            response_entity: HTTPResponse = request.urlopen(request_entity)
            error_code = 200

            response["status_code"] = str(
                config.get("AGENCY_INTEGRATION_STATUS")
                .get("CODE_MAPPING")
                .get("SEND_REQUEST_SUCCESS")
            )
            response["error_message"] = "Thành công"
            response["response_date"] = get_current_date_time_utc_7()
            # response_date = datetime.now(tz=timezone.utc)

            logger.info("Success call")
            logger.info(
                f"Response: code: {response_entity.status}, body: {response_entity.read()}"
            )
            # logger.info(f"Response: code: {error_code}, body: {payload}")

            return response, response_entity

        except HTTPError as e:
            logger.error("Fail call", e)
            logger.error("Retry attempt:", count_retry)
            count_retry += 1
            if count_retry == 5:
                response["status_code"] = str(
                    config.get("AGENCY_INTEGRATION_STATUS")
                    .get("CODE_MAPPING")
                    .get("SEND_REQUEST_FAIL")
                )
                response["error_message"] = e.__str__()
                response["response_date"] = None

                error_code = e.getcode()
                logger.info("Failed call")
                logger.info(f"Response: code: {error_code} - e.__str__()")

                return response, None

            time.sleep(3)

    pass


async def get_version_report_px(barcode):
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    response = requests.get(
        config["CARD_MANAGEMENT"]["KIT_REPORT_URL"] + barcode + "/dynamic-px-clinical",
        headers=request_headers,
        timeout=config["CARD_MANAGEMENT"]["TIMEOUT"],
    )
    res_data = json.loads(response.text)

    # if res_data.get('errors').get('code') == 404:
    if res_data.get("errors") or not res_data.get("data"):
        return None, None
    else:
        return res_data.get("data").get("version").get("report"), res_data.get(
            "data"
        ).get("version").get("database")


async def get_technology_w_product_code(code):
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    response = requests.get(
        config["SHOP_URL"][config["ENV"]]
        + config["SHOP_URL"]["GET_PRODUCT_BY_ID"]
        + code,
        headers=request_headers,
        timeout=3,
    )
    res_data = json.loads(response.text)
    return res_data.get("data").get("technology"), res_data.get("data").get("name")


async def get_product_n_technology():
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    response = requests.get(
        config["SHOP_URL"][config["ENV"]] + "/admin/product/",
        headers=request_headers,
        timeout=3,
    )
    res_data = json.loads(response.text).get("data")
    product_mapping = dict()

    for product in res_data:
        product_code = product["code"]
        product_mapping[product_code] = product

    return product_mapping


def filter_personal_infor(sample_infor: dict):
    if sample_infor.get("identifier_code"):
        sample_infor.pop("identifier_code")

    if sample_infor.get("full_name"):
        sample_infor.pop("full_name")

    if sample_infor.get("dob"):
        sample_infor.pop("dob")

    if sample_infor.get("yob"):
        sample_infor.pop("yob")

    if sample_infor["gender"] == "male":
        sample_infor["gender"] = 1
    if sample_infor["gender"] == "female":
        sample_infor["gender"] = 2

    return sample_infor


def combine_sample_infor_w_product_infor(sample_list, product_mapping: dict):
    if len(sample_list) > 0:
        data = []
        for row in sample_list:
            result = dict(row)
            product = product_mapping[result["product_code"]]
            result["technology"] = product["technology"]
            result["product_name"] = product["name"]

            result = filter_personal_infor(result)

            data.append(result)
        return data

    return sample_list


def filter_personal_infor_from_sample_list(sample_list):
    if len(sample_list) > 0:
        data = []
        for row in sample_list:
            result = dict(row)
            result = filter_personal_infor(result)
            data.append(result)

        return data

    return sample_list


def paging_a_list_of_rows(lst, page_size, page_number):
    total_pages = len(lst) // page_size + (len(lst) % page_size > 0)

    if page_number < 1 or page_number > total_pages:
        return []

    start_index = (page_number - 1) * page_size
    end_index = start_index + page_size

    return lst[start_index:end_index]


def get_alphabet_position_vertical(index: int, MAX_ROWS=8):
    """
    index: 1-based indexing
    MAX_ROWS: number of rows
    return:
        8x12: A1-H12 (96)
        10x10: A1-J10 (100)

        Vertical:
            A1  A2
            B1  B2
            C1  C2
            ..  ..
            H1  H2
            ..  ..
            (J1)(J2)
    """

    # alphabet_idx = index%8 - 1
    alphabet_idx = (index % MAX_ROWS if index % MAX_ROWS > 0 else MAX_ROWS) - 1
    pos_prefix = list(string.ascii_uppercase)[alphabet_idx]
    pos_suffix = math.ceil(index / MAX_ROWS)
    return pos_prefix + str(pos_suffix)


def get_alphabet_position_horizontal(index: int):
    """
    index: 1-based indexing
    return: A1-J10
    """

    # math.ceil(20/10)=2 --> alphabet_idx: 1 : B10
    # math.ceil(21/10)=3 --> alphabet_idx: 2 : C1

    alphabet_idx = math.ceil(index / 10) - 1
    pos_prefix = list(string.ascii_uppercase)[alphabet_idx]
    pos_suffix = "10" if index % 10 == 0 else str(index % 10)
    return pos_prefix + pos_suffix


def split_text_n_number(data):
    match = re.match(r"([a-z]|[A-Z]+)([0-9]+)", data, re.I)
    if match:
        items = match.groups()
        text = items[0]
        num = items[1]
        return text, num
    else:
        return None, None


def get_no_required_chips(num_samples_in_plate, chip_cap):
    return math.ceil(num_samples_in_plate / int(chip_cap))


def get_no_required_cols(num_samples_in_chip, max_cols):
    return math.ceil(num_samples_in_chip / int(max_cols))


# zero-based index
def get_position_given_format(row_idx, col_idx, ROW_FORMAT, COL_FORMAT):
    if ROW_FORMAT == "R" and COL_FORMAT == "C":
        row = str(row_idx + 1)
        col = str(col_idx + 1)
        position = "{}{}{}{}".format(ROW_FORMAT, row.zfill(2), COL_FORMAT, col.zfill(2))
        return position

    return None


def gen_random_unique_string(product_code, length: int = 0):
    if length:
        random_str = "".join((secrets.choice(string.digits) for x in range(length)))
        code = random_str
    else:
        random_str = "".join((secrets.choice(string.digits) for x in range(10)))
        code = str(random_str) + str(product_code)
    return code


def generate_barcode_string_v2(product_code, length: int = 10):
    random_str = "".join((secrets.choice(string.digits) for x in range(length)))
    code = str(product_code) + str(random_str)
    return code


def generate_samplecode_string(length: int = 6):
    characters = (
        string.ascii_uppercase + string.digits
    )  # Include uppercase letters and digits
    random_str = "".join(secrets.choice(characters) for _ in range(length))
    return random_str


def generate_request_code_string(length: int = 6):
    characters = (
        string.ascii_uppercase + string.digits
    )  # Include uppercase letters and digits
    random_str = "".join(secrets.choice(characters) for _ in range(length))
    return random_str


def ensure_trailing_slash(url):
    return os.path.join(url, "")


def success_response_w_pagination(body, pagination):
    if pagination:
        res = {
            "data": body,
            "pagination": {
                "page_size": pagination.get("page_size"),
                "page_number": pagination.get("page_number"),
                "total": pagination.get("total"),
            },
            "detail": [],
        }
    else:
        res = {"data": body, "detail": []}
    return res


def success_response(body):
    res = {"data": body, "detail": []}
    return res


def format_date(date, date_format=DEFAULT_DATE_STR, validate_current_time=True):
    date_time_obj, err = convert_str_to_iso_datetime(date, date_format=date_format)
    if err:
        err = str(f"{date} is not accepted format, the correct format is {date_format}")
        raise ValueError(err)
    current_date_obj = datetime.now()
    current_date_iso, err = convert_datetime_to_iso(
        current_date_obj, date_format=date_format
    )
    if validate_current_time:
        if date_time_obj > current_date_iso:
            err = str(f"{date} can not greater than the current time")
            raise ValueError(err)
    if date_format == DEFAULT_DATE_NO_TIME_ZONE:
        return date_time_obj.date()
    return date_time_obj


def lid_failure_response(err, data=None):
    logger.error(str(err))
    errs = [
        {
            "msg": str(err),
            "data": data,
        }
    ]
    return errs


def failure_response(err, data=None):
    logger.error(str(err), exc_info=True)
    errs = [
        {
            "msg": str(err),
            "data": data,
        }
    ]
    return errs


def get_product_by_product_code(product_code: str):
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    response = requests.get(
        config["SHOP_URL"][config["ENV"]]
        + config["SHOP_URL"]["GET_PRODUCT_BY_ID"]
        + product_code,
        headers=request_headers,
        timeout=3,
    )
    response.raise_for_status()
    product_detail = response.json().get("data")
    return product_detail


def get_current_time_gmt_plus_7():
    tz = pytz.timezone("Etc/GMT-7")
    return datetime.now(tz)


def failure_response_kit_registration(err, remain_attempts=None):
    logger.error(str(err))
    if remain_attempts is not None:
        errs = [{"msg": str(err), "remain_attempts": int(remain_attempts)}]
    else:
        errs = [{"msg": str(err)}]
    return errs


def validate_email(email):
    regex = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
    if re.fullmatch(regex, email):
        return True
    else:
        return False


def get_current_date_time():
    """
    UTC == 0h
    """
    res_date_time = datetime.utcnow().strptime(
        datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"), "%Y-%m-%dT%H:%M:%S.%fZ"
    )
    return res_date_time


def get_current_date_time_utc_7():
    """
    UTC+7 == UTC + 7h
    """
    utc_time = datetime.utcnow()
    utc_plus_7 = utc_time + timedelta(hours=7)

    # Format the datetime object to match the desired format
    # formatted_time = utc_plus_7.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    return utc_plus_7


def get_current_date_time_utc_7_in_desired_format():
    """
    Returns the current date and time in UTC+7 in the format "YYYY-MM-DD HH:MM:SS"
    """
    utc_time = datetime.utcnow()
    utc_plus_7 = utc_time + timedelta(hours=7)

    # Format the datetime object to match the desired format
    utc_plus_7 = utc_plus_7.replace(microsecond=0)
    return utc_plus_7


def convert_str_to_iso_datetime(data_str, date_format=DEFAULT_DATE_STR):
    try:
        date_time_obj = datetime.strptime(data_str, date_format)
        return date_time_obj, None
    except Exception as e:
        return None, e


def convert_str_to_datetime(data_str):
    try:
        date_time_obj = datetime.strptime(data_str, "%Y-%m-%dT%H:%M:%S.%f+00:00")
        return date_time_obj, None
    except Exception as e:
        return None, e


def convert_datetime_to_iso(date_obj, date_format=DEFAULT_DATE_STR):
    try:
        date_str = date_obj.strftime(date_format)
        iso_date = convert_str_to_iso_datetime(str(date_str), date_format=date_format)
        return iso_date[0], None
    except Exception as e:
        return None, e


def convert_datetime_to_str(date_obj):
    try:
        date_str = date_obj.strftime("%Y-%m-%dT%H:%M:%S.%f+00")
        return date_str, None
    except Exception as e:
        return None, e


def convert_current_utc_to_tz(tz=None):
    utc_time_object = datetime.utcnow()
    if tz:
        utc_time_object = utc_time_object.replace(tzinfo=pytz.UTC)
        gmt_time_object = utc_time_object.astimezone(pytz.timezone(tz))
        return gmt_time_object
    return utc_time_object


def extract_current_date_from_time(tz=None):
    date_obj = convert_current_utc_to_tz(tz)
    date_str = convert_datetime_to_str(date_obj)[0].split("T")[0]

    return date_str


def get_time_range_days_ago(tz=None, days=0):
    date_str = extract_current_date_from_time(tz)
    date_start_time = convert_str_to_datetime(date_str + "T00:00:00.000000+00:00")[
        0
    ] - timedelta(days=days)
    date_end_time = convert_str_to_datetime(date_str + "T23:59:59.999999+00:00")[
        0
    ] - timedelta(days=days)

    return date_start_time, date_end_time


def get_signing_keys(url, bearer_token):
    token = str(bearer_token.credentials)
    signing_key = PyJWKClient(url).get_signing_key_from_jwt(token)
    token_claims = jwt.decode(
        token,
        signing_key.key,
        algorithms=["RS256"],
        options={"verify_exp": False, "verify_aud": False},
    )

    return token_claims


def send_noti_to_teams(ms_webhook_url, body):
    msg = {
        "type": "message",
        # "text": embedded_link
        "attachments": [
            {
                "contentType": "application/vnd.microsoft.teams.card.o365connector",
                "content": {
                    "@type": "MessageCard",
                    "@context": "http://schema.org/extensions",
                    "title": body["taskName"],
                    "sections": [
                        {
                            "activityTitle": f"Environment: {body['env']}",
                            "activitySubtitle": f"CronTab: {body['cronTab']}",  #  e.g 11.30am every monday
                            "activityText": f"Result: {body['result']}",  # e.g True for success execution & False for sv error
                            "activityImage": "https://st3.depositphotos.com/8089676/32835/v/450/depositphotos_328355014-stock-illustration-notification-bell-icon-black-web.jpg",
                        },
                        {
                            "title": "Details",
                            "facts": [
                                {"name": "Successes", "value": body["total_successes"]},
                                {"name": "Fails", "value": body["total_fails"]},
                                {"name": "Total", "value": body["total"]},
                            ],
                        },
                    ],
                },
            }
        ],
    }

    msg["attachments"][0]["content"]["sections"].extend(body["fails"])
    encoded_msg = json.dumps(msg).encode("utf-8")

    # INCOMING WEBHOOK
    http = urllib3.PoolManager()
    resp = http.request("POST", ms_webhook_url, body=encoded_msg)

    return success_response([resp]), msg


def parse_delete_request_res(
    data: dict = None,
    env: str = "Local",
    cron_str: str = "Unknown",
    exec_result: bool = "False",
    err: str = "Internal Sever Error 500",
):
    body = dict()
    body["taskName"] = "Handling delete requests."
    body["env"] = env
    body["cronTab"] = cron_str
    body["result"] = exec_result
    body["total_successes"] = data["total_successes"] if data else None
    body["total_fails"] = data["total_fails"] if data else None
    body["total"] = data["total"] if data else None
    body["fails"] = []
    if data:
        for i, fail in enumerate(data["fails"]):
            entry = dict()
            entry["title"] = f"Fail #{i}"
            entry["facts"] = [{"name": k, "value": v} for k, v in fail.items()]

            body["fails"].append(entry)
    if err:
        entry = dict()
        entry["title"] = "Internal Sever Error 500"
        entry["facts"] = [{"name": "Cause", "value": err}]
        body["fails"].append(entry)

    return body
    pass


def get_s3_client():
    ACCESS_KEY = config["AWS"]["AWS_ACCESS_KEY_ID"]
    AWS_SECRET_ACCESS_KEY = config["AWS"]["AWS_SECRET_ACCESS_KEY"]
    s3_client = boto3.client(
        "s3", aws_access_key_id=ACCESS_KEY, aws_secret_access_key=AWS_SECRET_ACCESS_KEY
    )
    return s3_client


def get_aws_upload_presigned_for_a_kit(
    identifier_code: str, barcode: str, adn_type: str, file_name: str
):
    s3_client = get_s3_client()
    Bucket = config["AGENCY_STORAGE"]["BUCKET"]
    OBJECT_NAME = (
        identifier_code + "/" + adn_type + "/" + barcode + "/" + f"{file_name}"
    )

    response = s3_client.generate_presigned_post(
        Bucket=Bucket,
        Key=OBJECT_NAME,
        ExpiresIn=config["AGENCY_STORAGE"]["PRESIGNED"]["TIMEOUT"],
    )
    return response


def check_adn_result_for_a_kit(
    identifier_code: str, barcode: str, adn_type: str, extension: str
):
    s3_client = get_s3_client()
    Bucket = config["AGENCY_STORAGE"]["BUCKET"]

    OBJECT_NAME = (
        identifier_code
        + "/"
        + adn_type
        + "/"
        + barcode
        + "/"
        + f"{barcode}.{extension}"
    )
    # print("OBJECT_NAME: ", OBJECT_NAME)
    try:
        s3_client.head_object(Bucket=Bucket, Key=OBJECT_NAME)
        return True, OBJECT_NAME
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            return False, None
        else:
            raise


def check_adn_result_from_a_list(
    adn_result_obj_key_list: list, barcode: str, adn_type: str, extension: str
):
    OBJECT_NAME = adn_type + "/" + barcode + "/" + f"{barcode}.{extension}"
    for obj_key in adn_result_obj_key_list:
        if OBJECT_NAME in obj_key:
            return True, obj_key

    return False, None


def check_adn_result_from_a_list_w_code_n_lid(
    adn_result_obj_key_list: list, barcode: str, lid: str, adn_type: str, extension: str
):
    OBJECT_NAME = adn_type + "/" + barcode + "/" + f"{lid}{extension}"
    for obj_key in adn_result_obj_key_list:
        if OBJECT_NAME in obj_key:
            return True, obj_key

    return False, None


def check_raw_result_from_a_list_w_code_n_lid(
    adn_result_obj_key_list: list,
    barcode: str,
    request_id: str,
    adn_type: str,
    extension: str,
):
    OBJECT_NAME = adn_type + "/" + barcode + "/" + f"{request_id}{extension}"
    for obj_key in adn_result_obj_key_list:
        if OBJECT_NAME in obj_key:
            return True, obj_key

    return False, None


def list_files_in_identifier_code_folder(identifier_code: str) -> list:
    s3_client = get_s3_client()
    Bucket = config["AGENCY_STORAGE"]["BUCKET"]
    Prefix = identifier_code

    try:
        response = s3_client.list_objects_v2(Bucket=Bucket, Prefix=Prefix)
        if "Contents" in response:
            return [obj["Key"] for obj in response["Contents"]]
        else:
            return []
    except ClientError as e:
        raise e


def get_aws_upload_presigned_post(technology, name):
    s3_client = get_s3_client()
    OBJECT_NAME = ""
    Bucket = None

    if technology == "MICROARRAY":
        Bucket = config["RAW_DATA_ZIP_BUCKET"]["MICROARRAY"]
        if ".zip" not in name:
            name += ".zip"
        OBJECT_NAME = technology + "/" + name
        pass
    elif technology == "PCR":
        Bucket = config["RAW_DATA_BUCKET"]["PCR"]
        if ".csv" not in name:
            name += ".csv"
        for idx, c in enumerate(name[3:]):
            if not c.isnumeric():
                break
        batch_id = name[3 : 3 + idx]
        OBJECT_NAME = batch_id + "/" + name
        pass

    response = s3_client.generate_presigned_post(
        Bucket=Bucket,
        Key=OBJECT_NAME,
        ExpiresIn=config["RAW_DATA_ZIP_BUCKET"]["PRESIGNED"]["TIMEOUT"],
    )
    return response


def get_aws_uploaded_presigned_url(OBJECT_NAME):
    s3_client = get_s3_client()
    presigned_url = s3_client.generate_presigned_url(
        "put_object",
        Params={
            "Bucket": config["RAW_DATA_ZIP_BUCKET"]["MICROARRAY"],
            "Key": OBJECT_NAME,
        },
        ExpiresIn=config["RAW_DATA_ZIP_BUCKET"]["PRESIGNED"]["TIMEOUT"],
    )
    return presigned_url


def download_s3_file(bucket_name, s3_file_prefix, local_path):
    """
    Downloads a file from S3 to a local directory.

    Args:
        bucket_name (str): The name of the S3 bucket.
        s3_file_prefix (str): The S3 file prefix. This is the full path to the file inside the bucket.
        local_path (str): The local directory to download the file to.
    """
    ACCESS_KEY = config["AWS"]["AWS_ACCESS_KEY_ID"]
    AWS_SECRET_ACCESS_KEY = config["AWS"]["AWS_SECRET_ACCESS_KEY"]
    s3 = boto3.resource(
        "s3", aws_access_key_id=ACCESS_KEY, aws_secret_access_key=AWS_SECRET_ACCESS_KEY
    )
    bucket = s3.Bucket(bucket_name)

    # Get the S3 object key for the file
    obj = bucket.Object(s3_file_prefix)

    # Remove the S3 file prefix from the object key
    file_name = obj.key.split("/")[-1]

    # Download the S3 object to the local directory
    local_file_path = f"{local_path}/{file_name}"
    with open(local_file_path, "wb") as f:
        bucket.download_fileobj(obj.key, f)

    return local_file_path


def get_file_name(path):
    filename = os.path.basename(path)
    return filename


def get_chip_n_pos_from_gtc_file(gtc_file_name):
    chip_id, position = (
        gtc_file_name.split(".")[0].split("_")[0],
        gtc_file_name.split(".")[0].split("_")[1],
    )
    return chip_id, position


def calculate_qc_status_given_params(TECHNOLOGY, qc_status):
    if TECHNOLOGY == "MICROARRAY":
        if "True" in qc_status or "False" in qc_status:
            bool_status = [False if s == "False" else True for s in qc_status]
            return "PASS" if all(bool_status) else "FAIL"
    elif TECHNOLOGY == "PCR":
        if "PASS" in qc_status:
            return "PASS"
        elif "FAIL" in qc_status:
            return "FAIL"


def get_updated_qc_status_for_sample_mapping(
    TECHNOLOGY, row, updated_col_names, qc_status
):
    mapping_column_names = config["PIPELINE_QC_STATUS"]["COLUMN_MAPPING"][TECHNOLOGY]
    data = {}
    if mapping_column_names:
        data = {
            mapping_column_names[idx]: row[updated_col_names[idx]]
            for idx in range(len(mapping_column_names))
        }
    data["qc_status"] = qc_status

    for key, value in data.items():
        if value == "True":
            data[key] = True
        elif value == "False":
            data[key] = False

        if key == "call_rate":
            data[key] = float(value)
    return data


def get_field_names_w_additional_cols(Model, additional_cols):
    field_names = []
    if Model:
        field_names = [field for field in Model.__dict__.keys() if "_" != field[0]]
    field_names += additional_cols
    return field_names


def remove_file_given_local_path(local_path):
    if os.path.exists(local_path):
        os.remove(local_path)
    else:
        err = str(f"{local_path} does not exist")
        raise ValueError(err)
    pass


def export_list_dicts_to_csv(results, file_path):
    field_names = (
        config.get("PIPELINE_QC_STATUS").get("BARCODE_MAPPING_FILE").get("HEADERS")
    )

    with open(file_path, "w", newline="") as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=field_names)
        writer.writeheader()

        for data_dict in results:
            writer.writerow(data_dict)

            pass
    pass


def upload_file(prefix, file_name, bucket, object_name=None):
    """Upload a file to an S3 bucket

    :param file_name: File to upload
    :param bucket: Bucket to upload to
    :param object_name: S3 object name. If not specified then file_name is used
    :return: True if file was uploaded, else False
    """

    s3_client = get_s3_client()

    # If S3 object_name was not specified, use file_name
    if object_name is None:
        object_name = os.path.basename(file_name)

    # Upload the file
    try:
        response = s3_client.upload_file(file_name, bucket, prefix + "/" + object_name)
    except ClientError as e:
        logging.error(e)
        return False
    return True


def list_execution_function(stateMachineArn):
    stf_client = get_step_function_client()
    response = stf_client.list_executions(
        stateMachineArn=stateMachineArn,
        maxResults=int(config["AWS"]["STEP_FUNCTION_MAX_RESULTS"]),
    )

    list_of_executions = []
    for execution in response["executions"]:
        executionArn = execution["executionArn"]
        execution = stf_client.describe_execution(executionArn=executionArn)
        list_of_executions.append(execution)
    return list_of_executions


def get_step_function_client():
    ACCESS_KEY = config["AWS"]["AWS_ACCESS_KEY_ID"]
    AWS_SECRET_ACCESS_KEY = config["AWS"]["AWS_SECRET_ACCESS_KEY"]
    stf_client = boto3.client(
        "stepfunctions",
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        region_name="ap-southeast-1",
    )
    return stf_client


def parse_object_key(default_pdf_link):
    object_url = default_pdf_link.split("?")[0].split("//")[-1]
    object_key = object_url.split("com")[-1]
    object_key = object_key[1:] if object_key[0] == "/" else object_key
    pdf_bucket = object_url.split(".")[0]
    return pdf_bucket, object_key


def get_s3_client_ap():
    ACCESS_KEY = config["AWS"]["AWS_ACCESS_KEY_ID"]
    AWS_SECRET_ACCESS_KEY = config["AWS"]["AWS_SECRET_ACCESS_KEY"]
    s3_client = boto3.client(
        "s3",
        config=Config(signature_version="s3v4"),
        region_name="ap-southeast-1",
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    )
    return s3_client


def renew_obj_key(bucket, object_key):
    s3_client = get_s3_client_ap()
    presigned_url = s3_client.generate_presigned_url(
        "get_object", Params={"Bucket": bucket, "Key": object_key}, ExpiresIn=604800
    )
    return presigned_url


def get_s3_client_ap_w_region(region_name):
    ACCESS_KEY = config["AWS"]["AWS_ACCESS_KEY_ID"]
    AWS_SECRET_ACCESS_KEY = config["AWS"]["AWS_SECRET_ACCESS_KEY"]
    s3_client = boto3.client(
        "s3",
        config=Config(signature_version="s3v4"),
        region_name=region_name,
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    )
    return s3_client


def renew_obj_key_w_region(bucket, object_key, region_name):
    s3_client = get_s3_client_ap_w_region(region_name)
    presigned_url = s3_client.generate_presigned_url(
        "get_object", Params={"Bucket": bucket, "Key": object_key}, ExpiresIn=604800
    )
    return presigned_url


def get_obj_key_from_s3_uri(s3_uri):
    remove_prefix_part = s3_uri.split("//")[-1]
    remove_bucket_part = remove_prefix_part.split("/")[1:]
    obj_key = os.path.join(*remove_bucket_part)
    return obj_key


def create_card_series(report_ver, db_ver, name, barcode):
    ver = (
        sum([ord(report_ver[i]) + ord(db_ver[i]) for i in range(len(report_ver))])
        % 10000
    )
    full_char = [str(ord(s)) for s in name]
    full_char.extend([ord(d) for d in barcode])
    series = (
        int("".join([str(d) for d in random.sample(full_char, len(full_char))]))
        % 100000000
    )
    full_series = (
        "8888"
        + str(ver)
        + "0" * (4 - len(str(ver)) + 8 - len(str(series)))
        + str(series)
    )
    if len(full_series) == 16:
        return full_series
    else:
        raise Exception("Mismatch len")


def generate_card_id(report_ver, db_ver, name, barcode):
    card_series = create_card_series(report_ver, db_ver, name, barcode)
    while (
        "44" in card_series
        or "49" in card_series
        or "53" in card_series
        or "78" in card_series
    ):
        card_series = create_card_series(report_ver, db_ver, name, barcode)
    return card_series


def get_product_code_from_legacy_barcode(barcode):
    if len(barcode) == 12:
        return barcode[-2:]
    else:
        return None


def get_language_based_on_barcode(barcode):
    if len(barcode) == 12:
        product_code = get_product_code_from_legacy_barcode(barcode)
        available_langs = config["CARD_MANAGEMENT"]["LANGUAGE"]["AVAILABLE_FOREIGN"]
        for lang in available_langs:
            lang_mapping_lst = config["CARD_MANAGEMENT"]["LANGUAGE"][
                "PRODUCT_CODE_MAPPING"
            ][lang]
            if int(product_code) in lang_mapping_lst:
                return lang

        return config["CARD_MANAGEMENT"]["LANGUAGE"]["DEFAULT"]
    else:
        return None


def get_sqs_client():
    ACCESS_KEY = config["AWS"]["AWS_ACCESS_KEY_ID"]
    AWS_SECRET_ACCESS_KEY = config["AWS"]["AWS_SECRET_ACCESS_KEY"]
    sqs_client = boto3.client(
        "sqs",
        region_name="ap-southeast-1",
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
    )
    return sqs_client


def get_queue_url_by_name(queue_name):
    sqs_client = get_sqs_client()
    response = sqs_client.get_queue_url(QueueName=queue_name)
    return response["QueueUrl"]


def send_message_to_sqs_queue(message_body, queue_name):
    message_body_json = json.dumps(message_body)
    queue_url = get_queue_url_by_name(queue_name)
    sqs_client = get_sqs_client()
    response = sqs_client.send_message(
        QueueUrl=queue_url, MessageBody=message_body_json
    )
    return response


async def validate_user_information(phone=None, email=None, request_headers=None):
    # request_headers = {"Content-type": "application/json", "Accept": "text/plain"}

    logger.debug("Validate user infomation")

    response = requests.get(
        config["AUTHEN_URL"][config["ENV"]] + config["AUTHEN_URL"]["VALIDATE_USER"],
        headers=request_headers,
        params={
            "phone_number": phone,
            "email": email,
        },
        timeout=3,
    )

    logger.debug(f"Validate user infomation response: {response.text}")

    return response


async def check_user_existence(phone=None, email=None):
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    response = await validate_user_information(phone, email, request_headers)
    response.raise_for_status()
    user_info = response.json()
    return user_info["data"]["result"]


async def parse_user_information(phone=None, email=None):
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    response = await validate_user_information(phone, email, request_headers)
    response.raise_for_status()
    user_info = response.json()
    if user_info["error"]["code"] == 400:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(user_info["error"]["message"])
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        return user_info

async def admin_get_user_info(search: str, credential: str):
    request_headers = {
        "Authorization": "Bearer " + credential,
    }

    params = {
        "page": 1,
        "limit": 10,
        "nameLike": search
    }

    response = requests.get(
        config["AUTHEN_URL"][config["ENV"]] + config["AUTHEN_URL"]["SEARCH_USER"],
        headers=request_headers,
        params=params,
        timeout=10,
    )

    return response.json()

async def admin_create_phone_user(
    full_name: str, phone_number: str, password: str, credential: str
):
    request_headers = {
        "Content-type": "application/json",
        "Accept": "text/plain",
        "Authorization": "Bearer " + credential,
    }
    data = {
        "full_name": full_name,
        "gender": "female",
        "password": password,
        "phone_number": phone_number,
        "status": "active",
        "username": "",
    }

    response = requests.post(
        config["AUTHEN_URL"][config["ENV"]] + config["AUTHEN_URL"]["CREATE_USER"],
        headers=request_headers,
        json=data,
        timeout=3,
    )
    response.raise_for_status()

    pass


async def get_pdf_report_qr_dynamo_id(bucket: str, file_name: str, credential: str):
    request_headers = {
        "Content-type": "application/json",
        "Accept": "text/plain",
        "Authorization": "Bearer " + credential,
    }

    data = {"bucket": bucket, "key": file_name}
    host = config["CARD_MANAGEMENT"]["DYNAMO_ID_GENERATOR_URL"]
    logger.info(f"host: {host} - data: {data} - header: {request_headers}")

    response = requests.post(
        host,
        headers=request_headers,
        json=data,
        timeout=config["CARD_MANAGEMENT"]["TIMEOUT"],
    )
    response.raise_for_status()
    res = response.json()
    report_id = res.get("ReportID")

    return report_id


async def get_report_pdf_link(barcode: str, technology: str):
    data = {
        "kit_id": barcode,
        "report_name": config["CARD_MANAGEMENT"]["QR_CODE"][technology.upper()][
            "REPORT_NAME"
        ],
        "raw_name": config["CARD_MANAGEMENT"]["QR_CODE"][technology.upper()][
            "RAW_NAME"
        ],
    }
    # print("data: ", data)
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    host = (
        config["PDFEXPORTER"][config["ENV"]] + config["PDFEXPORTER"]["GENERATE_REPORT"]
    )
    logger.info(f"host: {host} - data: {data}")
    response = requests.post(
        host,
        headers=request_headers,
        json=data,
        timeout=config["CARD_MANAGEMENT"]["TIMEOUT"],
    )
    response.raise_for_status()
    pdf_link = response.json()[0]
    return pdf_link


def parse_pdf_report_link(pdf_link: str):
    url_path = pdf_link.split("?")[0].split("//")[-1]
    file_name = url_path.split("/")[-1]
    domain = url_path.split("/")[0]
    bucket = domain.split(".")[0]
    return bucket, file_name


def instantiate_from_dict(class_obj, data):
    # Get attribute names from the class definition
    attribute_names = [
        attr
        for attr in dir(class_obj)
        if not attr.startswith("__") and not callable(getattr(class_obj, attr))
    ]
    # Create a dictionary comprehension to filter data keys
    filtered_data = {key: data[key] for key in data if key in attribute_names}
    # Instantiate the class with the filtered data
    return class_obj(**filtered_data)


def get_attribute_names_from_obj(class_obj):
    attribute_names = [
        attr
        for attr in dir(class_obj)
        if not attr.startswith("__") and not callable(getattr(class_obj, attr))
    ]
    return attribute_names


def get_all_attributes_gino_model(model_class):
    # Use model_class.__dict__ to get all attributes
    attributes = model_class.__dict__.keys()
    # Exclude the magic methods and other internal attributes
    return [
        attr
        for attr in attributes
        if (not attr.startswith("__") and attr != "_column_name_map")
    ]


def parse_object_from_dict(class_obj, data):
    data_dict = convert_rowproxy_to_dict(data)
    # Get attribute names from the class definition
    attribute_names = get_all_attributes_gino_model(class_obj)
    # Create a dictionary comprehension to filter data keys
    filtered_data = {key: data_dict[key] for key in data_dict if key in attribute_names}
    # Instantiate the class with the filtered data
    return filtered_data


def download_file_from_s3(url, local_filename):
    """Downloads a file from an S3 presigned URL and saves it locally."""
    response = requests.get(url, stream=True)
    if response.status_code == 200:
        with open(local_filename, "wb") as f:
            for chunk in response.iter_content(1024):
                f.write(chunk)
    else:
        raise Exception(
            f"Failed to download file from S3. Status code: {response.status_code}"
        )


def file_to_base64(local_filename):
    """Encodes a local file to Base64."""
    with open(local_filename, "rb") as file:
        encoded_string = base64.b64encode(file.read())
    return encoded_string.decode("utf-8")


def save_base64_to_file(base64_string, output_filename):
    """Saves a Base64 string to a text file."""
    with open(output_filename, "w") as text_file:
        text_file.write(base64_string)


def clean_local_file(local_filename):
    """Removes the local file if it exists."""
    if os.path.exists(local_filename):
        os.remove(local_filename)


def parse_presigned_s3_url(s3_presigned_url, local_filename):
    # Step 1: Download the file from S3
    download_file_from_s3(s3_presigned_url, local_filename)

    # Step 2: Convert the file to Base64
    base64_string = file_to_base64(local_filename)

    # Step 4: Clean up the local file
    clean_local_file(local_filename)

    return base64_string


def vneid_request_reponse(is_success: bool):
    if is_success:
        return {"trangThai": "0", "moTaLoi": "Hệ thống tồn tại dữ liệu adn"}
    else:
        return {"trangThai": "1", "moTaLoi": "Dữ liệu ADN không tồn tại trong hệ thống"}


def convert_datetime_flag_format(date_datime):
    # Parse the input date string to a datetime object
    dt = datetime.strptime(str(date_datime), "%Y-%m-%d %H:%M:%S")
    # Format the datetime object to the desired string format
    return dt.strftime("%d%m%Y%H%M%S")


def compare_identity_details(current_cccd_detail, new_identity_card):
    """
    Compares relevant fields between current_cccd_detail and new_identity_card.

    Parameters:
        current_cccd_detail (dict): The current CCCD details.
        new_identity_card (dict): The new identity card details.

    Returns:
        dict: A dictionary of differences where keys are the differing fields
              and values are dictionaries with 'current_cccd_detail' and 'new_identity_card' values.
    """

    def compare_dicts(dict1, dict2, keys_to_compare):
        differences = {}
        for key in keys_to_compare:
            value1 = dict1.get(key)
            value2 = dict2.get(key)
            if value1 != value2:
                differences[key] = {
                    "current_cccd_detail": value1,
                    "new_identity_card": value2,
                }
        return differences

    # Extract relevant fields from both dictionaries
    current_cccd_relevant = {
        key: current_cccd_detail[key]
        for key in [
            "identifier_code",
            "full_name",
            "dob",
            "gender",
            "nationality",
            "origin",
            "residence",
        ]
    }

    # List of keys to compare
    keys_to_compare = [
        "identifier_code",
        "full_name",
        "dob",
        "gender",
        "nationality",
        "origin",
        "residence",
    ]

    # Compare the dictionaries
    differences = compare_dicts(
        current_cccd_relevant, new_identity_card, keys_to_compare
    )

    return differences


def add_business_days(start_date: date, duration: int) -> date:
    current_date = start_date
    days_added = 0

    while days_added < duration:
        current_date += timedelta(days=1)
        if current_date.weekday() < 5:  # Monday to Friday are considered business days
            days_added += 1

    return current_date


async def get_role_by_brearer_token(sub: str, credential: str):
    request_headers = {
        "Content-type": "application/json",
        "Accept": "text/plain",
        "Authorization": "Bearer " + credential,
    }

    authz_endpoint = config["AUTHZ"]["ENDPOINT"]
    role_listing_path = config["AUTHZ"]["ROLE_LISTING_PATH"]

    host = authz_endpoint + role_listing_path + f"/{sub}"
    logger.info(f"host: {host} ")

    response = requests.get(
        host, headers=request_headers, timeout=config["AUTHZ"]["TIMEOUT"]
    )

    response.raise_for_status()
    res = response.json()
    role_list = res.get("data")

    return role_list

async def get_account_by_cs_id(id: str, credential: str):
    request_headers = {
        "Content-type": "application/json",
        "Accept": "text/plain",
        "Authorization": "Bearer " + credential,
    }

    authz_endpoint = config["AUTHEN_URL"]["PROD"]
    role_listing_path = config["AUTHEN_URL"]["SEARCH_USER"]
    timeout = config["AUTHEN_URL"].get("TIMEOUT", 10)

    host = authz_endpoint + role_listing_path + f"/{id}"
    logger.info(f"host: {host}")

    try:
        response = requests.get(host, headers=request_headers, timeout=timeout)
        response.raise_for_status()
        res = response.json()
        return res.get("phone_number")
    except (RequestException, ValueError, KeyError) as e:
        logger.warning(f"Lỗi lấy tài khoản theo customer_support_id = {id} : {e}")
        return None


def standardize_full_name(name):
    # Convert the entire string to lowercase
    name = name.lower()

    # Split the string into a list of words
    words = name.split()

    # Capitalize the first letter of each word
    standardized_words = [word.capitalize() for word in words]

    # Join the capitalized words back into a single string
    standardized_name = " ".join(standardized_words)

    return standardized_name


def standardize_name(func):
    async def wrapper(*args, **kwargs):
        # Assuming 'employee_name' is passed as a keyword argument or positional argument.
        if "employee_name" in kwargs:
            kwargs["employee_name"] = standardize_full_name(kwargs["employee_name"])
        elif "hoTenNhanVien" in kwargs:
            kwargs["hoTenNhanVien"] = standardize_full_name(kwargs["hoTenNhanVien"])
        elif len(args) > 0:
            args = (standardize_full_name(args[0]), *args[1:])

        return await func(*args, **kwargs)

    return wrapper


def validate_access_token(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        auth = kwargs.get("auth")
        if auth:
            try:
                _, err = await auth.get_token_claims()
                if err:
                    errs = failure_response(err["err_msg"])
                    raise HTTPException(status_code=err["err_code"], detail=errs)

            except Exception:
                logger.info("no access token provided")
                raise HTTPException(
                    status_code=HTTP_401_UNAUTHORIZED, detail="Unauthorized"
                )
        return await func(*args, **kwargs)

    return wrapper


def handle_exceptions(func):
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)

        except requests.exceptions.ConnectionError as err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        except requests.exceptions.Timeout as err:
            http_code = HTTP_408_REQUEST_TIMEOUT
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        except requests.exceptions.RequestException as err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        except ValueError as err:
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

    return wrapper

def number_to_vietnamese_words(n):
    digits = ["không", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín"]
    units = ["", "nghìn", "triệu", "tỷ"]

    def read_three_digits(num):
        words = ""
        hundred = num // 100
        ten = (num % 100) // 10
        unit = num % 10

        if hundred != 0:
            words += digits[hundred] + " trăm"
            if ten == 0 and unit != 0:
                words += " linh"
        elif num >= 100:
            words += "không trăm"

        if ten != 0 and ten != 1:
            words += " " + digits[ten] + " mươi"
            if unit == 1:
                words += " mốt"
            elif unit == 5:
                words += " lăm"
            elif unit != 0:
                words += " " + digits[unit]
        elif ten == 1:
            words += " mười"
            if unit == 5:
                words += " lăm"
            elif unit != 0:
                words += " " + digits[unit]
        elif ten == 0 and unit != 0:
            words += " " + digits[unit]

        return words.strip()

    if n == 0:
        return "không"

    parts = []
    group_index = 0

    while n > 0:
        chunk = n % 1000
        if chunk != 0:
            words = read_three_digits(chunk)
            if units[group_index]:
                words += " " + units[group_index]
            parts.insert(0, words)
        n //= 1000
        group_index += 1

    return " ".join(parts).strip()

def standardize_phone_number(phone: str) -> str:
    if not phone:
        return None

    digits = re.sub(r"[^\d]", "", phone)

    if digits.startswith("0") and len(digits) == 10:
        digits = "84" + digits[1:]
    elif digits.startswith("84") and len(digits) == 11:
        pass
    elif digits.startswith("0084") and len(digits) == 13:
        digits = "84" + digits[4:]
    elif digits.startswith("+84") and len(digits) == 12:
        digits = "84" + digits[3:]
    else:
        return None

    if re.match(r"^84[3|5|7|8|9]\d{8}$", digits):
        return digits

    return None


def build_phone_variants(phone: str) -> list:
    base = standardize_phone_number(phone)
    if not base:
        return []

    local = "0" + base[2:]    # like 0912345678
    plus = "+84" + base[2:]   # like +84912345678
    raw = base                # like 84912345678

    return [local, plus, raw]