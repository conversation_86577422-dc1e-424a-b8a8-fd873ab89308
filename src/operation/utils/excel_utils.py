import openpyxl
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter
from io import Bytes<PERSON>
from typing import List, Dict

from typing import List, Dict
from io import BytesIO
import openpyxl
from openpyxl.styles import Font, Alignment
from openpyxl.utils import get_column_letter

def export_to_excel(
    data: List[Dict],
    columns: List[Dict[str, str]],
    sheet_name: str = "Sheet1",
    include_index: bool = True,
    index_title: str = "STT"
) -> BytesIO:

    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = sheet_name

    effective_columns = columns.copy()
    if include_index:
        effective_columns = [{"key": "__index__", "title": index_title, "align": "right"}] + columns

    header_font = Font(bold=True)
    for col_idx, col in enumerate(effective_columns, 1):
        cell = ws.cell(row=1, column=col_idx, value=col["title"])
        cell.font = header_font
        cell.alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)

    for row_idx, item in enumerate(data, 2):
        max_lines_in_row = 1
        for col_idx, col in enumerate(effective_columns, 1):
            if col["key"] == "__index__":
                value = row_idx - 1
            else:
                value = item.get(col["key"], "")
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.alignment = Alignment(horizontal=col.get("align", "left"), vertical="top", wrap_text=True)

            if isinstance(value, str):
                lines = value.count("\n") + 1
                max_lines_in_row = max(max_lines_in_row, lines)

        ws.row_dimensions[row_idx].height = max_lines_in_row * 15

    for col_idx, col in enumerate(effective_columns, 1):
        column_letter = get_column_letter(col_idx)
        max_length = 0
        for row in range(1, len(data) + 2):
            cell_value = ws.cell(row=row, column=col_idx).value
            if cell_value is not None:
                lines = str(cell_value).split("\n")
                longest_line = max(lines, key=len, default="")
                max_length = max(max_length, len(longest_line))
        ws.column_dimensions[column_letter].width = min(max_length + 2, 50)

    output = BytesIO()
    wb.save(output)
    output.seek(0)
    return output
