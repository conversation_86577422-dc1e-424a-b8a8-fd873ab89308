from datetime import datetime
from ..config import config
from enum import Enum
import uuid
from .models import db
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB

from ..utils.utils import (
    DEFAULT_DATE_STR, DEFAULT_DATE_NO_TIME_ZONE
)

class BookingStatus(Enum):
    RECEIVED = 'RECEIVED'
    IN_PROGRESS = 'IN_PROGRESS'
    COMPLETED = 'COMPLETED'
    CANCELED = 'CANCELED'
    
class BarcodeStatus(Enum):
    RECEIVED = 'RECEIVED'
    SAMPLE_COLLECTION = 'SAMPLE_COLLECTION'
    IN_ANALYSIS = 'IN_ANALYSIS'
    COMPLETED = 'COMPLETED'
    CANCELED = 'CANCELED'
    
class PaymentMethod(Enum):
    BANKING = 'BANKING'
    
class PaymentStatus(Enum):
    PENDING = 'PENDING'
    COMPLETED = 'COMPLETED'


class PartnerBooking(db.Model):
    __tablename__ = "partner_booking"
    id = Column(UUID(), nullable=False, primary_key=True)
    customer_support_id = Column(UUID())
    partner_name = Column(String(50))
    partner_booking_id = Column(String(100))
    customer_name = Column(String(100))
    customer_phone = Column(String(50))
    payment_amount = Column(Integer, nullable=False)
    payment_method = Column(String(50))
    payment_status = Column(String(50))
    referral_code = Column(String(50))
    status = Column(String(30))
    error_message = Column(String(200))
    created_at = Column(DateTime(timezone=True),
                        default=datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
                        nullable=False)
    updated_at = Column(DateTime(timezone=True))
    deleted_at = Column(DateTime(timezone=True))
    products = Column(String(150))
    note = Column(String(150))
    consulted = Column(Boolean, nullable=False, default=False)
    kits = relationship("PartnerBookingKit", back_populates="booking")

class PartnerBookingKit(db.Model):
    __tablename__ = "partner_booking_kit"
    id = Column(UUID(), nullable=False, primary_key=True)
    product_code = Column(String(10))
    product_name = Column(String(50))
    product_id = Column(String(100))
    booking_id = Column(UUID(), ForeignKey("partner_booking.id"))
    booking = relationship("PartnerBooking", back_populates="kits")
    checkout_price = Column(Integer)
    barcode = Column(String(50))
    status = Column(String(50))
    result_id = Column(String(100))
    created_at = Column(DateTime(timezone=True),
                        default=datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
                        nullable=False)
    updated_at = Column(DateTime(timezone=True))
    
