from datetime import datetime

from sqlalchemy import Column, DateTime, Foreign<PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import UUID

from src.operation.models.models import db
from src.operation.utils.utils import DEFAULT_DATE_STR


class Sponsor(db.Model):
    __tablename__ = "sponsors"
    id = Column(UUID(), primary_key=True)
    name = Column(String(200), nullable=False)
    code = Column(String(200), unique=True, nullable=False)
    description = Column(String(200), nullable=True)
    type = Column(String(50), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class SponsorContract(db.Model):
    __tablename__ = "sponsor_contracts"
    id = Column(UUID(), primary_key=True)
    name = Column(String(200), nullable=False)
    code = Column(String(200), unique=True, nullable=False)
    signing_date = Column(DateTime(), nullable=False)
    sponsor_id = Column(ForeignKey(Sponsor.id), nullable=False)
    sponsored_party = Column(String(50), nullable=True)
    s3_key = Column(String(100), nullable=True)
    s3_bucket = Column(String(100), nullable=True)
    total_quantity = Column(Integer(), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class GsArea(db.Model):
    __tablename__ = "gs_areas"
    id = Column(Integer(), primary_key=True)
    area = Column(String(), nullable=True)


class SponsorCampaign(db.Model):
    __tablename__ = "sponsor_campaigns"
    id = Column(UUID(), primary_key=True)
    name = Column(String(200), nullable=False)
    start_date = Column(DateTime(), nullable=True)
    end_date = Column(DateTime(), nullable=True)
    quantity = Column(Integer(), nullable=True)
    contract_id = Column(ForeignKey(SponsorContract.id), nullable=False)
    gs_area_code = Column(Integer(), ForeignKey(GsArea.id), nullable=True)
    description = Column(String(200), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)
