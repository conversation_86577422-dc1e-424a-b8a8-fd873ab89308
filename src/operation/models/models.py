import uuid
from datetime import datetime

# from re import template
from gino.ext.starlette import Gino
from sqlalchemy import (
    Boolean,
    Column,
    Date,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.types import TIM<PERSON><PERSON>MP

from ..config import config
from ..utils.utils import DEFAULT_DATE_NO_TIME_ZONE, DEFAULT_DATE_STR

db = Gino(
    dsn=config["DB_URL"],
    pool_min_size=config["DB_POOL_MIN_SIZE"],
    pool_max_size=config["DB_POOL_MAX_SIZE"],
    echo=config["DB_ECHO"],
    ssl=config["DB_SSL"],
    use_connection_for_request=config["DB_USE_CONNECTION_FOR_REQUEST"],
    retry_limit=config["DB_RETRY_LIMIT"],
    retry_interval=config["DB_RETRY_INTERVAL"],
)


class Kit(db.Model):
    __tablename__ = "old_kit"

    barcode = Column(String(50), primary_key=True)
    sample_meta_id = Column(UUID(), nullable=False)
    nickname = Column(String(50))
    # email = Column(String(50))
    # phone_number = Column(String(50))
    # userid = Column(String(50), nullable=True)
    # name = Column(String(50), nullable=False)
    # gender = Column(String(50), nullable=False)
    version = Column(String(50), nullable=False)
    current_status = Column(String(50))
    current_status_id = Column(Integer, nullable=False)
    # info = Column(JSONB, nullable=False, server_default="{}")
    # dob = Column(DateTime(timezone=True),
    #                       default=datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR),
    #                                                                    DEFAULT_DATE_STR),
    #                       nullable=False)
    created_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    product_code = Column(String(50), nullable=False)
    product_name = Column(String(50), nullable=False)
    product_type = Column(String(50), nullable=False, default="CLINIC")
    sample_collection_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=True,
    )
    sample_collection_time = Column(Integer, nullable=False, default=1)
    sample_receipt_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    lab_receipt_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=True,
    )

    sample_collector_name = Column(String(50), nullable=True)
    sample_receiver_name = Column(String(50), nullable=True)
    expected_report_release_date = Column(Date(), default=None, nullable=False)
    source_id = Column(UUID, nullable=False)
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)
    customer_support_id = Column(UUID(), nullable=True)
    customer_support_name = Column(String(120), nullable=True)
    # validate_account = Column(Boolean, nullable=False, default=True)
    note = Column(String(100), nullable=True)
    # diagnosis = Column(String(120), nullable=True)
    free_of_charge = Column(Boolean, nullable=True)
    promotion = Column(UUID(), nullable=True)
    is_priority = Column(Boolean, nullable=False, default=False)
    pdf_generation_date = Column(Date(), nullable=True)
    default_pdf_link = Column(String(500), nullable=True)
    lab_check_date = Column(Date(), nullable=True)
    sample_type = Column(String(30), nullable=True)
    actual_report_release_time = Column(type_=TIMESTAMP(timezone=False), nullable=True)


class SampleMeta(db.Model):
    __tablename__ = "sample_meta"
    id = Column(UUID(), primary_key=True)
    userid = Column(String(50), nullable=True)
    full_name = Column(String(50), nullable=False)
    address = Column(String(100), nullable=True)
    email = Column(String(50), nullable=True)
    phone_number = Column(String(50), nullable=True)
    validate_account = Column(Boolean, nullable=False, default=True)
    gender = Column(String(50), nullable=False)
    dob = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    yob = Column(String(4), nullable=True)
    diagnosis = Column(String(120), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class Code(db.Model):
    __tablename__ = "code"

    barcode = Column(String(50), primary_key=True)
    qrcode = Column(String(100), nullable=False)
    printed = Column(Boolean, default=False, nullable=False)
    state = Column(String(50))
    note = Column(String(120), nullable=True)
    product_code = Column(String(10), nullable=True)
    product_name = Column(String(100), nullable=True)
    created_time = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )
    updated_time = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )
    # deleted_at = Column(DateTime(timezone=False), nullable=True)


class Status(db.Model):
    __tablename__ = "status"

    id = Column(Integer(), primary_key=True)
    barcode = Column(String(50))
    status = Column(String(50))
    note = Column(String(500))
    created_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )


class Attempts(db.Model):
    __tablename__ = "attempt"

    userid = Column(String(50), primary_key=True)
    email = Column(String(50))
    block = Column(Boolean, default=False, nullable=False)
    attempt = Column(Integer, nullable=False)
    note = Column(String(500))
    created_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )


class Sample(db.Model):
    __tablename__ = "old_sample"

    id = Column(Integer, primary_key=True)
    operation_id = Column(String(50), nullable=False)
    num = Column(Integer, nullable=False)
    position = Column(String(50), nullable=False)
    physical_position = Column(String(50), nullable=False)
    barcode = Column(String(50), nullable=False)
    batch_barcode = Column(String(50), nullable=False)
    vinmec_id = Column(String(50), nullable=True)
    chip_id = Column(String(50), nullable=False)
    chip_type = Column(String(50), default="gsa", nullable=False)
    assembly = Column(String(50), default="hg38", nullable=False)
    gender = Column(String(50), nullable=False)
    technician_name = Column(String(50), nullable=False)
    qc_status = Column(String(50), nullable=False)
    positive_tested = Column(Boolean, default=False, nullable=False)
    created_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_time = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )


class Source(db.Model):
    __tablename__ = "source"

    id = Column(UUID(), primary_key=True)
    account_history_id = Column(UUID(), nullable=False)
    nominator_id = Column(UUID(), nullable=True)
    freelancer_id = Column(UUID(), nullable=True)


class SaleAccountHistory(db.Model):
    __tablename__ = "sale_account_history"

    id = Column(UUID(), primary_key=True)
    account_id = Column(UUID(), nullable=False)
    pic_id = Column(UUID(), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    # deleted_at = Column(DateTime(timezone=True),
    #                     nullable=True)


class Staff(db.Model):
    __tablename__ = "staff"

    id = Column(UUID(), primary_key=True)
    name = Column(String(50), nullable=False)
    email = Column(String(50), nullable=True)
    userid = Column(String(50), nullable=True)
    account_id = Column(UUID(), ForeignKey("account.id"), nullable=True)
    phone_number = Column(String(50), nullable=True)
    role = Column(String(50))
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class Account(db.Model):
    __tablename__ = "account"

    id = Column(UUID(), primary_key=True)
    name = Column(String(200), nullable=True)
    address = Column(String(255), nullable=True)
    area = Column(String(50), nullable=True)
    description = Column(String(120), nullable=True)
    type = Column(String(50))
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class Promotion(db.Model):
    __tablename__ = "promotion"

    id = Column(UUID(), primary_key=True)
    code = Column(String(250), nullable=False)
    name = Column(String(500), nullable=False)
    discount = Column(String(250), nullable=False)
    department = Column(String(250), nullable=False)
    start_date = Column(
        DateTime(timezone=False),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    end_date = Column(
        DateTime(timezone=False),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class SampleMapping(db.Model):
    __tablename__ = "sample_mapping"
    id = Column(Integer, primary_key=True)
    chip_id = Column(String(12), nullable=True)
    position = Column(String(30), nullable=True)
    dna_extraction_id = Column(Integer, nullable=False)
    plate_id = Column(UUID(), nullable=False)
    well_position = Column(String(12), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    prob_pass = Column(Boolean, nullable=True)
    call_rate = Column(Float, nullable=True)
    call_rate_pass = Column(Boolean, nullable=True)
    gender_pass = Column(Boolean, nullable=True)
    qc_status = Column(String(12), nullable=True)


class Chip(db.Model):
    __tablename__ = "chip"
    id = Column(Integer, primary_key=True)
    chip_id = Column(String(12), nullable=False)
    type = Column(String(12), nullable=False, default="GSAv3")
    technology = Column(String(12), nullable=False)
    # pipeline_qc_report = Column(String(250), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class BatchMapping(db.Model):
    __tablename__ = "batch_mapping"
    id = Column(Integer, primary_key=True)
    plate_id = Column(UUID(), nullable=False)
    batch_id = Column(UUID(), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    wetlab_date = Column(
        DateTime(timezone=False),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=True,
    )
    drylab_date = Column(
        DateTime(timezone=False),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=True,
    )
    raw_data_uploaded_date = Column(
        DateTime(timezone=False),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=True,
    )
    raw_report_to_reviewers = Column(
        DateTime(timezone=False),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=True,
    )


class Batch(db.Model):
    __tablename__ = "batch"
    id = Column(String(12), primary_key=True)
    number = Column(Integer(), nullable=False)
    name = Column(String(20), nullable=False)
    note = Column(String(120), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    type = Column(String(24), nullable=False)


class LabSample(db.Model):
    __tablename__ = "lab_sample"
    lid = Column(String(12), primary_key=True)
    barcode = Column(String(12), nullable=False)
    samplecode = Column(String(12), nullable=True)
    lab_receipt_date = Column(
        DateTime(timezone=False),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    note = Column(String(120), nullable=True)
    technology = Column(String(20), nullable=False)
    positive_control = Column(Boolean, nullable=False, default=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class MissingSample(db.Model):
    __tablename__ = "missing_sample"
    barcode = Column(String(12), nullable=False)
    samplecode = Column(String(12), nullable=True)
    status = Column(String(50), nullable=False)
    note = Column(String(50), nullable=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class DnaExtraction(db.Model):
    __tablename__ = "dna_extractions"

    id = Column(Integer, primary_key=True)
    lid = Column(String(12), nullable=False)
    dna_extraction_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    qubit = Column(Float, nullable=True)
    nano_drop = Column(Float, nullable=False)
    a260_a280 = Column(Float, nullable=False)
    agarose_gel = Column(String(50), nullable=False)
    dna_qc_status = Column(String(50), nullable=False)
    note = Column(String(100), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)


class Plate(db.Model):
    __tablename__ = "plate"
    id = Column(UUID(), primary_key=True)
    name = Column(String(12), nullable=False)  # GET next batch_barcode API
    status = Column(
        String(12), nullable=False
    )  # Status of Plate instead of using Status of Chip
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    type = Column(String(24), nullable=False)


class DnaBox(db.Model):
    __tablename__ = "dna_box"
    id = Column(String(12), primary_key=True)
    capacity = Column(Integer(), nullable=False)  # GET next batch_barcode API
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )


class DnaBoxMappings(db.Model):
    __tablename__ = "dna_box_mappings"
    id = Column(UUID(), primary_key=True)
    position = Column(String(12), nullable=False)  # GET next batch_barcode API
    dna_box_id = Column(String(12), nullable=False)  # GET next batch_barcode API
    lid = Column(String(12), nullable=False)  # GET next batch_barcode API
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )


class CardProduct(db.Model):
    __tablename__ = "card_product"
    id = Column(UUID(), primary_key=True)
    type = Column(String(50), nullable=False)  # GET next batch_barcode API
    card_product_name = Column(
        String(200), nullable=False
    )  # GET next batch_barcode API
    policy = Column(String(50), nullable=False)  # GET next batch_barcode API
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )


class Card(db.Model):
    __tablename__ = "card"
    id = Column(String(32), primary_key=True)
    card_product_id = Column(UUID(), nullable=False)  # GET next batch_barcode API
    barcode = Column(String(120), nullable=False)
    phone_number = Column(String(120), nullable=False)
    user_id = Column(String(120), nullable=False)
    report_ver = Column(String(14), nullable=False)
    db_ver = Column(String(14), nullable=False)
    lang = Column(String(5), nullable=False)
    card_status = Column(String(14), nullable=True)
    full_name = Column(String(255), nullable=True)
    qr_url = Column(String(255), nullable=True)
    s3_object_key = Column(String(500), nullable=True)
    presigned_s3_font_url = Column(String(500), nullable=True)
    presigned_s3_back_url = Column(String(500), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )


class Subject(db.Model):
    __tablename__ = "subject"
    id = Column(UUID(), primary_key=True)
    user_id = Column(UUID(), nullable=True)
    full_name = Column(String(120), nullable=False)
    email = Column(String(50), nullable=True)
    phone_number = Column(String(50), nullable=True)
    diagnosis = Column(String(200), nullable=True)
    address = Column(String(250), nullable=False)
    identifier_code = Column(String(50), nullable=False)
    legal_guardian = Column(String(120), nullable=True)
    martyr_name = Column(Text, nullable=True)
    martyr_relationships = Column(JSONB, nullable=True)
    dob = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    gender = Column(String(50), nullable=False)
    yob = Column(String(4), nullable=True)
    validate_account = Column(Boolean, nullable=False, default=False)
    require_registration = Column(Boolean, nullable=False, default=False)
    guardian_name = Column(String(120), nullable=True)
    guardian_gender = Column(String(50), nullable=True)
    guardian_phone_number = Column(String(50), nullable=True)
    guardian_identifier_code = Column(String(50), nullable=True)

    created_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )
    updated_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=True,
    )
    deleted_at = Column(Date(), default=None, nullable=True)


class IdentityCard(db.Model):
    __tablename__ = "identity_card"
    id = Column(UUID(), primary_key=True)
    identifier_code = Column(String(50), nullable=False)
    full_name = Column(String(120), nullable=False)
    dob = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    gender = Column(String(50), nullable=False)
    nationality = Column(String(250), nullable=False)
    origin = Column(String(250), nullable=False)
    residence = Column(String(250), nullable=False)
    avatar_image = Column(Text(), nullable=False)
    fingerprint_image = Column(Text(), nullable=False)
    ethnic = Column(String(50), nullable=False)
    email = Column(String(50), nullable=True)
    phone_number = Column(String(50), nullable=True)
    customer_support_name = Column(String(120), nullable=True)
    customer_support_id = Column(UUID(), nullable=True)
    manual_input = Column(Boolean, nullable=True, default=False)
    created_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )
    updated_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=True,
    )
    deleted_at = Column(Date(), default=None, nullable=True)


class SampleV3(db.Model):
    __tablename__ = "sample"
    id = Column(UUID(), primary_key=True)
    samplecode = Column(String(12), nullable=False)
    subject_id = Column(UUID(), nullable=False)
    sample_collection_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=True,
    )
    sample_recollection = Column(Boolean, nullable=False, default=False)
    sample_collection_time = Column(Integer, nullable=False, default=1)
    sample_receipt_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=True,
    )
    sample_collector_name = Column(String(50), nullable=True)
    sample_receiver_name = Column(String(50), nullable=True)
    source_id = Column(UUID, nullable=False)
    scan_status = Column(Integer, nullable=False, default=0)
    lab_check_date = Column(Date(), nullable=True)
    lab_receipt_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=True,
    )
    sample_type = Column(String(30), nullable=True)
    run_id = Column(String(100), nullable=True)
    sponsor_id = Column(UUID, nullable=True)
    created_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )
    updated_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=True,
    )
    deleted_at = Column(Date(), default=None, nullable=True)


class Samplecode(db.Model):
    __tablename__ = "samplecode"

    samplecode = Column(String(12), primary_key=True)
    printed = Column(Boolean, default=False, nullable=False)
    state = Column(String(50))
    note = Column(String(120), nullable=True)
    created_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )
    updated_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )


class Billcode(db.Model):
    __tablename__ = "billcode"

    billcode = Column(String(36), primary_key=True)
    printed = Column(Boolean, default=False, nullable=False)
    status = Column(String(100))
    created_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )
    updated_at = Column(
        Date(),
        default=datetime.utcnow()
        .strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR)
        .date(),
        nullable=False,
    )


class KitV3(db.Model):
    __tablename__ = "kit"
    id = Column(UUID(), primary_key=True)
    barcode = Column(String(50), nullable=True)
    samplecode = Column(String(12), nullable=False)
    nickname = Column(String(50), nullable=True)
    version = Column(String(50), nullable=True)
    expected_report_release_date = Column(Date(), default=None, nullable=True)

    actual_report_release_time = Column(type_=TIMESTAMP(timezone=False), nullable=True)
    customer_support_id = Column(UUID(), nullable=True)
    customer_support_name = Column(String(120), nullable=True)
    free_of_charge = Column(Boolean, nullable=True, default=False)
    promotion = Column(UUID(), nullable=True)
    is_priority = Column(Boolean, nullable=False, default=False)
    product_code = Column(String(50), nullable=False)
    product_name = Column(String(50), nullable=False)
    product_type = Column(String(50), nullable=False, default="CLINIC")
    pdf_generation_date = Column(Date(), nullable=True)
    default_pdf_link = Column(String(500), nullable=True)
    is_card_issued = Column(Boolean, nullable=False, default=False)
    note = Column(String(100), nullable=True)
    current_status = Column(String(50), nullable=True)
    current_status_id = Column(Integer, nullable=True)
    workflow = Column(String(120), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class AdnIntegrationType(db.Model):
    __tablename__ = "adn_integration_type"
    id = Column(UUID(), primary_key=True)
    name = Column(String(120), nullable=False)
    adn_type = Column(String(120), nullable=False)
    method = Column(String(120), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)
    pass


class Agency(db.Model):
    __tablename__ = "agency"
    id = Column(UUID(), primary_key=True)
    name = Column(String(120), nullable=False)
    type = Column(String(120), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)
    pass


class AdnIntegration(db.Model):
    __tablename__ = "adn_integration"
    id = Column(UUID(), primary_key=True)
    samplecode = Column(String(120), nullable=False)
    barcode = Column(String(120), nullable=False)
    type_id = Column(UUID(), nullable=True)
    status = Column(String(120), nullable=False, default="PENDING")
    presigned_s3_url = Column(String(500), nullable=True)
    raw_adn_s3_obj_key = Column(String(500), nullable=True)
    review_required = Column(Boolean, nullable=False, default=True)
    response_date = Column(DateTime(timezone=True), default=None, nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)
    pass


class RequestAdnIntegration(db.Model):
    __tablename__ = "request_adn_integration"
    id = Column(UUID(), primary_key=True)
    request_id = Column(UUID(), nullable=False)
    adn_integration_id = Column(UUID(), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class Request(db.Model):
    __tablename__ = "request"
    id = Column(UUID(), primary_key=True)
    request_code = Column(String(120), nullable=False)
    agency_id = Column(UUID(), nullable=False)
    customer_support_name = Column(String(120), nullable=True)
    customer_support_id = Column(UUID(), nullable=True)
    customer_name = Column(String(120), nullable=False)
    customer_phone = Column(String(120), nullable=False)
    dob = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    gender = Column(String(50), nullable=False)
    payment_amount = Column(Integer, nullable=False, default=0)
    payment_method = Column(String(120), nullable=False, default="Cash")
    payment_status = Column(String(50), nullable=False, default="PENDING")
    status = Column(String(50), nullable=False, default="SAMPLE_NOT_FOUND")
    status_code = Column(String(50), nullable=True)
    error_message = Column(String(120), nullable=True)
    transaction_date = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=True,
    )
    collect_date = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=True,
    )
    request_date = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=True,
    )
    response_date = Column(DateTime(timezone=True), default=None, nullable=True)
    identifier_code = Column(String(50), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)
    pass


class Tracking(db.Model):
    __tablename__ = "tracking"
    id = Column(UUID(), primary_key=True, default=uuid.uuid4)
    samplecode = Column(String(120), nullable=False)
    identifier_code = Column(String(120), nullable=True)
    status = Column(String(120), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingTemplate(db.Model):
    __tablename__ = "tracking_template"
    id = Column(UUID(), primary_key=True)
    congNghe = Column(String(120), nullable=False)
    tenKit = Column(String(120), nullable=False)
    gs_template_name = Column(String(120), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingUnit(db.Model):
    __tablename__ = "tracking_unit"
    id = Column(Integer, primary_key=True)
    tenDonVi = Column(String(120), nullable=False)
    gs_area = Column(String(120), nullable=False)
    gs_area_code = Column(Integer, nullable=False)
    gs_phone_number = Column(Text, nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingUnitLocation(db.Model):
    __tablename__ = "tracking_unit_location"
    id = Column(UUID(), primary_key=True)
    unit_id = Column(Integer, nullable=False)
    name = Column(String(200), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingCollectSession(db.Model):
    __tablename__ = "tracking_collect_session"
    id = Column(UUID(), primary_key=True)
    location_id = Column(UUID(), nullable=False)
    user_id = Column(UUID(), nullable=False)
    phone_number = Column(String(50), nullable=False)
    employee_id = Column(UUID(), nullable=False)
    employee_name = Column(String(120), nullable=False)
    collect_date = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingSessionSample(db.Model):
    __tablename__ = "tracking_session_sample"
    id = Column(UUID(), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(), nullable=False)
    samplecode = Column(String(36), nullable=True)
    subject_id = Column(UUID(), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingStep(db.Model):
    __tablename__ = "tracking_step"
    id = Column(UUID(), primary_key=True)
    tenBuoc = Column(String(120), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingEmployee(db.Model):
    __tablename__ = "tracking_employee"
    id = Column(UUID(), primary_key=True)
    hoTenNhanVien = Column(String(120), nullable=False)
    hocVi = Column(String(120), nullable=True)
    soDinhDanh = Column(String(120), nullable=False)
    chucVu = Column(String(120), nullable=False)
    unit_id = Column(Integer, nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingCollection(db.Model):
    __tablename__ = "tracking_collection"
    id = Column(UUID(), primary_key=True, default=uuid.uuid4)
    tracking_id = Column(UUID(), nullable=False)
    maThuNhan = Column(String(120), nullable=False)
    donViThuNhanMau_id = Column(Integer, nullable=False)
    noiThuThapMau = Column(Integer, nullable=False)
    ngayGioThuThapMau = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    nhanVienLayMau_id = Column(UUID(), nullable=False)
    nhanVienGhiHoSo_id = Column(UUID(), nullable=False)
    nhanVienLuuMau_id = Column(UUID(), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)

    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}


class TrackingDelivery(db.Model):
    __tablename__ = "tracking_delivery"
    id = Column(UUID(), primary_key=True)
    tracking_id = Column(UUID(), nullable=False)
    maVanChuyen = Column(String(120), nullable=True)
    nhietDoChuyenGiao = Column(String(120), nullable=True)
    tinhTrangNiemPhong = Column(Boolean, nullable=False)
    ngayGioChuyenGiao = Column(
        Date(),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_NO_TIME_ZONE),
            DEFAULT_DATE_NO_TIME_ZONE,
        ),
        nullable=False,
    )
    diaDiemChuyenGiao = Column(Integer, nullable=False)
    donViBanGiao_id = Column(Integer, nullable=False)
    donViVanChuyen_id = Column(Integer, nullable=True)
    donViNhanMau_id = Column(Integer, nullable=False)
    nhanVienBanGiao_id = Column(UUID(), nullable=False)
    nhanVienNhanMau_id = Column(UUID(), nullable=False)
    khac = Column(String(200), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingProcedure(db.Model):
    __tablename__ = "tracking_procedure"
    id = Column(UUID(), primary_key=True)
    tracking_id = Column(UUID(), nullable=False)
    template_id = Column(UUID(), nullable=False)
    maXetNghiem = Column(String(120), nullable=False)
    nhietDoLuuTru = Column(String(120), nullable=True)
    gs_barcode = Column(String(120), nullable=False)
    gs_lid = Column(String(120), nullable=False)
    maXetNghiem = Column(String(120), nullable=False)
    gs_currStep = Column(Integer, nullable=False)
    gs_totalStep = Column(Integer, nullable=False)
    gs_isComplete = Column(Boolean, nullable=False, default=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingTemplateStep(db.Model):
    __tablename__ = "tracking_template_step"
    id = Column(UUID(), primary_key=True)
    step_id = Column(UUID(), nullable=False)
    template_id = Column(UUID(), nullable=False)
    gs_step_number = Column(Integer, nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class TrackingProcedureStep(db.Model):
    __tablename__ = "tracking_procedure_step"
    id = Column(UUID(), primary_key=True)
    procedure_id = Column(UUID(), nullable=False)
    employee_id = Column(UUID(), nullable=False)
    template_step_id = Column(UUID(), nullable=False)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class AdnResult(db.Model):
    __tablename__ = "adn_result"
    id = Column(UUID(), primary_key=True)
    adn_integration_id = Column(UUID(), nullable=False)
    tenFileADN = Column(String(120), nullable=False)
    congNghe = Column(String(120), nullable=False)
    tenKit = Column(String(120), nullable=False)
    tenThuMucTho = Column(String(120), nullable=True)
    loaiDuLieu = Column(String(120), nullable=False)
    gs_adn_result_s3_url = Column(String(500), nullable=True)
    gs_raw_result_s3_url = Column(String(500), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class SampleRack(db.Model):
    __tablename__ = "sample_rack"
    id = Column(UUID(), primary_key=True)
    code = Column(String(120), nullable=False, unique=True)  # Unique identifier for the rack
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)


class SampleBox(db.Model):
    __tablename__ = "sample_box"
    id = Column(UUID(), primary_key=True)
    code = Column(String(120), nullable=False, unique=True)  # Unique identifier for the box
    sample_rack_id = Column(UUID(), ForeignKey("sample_rack.id"), nullable=True)  # Foreign key to SampleRack
    created_by = Column(String(120), nullable=True)
    updated_by = Column(String(120), nullable=True)
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)



class SampleBoxPosition(db.Model):
    __tablename__ = "sample_box_position"
    id = Column(UUID(), primary_key=True)
    sample_box_id = Column(UUID(), ForeignKey("sample_box.id"), nullable=False)
    barcode = Column(String(12), nullable=False)
    x_position = Column(String(12), nullable=False)  # X coordinate in the box
    y_position = Column(String(12), nullable=False)  # Y coordinate in the box
    created_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow().strptime(
            datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR
        ),
        nullable=False,
    )
    deleted_at = Column(DateTime(timezone=True), default=None, nullable=True)

