import requests
from fastapi import APIRout<PERSON>, FastAPI, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.sale_account_history import *

from ...schemas.sale_account_history import *

router = APIRouter()

@router.post("/admin/partner/history", status_code=HTTP_200_OK)
async def admin_create_sale_account_history(
  history_body: AddSaleAccountHistory,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)
  
  data = history_body.dict()
  if not data:
      err = "No changed field to update"
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
  logger.info(f"Admin create a Sale Account History with data {data}")

  data, err = await create_sale_account_history(data)
  if err:
    http_code = HTTP_400_BAD_REQUEST
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)
  else:
    return success_response(data), None



# @router.get("/admin/partner/histories", status_code=HTTP_200_OK)
# async def admin_get_histories(
#   page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
#   page_number: int = Query(1, ge=1),
#   account_name: Optional[str] = Query(None),
#   sale_pic_name: Optional[str] = Query(None),
#   start_date: Optional[str]=Query(None),
#   end_date: Optional[str]=Query(None),
#   auth=Depends(Auth),
# ):


@router.get("/admin/partner/history/{id}", status_code=HTTP_200_OK)
async def admin_get_account_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)
  
  history = await get_sale_account_history(id)
  if history:
    res_history = history.to_dict()
    return success_response(res_history)
  else:
    err = f"Sale Account History with id {id} can not be found"
    http_code = HTTP_404_NOT_FOUND
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)


# @router.put("/admin/partner/history/{id}", status_code=HTTP_200_OK)
# async def admin_update_history_info_by_id(
#   id: str,
#   body: UpdateSaleAccountHistory,
#   auth=Depends(Auth),
# ) -> dict:


# @router.delete("/admin/partner/history/{id}", status_code=HTTP_200_OK)
# async def admin_delete_account_by_id(
#   id: str,
#   auth=Depends(Auth),
# ) -> dict:

#   logger.info(f"Delete a sale account history with id {id}")
#   _, err = await auth.get_token_claims()
#   if err:
#     errs = failure_response(err['err_msg'])
#     raise HTTPException(status_code=err['err_code'], detail=errs)
  
#   history = await get_sale_account_history(id)
#   if not history:
#       err = f"Sale Account History with id {id} can not be found"
#       http_code = HTTP_404_NOT_FOUND
#       errs = failure_response(err)
#       raise HTTPException(status_code=http_code, detail=errs)

#   current_time = get_current_date_time_utc_7()
#   data = {
#     'updated_at': current_time,
#     'deleted_at': current_time,
#   }

#   history, err = await delete_sale_account_history(history,data)
#   if err:
#     http_code = HTTP_404_NOT_FOUND
#     errs = failure_response(err)
#     raise HTTPException(status_code=http_code, detail=errs)
#   else:
#     updated_history_info = await get_sale_account_history(id)
#     return success_response(updated_history_info.to_dict())

  pass

def init_app(app: FastAPI):
    app.include_router(router, tags=["sale_account_history"])