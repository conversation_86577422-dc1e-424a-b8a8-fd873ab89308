import requests
from ...auth import Auth
from ...config import config
from fastapi import APIRouter, FastAPI, HTTPException, Query, Depends, Body
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)
from typing import List
from ...schemas.partner_booking import *
from ...services.partner_booking import *
from ...utils.utils import (
    success_response,
    failure_response,
)

router = APIRouter()

@router.get("/partner/booking", status_code=HTTP_200_OK)
async def get_partner_booking_ctrl(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    partner_name: Optional[str]=None,
    customer_name: Optional[str]=None,
    customer_phone: Optional[str]=None,
    payment_method: Optional[str]=None,
    payment_status: Optional[str]=None,
    status: Optional[str]=None,
    consulted: Optional[str]=None,
    creation_start_date: Optional[str]=None,
    creation_end_date: Optional[str]=None,
    include_deleted: bool=False,
    auth=Depends(Auth),
):
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        data, total = await get_partner_booking_srv_v3(
            offset=offset,
            size=size,
            partner_name=partner_name,
            customer_name=customer_name,
            customer_phone=customer_phone,
            payment_method=payment_method,
            payment_status=payment_status,
            status=status,
            consulted=consulted,
            creation_start_date=creation_start_date,
            creation_end_date=creation_end_date,
            include_deleted=include_deleted,
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/partner/booking/{id}", status_code=HTTP_200_OK)
async def get_partner_booking_by_id_ctrl(
    id: str,
    auth=Depends(Auth),
):
    try:
        res = await get_partner_booking_by_id_srv_v3(id=id)
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    
@router.post("/external/partner/booking", status_code=HTTP_200_OK)
async def external_create_partner_booking_ctrl(
    req: PartnerBookingCreationReq,
):
    try:
        res = await create_partner_booking_srv_v3(req=req)
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)


@router.post("/partner/booking", status_code=HTTP_200_OK)
async def create_partner_booking_ctrl(
    req: PartnerBookingCreationReq,
    auth=Depends(Auth),
):
    try:
        claim, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        
        req.customer_support_id = claim["user_id"]
        res = await create_partner_booking_srv_v3(req=req)
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
        
@router.put("/partner/booking/{id}", status_code=HTTP_200_OK)
async def update_partner_booking_ctrl(
    id: str,
    req: PartnerBookingUpdateReq,
    auth=Depends(Auth),
):
    try:
        claim, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        req.customer_support_id = claim["user_id"]
        res = await update_partner_booking_srv_v3(
            partner_booking_id=id,
            req=req
        )
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    
@router.put("/internal/partner/booking/{id}", status_code=HTTP_200_OK)
async def internal_update_partner_booking_ctrl(
    id: str,
    req: PartnerBookingUpdateReq,
):
    try:
        res = await update_partner_booking_srv_v3(
            partner_booking_id=id,
            req=req
        )
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)

@router.delete("/partner/booking/{id}", status_code=HTTP_200_OK)
async def delete_partner_booking_ctrl(
    id: str,
    auth=Depends(Auth)
):
    try:
        claim, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        res = await delete_booking_by_id_srv_v3(
            booking_id=id
        )
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    
@router.post("/partner/booking/results/{id}", status_code=HTTP_200_OK)
async def send_booking_results_ctrl(
    id: str,
    req: PartnerBookingResultReq,
    auth=Depends(Auth)
):
    try:
        claim, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        res = await send_booking_results_srv_v3(
            booking_id=id,
            req=req,
        )
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)


@router.post("/partner/booking/sync", status_code=HTTP_200_OK)
async def sync_partner_booking_info(
    req: SyncBookingInfoReq,
    auth=Depends(Auth)
):
    try:
        claim, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        res = await sync_booking_information_srv_v3(req=req)
        return success_response(res)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    
@router.put("/partner/booking/results/{booking_id}", status_code=HTTP_200_OK)
async def update_booking_results(
    booking_id: str,
    req: ResultUpdateReq,
):
    try:
        resp = await update_results_by_booking_id_srv_v3(
            booking_id=booking_id,
            results=req
        )
        return success_response(resp)
    except ValueError as e:
        http_code = HTTP_400_BAD_REQUEST
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    except Exception as e:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        err = failure_response(e)
        raise HTTPException(status_code=http_code, detail=err)
    
def init_app(app: FastAPI):
    app.include_router(router, tags=["partner_booking"])
    