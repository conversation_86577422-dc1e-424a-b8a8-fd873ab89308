import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, FastAPI, HTTPException, Query, status
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ... import logger
from ...auth import Auth
from ...config import config
from ...cruds.staff import (
    count_staffs,
    create_staff,
    delete_staff,
    get_all_staffs,
    get_current_sale_account_history,
    get_sale_pic_stats,
    get_staff,
    update_staff,
)
from ...models.models import Staff
from ...schemas.staff import *
from ...services.staff import check_default_sale_pic_exist, parse_monthly_sale_pic_stats
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    failure_response,
    get_current_date_time_utc_7,
    paging_a_list_of_rows,
    success_response,
)

router = APIRouter()

# @router.post("/admin/partner/staff", status_code=HTTP_200_OK)
# @router.get("/admin/partner/staffs", status_code=HTTP_200_OK)
# @router.get("/admin/partner/staff/{id}", status_code=HTTP_200_OK)
# @router.put("/admin/partner/staff/{id}", status_code=HTTP_200_OK)
# @router.delete("/admin/partner/staff/{id}", status_code=HTTP_200_OK)

router = APIRouter()


@router.get("/admin/partner/staff/default", status_code=HTTP_200_OK)
async def admin_create(
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    data, _ = await check_default_sale_pic_exist()
    return success_response(data), None


@router.post("/admin/partner/staff", status_code=HTTP_200_OK)
async def admin_create(
    staff_body: AddStaff,
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    data = staff_body.dict()
    if not data:
        err = "No changed field to update"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    logger.info(f"Admin create a Sale staff with data {data}")

    data, err = await create_staff(data)
    if err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        return success_response(data), None


# DONE
@router.get("/admin/partner/staffs", status_code=HTTP_200_OK)
async def admin_get_staffs(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    account_name: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    name: Optional[str] = Query(None),
    account_id: Optional[str] = Query(None),
    userid: Optional[str] = Query(None),
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    auth=Depends(Auth),
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    start_date = (
        convert_str_to_datetime(start_date + "T00:00:00.000000+00:00")[0]
        if start_date
        else convert_str_to_datetime("1970-01-01" + "T00:00:00.000000+00:00")[0]
    )
    end_date = (
        convert_str_to_datetime(end_date + "T23:59:59.999999+00:00")[0]
        if end_date
        else convert_current_utc_to_tz()
    )

    logger.info("Filter staffs info")
    size = page_size
    offset = (page_number - 1) * page_size
    total = await count_staffs()
    if offset > 0 and offset >= total:
        err = "Offset number is too large."
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        results, total_match = await get_all_staffs(
            offset=offset,
            size=size,
            account_name=account_name,
            role=role,
            name=name,
            account_id=account_id,
            userid=userid,
            email=email,
            phone_number=phone_number,
            start_date=start_date,
            end_date=end_date,
            order_by="created_at",
        )

        if not total_match:
            total_match = total

        field_names = [field for field in Staff.__dict__.keys() if "_" != field[0]]
        field_names.append("account_name")

        res_data = [
            {field_names[idx]: res[idx] for idx in range(len(field_names))}
            for res in results
        ]

        res = {
            "data": res_data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/admin/partner/staffs/pic/{pic_phone_number}", status_code=HTTP_200_OK)
async def admin_get_staffs_by_pic(
    pic_phone_number: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    account_name: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    name: Optional[str] = Query(None),
    account_id: Optional[str] = Query(None),
    userid: Optional[str] = Query(None),
    email: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    auth=Depends(Auth),
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    start_date = (
        convert_str_to_datetime(start_date + "T00:00:00.000000+00:00")[0]
        if start_date
        else convert_str_to_datetime("1970-01-01" + "T00:00:00.000000+00:00")[0]
    )
    end_date = (
        convert_str_to_datetime(end_date + "T23:59:59.999999+00:00")[0]
        if end_date
        else convert_current_utc_to_tz()
    )

    logger.info("Filter staffs info")
    size = page_size
    offset = (page_number - 1) * page_size
    total = await count_staffs()
    if offset > 0 and offset >= total:
        err = "Offset number is too large."
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        results, total_match = await get_all_staffs(
            offset=offset,
            size=size,
            account_name=account_name,
            role=role,
            name=name,
            account_id=account_id,
            userid=userid,
            email=email,
            phone_number=pic_phone_number,
            start_date=start_date,
            end_date=end_date,
            order_by="created_at",
        )

        if not total_match:
            total_match = total

        field_names = [field for field in Staff.__dict__.keys() if "_" != field[0]]
        field_names.append("account_name")

        res_data = [
            {field_names[idx]: res[idx] for idx in range(len(field_names))}
            for res in results
        ]

        res = {
            "data": res_data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/admin/partner/staffs/stats", status_code=HTTP_200_OK)
async def admin_get_staff_info_by_id(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    account_name: Optional[str] = Query(None),
    sale_pic_name: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    order_by: Optional[str] = Query(None),
    order_option: Optional[str] = "desc",
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        data, _ = await get_sale_pic_stats(
            order_by=order_by if order_by else "sale_account_history.created_at",
            order_option=order_option,
            account_name=account_name,
            sale_pic_name=sale_pic_name,
            year=year,
        )
        res_data, total = parse_monthly_sale_pic_stats(data)
        paged_res = paging_a_list_of_rows(res_data, page_size, page_number)
        res = {
            "data": paged_res,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res

    except requests.exceptions.ConnectionError as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs
        )
    except requests.exceptions.Timeout as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=errs)
    except requests.exceptions.RequestException as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs
        )
    except ValueError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=errs
        )


@router.get("/admin/partner/staffs/stats/pic/{pic_phone_number}", status_code=HTTP_200_OK)
async def get_staff_stats_by_pic(
    pic_phone_number: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    account_name: Optional[str] = Query(None),
    sale_pic_name: Optional[str] = Query(None),
    year: Optional[int] = Query(None),
    order_by: Optional[str] = Query(None),
    order_option: Optional[str] = "desc",
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        data, _ = await get_sale_pic_stats(
            order_by=order_by if order_by else "sale_account_history.created_at",
            order_option=order_option,
            account_name=account_name,
            sale_pic_name=sale_pic_name,
            year=year,
            pic_phone_number=pic_phone_number
        )
        res_data, total = parse_monthly_sale_pic_stats(data)
        paged_res = paging_a_list_of_rows(res_data, page_size, page_number)
        res = {
            "data": paged_res,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res

    except requests.exceptions.ConnectionError as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs
        )
    except requests.exceptions.Timeout as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=errs)
    except requests.exceptions.RequestException as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs
        )
    except ValueError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=errs
        )


@router.get("/admin/partner/sale_account_history", status_code=HTTP_200_OK)
async def admin_get_staff_info_by_id(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    pic_id: Optional[str] = Query(None),
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_current_sale_account_history(
            offset=offset, size=size, pic_id=pic_id
        )
        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res

    except requests.exceptions.ConnectionError as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs
        )
    except requests.exceptions.Timeout as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=errs)
    except requests.exceptions.RequestException as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs
        )
    except ValueError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        errs = failure_response(err)
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=errs
        )


# DONE
@router.get("/admin/partner/staff/{id}", status_code=HTTP_200_OK)
async def admin_get_staff_info_by_id(
    id: str,
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    staff = await get_staff(id)
    if staff:
        res_staff = staff.to_dict()
        return success_response(res_staff)
    else:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# DONE
@router.put("/admin/partner/staff/{id}", status_code=HTTP_200_OK)
async def admin_update_staff_info_by_id(
    id: str,
    body: UpdateStaff,
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    data = body.dict()
    data = {k: v for k, v in data.items() if v is not None}
    if not data:
        err = "No changed field to update"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    current_time = get_current_date_time_utc_7()
    new_staff_info = {**data, "updated_at": current_time}

    staff = await get_staff(id)
    if not staff:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    err = await update_staff(staff, new_staff_info)
    if err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        updated_staff_info = await get_staff(id)
        return success_response(updated_staff_info.to_dict())


# DONE
@router.delete("/admin/partner/staff/{id}", status_code=HTTP_200_OK)
async def admin_delete_staff_by_id(
    id: str,
    auth=Depends(Auth),
) -> dict:
    logger.info(f"Delete an sale staff with id {id}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    staff = await get_staff(id)
    if not staff:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    current_time = get_current_date_time_utc_7()
    data = {
        "updated_at": current_time,
        "deleted_at": current_time,
    }

    staff, err = await delete_staff(staff, data)
    if err:
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        updated_staff_info = await get_staff(id)
        return success_response(updated_staff_info.to_dict())


def init_app(app: FastAPI):
    app.include_router(router, tags=["staff"])
