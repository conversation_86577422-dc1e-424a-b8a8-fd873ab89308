from typing import Optional

from fastapi import APIRouter, Depends, FastAPI, HTTPException, Query
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ... import logger
from ...auth import Auth
from ...dependencies.pagination import PaginationParams

from ...config import config
from ...cruds.account import (
    count_accounts,
    create_account,
    delete_account,
    get_account,
    get_all_accounts,
    update_account,
)
from ...cruds.sale_account_history import (
    create_sale_account_history,
    get_current_sale_account_history_by_account_id,
)
from ...models.models import Account
from ...schemas.account import *
from ...schemas.staff import *
from ...services.staff import *
from ...utils.utils import (
    convert_str_to_datetime,
    failure_response,
    get_current_date_time_utc_7,
    success_response,
)

router = APIRouter()


@router.post("/admin/partner/account", status_code=HTTP_200_OK)
async def admin_create(
    account_body: AddAccount,
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    data = account_body.dict()
    if not data:
        err = "No changed field to update"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    logger.info(f"Admin create a Sale Account with data {data}")
    data, account_id, err = await create_account(data)

    new_history_data = dict()
    new_history_data["pic_id"] = account_body.pic_id
    new_history_data["account_id"] = account_id

    if err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        if not account_body.pic_id:
            default_, _ = await check_default_sale_pic_exist()
            new_history_data["pic_id"] = default_["id"]

        logger.info(f"Admin create a Sale Account History with data {new_history_data}")
        new_history_data, err = await create_sale_account_history(new_history_data)
        if err:
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            data["pic_id"] = new_history_data["pic_id"]
            return success_response(data), None


# DONE
@router.get("/admin/partner/accounts", status_code=HTTP_200_OK)
async def admin_get_accounts(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    type: Optional[str] = Query(None),
    area: Optional[str] = Query(None),
    name: Optional[str] = Query(None),
    staff_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    pagination: PaginationParams = Depends(),
    auth=Depends(Auth),
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    start_date = (
        convert_str_to_datetime(start_date + "T00:00:00.000000+00:00")[0]
        if start_date
        else convert_str_to_datetime("1970-01-01" + "T00:00:00.000000+00:00")[0]
    )
    end_date = (
        convert_str_to_datetime(end_date + "T23:59:59.999999+00:00")[0]
        if end_date
        else convert_str_to_datetime("2300-01-01" + "T00:00:00.000000+00:00")[0]
    )

    logger.info("Filter accounts info")

    total = await count_accounts()
    if pagination.offset is not None and pagination.offset > 0 and pagination.offset >= total:
        err = "Offset number is too large."
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        results, total_match = await get_all_accounts(
            size=pagination.size,
            offset=pagination.offset,
            type=type,
            area=area,
            name=name,
            staff_name=staff_name,
            start_date=start_date,
            end_date=end_date,
            order_by="created_at",
        )

        if not total_match:
            total_match = total

        field_names = [field for field in Account.__dict__.keys() if "_" != field[0]]
        field_names.append("staff_name")
        field_names.append("current_history_id")
        field_names.append("pic_id")

        # res_data = [r.to_dict() for r in accounts]
        res_data = [
            {field_names[idx]: res[idx] for idx in range(len(field_names))}
            for res in results
        ]
        res = {
            "data": res_data,
            "pagination": {
                "page_size": pagination.page_size,
                "page_number": pagination.page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# DONE
@router.get("/admin/partner/account/{id}", status_code=HTTP_200_OK)
async def admin_get_account_info_by_id(
    id: str,
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    account = await get_account(id)

    if account:
        res_account = account.to_dict()
        try:
            latest_history = await get_current_sale_account_history_by_account_id(id)
            res_account["pic_id"] = latest_history["pic_id"]
        except IndexError:
            logger.info("Account is not assigned with PIC_ID yet!")
            res_account["pic_id"] = None

        return success_response(res_account)
    else:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# DONE
@router.put("/admin/partner/account/{id}", status_code=HTTP_200_OK)
async def admin_update_account_info_by_id(
    id: str,
    body: UpdateAccount,
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    data = body.dict()
    data = {k: v for k, v in data.items() if v is not None}
    if not data:
        err = "No changed field to update"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    current_time = get_current_date_time_utc_7()
    new_account_info = {**data, "updated_at": current_time}

    if new_account_info.get("pic_id"):
        new_account_info.pop("pic_id")

    account = await get_account(id)
    if not account:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    err = await update_account(account, new_account_info)

    new_history_data = dict()
    new_history_data["pic_id"] = body.pic_id
    new_history_data["account_id"] = id

    if err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        if not new_history_data["pic_id"]:
            default_, _ = await check_default_sale_pic_exist()
            new_history_data["pic_id"] = default_[0]["id"]
        logger.info(f"Admin create a Sale Account History with data {new_history_data}")
        new_history_data, err = await create_sale_account_history(new_history_data)
        if err:
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            updated_account_info = await get_account(id)
            data = updated_account_info.to_dict()
            data["pic_id"] = new_history_data["pic_id"]
            return success_response(data)


# DONE
@router.delete("/admin/partner/account/{id}", status_code=HTTP_200_OK)
async def admin_delete_account_by_id(
    id: str,
    auth=Depends(Auth),
) -> dict:
    logger.info(f"Delete a sale account with id {id}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    account = await get_account(id)
    if not account:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    current_time = get_current_date_time_utc_7()
    data = {
        "updated_at": current_time,
        "deleted_at": current_time,
    }

    account, err = await delete_account(account, data)
    if err:
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        updated_account_info = await get_account(id)
        return success_response(updated_account_info.to_dict())


@router.get("/admin/partner/accounts/pic/{pic_phone_number}", status_code=HTTP_200_OK)
async def get_accounts_by_sale_pic(
    pic_phone_number: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    type: Optional[str] = Query(None),
    area: Optional[str] = Query(None),
    name: Optional[str] = Query(None),
    staff_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    auth=Depends(Auth),
):
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    # sub = token_claims.get("sub")
    # if sale_pic_id is None or sub != sale_pic_id:
    #     raise HTTPException(
    #         status_code=HTTP_401_UNAUTHORIZED, detail="Unauthorized to access"
    #     )

    start_date = (
        convert_str_to_datetime(start_date + "T00:00:00.000000+00:00")[0]
        if start_date
        else convert_str_to_datetime("1970-01-01" + "T00:00:00.000000+00:00")[0]
    )
    end_date = (
        convert_str_to_datetime(end_date + "T23:59:59.999999+00:00")[0]
        if end_date
        else convert_str_to_datetime("2300-01-01" + "T00:00:00.000000+00:00")[0]
    )

    logger.info("Filter accounts info")
    size = page_size
    offset = (page_number - 1) * page_size
    total = await count_accounts()
    if offset > 0 and offset >= total:
        err = "Offset number is too large."
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        results, total_match = await get_all_accounts(
            size=size,
            offset=offset,
            type=type,
            area=area,
            name=name,
            staff_name=staff_name,
            start_date=start_date,
            end_date=end_date,
            order_by="created_at",
            pic_phone_number=pic_phone_number,
        )

        if not total_match:
            total_match = total

        field_names = [field for field in Account.__dict__.keys() if "_" != field[0]]
        field_names.append("staff_name")
        field_names.append("current_history_id")
        field_names.append("pic_id")

        # res_data = [r.to_dict() for r in accounts]
        res_data = [
            {field_names[idx]: res[idx] for idx in range(len(field_names))}
            for res in results
        ]
        res = {
            "data": res_data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, tags=["sale_account"])
