import os
import uuid
from typing import Optional

from fastapi import APIRouter, File, HTTPException, Path, Query, UploadFile, Depends
from fastapi.responses import JSONResponse, FileResponse

from src.operation import logger
from src.operation.schemas.sponsor import (
    GsAreaCreate,
    GsAreaUpdate,
    SponsorCampaignCreate,
    SponsorCampaignUpdate,
    SponsorContractCreate,
    SponsorContractUpdate,
    SponsorCreate,
    SponsorUpdate,
)
from src.operation.services.sponsor import (
    GsAreaService,
    SponsorCampaignService,
    SponsorContractService,
    SponsorService,
)
from src.operation.utils.utils import (
    failure_response,
    success_response,
    success_response_w_pagination,
)

from src.operation.utils.file_utils import ExcelValidationError

from ...auth import Auth
from ...dependencies.pagination import PaginationParams


router = APIRouter()

# --- Sponsor Routes ---


@router.get("/sponsors/{sponsor_id}", tags=["sponsors"])
async def get_sponsor(sponsor_id: uuid.UUID, details: bool = False, auth: Auth = Depends(Auth)):
    """Get sponsor by ID"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        sponsor = await SponsorService.get_sponsor(sponsor_id, details)
        return success_response(sponsor)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting sponsor: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.get("/sponsors", tags=["sponsors"])
async def get_all_sponsors(
    name: Optional[str] = Query(None, description="Filter by name"),
    code: Optional[str] = Query(None, description="Filter by code"),
    created_at_from: Optional[str] = Query(None, description="Filter by start date"),
    created_at_to: Optional[str] = Query(None, description="Filter by end date"),
    order_by: Optional[str] = Query("created_at", description="Order by field"),
    order_option: Optional[str] = Query(
        "desc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
    pagination: PaginationParams = Depends(),

):
    """Get all sponsors with optional filtering"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        sponsors, total = await SponsorService.get_all_sponsors(
            page_number=pagination.page_number,
            page_size=pagination.page_size,
            name=name,
            code=code,
            start_date=created_at_from,
            end_date=created_at_to,
            order_by=order_by,
            order_option=order_option,
        )
        return success_response_w_pagination(
            sponsors,
            pagination={
                "page_size": pagination.page_size,
                "page_number": pagination.page_number,
                "total": total,
            },
        )
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting sponsors: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sponsors", tags=["sponsors"])
async def create_sponsor(data: SponsorCreate, auth: Auth = Depends(Auth)):
    """Create a new sponsor"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        new_sponsor = await SponsorService.create_sponsor(data)
        return success_response(new_sponsor)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error creating sponsor: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.put("/sponsors/{sponsor_id}", tags=["sponsors"])
async def update_sponsor(sponsor_id: uuid.UUID, data: SponsorUpdate, auth: Auth = Depends(Auth)):
    """Update an existing sponsor"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        updated_sponsor = await SponsorService.update_sponsor(sponsor_id, data)
        return success_response(updated_sponsor)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error updating sponsor: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.delete("/sponsors/{sponsor_id}", tags=["sponsors"])
async def delete_sponsor(sponsor_id: uuid.UUID, auth: Auth = Depends(Auth)):
    """Delete a sponsor"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await SponsorService.delete_sponsor(sponsor_id)
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error deleting sponsor: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


# --- GsArea Routes ---


@router.get("/gs-areas/{area_id}", tags=["gs-areas"])
async def get_gs_area(area_id: int, auth: Auth = Depends(Auth)):
    """Get GS area by ID"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        area = await GsAreaService.get_gs_area(area_id)
        return success_response(area)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting GS area: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.get("/gs-areas", tags=["gs-areas"])
async def get_all_gs_areas(
    area: Optional[str] = Query(None, description="Filter by area name"),
    order_by: Optional[str] = Query("area", description="Order by field"),
    order_option: Optional[str] = Query(
        "asc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
    pagination: PaginationParams = Depends(),

):
    """Get all GS areas with optional filtering"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        areas, total = await GsAreaService.get_all_gs_areas(
            page_number=pagination.page_number,
            page_size=pagination.page_size,
            area=area,
            order_by=order_by,
            order_option=order_option,
        )
        return success_response_w_pagination(
            areas,
            pagination={
                "page_size": pagination.page_size,
                "page_number": pagination.page_number,
                "total": total,
            },
        )
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting GS areas: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/gs-areas", tags=["gs-areas"])
async def create_gs_area(data: GsAreaCreate, auth: Auth = Depends(Auth)):
    """Create a new GS area"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        new_area = await GsAreaService.create_gs_area(data)
        return success_response(new_area)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error creating GS area: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.put("/gs-areas/{area_id}", tags=["gs-areas"])
async def update_gs_area(area_id: int, data: GsAreaUpdate, auth: Auth = Depends(Auth)):
    """Update an existing GS area"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        updated_area = await GsAreaService.update_gs_area(area_id, data)
        return success_response(updated_area)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error updating GS area: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.delete("/gs-areas/{area_id}", tags=["gs-areas"])
async def delete_gs_area(area_id: int, auth: Auth = Depends(Auth)):
    """Delete a GS area"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await GsAreaService.delete_gs_area(area_id)
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error deleting GS area: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


# --- SponsorContract Routes ---


@router.get("/sponsor-contracts/{contract_id}", tags=["sponsor-contracts"])
async def get_sponsor_contract(contract_id: uuid.UUID, auth: Auth = Depends(Auth)):
    """Get sponsor contract by ID"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        contract = await SponsorContractService.get_sponsor_contract(contract_id)
        return success_response(contract)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting sponsor contract: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.get("/sponsor-contracts", tags=["sponsor-contracts"])
async def get_all_sponsor_contracts(
    name: Optional[str] = Query(None, description="Filter by name"),
    code: Optional[str] = Query(None, description="Filter by code"),
    sponsor_id: Optional[uuid.UUID] = Query(None, description="Filter by sponsor ID"),
    gs_area_code: Optional[int] = Query(None, description="Filter by GS area code"),
    created_at_from: Optional[str] = Query(None, description="Filter by start date"),
    created_at_to: Optional[str] = Query(None, description="Filter by end date"),
    order_by: Optional[str] = Query("created_at", description="Order by field"),
    order_option: Optional[str] = Query(
        "desc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
    pagination: PaginationParams = Depends(),

):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        contracts, total = await SponsorContractService.get_all_sponsor_contracts(
            page_number=pagination.page_number,
            page_size=pagination.page_size,
            name=name,
            code=code,
            sponsor_id=sponsor_id,
            gs_area_code=gs_area_code,
            start_date=created_at_from,
            end_date=created_at_to,
            order_by=order_by,
            order_option=order_option,
        )
        return success_response_w_pagination(
            contracts,
            pagination={
                "page_size": pagination.page_size,
                "page_number": pagination.page_number,
                "total": total,
            },
        )
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting sponsor contracts: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.get("/sponsor-contracts/{contract_id}/details", tags=["sponsor-contracts"])
async def get_sponsor_contract_details(contract_id: uuid.UUID, auth: Auth = Depends(Auth)):
    """Get sponsor contract details including samples and kits"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        details = await SponsorContractService.get_sponsor_contract_details(contract_id)
        return success_response(details)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting sponsor contract details: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sponsor-contracts", tags=["sponsor-contracts"])
async def create_sponsor_contract(contract_data: SponsorContractCreate, auth: Auth = Depends(Auth)):
    """Create a new sponsor contract"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        new_contract = await SponsorContractService.create_sponsor_contract(
            contract_data
        )
        return success_response(new_contract)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error creating sponsor contract: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sponsor-contracts/{contract_id}/upload-file", tags=["sponsor-contracts"])
async def upload_contract_file(
    contract_id: uuid.UUID = Path(..., description="The ID of the sponsor contract"),
    contract_file: UploadFile = File(..., description="The contract document file"),
    auth: Auth = Depends(Auth),
):
    """Upload a file for an existing sponsor contract"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await SponsorContractService.upload_contract_file(
            contract_id=contract_id, contract_file=contract_file
        )
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error uploading contract file: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.put("/sponsor-contracts/{contract_id}", tags=["sponsor-contracts"])
async def update_sponsor_contract(contract_id: uuid.UUID, data: SponsorContractUpdate, auth: Auth = Depends(Auth)):
    """Update an existing sponsor contract"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        updated_contract = await SponsorContractService.update_sponsor_contract(
            contract_id, data
        )
        return success_response(updated_contract)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error updating sponsor contract: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.delete("/sponsor-contracts/{contract_id}", tags=["sponsor-contracts"])
async def delete_sponsor_contract(contract_id: uuid.UUID, auth: Auth = Depends(Auth)):
    """Delete a sponsor contract"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await SponsorContractService.delete_sponsor_contract(contract_id)
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error deleting sponsor contract: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.get("/sponsor-contracts/{contract_id}/download", tags=["sponsor-contracts"])
async def get_contract_file_download_url(
    contract_id: uuid.UUID = Path(..., description="The ID of the sponsor contract"),
):
    """
    Get a presigned URL for downloading the contract file

    Returns:
        Dictionary containing the download URL and expiration information
    """
    try:
        result = await SponsorContractService.get_contract_file_download_url(
            contract_id
        )
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting contract file download URL: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


# --- SponsorCampaign Routes ---


@router.get("/sponsor-campaigns/{campaign_id}", tags=["sponsor-campaigns"])
async def get_sponsor_campaign(campaign_id: uuid.UUID, auth: Auth = Depends(Auth)):
    """Get sponsor campaign by ID"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        campaign = await SponsorCampaignService.get_sponsor_campaign(campaign_id)
        return success_response(campaign)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting sponsor campaign: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.get("/sponsor-campaigns", tags=["sponsor-campaigns"])
async def get_all_sponsor_campaigns(
    contract_id: Optional[uuid.UUID] = Query(None, description="Filter by contract ID"),
    gs_area_code: Optional[uuid.UUID] = Query(
        None, description="Filter by GS area code"
    ),
    created_at_from: Optional[str] = Query(None, description="Filter by start date"),
    created_at_to: Optional[str] = Query(None, description="Filter by end date"),
    order_by: Optional[str] = Query("created_at", description="Order by field"),
    order_option: Optional[str] = Query(
        "desc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
    pagination: PaginationParams = Depends(),

):
    """Get all sponsor campaigns with optional filtering"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        campaigns, total = await SponsorCampaignService.get_all_sponsor_campaigns(
            page_number=pagination.page_number,
            page_size=pagination.page_size,
            contract_id=contract_id,
            gs_area_code=gs_area_code,
            start_date=created_at_from,
            end_date=created_at_to,
            order_by=order_by,
            order_option=order_option,
        )
        return success_response_w_pagination(
            campaigns,
            pagination={
                "page_size": pagination.page_size,
                "page_number": pagination.page_number,
                "total": total,
            },
        )
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error getting sponsor campaigns: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sponsor-campaigns", tags=["sponsor-campaigns"])
async def create_sponsor_campaign(data: SponsorCampaignCreate, auth: Auth = Depends(Auth)):
    """Create a new sponsor campaign"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        new_campaign = await SponsorCampaignService.create_sponsor_campaign(data)
        return success_response(new_campaign)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error creating sponsor campaign: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.put("/sponsor-campaigns/{campaign_id}", tags=["sponsor-campaigns"])
async def update_sponsor_campaign(campaign_id: uuid.UUID, data: SponsorCampaignUpdate, auth: Auth = Depends(Auth)):
    """Update an existing sponsor campaign"""

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        updated_campaign = await SponsorCampaignService.update_sponsor_campaign(
            campaign_id, data
        )
        return success_response(updated_campaign)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error updating sponsor campaign: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.delete("/sponsor-campaigns/{campaign_id}", tags=["sponsor-campaigns"])
async def delete_sponsor_campaign(campaign_id: uuid.UUID, auth: Auth = Depends(Auth)):
    """Delete a sponsor campaign"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await SponsorCampaignService.delete_sponsor_campaign(campaign_id)
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error deleting sponsor campaign: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sponsors/import-excel", tags=["sponsor"])
async def import_sponsor_excel(
    file: UploadFile = File(..., description="Excel file to import"),
    auth: Auth = Depends(Auth)
):
    """Import sponsors from an Excel file"""
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await SponsorService.import_sponsor(file)
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except ExcelValidationError as e:
        return JSONResponse(
            status_code=400,
            content=failure_response(f"Excel validation error: {e}"),
        )
    except Exception as e:
        logger.error(f"Error importing sponsor from Excel: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sponsor-campaigns/import-excel", tags=["sponsor-campaigns"])
async def import_sponsor_campaigns(
    file: UploadFile = File(..., description="Excel file to import"),
    auth : Auth = Depends(Auth)
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await SponsorCampaignService.import_sponsor_campaign(file)
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except ExcelValidationError as e:
        return JSONResponse(
            status_code=400,
            content=failure_response(f"Excel validation error: {e}"),
        )
    except Exception as e:
        logger.error(f"Error importing sponsor campaigns from Excel: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sponsor-contracts/import-excel", tags=["sponsor-contracts"])
async def import_sponsor_contracts(
    file: UploadFile = File(..., description="Excel file to import"),
    auth: Auth = Depends(Auth)
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        result = await SponsorContractService.import_sponsor_contract(file)
        return success_response(result)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except ExcelValidationError as e:
        return JSONResponse(
            status_code=400,
            content=failure_response(f"Excel validation error: {e}"),
        )
    except Exception as e:
        logger.error(f"Error importing sponsor contracts from Excel: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))

@router.get("/template/{template_type}", tags=["template"])
async def download_template(
    template_type: str,
    auth: Auth = Depends(Auth)
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    template_mapping = {
        "sponsor_campaign": "Mẫu import chương trình tài trợ.xlsx",
        "sponsor_contract": "Mẫu import hợp đồng tài trợ.xlsx",
        "sponsor": "Mẫu import nhà tài trợ.xlsx"
    }

    if template_type not in template_mapping:
        return {"error": "File not found"}

    base_path = "src/operation/templates"

    file_path = f"{base_path}/{template_mapping[template_type]}"
    if os.path.exists(file_path):
        return FileResponse(
            path=file_path,
            filename=template_mapping[template_type],
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
)
    return {"error": "File not found"}


