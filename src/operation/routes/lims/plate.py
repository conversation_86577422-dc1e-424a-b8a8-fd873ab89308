import requests
from fastapi import API<PERSON><PERSON><PERSON>, FastAP<PERSON>, HTTPException, Query, Depends, Body, status
from typing import List
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.plate import (
  count_plates,
  get_plate,
  count_matched_plates,
  get_all_plates,
  create_plate,
  update_plate,
  delete_plate,
  get_plate_by_name,
  get_all_plates_w_tech,
  count_matched_plates_w_tech
)

from ...cruds.sample_mapping import get_all_chip_id_bind_to_plate_name

from ...services.plate import (
    get_next_plate_name_w_type
)

from ...models.models import Plate, db
from ...schemas.plate import *
from datetime import datetime, timedelta

from ...services.batch import (
    check_is_plate_added_to_batch
)

router = APIRouter()

# @router.post("/operation/lims/plate", status_code=HTTP_200_OK)
# @router.get("/operation/lims/plates", status_code=HTTP_200_OK)
# @router.get("/operation/lims/plate/{name}", status_code=HTTP_200_OK)
# @router.put("/operation/lims/plate/{name}", status_code=HTTP_200_OK)
# @router.delete("/operation/lims/plate/{name}", status_code=HTTP_200_OK)

router = APIRouter()

@router.post("/lims/plate/{type}", status_code=HTTP_200_OK)
async def admin_create(
  type: str,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  type = type.upper()
  response = await admin_get_next_plate_name(type=type, auth=auth)
  next_plate_name = response[0]['data']
  plate_body = AddPlate(name=next_plate_name,status='CREATED',type=type)


  data = plate_body.dict()
  
  if not data:
      err = "Empty Request Body"
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)

    
  logger.info(f"Create a plate code with data {data}")
  
  data, err = await create_plate(data)
  if err:
    http_code = HTTP_400_BAD_REQUEST
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)
  else:
    return success_response(data), None

#DONE
@router.get("/lims/plates/{type}", status_code=HTTP_200_OK)
async def admin_get_plates_w_tech(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  name: Optional[str] = Query(None),
  type: Optional[str] = Query(None),
  status: Optional[str] = Query(None),
  is_not_filled_into_chips: Optional[bool] = False,
  order_by: Optional[str] = 'name',
  auth=Depends(Auth),
):
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)


  current_date = get_current_date_time_utc_7()
  status = status.split(',')
  logger.info(f"FILTER with status: {status}")

  logger.info(f"Filter plates info")
  size = page_size
  offset = (page_number - 1) * page_size

  total = await count_matched_plates_w_tech()
  total = total if total else 0
  if offset > 0 and offset >= total:
      err = "Offset number is too large."
      http_code = HTTP_422_UNPROCESSABLE_ENTITY
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
  try:
      results, total_match = await get_all_plates_w_tech(
          offset=offset,
          size=size,
          name=name,
          technology=type.upper(),
          status= status if status else ['CREATED','PREPARING'],
          is_not_filled_into_chips=is_not_filled_into_chips,
          order_by=order_by
      )

      if not total_match:
          total_match = total

      # res_data = [res.to_dict() for res in results]
      field_names = [field for field in Plate.__dict__.keys() if '_' != field[0]]
      res_data = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in results]
      
      res = {
          "data": res_data,
          'pagination': {
              'page_size': page_size,
              'page_number': page_number,
              'total': total_match
          },
          "detail": []
      }
      return res
  except Exception as err:
      http_code = HTTP_500_INTERNAL_SERVER_ERROR
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/lims/plates", status_code=HTTP_200_OK)
async def admin_get_plates(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  name: Optional[str] = Query(None),
  status: Optional[str] = Query(None),
  order_by: Optional[str] = 'name',
  auth=Depends(Auth),
):
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)


  current_date = get_current_date_time_utc_7()
  status = status.split(',')
  logger.info(f"FILTER with status: {status}")

  logger.info(f"Filter plates info")
  size = page_size
  offset = (page_number - 1) * page_size

  total = await count_matched_plates()
  total = total if total else 0
  if offset > 0 and offset >= total:
      err = "Offset number is too large."
      http_code = HTTP_422_UNPROCESSABLE_ENTITY
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
  try:
      results, total_match = await get_all_plates(
          offset=offset,
          size=size,
          name=name,
          status= status if status else ['CREATED','PREPARING'],
          order_by=order_by
      )

      if not total_match:
          total_match = total

      res_data = [res.to_dict() for res in results]
      # field_names = [field for field in Plate.__dict__.keys() if '_' != field[0]]
      # res_data = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in results]
      
      res = {
          "data": res_data,
          'pagination': {
              'page_size': page_size,
              'page_number': page_number,
              'total': total_match
          },
          "detail": []
      }
      return res
  except Exception as err:
      http_code = HTTP_500_INTERNAL_SERVER_ERROR
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/lims/plate/{type}/{name}", status_code=HTTP_200_OK)
async def admin_get_plate_info_by_name(
  type: str,
  name: str,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  plate = await get_plate_by_name(type.upper(),name)

  if plate:
    res_plate = plate.to_dict()
    return success_response(res_plate)
  else:
    err = f"Plate with name {name} can not be found"
    http_code = HTTP_404_NOT_FOUND
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)



# DONE
@router.get("/lims/plates/next_plate_name/{type}", status_code=HTTP_200_OK)
async def admin_get_next_plate_name(
    type: str,
    auth=Depends(Auth)
):
  
  _, err = await auth.get_token_claims()
  if err:
      errs = failure_response(err['err_msg'])
      raise HTTPException(status_code=err['err_code'], detail=errs)
  
  next_plate_name, err = await get_next_plate_name_w_type(type)
  if err:
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
  
  return success_response(next_plate_name),None
            



#DONE
@router.put("/lims/plate/{type}/{name}", status_code=HTTP_200_OK)
async def admin_update_plate_info_by_name(
  type: str,
  name: str,
  body: UpdatePlate,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  data = body.dict()
  data = {k: v for k, v in data.items() if v is not None}
  if not data:
      err = "No changed field to update"
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)


  try:
    current_time = get_current_date_time_utc_7()
    new_plate_info = {
      **data,
      'updated_at': current_time
    }

    plate = await get_plate_by_name(type.upper(),name)

    if not plate:
        err = f"Plate with name {name} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    err = await update_plate(plate,new_plate_info)
    if err:
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
    else:
      updated_plate_info = await get_plate_by_name(type.upper(),name)
      return success_response(updated_plate_info.to_dict())
  except Exception as e:
    err = str(e)
    http_code = HTTP_400_BAD_REQUEST
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.delete("/lims/plate/{type}/{name}", status_code=HTTP_200_OK)
async def admin_delete_plate_by_name(
  type: str,
  name: str,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  logger.info(f"Delete a plate with name {name}")   
  plate = await get_plate_by_name(type.upper(),name)
  if not plate:
      err = f"Plate with name {name} can not be found"
      http_code = HTTP_404_NOT_FOUND
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)

  current_time = get_current_date_time_utc_7()
  data = {
    'updated_at': current_time,
    'deleted_at': current_time,
  }

  plate, err = await delete_plate(plate,data)
  if err:
    http_code = HTTP_404_NOT_FOUND
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)
  else:
    updated_plate_info = await get_plate(type.upper(),plate.name)
    return success_response(updated_plate_info.to_dict())

@router.get("/lims/plate/added_to_batch/{type}/{plate_name}")
async def check_plate_is_added_to_back(
    type: Optional[str] = Query(None),
    plate_name: Optional[str] = Query(None),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        is_added_to_batch,batch_number = await check_is_plate_added_to_batch(type, plate_name)
        res = {
            "is_added_to_batch": is_added_to_batch,
            "batch_number": batch_number 
        }
        return success_response(res)
        
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, tags=["lims_plate"])