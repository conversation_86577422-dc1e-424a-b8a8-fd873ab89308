import requests
from fastapi import APIRout<PERSON>, FastAP<PERSON>, HTTPException, Query, Depends, Body, File, UploadFile
from typing import List
from starlette.status import (
		HTTP_200_OK,
		HTTP_400_BAD_REQUEST,
		HTTP_403_FORBIDDEN,
		HTTP_404_NOT_FOUND,
		HTTP_408_REQUEST_TIMEOUT,
		HTTP_409_CONFLICT,
		HTTP_422_UNPROCESSABLE_ENTITY,
		HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
		convert_current_utc_to_tz,
		convert_str_to_datetime,
		convert_str_to_iso_datetime,
		success_response,
		failure_response,
		get_current_date_time,
		validate_email,
		get_no_required_chips
)

from ...cruds.sample_mapping import (
	count_matched_sample_mappings,
	get_all_sample_mappings,
	existed_sample_mapping_dna_extraction_id,
	create_sample_mapping,
	delete_sample_mapping,
	delete_many_sample_mappings,
	delete_sample_mappings_w_plate_name,
	create_many_sample_mappings,
	create_many_sample_mappings_w_plate_name,
	create_many_sample_mappings_w_plate_name_n_pc_sample_idx,
	get_all_chip_id_bind_to_plate_name,
	delete_sample_mappings_w_plate_name_n_chips
)

from ...cruds.chip import (
	create_chips
)

from ...services.sample_mapping import (
    transfer_samples_to_chips,
    check_valid_lids_w_pc,
    check_valid_sample_mapping_bodies_w_pc_sample,
    check_valid_sample_mapping_bodies_w_pc_sample_v3
)

from ...services.plate import (
    check_plate_passed_to_run
)

from ...models.models import SampleMapping, db
from ...schemas.sample_mapping import *
from ...schemas.plate import *
from datetime import datetime, timedelta
from .chip import *
from .plate import *
import csv

router = APIRouter()

# /lims/sample_mappings/{technology}
@router.put("/lims/sample_mappings/plate/{technology}/{plate_name}/pc", status_code=HTTP_200_OK)
async def admin_update_samples_to_plate(
	technology: str,
	plate_name: str,
	sample_mapping_bodies: list = [],
	auth=Depends(Auth),
) -> dict:
	"""
	1, Only allowed "CREATED', "PREPARING" chip status to process
	"""
    
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	is_passed_to_run, plate_status = await check_plate_passed_to_run(technology, plate_name)
	if is_passed_to_run:
		err = str(f"NOT ALLOWED TO UPDATE A {plate_status} PLATE {plate_name}.")
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


	if sample_mapping_bodies:
		pc_data_index, data_arrs = await check_valid_sample_mapping_bodies_w_pc_sample_v3(sample_mapping_bodies, plate_name)

		# Remove all plate
		if plate_name:
			try:
				# NOT REMOVE CHIP from chip table
				await delete_sample_mappings_w_plate_name(technology,plate_name)
				# REMOVE CHIPS
				# _, _ = await admin_remove_samples_from_plate_name(technology, plate_name, auth)
			except Exception as e:
				logger.info(e)
			
			try:
				data = await create_many_sample_mappings_w_plate_name_n_pc_sample_idx( technology, data_arrs, pc_data_index, plate_name)
				if data:
					body = UpdatePlate(status="PREPARING")
					_ = await admin_update_plate_info_by_name(type=technology,name=plate_name,body=body,auth=auth)
				# _ = await admin_update_chip_info_by_chip_id(chip_id=chip_id,body=body,auth=auth)
				return success_response(data), None
			except ValueError as e:
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(str(e))
				raise HTTPException(status_code=http_code, detail=errs)
		else:
			err = str("Empty plate_name in Request Body.")
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

	else:
		try:
			await delete_sample_mappings_w_plate_name(technology,plate_name)
			return success_response(sample_mapping_bodies), None
		except Exception as e:
			logger.info(e)
		# err = str("Empty Request Body.")
		# http_code = HTTP_400_BAD_REQUEST
		# errs = failure_response(err)
		# raise HTTPException(status_code=http_code, detail=errs)

# /lims/sample_mappings/{technology}
@router.put("/lims/sample_mappings/plate/{technology}/{plate_name}", status_code=HTTP_200_OK)
async def admin_update_samples_to_plate(
	technology: str,
	plate_name: str,
	sample_mapping_bodies: list = [],
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	is_passed_to_run, plate_status = await check_plate_passed_to_run(technology, plate_name)
	if is_passed_to_run:
		err = str(f"NOT ALLOWED TO UPDATE A {plate_status} PLATE {plate_name}.")
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


	if sample_mapping_bodies:
		data_arrs = []
		dna_extraction_ids = []
		for sample_mapping_body in sample_mapping_bodies:
			sample_mapping_obj = AddSampleMappingWNameReq(**sample_mapping_body)
			if sample_mapping_obj.dna_extraction_id in dna_extraction_ids:
				err = str(f"DNA EXTRACTION with ID {sample_mapping_obj.dna_extraction_id } is duplicated in payload.")
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(err)
				raise HTTPException(status_code=http_code, detail=errs)
			else:
				dna_extraction_ids.append(sample_mapping_obj.dna_extraction_id)
				data = sample_mapping_obj.dict()
				data_arrs.append(data)

		# Remove all plate
		if plate_name:
			try:
				# NOT REMOVE CHIP from chip table
				await delete_sample_mappings_w_plate_name(technology,plate_name)
				# REMOVE CHIPS
				# _, _ = await admin_remove_samples_from_plate_name(technology, plate_name, auth)
			except Exception as e:
				logger.info(e)
			
			try:
				data = await create_many_sample_mappings_w_plate_name(technology,data_arrs,plate_name)
				if data:
					body = UpdatePlate(status="PREPARING")
					_ = await admin_update_plate_info_by_name(type=technology,name=plate_name,body=body,auth=auth)
				# _ = await admin_update_chip_info_by_chip_id(chip_id=chip_id,body=body,auth=auth)
				return success_response(data), None
			except ValueError as e:
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(str(e))
				raise HTTPException(status_code=http_code, detail=errs)
		else:
			err = str("Empty plate_name in Request Body.")
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

	else:
		try:
			await delete_sample_mappings_w_plate_name(technology,plate_name)
			return success_response(sample_mapping_bodies), None
		except Exception as e:
			logger.info(e)
		# err = str("Empty Request Body.")
		# http_code = HTTP_400_BAD_REQUEST
		# errs = failure_response(err)
		# raise HTTPException(status_code=http_code, detail=errs)
	
# /lims/sample_mappings/{technology}
@router.post("/lims/sample_mapping/{technology}", status_code=HTTP_200_OK)
async def admin_create(
	technology: str,
	sample_mapping_body: AddSampleMappingWNameReq,
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	data = sample_mapping_body.dict()
	
	if not data:
			err = "Empty Request Body"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

		
	logger.info(f"Create a chip code with data {data}")
	
	data, err = await create_sample_mapping(technology,data)
	if err:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)
	else:
		body = UpdatePlate(status="PREPARING")
		_ = await admin_update_plate_info_by_name(type=technology,name=data['plate_name'],body=body,auth=auth)
		# _ = await admin_update_chip_info_by_chip_id(chip_id=data['chip_id'],body=body,auth=auth)
		return success_response(data), None

# /lims/sample_mappings/plate/{technology}
@router.post("/lims/sample_mappings/plate/{technology}", status_code=HTTP_200_OK)
async def admin_add_samples_to_plate(
	technology: str,
	sample_mapping_bodies: list = [],
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	plate_name = None
	if sample_mapping_bodies:
		data_arrs = []
		for sample_mapping_body in sample_mapping_bodies:
			sample_mapping_obj = AddSampleMappingWNameReq(**sample_mapping_body)			
			if plate_name is None:
				plate_name=sample_mapping_obj.plate_name
			data = sample_mapping_obj.dict()
			data_arrs.append(data)

		# Remove all plate
		if plate_name:

			is_passed_to_run, plate_status = await check_plate_passed_to_run(technology, plate_name)
			if is_passed_to_run:
				err = str(f"NOT ALLOWED TO UPDATE A {plate_status} PLATE {plate_name}.")
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(err)
				raise HTTPException(status_code=http_code, detail=errs)
			
			try:
				await delete_sample_mappings_w_plate_name(technology,plate_name)
			except Exception as e:
				logger.info(e)
			
			try:
				data = await create_many_sample_mappings_w_plate_name(technology,data_arrs,plate_name)
				if data:
					body = UpdatePlate(status="PREPARING")
					_ = await admin_update_plate_info_by_name(type=technology,name=plate_name,body=body,auth=auth)
				# _ = await admin_update_chip_info_by_chip_id(chip_id=chip_id,body=body,auth=auth)
				return success_response(data), None
			except ValueError as e:
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(str(e))
				raise HTTPException(status_code=http_code, detail=errs)

		else:
			err = str("Empty plate_name in Request Body.")
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

	else:
		err = str("Empty Request Body.")
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

# TRANSFER SAMPLES FROM PLATE TO CHIP
# ADD TO CHIP --> RETURN No CHIPS required
# TRANSFER TO CHIP --> SPLIT SAMPLE_MAPPINGS into appropriate CHIPs

# /lims/sample_mappings/chip/{technology}
@router.get("/lims/sample_mappings/plate/add/{technology}/{plate_name}", status_code=HTTP_200_OK)
async def admin_plate_get_no_chips(
	technology: str,
	plate_name: str,
    chip_type: Optional[str] = Query(None),
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	
	total = await count_matched_sample_mappings(
			type=technology,
			plate_name=plate_name
	)

	if total > 0:
		CHIP_CAP = config["CHIP"][technology.upper()][chip_type.upper()]["CAPACITY"] if chip_type else config["CHIP"][technology.upper()]["CAPACITY"]
		logger.info(f"Capacity {CHIP_CAP} of selected chip {chip_type.upper()}")
		number_of_chips = get_no_required_chips(total, int(CHIP_CAP))
		res = {
				"data": {"number_of_chips": number_of_chips},
				'pagination': {},
				"detail": []
		}
		return res
	else:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response("Empty Plate !!")
		raise HTTPException(status_code=http_code, detail=errs)

	

# /lims/sample_mappings/chip/{technology}
@router.post("/lims/sample_mappings/plate/fill/{technology}", status_code=HTTP_200_OK)
async def admin_transfer_samples_to_chips(
	technology: str,
	req_body: FillChipWSamples,
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	is_passed_to_run, plate_status = await check_plate_passed_to_run(technology, req_body.plate_name)
	if is_passed_to_run:
		err = str(f"NOT ALLOWED TO UPDATE A {plate_status} PLATE {req_body.plate_name}.")
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)
	
	try:

		# res = await admin_plate_get_no_chips(technology,req_body.plate_name,auth)
		# GET All samples of given PLATE_NAME
		sample_mappings, total_match = await get_all_sample_mappings(
				type=technology,
				plate_name=req_body.plate_name,
				order_by="created_at"
				)
		
		field_names = [field for field in SampleMapping.__dict__.keys() if '_' != field[0]]
		field_names.append('type')
		field_names.append('plate_name')
		sample_mappings = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in sample_mappings[::-1]] # ASC of created_at order
		# print('len of update: ',len(sample_mappings))
		if total_match > 0:
			CHIP_CAP = config["CHIP"][technology.upper()][req_body.type.upper()]["CAPACITY"] if req_body.type else config["CHIP"][technology.upper()]["CAPACITY"]
			number_of_chips = get_no_required_chips(total_match, int(CHIP_CAP))
			# CHECK # of CHIPs from BE
			if number_of_chips > 0 and len(req_body.chip_ids) == number_of_chips:
				# CREATE CHIPs given CHIP_IDs
				# print('Hello??!!')
				res_chips, err = await create_chips(req_body, technology)
				if err:
					http_code = HTTP_400_BAD_REQUEST
					errs = failure_response(err)
					raise HTTPException(status_code=http_code, detail=errs)
				# FILL CHIP with SAMPLEs
				results, err = await transfer_samples_to_chips(technology, res_chips, sample_mappings, req_body.type.upper())
			
				if err:
					http_code = HTTP_400_BAD_REQUEST
					errs = failure_response(err)
					raise HTTPException(status_code=http_code, detail=errs)
		
				res = {
						"data": results,
						'pagination': {},
						"detail": []
				}
				return res
			else:
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response("Not enough chip_ids !!")
				raise HTTPException(status_code=http_code, detail=errs)
		else:
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response("Empty Plate !!")
			raise HTTPException(status_code=http_code, detail=errs)

	except Exception as err:
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/lims/sample_mappings", status_code=HTTP_200_OK)
async def admin_get_sample_mappings(
	page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
	page_number: int = Query(1, ge=1),
	chip_id: Optional[str] = Query(None),
	position: Optional[str] = Query(None),
	dna_extraction_id: Optional[int]=Query(None),
	technology: Optional[str] = Query(None),
	plate_name: Optional[str] = Query(None),
	well_position: Optional[str] = Query(None),
	auth=Depends(Auth),
):
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	logger.info(f"Filter dna_extractions info")
	size = page_size
	offset = (page_number - 1) * page_size
	total = await count_matched_sample_mappings()
	if offset > 0 and offset >= total:
		err = "Offset number is too large."
		http_code = HTTP_422_UNPROCESSABLE_ENTITY
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

	try:
		results, total_match = await get_all_sample_mappings(
				offset=offset,
				size=size,
				chip_id=chip_id,
				position=position,
				plate_name=plate_name,
				type=technology,
				well_position=well_position,
				dna_extraction_id=dna_extraction_id,
				order_by="created_at"
		)

		if not total_match:
			total_match = total

		# res_data = [res.to_dict() for res in results]
		# return plate_name for each record
		# res_data = [dict(res.to_dict(), **{'plate_name':plate_name}) for res in results]

		field_names = [field for field in SampleMapping.__dict__.keys() if '_' != field[0]]
		field_names.append('type')
		field_names.append('plate_name')
		res_data = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in results]
		
		res = {
				"data": res_data,
				'pagination': {
						'page_size': page_size,
						'page_number': page_number,
						'total': total_match
				},
				"detail": []
		}
		return res

	except Exception as err:
			http_code = HTTP_500_INTERNAL_SERVER_ERROR
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/lims/sample_mappings/{technology}/{plate_name}/chips", status_code=HTTP_200_OK)
async def admin_get_binded_chips_via_plate_name(
	technology: Optional[str] = Query(None),
	plate_name: Optional[str] = Query(None),
	auth=Depends(Auth),
):
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	logger.info(f"GET CHIPs binded to plate name {plate_name} info")
	# total = await count_matched_all_chip_id_bind_to_plate_name()
	total = None

	try:
		results = await get_all_chip_id_bind_to_plate_name(
				type=technology,
				plate_name=plate_name
		)

		# res_data = [res.to_dict() for res in results]
		# return plate_name for each record
		# res_data = [dict(res.to_dict(), **{'plate_name':plate_name}) for res in results]

		field_names = ['chip_id','type','plate_name']
		res_data = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in results]
		
		res = {
				"data": res_data,
				'pagination': {
						'total': len(res_data)
				},
				"detail": []
		}
		return res

	except Exception as err:
			http_code = HTTP_500_INTERNAL_SERVER_ERROR
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)


# /lims/sample_mapping/{technology}
@router.delete("/lims/sample_mappings/{technology}", status_code=HTTP_200_OK)
async def admin_delete_sample_mappings(
	technology: str,
	existed_sample_mappings: list = [],
	auth=Depends(Auth),
) -> dict:

	logger.info(f"Delete many sample_mappings")
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	try:
		results, _ = await delete_many_sample_mappings(existed_sample_mappings)
		return success_response(results), None	
	except ValueError as err:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)



@router.delete("/lims/sample_mappings/plate/{technology}/{plate_name}", status_code=HTTP_200_OK)
async def admin_remove_samples_from_plate_name(
	technology: str,
	plate_name: str,
	# existed_sample_mappings: list = [],
	auth=Depends(Auth),
) -> dict:

	logger.info(f"Delete many sample_mappings")
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	
	chip_ids = None
	temp_results = await get_all_chip_id_bind_to_plate_name(
			type=technology.upper(),
			plate_name=plate_name
	)

	if temp_results:
		field_names = ['chip_id','type','plate_name']
		temp_data = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in temp_results]
		chip_ids = [entry['chip_id'] for entry in temp_data]

	existed_sample_mappings, _ = await get_all_sample_mappings(
			plate_name=plate_name,
			type=technology,
			order_by="created_at"
	)

	# existed_sample_mappings = [res.to_dict() for res in results]
		
	body = UpdatePlate(status="CREATED")
	_ = await admin_update_plate_info_by_name(type=technology,name=plate_name,body=body,auth=auth)


	try:
		if chip_ids:
			# logger.info(f"Deleting following CHIPs: ", chip_ids)
			await delete_sample_mappings_w_plate_name_n_chips(technology,plate_name,chip_ids)
		else:
			await delete_sample_mappings_w_plate_name(technology,plate_name)
		return success_response(existed_sample_mappings), None	
	except ValueError as err:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err) 
		raise HTTPException(status_code=http_code, detail=errs)

# Native Pre-signed URL
@router.post("/lims/sample_mappings/qc_status/{technology}", status_code=HTTP_200_OK)
async def admin_create(
  technology: str,
  upload_file: UploadFile = File(...),
  auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	

	try:
		with open(upload_file.filename, "wb") as f:
			f.write(await upload_file.read())
	except Exception as err:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err) 
		raise HTTPException(status_code=http_code, detail=errs)

	try:
		logger.info(f"File Name {upload_file.filename}")

		with open(upload_file.filename, 'r') as csvfile:
			# Create a CSV reader object
			csvreader = csv.DictReader(csvfile, delimiter=',')

			# Skip the header row
			# next(csvreader)

			# Loop through each row in the CSV file
			for row in csvreader:
				# Print the current row
				print(row['PROBE_PASS'],row['Call rate'],row['Gender PASS'],row['Call rate PASS'])
		pass
	except ValueError as err:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err) 
		raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
		app.include_router(router, tags=["lims_sample_mapping"])