import requests
from fastapi import status, <PERSON><PERSON><PERSON><PERSON>, HTTPException, FastAPI, Query, Depends, Body
from typing import Optional, List
from ...config import config
from ... import logger
from ...auth import Auth
from ...dependencies.pagination import PaginationParams

from ...schemas.card import (
    CardDownloadReqs,
    CreateCardReq,
    CardUpdateReq,
    IssueCardsReq,
    CreateCardProductReq,
    CardProductUpdateReq,
    GenerateCardsReq
)

from ...utils.utils import (
    success_response,
    failure_response,
    get_current_date_time,
    paging_a_list_of_rows,
    renew_obj_key,
    get_obj_key_from_s3_uri,
    parse_user_information,
    get_report_pdf_link
)
from ...cruds.cards import *
from ...services.cards import *

router = APIRouter()

@router.post("/")
async def create_card(
    create_card_req: CreateCardReq,
) -> dict:
    try:
        res = await create_card_ctl(
            card_req=create_card_req
        )
        return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/generate")
async def generate_cards(
    generate_cards_req: GenerateCardsReq,
) -> dict:
    try:
        generating_reqs = []
        for req in generate_cards_req.card_requests:
            valid_res, generating_req = await check_card_metadata_before_generating(
                card_id=req.card_id
                )
            if not valid_res.get('result'):
                raise ValueError(valid_res.get('msg'))
            else:
                generating_reqs.append(generating_req)
        
        # SEND SQS MESSAGE TO QUEUE
        res = await generate_cards_ctl(
            generating_reqs=generating_reqs,
            card_product_id=generate_cards_req.card_product_id,
            card_product_name=generate_cards_req.card_product_name
        )
        # print("generating_reqs: ", generating_reqs)
        return success_response(generating_reqs)
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/issue")
async def issue_cards(
    issue_cards_req: IssueCardsReq,
    auth=Depends(Auth)
) -> dict:
    
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        cards = await issue_cards_ctl(
            issue_cards_req=issue_cards_req
        )

        for card in cards:

            await generate_qr_code_for_card_ctl(
                card=card,
                bearer_token_credential=auth.bearer_token.credentials
            )

        return success_response(cards)
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/check")
async def check_before_issuing_cards(
    issue_cards_req: IssueCardsReq,
    auth=Depends(Auth)
) -> dict:
    claim, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        card_product = await get_card_product_infor(issue_cards_req.card_product_id)

        for req in issue_cards_req.card_requests:
            req.is_valid=True
            # CHECK PHONE_NUMBER
            if not req.phone_number:
                req.note = f"KIT {req.barcode} does not have the phone number!"
                req.is_valid=False
                continue
            else:
                try:
                    # VALIDATE PHONE NUMBER
                    user_info = await parse_user_information(phone=req.phone_number)
                    req.user_id = user_info['data']["uuid"]

                except Exception as err:
                    req.note = f"KIT {req.barcode} with PHONE {req.phone_number} is not validated!"
                    req.is_valid=False
                    continue
                else:


                    if req.current_status != 'COMPLETED':
                        req.note = f" PDF Report of KIT {req.barcode} is in the status of {req.current_status} and not ready to generate QR code!"
                        req.is_valid=False
                        continue
                    
                    
                    # SKIP GET pdf link due to 2-3s RESPONSE TIME per request!
                    # try:
                    #     _ = await get_report_pdf_link(barcode=req.barcode,technology='pgx')
                    # except Exception as err:
                    #     logger.error(str(err))
                    #     req.note = f" Even though KIT {req.barcode} is {req.current_status}. PDF Report is not ready to generate QR code!"
                    #     req.is_valid=False
           
                

            # GENERATE QR_CODE
        # print("issue_cards_req 2: ",issue_cards_req)
        # RE-ISSUE
        res = await check_version_n_get_pre_issued_cards(
            issue_cards_req=issue_cards_req,
            card_product_name=card_product.get('card_product_name'),
            card_product_id=issue_cards_req.card_product_id
        )
        data = {
            "card_requests":res,
            "card_product_id": issue_cards_req.card_product_id,
            "included_qr": False
        }
        return success_response(data)
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/download")
async def download_issued_card(
    card_download_reqs: CardDownloadReqs,
    auth=Depends(Auth)
) -> dict:
    try:
        data = []
        for download_req in card_download_reqs.download_reqs:
            front_s3_object_key = get_obj_key_from_s3_uri(download_req.card_front_s3_object_uri)
            back_s3_object_key = get_obj_key_from_s3_uri(download_req.card_back_s3_object_uri)
            card_font_presigned_link, card_back_presigned_link = get_card_printing_link(
                front_s3_object_key = front_s3_object_key,
                back_s3_object_key = back_s3_object_key
            )
            # RE-ISSUE
            entry = {
                "card_id": download_req.card_id,
                "card_font_presigned_link": card_font_presigned_link,
                "card_back_presigned_link": card_back_presigned_link
            }
            data.append(entry)
        return success_response(data)
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    
@router.put("/{id}")
async def update_card(
    id: str,
    update_req: CardUpdateReq,
    auth=Depends(Auth)
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        res = await update_card_ctl(id=id,update_data=update_req)
        return success_response(res)

    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/link")
async def update_card_presigned_links(
    card_download_reqs: CardDownloadReqs,
    auth=Depends(Auth)
) -> dict:
    claim, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    pass
    try:
        data = []
        for download_req in card_download_reqs.download_reqs:
            entry = await renew_card_presigned_links(download_req.card_id, download_req.card_front_s3_object_uri, download_req.card_back_s3_object_uri)
            data.append(entry)
            pass
        return success_response(data)


    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#get sample management
@router.get("/all")
async def get_cards(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    card_id: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    report_ver: Optional[str] = Query(None),
    db_ver: Optional[str] = Query(None),
    card_status: Optional[str] = Query(None),
    card_product_id: Optional[str] = Query(None),
    order_option: Optional[str] = 'desc',
    order_by: Optional[str]=Query(None),
    is_unlocked_only: Optional[bool]=Query(None),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_filtered_card_list(
            card_id=card_id,
            barcode=barcode,
            full_name=full_name,
            report_ver=report_ver,
            db_ver=db_ver,
            card_status=card_status,
            card_product_id=card_product_id,
            offset=offset,
            size=size,
            is_unlocked_only=is_unlocked_only,
            order_by=order_by if order_by else "c.created_at",
            order_option=order_option
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)




@router.delete("/{id}")
async def delete_card(
    id: str,
    auth=Depends(Auth)
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    # print("Helo 20 4")
    try:
        await delete_card_ctl(id=id)
        return success_response(f"successfully delete card with ID: {id}")
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# CARD PRODUCT MANAGEMENT

@router.post("/product")
async def create_card_product(
    create_card_product_req: CreateCardProductReq,
) -> dict:
    try:
        res = await create_card_product_ctl(
            card_product_req=create_card_product_req
        )
        return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.put("/product/{id}")
async def update_card_product(
    id: str,
    update_req: CardProductUpdateReq,
    auth=Depends(Auth)
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        res = await update_card_product_ctl(id=id,update_data=update_req)
        return success_response(res)

    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#get sample management
@router.get("/product/all")
async def get_card_products(
    card_type: Optional[str] = Query(None),
    card_product_id: Optional[str] = Query(None),
    card_product_name: Optional[str] = Query(None),
    policy: Optional[str] = Query(None),
    order_option: Optional[str] = 'desc',
    order_by: Optional[str]=Query(None),
    is_active_only: Optional[bool]=Query(None),
    pagination: PaginationParams = Depends(),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        data, total = await get_filtered_card_product_list(
            card_type=card_type,
            card_product_id=card_product_id,
            card_product_name=card_product_name,
            policy=policy,
            size=pagination.size,
            offset=pagination.offset,
            order_by=order_by if order_by else "cp.created_at",
            order_option=order_option,
            is_active_only=is_active_only
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': pagination.page_size,
                'page_number': pagination.page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, prefix="/lims/card", tags=["lims"])