import requests
from fastapi import status, <PERSON><PERSON><PERSON><PERSON>, HTTPException, FastAPI, Query, Depends, Body
from typing import Optional, List
from ...config import config
from ... import logger
from ...auth import Auth
from ...schemas.sample_management import (
    CreateBatchReq,
    BatchUpdateReq,
    AddChipToBatchReq,
    TriggerPipelineReq
)

from ...cruds.plate import *
from ...cruds.batch import *
from ...cruds.batch_mapping import *
from ...cruds.aws import *

from ...utils.utils import (
    success_response,
    failure_response,
    get_current_date_time,
    paging_a_list_of_rows
)


# from ...services.plate import (
#     get_next_batch_number,
#     check_plate_existed_to_create_batch
# )


from ...services.batch import (
    get_today_date_service,
    check_is_plate_added_to_batch,
    get_recent_execution_by_batch
)

from ...schemas.batch import *
from ...schemas.batch_mapping import *
from ...schemas.pipeline_input import *
from .plate import *


router = APIRouter()

 
@router.post("/")
async def create_batch_ctl(
    create_batch_req: CreateBatchReq,
) -> dict:
    try:
        res = await create_batch(
            batch_req=create_batch_req
        )
        return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

# # [DISCARD]
# @router.post("/{type}")
# async def create_batch_ctl(
#     type: str,
#     create_batch_req: CreateBatchReq,
#     auth=Depends(Auth)
# ) -> dict:
#     _, err = await auth.get_token_claims()
#     if err:
#         errs = failure_response(err['err_msg'])
#         raise HTTPException(status_code=err['err_code'], detail=errs)
    
#     try:
#         plate_res, batch_res = None, None
#         type = type.upper()
#         next_batch_number_str = await get_next_batch_number_w_type(type)
#         next_batch_number = int(next_batch_number_str)
#         if not await check_plate_existed_to_create_batch(type,next_batch_number):
#             errs = failure_response(f"PLATE {next_batch_number} is not existed!! Create Plate to proceed!")
#             http_code = status.HTTP_400_BAD_REQUEST
#             raise HTTPException(status_code=http_code, detail=errs)
#         else:
#             plate_res = await get_plate_by_name(type,next_batch_number_str)
#             logger.info(f"Add PLATE {plate_res.name} into BATCH {next_batch_number}")
#             create_batch_req.number = next_batch_number
#             create_batch_req.type = type
        
#         if await existed_batch_number(type,next_batch_number):
#             errs = failure_response(f"Batch {next_batch_number} is existed!!")
#             http_code = status.HTTP_400_BAD_REQUEST
#             raise HTTPException(status_code=http_code, detail=errs)
#         else:
#             batch_res = await create_batch(
#                 batch_req=create_batch_req
#             )
#             logger.info(f"Create BATCH w/ number {batch_res['number']}")

#         # BATCH_PLATE mapping
#         logger.info(f"Create BATCH_MAPPING w/ plate_id {plate_res.id} and batch_id {batch_res['id']}")
#         batch_mapping_req = AddBatchMapping(plate_id= str(plate_res.id), batch_id = str(batch_res['id']))
#         batch_mapping_res = await create_batch_mapping(batch_mapping_req)

#         return success_response(batch_mapping_res)
#     except requests.exceptions.ConnectionError as err:
#         http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.Timeout as err:
#         http_code = status.HTTP_408_REQUEST_TIMEOUT
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.RequestException as err:
#         http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except ValueError as err:
#         http_code = status.HTTP_400_BAD_REQUEST
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)


@router.post("/{type}")
async def create_batch_ctl(
    type: str,
    body_req: CreateBatchMappingReqs,
    auth=Depends(Auth)
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
        
    try:
        results = await create_batch_w_plate_names(type.upper(),body_req.number,body_req)
        return success_response(results)
    
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



@router.put("/{type}/{number}")
async def update_batch_ctl(
    type: str,
    number: int,
    update_req: BatchUpdate,
    auth=Depends(Auth)
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        res = await update_batch_w_number(type=type,number=number,update_data=update_req)
        return success_response(res)

    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    pass

#get sample management
@router.get("/all")
async def get_batch_samples(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    number: Optional[int] = Query(None),
    name: Optional[str] = Query(None),
    note: Optional[str] = Query(None),
    type: Optional[str] = Query(None),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        res, total = await get_all_batches(
            number=number,
            name=name,
            note=note,
            type=type,
            offset=offset,
            size=size,
            order_by="created_at",
        )

        data = [r.to_dict() for r in res]
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
#get sample management
@router.get("/{type}")
async def get_batch_samples(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    number: Optional[int] = Query(None),
    name: Optional[str] = Query(None),
    note: Optional[str] = Query(None),
    type: Optional[str] = Query(None),
    order_option: Optional[str] = 'desc',
    order_by: Optional[str]=None,
    export: Optional[bool] = Query(False),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_filtered_batch_list(
            number=number,
            name=name,
            note=note,
            type=type.upper(),
            offset=offset,
            size=size,
            plate_status_filter=config['AVAILABLE_CHIP_STATUS']+config['EXPORT_PLATE_STATUS'] if export else config['AVAILABLE_CHIP_STATUS'],
            order_by=order_by if order_by else "created_at",
            order_option=order_option
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

 
#get sample management
@router.get("/next_batch_number/{type}")
async def get_next_batch(
    type: Optional[str] = Query(None),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        next_batch_number = await get_next_batch_number_w_type(type)
        return success_response(next_batch_number)
        
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#get sample management
@router.get("/{type}/{number}/check_raw_data_uploaded")
async def check_raw_data_uploaded(
    type: Optional[str] = Query(None),
    number: Optional[int] = Query(None),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    try:
        missing_files = await check_uploaded_samples_w_tech_n_batch_v3(technology=type.upper(), number=number)
        return success_response(missing_files)
        
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/binded_plate_names/{type}/{number}")
async def get_binded_plate_names_w_batch_number(
    type: Optional[str] = Query(None),
    number: Optional[int] = Query(None),
    auth=Depends(Auth)
    # chip_status_list:
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    

    # CHECK BATCH EXIST
    if not await existed_batch_number(type,number):
        err = f"BATCH with number {number} NOT existed!!"
        # raise ValueError(err)
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    
    try:
        plate_names = await get_binded_plate_names_w_tech_n_batch_v3(technology=type.upper(), number=number)
        return success_response(plate_names)
        
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.delete("/{type}/{number}")
async def delete_batch_ctl(
    type: str,
    number: int,
    auth=Depends(Auth)
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    # print("Helo 20 4")
    try:
        await delete_batch(type=type,number=number)
        return success_response(f"successfully delete batch with number: {number}")
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/wetlab/{type}/{number}")
async def trigger_wetlab_pipeline(
  type: str,
  number: int,
  body: UpdateBatchMappingWetlabDate,
  auth=Depends(Auth),
) -> dict:
    try:
        # CHECK BATCH EXIST
        if not await existed_batch_number(type,number):
            err = f"BATCH with number {number} NOT existed!!"
            # raise ValueError(err)
            errs = failure_response(err)
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
        
        results = await update_batch_mapping_wetlab_date_v3(type,number,body)
        return success_response(results)
        
        pass
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.put("/raw/{type}/{number}")
async def update_raw_data_uploaded_date(
  type: str,
  number: int,
  body: UpdateBatchMappingRawDataUploadDate,
  auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        
        res = await update_batch_mapping_raw_data_uploaded_date_v3(type,number,body)
        return success_response(res)

    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    # except Exception as err:
    #     http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    #     errs = failure_response(err)
    #     raise HTTPException(status_code=http_code, detail=errs)

@router.put("/internal/raw/{type}/{number}")
async def update_raw_data_uploaded_date(
  type: str,
  number: int,
  body: UpdateBatchMappingRawDataUploadDate
) -> dict:
    try:
        
        res = await update_batch_mapping_raw_data_uploaded_date_v3(type,number,body)
        return success_response(res)

    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/drylab/pcr/gene_kits")
async def get_pcr_gene_kits(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    auth=Depends(Auth)
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    
    gene_kits_list = config.get('PCR_GENE_KITS')
    data = paging_a_list_of_rows(gene_kits_list, page_size, page_number)
    res = {
        "data": data,
        'pagination': {
            'page_size': page_size,
            'page_number': page_number,
            'total': len(gene_kits_list),
        },
        "detail": []
    }

    return res


@router.get("/drylab/{type}/{number}")
async def get_drylab_pipeline_execution_status(
    type: str,
    number: int,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        
        # CHECK RECENT EXECUTION STATUS V3 compatible
        results = await get_recent_execution_by_batch(type,number)

        # RUNNING
        # FAILED
        # COMPLETED
        return success_response(results)
        
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)  

#trigger analysis for all chips in batch
#should define logic to trigger analysis for all PCR/MICROARRAY chips in the batch
@router.post("/drylab/{type}/{number}")
async def trigger_drylab_pipeline(
    type: str,
    number: int,
    payload: Optional[TriggerPipelineReq] = TriggerPipelineReq(gene_kit=None),
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        
        results = await trigger_drylab_pipeline_w_batch_number_v3(type,number,payload)
        return success_response(results)
        
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    # except Exception as err:
    #     http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    #     errs = failure_response(err)
    #     raise HTTPException(status_code=http_code, detail=errs)

@router.put("/drylab/{type}/{number}/date")
async def manually_update_drylab_date(
    type: str,
    number: int,
    body: UpdateBatchMappingDrylabDate,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        
        # GET ALL PLATEs binded to batch number
        results = await manually_update_drylab_date_w_body_v3(type, number, body)

        return success_response(results)
        
    except requests.exceptions.ConnectionError as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = status.HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = status.HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    # except Exception as err:
    #     http_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    #     errs = failure_response(err)
    #     raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, prefix="/lims/batch", tags=["lims"])