import requests
from fastapi import APIRouter, FastAP<PERSON>, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
	HTTP_200_OK,
	HTTP_400_BAD_REQUEST,
	HTTP_403_FORBIDDEN,
	HTTP_404_NOT_FOUND,
	HTTP_408_REQUEST_TIMEOUT,
	HTTP_409_CONFLICT,
	HTTP_422_UNPROCESSABLE_ENTITY,
	HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
	convert_current_utc_to_tz,
	convert_str_to_datetime,
	convert_str_to_iso_datetime,
	success_response,
	failure_response,
	get_current_date_time_utc_7,
	validate_email
)

from ...cruds.dna_box import *
from ...cruds.dna_box_mappings import *

from ...services.dna_box import *

from ...models.models import Chip, db
from ...schemas.dna_box import *
from ...schemas.dna_box_mappings import *
from datetime import datetime, timedelta

router = APIRouter()

# @router.post("/operation/lims/dna_box", status_code=HTTP_200_OK)
# @router.get("/operation/lims/dna_boxes", status_code=HTTP_200_OK)
# @router.get("/operation/lims/dna_box/{id}", status_code=HTTP_200_OK)
# @router.put("/operation/lims/dna_box/{id}", status_code=HTTP_200_OK)
# @router.delete("/operation/lims/dna_box/{id}", status_code=HTTP_200_OK)

router = APIRouter()

@router.post("/lims/dna_box/{technology}", status_code=HTTP_200_OK)
async def admin_create_dna_box(
  technology: str,
  dna_box_body: AddDnaBox,
  auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	
	supported_techs = get_dna_box_supported_tech()
	if technology.upper() in supported_techs:

		data = dna_box_body.dict()
		if not data:
			err = "Empty Request Body"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

		logger.info(f"Create a dna_box code with data {data}")
		res, _ = await admin_get_next_dna_box_id(technology,auth)
		valid_next_id = res["data"]
		if await existed_id(data.get('id')):
			err = f"Error: DnaBox with ID {data.get('id')} is already existed"
			errs = failure_response(err)
			http_code = HTTP_400_BAD_REQUEST
			raise HTTPException(status_code=http_code, detail=errs)
		
		if valid_next_id != dna_box_body.id:
			err = f"Invalid input DNA_BOX ID: {dna_box_body.id} . Next valid DNA_BOX ID: {valid_next_id}"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

		data, err = await create_dna_box(data)
		if err:
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		else:
			return success_response(data), None
	
	else:
		err = f"{technology} technology is not supported"
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.get("/lims/dna_boxes", status_code=HTTP_200_OK)
async def admin_get_dna_boxes(
	page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
	page_number: int = Query(1, ge=1),
	id: Optional[str] = Query(None),
	capacity: Optional[int] = Query(None),
	technology: Optional[str] = Query(None),
	auth=Depends(Auth),
):
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	print('technology: ',technology)
	if not technology:
		http_code = HTTP_422_UNPROCESSABLE_ENTITY
		errs = failure_response("Need to input technology!!")
		raise HTTPException(status_code=http_code, detail=errs)

	current_prefix = get_dna_box_tech_prefix(technology)

	if not id:
		id = current_prefix
	else:
		if current_prefix not in id:
			http_code = HTTP_422_UNPROCESSABLE_ENTITY
			errs = failure_response(f"Input technology {technology} not compatible with {id}")
			raise HTTPException(status_code=http_code, detail=errs)


	logger.info(f"Filter dna_boxes info")
	size = page_size
	offset = (page_number - 1) * page_size
	total = await count_matched_dna_boxes()
	total = total if total else 0
	if offset > 0 and offset >= total:
		err = "Offset number is too large."
		http_code = HTTP_422_UNPROCESSABLE_ENTITY
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)
	

	
	try:
		results, total_match = await get_all_dna_boxes(
		offset=offset,
		size=size,
		id=id,
		capacity=capacity,
		order_by="created_at"
		)

		if not total_match:
			total_match = total

		res_data = [res.to_dict() for res in results]

		res = {
				"data": res_data,
				'pagination': {
				'page_size': page_size,
				'page_number': page_number,
				'total': total_match
				},
				"detail": []
			}
		
		return res

	except Exception as err:
	
		http_code = HTTP_500_INTERNAL_SERVER_ERROR
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/lims/dna_boxes/{technology}", status_code=HTTP_200_OK)
async def admin_get_dna_boxes(
	technology: str,
	page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
	page_number: int = Query(1, ge=1),
	needed_capacity: Optional[int] = 0,
	auth=Depends(Auth),
):
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	if not technology:
		http_code = HTTP_422_UNPROCESSABLE_ENTITY
		errs = failure_response("Need to input technology!!")
		raise HTTPException(status_code=http_code, detail=errs)

	current_prefix = get_dna_box_tech_prefix(technology)
	dna_box_current_cap = get_dna_box_capacity(technology)

	logger.info(f"Filter dna_boxes info with needed_capacity: {needed_capacity}")
	size = page_size
	offset = (page_number - 1) * page_size
	total = await count_matched_dna_boxes()
	total = total if total else 0
	if offset > 0 and offset >= total:
		err = "Offset number is too large."
		http_code = HTTP_422_UNPROCESSABLE_ENTITY
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)
	
	estimated_capacity = dna_box_current_cap - needed_capacity

	
	try:
		results, total_match = await get_all_dna_boxes(
		offset=offset,
		size=size,
		id=current_prefix,
		capacity=estimated_capacity,
		order_by="created_at"
		)

		if not total_match:
			total_match = total

		res_data = [res.to_dict() for res in results]

		res = {
				"data": res_data,
				'pagination': {
				'page_size': page_size,
				'page_number': page_number,
				'total': total_match
				},
				"detail": []
			}
		return res

	except Exception as err:
		http_code = HTTP_500_INTERNAL_SERVER_ERROR
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/lims/dna_box/{id}", status_code=HTTP_200_OK)
async def admin_get_dna_box_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()

	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	dna_box = await get_dna_box(id)

	if dna_box:
		res_dna_box = dna_box.to_dict()
		return success_response(res_dna_box)
	else:
		err = f"dna_box_id {id} can not be found"
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

#DONE
#DNA_BOX_MAPPINGS
@router.put("/lims/dna_box/{technology}/{id}", status_code=HTTP_200_OK)
async def admin_update_dna_box_info_by_id(
  technology: str,
  id: str,
  req_body: FillDnaBoxReq,
  auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	
	supported_techs = get_dna_box_supported_tech()
	if technology.upper() in supported_techs:

		expected_box_prefix = get_dna_box_tech_prefix(technology)
		if expected_box_prefix not in id:
			err = f"Technology {technology} and DNA_BOX id {id} not match!"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

		dna_box = await get_dna_box(id)

		if not dna_box:
			err = f"DNA BOX with id {id} can not be found"
			http_code = HTTP_404_NOT_FOUND
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		
		needed_capacity = len(req_body.lids)

		if not req_body.lids or needed_capacity == 0:
			err = f"Passing empty list of Lab Sample Ids LIDs"
			http_code = HTTP_404_NOT_FOUND
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		
		current_capacity = dna_box.capacity

		if current_capacity >= 100:
			err = f"DNA_BOX w/ id {id} is already full!"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		
		if current_capacity + needed_capacity > 100:
			err = f"DNA_BOX w/ id {id} is not enough space for {str(needed_capacity)} since total would be {str(current_capacity + needed_capacity)} > current capacity {current_capacity}!"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
				
	else:
		err = f"{technology} technology is not supported"
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


	# CREATE dna_box_mappings w/ appropriate position
	# dna_box_id, current_capacity, lids
	try:
		dna_box_mappings_res = await put_samples_into_dna_box_w_curr_capacity(technology, id, current_capacity, req_body.lids)

		current_time = get_current_date_time_utc_7()
		updated_data = {
			"capacity": current_capacity + needed_capacity
		}
		new_dna_box_info = {
		**updated_data,
		'updated_at': current_time
		}


		err = await update_dna_box(dna_box,new_dna_box_info)
		if err:
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		else:
			updated_dna_box_info = await get_dna_box(id)
			return success_response({**updated_dna_box_info.to_dict(),'added_samples': dna_box_mappings_res})
		
	except Exception as e:
		err = str(e)
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


# DONE
@router.get("/lims/dna_box/next_id/{technology}", status_code=HTTP_200_OK)
async def admin_get_next_dna_box_id(
	technology: str,
	auth=Depends(Auth)
):
  
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	if technology:
		supported_techs = get_dna_box_supported_tech()
		if technology.upper() in supported_techs:
			
			current_prefix = get_dna_box_tech_prefix(technology) #P

			logger.info(f"DNA BOX prefix {current_prefix}")
			total_match = await count_total_dna_boxes(
				id=current_prefix
				)

			logger.info(f"DNA BOX total w/ prefix {current_prefix}: {total_match}")
			
			next_pcr_id, err = get_next_dna_box_id(current_prefix,total_match)
			if err:
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(err)
				raise HTTPException(status_code=http_code, detail=errs)
			
			return success_response(next_pcr_id),None
				
		else:
			err = f"{technology} technology is not supported"
			http_code = HTTP_404_NOT_FOUND
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
	else:
		err = f"No {technology} typed in"
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.delete("/lims/dna_box/{technology}/{id}", status_code=HTTP_200_OK)
async def admin_delete_dna_box_by_id(
  technology: str,
  id: str,
  auth=Depends(Auth),
) -> dict:

	logger.info(f"Delete an dna_box with id {id}")
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	
	supported_techs = get_dna_box_supported_tech()
	if technology.upper() in supported_techs:
		dna_box = await get_dna_box(id)
		if not dna_box:
			err = f"Dna_box_id {id} can not be found"
			http_code = HTTP_404_NOT_FOUND
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		try:
			await delete_dna_box(dna_box)
			return success_response(dna_box.to_dict())
		except Exception as e:
			err = str(e)
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
				
	else:
		err = f"{technology} technology is not supported"
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

def init_app(app: FastAPI):
	app.include_router(router, tags=["lims_dna_box"])