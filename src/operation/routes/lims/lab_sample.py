import requests
from fastapi import APIRout<PERSON>, FastAP<PERSON>, HTTPException, Query, Depends, Body
from typing import List, Literal, Optional
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger

from ...schemas.lab_sample import *
from ...cruds.lab_sample import *
from ...utils.utils import (
    get_current_date_time_utc_7,
    failure_response,
    format_date,
    filter_personal_infor_from_sample_list,
    DEFAULT_DATE_NO_TIME_ZONE
)
from ...services.lab_sample import (
    create_lab_sample_srv, 
    get_next_lid,
    get_lab_check_sample_list,
    get_lab_checked_sample_list,
    get_lab_checked_sample_list_v3,
    update_lab_check_status,
    update_lab_check_status_by_batch,
    get_mapping_from_product_list,
    map_samples_w_tech_by_product_code,
    get_lab_check_sample_list_v3,
    update_lab_check_status_v3,
    get_lab_check_passed_sample_list_v3
)

router = APIRouter()

@router.post("")
async def create_sample(
    creation_req: LabSampleCreationReq,
) -> dict:
    try:
        res = await create_lab_sample_srv(creation_req=creation_req)
        return {
            "data": res.to_dict()
        }
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

# @router.post("/lab_sample/batch")
# async def batch_create(
#     batch_creation_req: LabSampleBatchCreationReq,
# ) -> dict:
#     try:
#         try:
#             for creation_req in batch_creation_req.data:
#                 if creation_req.lab_receipt_date is None:
#                     creation_req.lab_receipt_date = get_current_date_time_utc_7()
#                 lab_sample = LabSample(
#                     lid=creation_req.lid,
#                     barcode=creation_req.barcode,
#                     lab_receipt_date=creation_req.lab_receipt_date,
#                     note=creation_req.note,
#                     created_at = get_current_date_time_utc_7(),
#                     updated_at = get_current_date_time_utc_7()
#                 )
#                 await create_lab_sample(lab_sample)

@router.put("/{id}")
async def update_sample(
    id: str,
    update_req: LabSampleUpdateReq,
) -> dict:
    try:
        update_req.lid = id
        res = await update_lab_sample(update_data=update_req)
        return {
            "data": res.to_dict()
        }
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.delete("/{id}")
async def delete_sample(
    id: str,
) -> dict:
    try:
        res = await delete_lab_sample(id=id)
        return {
            "data": res,
        }
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.put("/positive_control/pool")
async def update_positive_control_pool(
	lab_sample_bodies: list = []
) -> dict:
    try:
        # res = await add_positive_control_sampple(id=id)
        # return {
        #     "data": res,
        # }
        if lab_sample_bodies:
            res = await add_samples_into_positive_control_pool(lab_sample_bodies)
            return {
                "data": res,
            }
        else:
            res = await reset_positive_control_pool()
            return {
                "data": res,
            }
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
@router.put("/positive_control/pool/reset")
async def reset_positive_control_samples(
) -> dict:
    try:
        res = await reset_positive_control_pool()
        return {
            "data": res,
        }
        
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



@router.put("/positive_control/add/{id}")
async def add_positive_control(
    id: str,
) -> dict:
    try:
        res = await add_positive_control_sampple(id=id)
        return {
            "data": res,
        }
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/positive_control/remove/{id}")
async def remove_positive_control(
    id: str,
) -> dict:
    try:
        res = await remove_positive_control_sampple(id=id)
        return {
            "data": res,
        }
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



@router.get("/lid/current")
async def get_current_lid() -> dict:
    try:
        lid = await get_next_lid()
        return {
            "data": lid,
        }
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
@router.get("/samples/lab_check")
async def get_current_lid_v3(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
) -> dict:
    """
    CS: MISSING SAMPLE
    LIMS: GATE 1 -> 2 -> 3 -> 4
    """
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_lab_check_sample_list_v3(
            offset=offset,
            size=size,
            order_by="updated_at",
            barcode=barcode
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/samples/lab_check/passed")
async def get_current_lid_v3(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
) -> dict:
    """
    CS: MISSING SAMPLE
    LIMS: GATE 1 -> 2 -> 3 -> 4
    """
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_lab_check_passed_sample_list_v3(
            offset=offset,
            size=size,
            order_by="updated_at",
            barcode=barcode
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/checked/v2")
async def get_checked_sample_list(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    lid: Optional[str] = Query(None),
    name: Optional[str] = Query(None),
    product_code: Optional[str] = Query(None),
    account_name: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    lab_check_start_date: Optional[str] = Query(None),
    lab_check_end_date: Optional[str] = Query(None),
    lab_receipt_start_date: Optional[str] = Query(None),
    lab_receipt_end_date: Optional[str] = Query(None),
    release_start_date: Optional[str]= Query(None),
    release_end_date: Optional[str]= Query(None),
    batch_barcode: Optional[int]= Query(None),
    plate_name: Optional[int]= Query(None),
    dna_qc_status: Optional[str] = 'ALL',
    technology: Optional[str]= Query(None),
    positive_control: Optional[bool]= Query(None),
    is_all: Optional[bool] = Query(False)

) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        logger.info(f"FILTER WITH DNA_QC_STATUS: {dna_qc_status}")
        data, total = await get_lab_checked_sample_list(
            offset=offset,
            size=size,
            order_by="updated_time",
            barcode=barcode,
            phone_number=phone_number,
            lid=lid,
            name=name,
            product_code=product_code,
            account_name=account_name,
            current_status=current_status,
            lab_check_start_date=lab_check_start_date,
            lab_check_end_date=lab_check_end_date,
            lab_receipt_start_date=lab_receipt_start_date,
            lab_receipt_end_date=lab_receipt_end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            batch_barcode=batch_barcode,
            plate_name=plate_name,
            dna_qc_status=dna_qc_status,
            technology=technology,
            positive_control=positive_control,
            is_all=is_all
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/status")
async def update_lab_sample_status_v3(
    update_lab_sample_req: LabSampleUpdateStatus,
) -> dict:
    try:
        await update_lab_check_status_v3(
            barcode=update_lab_sample_req.barcode,
            status=update_lab_sample_req.status,
            note=update_lab_sample_req.note,
        )
        res = {
            "data": "lab sample status updated",
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
@router.post("/status/list")
async def update_lab_sample_status(
    update_lab_sample_req: LabSampleBatchUpdateStatus,
) -> dict:
    try:
        await update_lab_check_status_by_batch(
            barcode_list=update_lab_sample_req.barcode_list,
            status=update_lab_sample_req.status,
        )
        res = {
            "data": "lab sample status updated",
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
@router.delete("/missing_sample/barcode/{barcode}")
async def delete_missing_sample_by_barcode(
    barcode: str,
) -> dict:
    try:
        # [CASE 3] MISSING_INFO --> HANDLE
        await handle_missing_sample(barcode=barcode)
        res = {
            "data": f"missing sample with barcode {barcode} handled",
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
@router.delete("/missing_sample/list")
async def delete_missing_sample_by_barcode_list(
    barcode_list: DeleteMissingSamplesReq,
) -> dict:
    try:
        # [CASE 3] MISSING_INFO --> HANDLE
        for barcode in barcode_list.barcode_list:
            await handle_missing_sample(barcode=barcode)
        res = {
            "data": f"List of missing samples {barcode_list.barcode_list} handled",
        }
        return res
        
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
@router.get("/missing_sample/v2")
async def get_all_missing_samples(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    lab_check_start_date: Optional[str] = Query(None),
    lab_check_end_date: Optional[str] = Query(None),
) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        if lab_check_start_date:
            lab_check_start_date_std = format_date(lab_check_start_date, DEFAULT_DATE_NO_TIME_ZONE)
        if lab_check_end_date:
            lab_check_end_date_std = format_date(lab_check_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
            if lab_check_end_date < lab_check_start_date:
                raise ValueError("end date cannot be after start date")
        
        data, total = await get_missing_samples(
            offset=offset,
            size=size,
            barcode=barcode,
            status=status,
            lab_check_start_date=lab_check_start_date_std if lab_check_start_date else None,
            lab_check_end_date=lab_check_end_date_std if lab_check_end_date else None,
        )

        product_code_mapping = get_mapping_from_product_list()
        data = map_samples_w_tech_by_product_code(data=data, mapping=product_code_mapping)
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/handled_missing_sample")
async def handle_all_missing_samples(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    previous_status: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    correction_start_date: Optional[str] = Query(None),
    correction_end_date: Optional[str] = Query(None),
) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        if correction_start_date:
            correction_start_date_std = format_date(correction_start_date, DEFAULT_DATE_NO_TIME_ZONE)
        if correction_end_date:
            correction_end_date_std = format_date(correction_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
            if correction_end_date < correction_start_date:
                raise ValueError("end date cannot be after start date")
        data, total = await get_handled_missing_samples(
            offset=offset,
            size=size,
            barcode=barcode,
            previous_status=previous_status,
            current_status=current_status,
            correction_start_date=correction_start_date_std if correction_start_date else None,
            correction_end_date=correction_end_date_std if correction_end_date else None
        )

        product_code_mapping = get_mapping_from_product_list()
        data = map_samples_w_tech_by_product_code(data=data, mapping=product_code_mapping)
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    
@router.get("/missing_sample")
async def get_all_missing_samples_v3(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    lab_check_start_date: Optional[str] = Query(None),
    lab_check_end_date: Optional[str] = Query(None),
) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        if lab_check_start_date:
            lab_check_start_date_std = format_date(lab_check_start_date, DEFAULT_DATE_NO_TIME_ZONE)
        if lab_check_end_date:
            lab_check_end_date_std = format_date(lab_check_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
            if lab_check_end_date < lab_check_start_date:
                raise ValueError("end date cannot be after start date")
        
        data, total = await get_missing_samples_v3(
            offset=offset,
            size=size,
            barcode=barcode,
            samplecode=samplecode,
            status=status,
            lab_check_start_date=lab_check_start_date_std if lab_check_start_date else None,
            lab_check_end_date=lab_check_end_date_std if lab_check_end_date else None,
        )

        product_code_mapping = get_mapping_from_product_list()
        data = map_samples_w_tech_by_product_code(data=data, mapping=product_code_mapping)
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    
@router.get("/checked")
async def get_checked_sample_list_v3(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    lid: Optional[str] = Query(None),
    name: Optional[str] = Query(None),
    product_code: Optional[str] = Query(None),
    account_name: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    lab_check_start_date: Optional[str] = Query(None),
    lab_check_end_date: Optional[str] = Query(None),
    lab_receipt_start_date: Optional[str] = Query(None),
    lab_receipt_end_date: Optional[str] = Query(None),
    release_start_date: Optional[str]= Query(None),
    release_end_date: Optional[str]= Query(None),
    batch_barcode: Optional[int]= Query(None),
    plate_name: Optional[int]= Query(None),
    dna_qc_status: Optional[str] = 'ALL',
    technology: Optional[str]= Query(None),
    positive_control: Optional[bool]= Query(None),
    is_all: Optional[bool] = Query(False)

) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        logger.info(f"FILTER WITH DNA_QC_STATUS: {dna_qc_status}")
        data, total = await get_lab_checked_sample_list_v3(
            offset=offset,
            size=size,
            order_by="updated_at",
            barcode=barcode,
            phone_number=phone_number,
            lid=lid,
            name=name,
            product_code=product_code,
            account_name=account_name,
            current_status=current_status,
            lab_check_start_date=lab_check_start_date,
            lab_check_end_date=lab_check_end_date,
            lab_receipt_start_date=lab_receipt_start_date,
            lab_receipt_end_date=lab_receipt_end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            batch_barcode=batch_barcode,
            plate_name=plate_name,
            dna_qc_status=dna_qc_status,
            technology=technology,
            positive_control=positive_control,
            is_all=is_all
        )
        data = filter_personal_infor_from_sample_list(data)
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, prefix="/lims/lab_sample", tags=["lims"])