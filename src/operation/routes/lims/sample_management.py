import requests

from fastapi import status, APIRouter, HTTPException, FastAPI, Query
from typing import Optional, List
from ...config import config
from ...schemas.sample_management import *
from ...services.sample_management import (
    get_available_sample_mappings,
    get_available_sample_mappings_v3,
    check_raw_data_microarray_chip,
    get_batch_stats
)
from ...services.sample_mapping import check_valid_lids_w_pc, check_valid_lids_w_pc_v3
from ...utils.utils import (
    failure_response
)

router = APIRouter()

@router.get("/available/v2")
async def get_all_available_sample_mappings(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    lid: Optional[str] = Query(None),
    dna_extraction_id: Optional[int] = Query(None),
    chip_id: Optional[str] = Query(None),
    plate_name: Optional[str] = Query(None),
    plate_name_null_only: Optional[bool] = False,
    dna_box: Optional[str] = Query(None),
    technology: Optional[str] = Query(None),
    note: Optional[str] = Query(None),
    dna_extraction_date_start: Optional[str] = Query(None),
    dna_extraction_date_end: Optional[str] = Query(None),
	agarose_gel: Optional[str] = Query(None),
	dna_qc_status: Optional[str] = 'PASS',
    pipeline_qc_status: Optional[str] = None,
    wetlab_date_start: Optional[str] = Query(None),
    wetlab_date_end: Optional[str] = Query(None),
    drylab_date_start: Optional[str] = Query(None),
    drylab_date_end: Optional[str] = Query(None),
    plate_added_date_start: Optional[str] = Query(None),
    plate_added_date_end: Optional[str] = Query(None),
    order_by: Optional[str] = Query(None),
    order_option: Optional[str] = 'desc',
    is_added_to_batch: Optional[bool] = Query(False),
    export: Optional[bool] = Query(False)
) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_available_sample_mappings(
            offset=offset,
            size=size,
            order_by=order_by if order_by else "sm.updated_at",
            order_option=order_option,
            barcode=barcode,
            lid=lid,
            dna_extraction_id=dna_extraction_id,
            chip_id=chip_id,
            plate_name=plate_name,
            plate_name_null_only=plate_name_null_only,
            dna_box=dna_box,
            technology=technology,
            note=note,
            dna_extraction_date_start=dna_extraction_date_start,
            dna_extraction_date_end=dna_extraction_date_end,
            agarose_gel=agarose_gel,
            dna_qc_status=dna_qc_status,
            pipeline_qc_status=pipeline_qc_status,
            wetlab_date_start=wetlab_date_start,
            wetlab_date_end=wetlab_date_end,
            drylab_date_start=drylab_date_start,
            drylab_date_end=drylab_date_end,
            plate_added_date_start=plate_added_date_start,
            plate_added_date_end=plate_added_date_end,
            is_added_to_batch=is_added_to_batch,
            export=export
        )

        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        if plate_name:
            lids = [ entry['lid'] for entry in data]
            _, _, last_pc_lid = await check_valid_lids_w_pc(lids=lids)
            pc_detail = {
                "pc_sample":last_pc_lid
            }
            res['detail']=pc_detail
        return res
    except requests.exceptions.ConnectionError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except requests.exceptions.Timeout as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=errs)
    except requests.exceptions.RequestException as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except ValueError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=errs)

@router.get("/check-microarray-raw-data/{chip_id}")
async def check_microarray_samples_on_chip(
    chip_id: str
) -> dict:
    try:
        missing_samples = await check_raw_data_microarray_chip(chip_id=chip_id)
        if len(missing_samples) > 0:
            is_missing = True
        else:
            is_missing = False
        res = {
            "data": {
                "is_missing": is_missing,
                "missing_samples": missing_samples,
            },
            "detail": []
        }
        return res
    except requests.exceptions.ConnectionError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except requests.exceptions.Timeout as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=errs)
    except requests.exceptions.RequestException as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except ValueError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=errs)

@router.get("/batch_stats")
async def get_all_batch_stats(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    batch_number: Optional[str]=None,
    product_name: Optional[str]=None,
    product_code: Optional[str]=None,
    wetlab_date_start: Optional[str] = Query(None),
    wetlab_date_end: Optional[str] = Query(None),
    order_by: Optional[str] = Query(None),
    order_option: Optional[str] = 'desc',
    ) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_batch_stats(
            offset=offset,
            size=size,
            order_by=order_by if order_by else "b.number",
            order_option=order_option,
            batch_number=batch_number,
            product_name=product_name,
            product_code=product_code,
            wetlab_date_start=wetlab_date_start,
            wetlab_date_end=wetlab_date_end,
        )
    
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }

        return res
    except requests.exceptions.ConnectionError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except requests.exceptions.Timeout as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=errs)
    except requests.exceptions.RequestException as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except ValueError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=errs)

@router.get("/available")
async def get_all_available_sample_mappings_v3(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    lid: Optional[str] = Query(None),
    dna_extraction_id: Optional[int] = Query(None),
    chip_id: Optional[str] = Query(None),
    plate_name: Optional[str] = Query(None),
    plate_name_null_only: Optional[bool] = False,
    dna_box: Optional[str] = Query(None),
    technology: Optional[str] = Query(None),
    note: Optional[str] = Query(None),
    dna_extraction_date_start: Optional[str] = Query(None),
    dna_extraction_date_end: Optional[str] = Query(None),
	agarose_gel: Optional[str] = Query(None),
	dna_qc_status: Optional[str] = 'PASS',
    pipeline_qc_status: Optional[str] = None,
    wetlab_date_start: Optional[str] = Query(None),
    wetlab_date_end: Optional[str] = Query(None),
    drylab_date_start: Optional[str] = Query(None),
    drylab_date_end: Optional[str] = Query(None),
    plate_added_date_start: Optional[str] = Query(None),
    plate_added_date_end: Optional[str] = Query(None),
    order_by: Optional[str] = Query(None),
    order_option: Optional[str] = 'desc',
    is_added_to_batch: Optional[bool] = Query(False),
    is_encoded: Optional[bool] = Query(False),
    export: Optional[bool] = Query(False)
) -> dict:
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        data, total = await get_available_sample_mappings_v3(
            offset=offset,
            size=size,
            order_by=order_by if order_by else "smp.updated_at",
            order_option=order_option,
            barcode=barcode,
            lid=lid,
            dna_extraction_id=dna_extraction_id,
            chip_id=chip_id,
            plate_name=plate_name,
            plate_name_null_only=plate_name_null_only,
            dna_box=dna_box,
            technology=technology,
            note=note,
            dna_extraction_date_start=dna_extraction_date_start,
            dna_extraction_date_end=dna_extraction_date_end,
            agarose_gel=agarose_gel,
            dna_qc_status=dna_qc_status,
            pipeline_qc_status=pipeline_qc_status,
            wetlab_date_start=wetlab_date_start,
            wetlab_date_end=wetlab_date_end,
            drylab_date_start=drylab_date_start,
            drylab_date_end=drylab_date_end,
            plate_added_date_start=plate_added_date_start,
            plate_added_date_end=plate_added_date_end,
            is_added_to_batch=is_added_to_batch,
            is_encoded=is_encoded,
            export=export
        )

        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total,
            },
            "detail": []
        }
        if plate_name:
            lids = [ entry['lid'] for entry in data]
            _, _, last_pc_lid = await check_valid_lids_w_pc_v3(lids=lids)
            pc_detail = {
                "pc_sample":last_pc_lid
            }
            res['detail']=pc_detail
        return res
    except requests.exceptions.ConnectionError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except requests.exceptions.Timeout as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT, detail=errs)
    except requests.exceptions.RequestException as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)
    except ValueError as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        errs = failure_response(err)
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=errs)
    
def init_app(app: FastAPI):
    app.include_router(router, prefix="/lims/sample_management", tags=["lims"])