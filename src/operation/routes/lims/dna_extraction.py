import requests
from fastapi import APIRouter, FastAP<PERSON>, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
		HTTP_200_OK,
		HTTP_400_BAD_REQUEST,
		HTTP_403_FORBIDDEN,
		HTTP_404_NOT_FOUND,
		HTTP_408_REQUEST_TIMEOUT,
		HTTP_409_CONFLICT,
		HTTP_422_UNPROCESSABLE_ENTITY,
		HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
		convert_current_utc_to_tz,
		convert_str_to_datetime,
		convert_str_to_iso_datetime,
		success_response,
		failure_response,
		lid_failure_response,
		get_current_date_time_utc_7,
		validate_email
)

from ...cruds.dna_extraction import (
	existed_lid,
	existed_dna_extractions_w_lid,
	count_dna_extractions,
	get_dna_extraction,
	count_matched_dna_extractions,
	get_all_dna_extractions,
	count_matched_dna_extractions_join_lab_sample,
	get_all_dna_extractions_join_lab_sample,
	create_many_dna_extractions,
	create_dna_extraction,
	update_dna_extraction,
	delete_dna_extraction
)

from ...cruds.sample_mapping import *

from ...services.dna_extraction import (
		standardize_dna_extraction_service,
		convert_start_dna_extraction_date,
		convert_end_dna_extraction_date
)

from ...models.models import DnaExtraction, db
from ...schemas.dna_extraction import *
from datetime import datetime, timedelta
from .sample_mapping import *

router = APIRouter()

# @router.post("/operation/lims/dna_extraction", status_code=HTTP_200_OK)
# @router.get("/operation/lims/dna_extractions", status_code=HTTP_200_OK)
# @router.get("/operation/lims/dna_extraction/{id}", status_code=HTTP_200_OK)
# @router.put("/operation/lims/dna_extraction/{id}", status_code=HTTP_200_OK)
# @router.delete("/operation/lims/dna_extraction/{id}", status_code=HTTP_200_OK)

router = APIRouter()

@router.post("/lims/dna_extractions/check", status_code=HTTP_200_OK)
async def admin_check_lid_before_import(
	dna_extraction_bodies: list = [],
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	if dna_extraction_bodies:
		lid_arrs = set()
		confirm_list = set()
		for dna_extraction_body in dna_extraction_bodies:
			# CHECK DUPLICATED LID in EXCEL file
			if dna_extraction_body['lid'] in lid_arrs:
				# err = f"LID {dna_extraction_body['lid']} is duplicated in EXCEL file !!"
				err = f"Upload thất bại. Có 2 dòng thông tin trùng LID:  {dna_extraction_body['lid']}"
				http_code = HTTP_422_UNPROCESSABLE_ENTITY
				errs = lid_failure_response(err)
				raise HTTPException(status_code=http_code, detail=errs)
			else:
				lid_arrs.add(dna_extraction_body['lid'])
			
			# CHECK DNA_EXTRACTION w/ LID ALREADY EXISTED
			is_existed = await existed_dna_extractions_w_lid(dna_extraction_body['lid']) 
			if is_existed:
				sample_mappings = await get_all_sample_mappings_and_plate_status(lid=dna_extraction_body['lid'])
				# NOT ADDED TO PLATE

				if len(sample_mappings) == 0:
					err = f"Upload thất bại. Hệ thống đã có LID: {dna_extraction_body['lid']} và chưa xử lý xong."
					http_code = HTTP_422_UNPROCESSABLE_ENTITY
					errs = lid_failure_response(err)
					raise HTTPException(status_code=http_code, detail=errs)
					pass
				# ADDED TO PLATE
				else:
					field_names = ['dna_extraction_id','type','plate_name','status']
					sample_mappings = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in sample_mappings[::-1]] # ASC of created_at order

					for sm in sample_mappings:
						# ADDED TO PLATE BUT NOT RUN
						# NEW DNA_EXTRACTION (ON RE-RUN: len > 0) NOT ADDED TO PLATE
						if sm['status'] == 'PREPARING' or sm['plate_name'] is None:
							err = f"Upload thất bại. Hệ thống đã có LID: {dna_extraction_body['lid']} và chưa xử lý xong."
							http_code = HTTP_422_UNPROCESSABLE_ENTITY
							# failed_dna_extraction_bodies = dna_extraction_bodies
							errs = lid_failure_response(err)
							raise HTTPException(status_code=http_code, detail=errs)
						# ADDED TO PLACE, RUNNING OR FINISHED
						else:
							confirm_list.add(dna_extraction_body['lid'])

		data = {
			"confirm_list": confirm_list
		}
		return success_response(data)
								
			# pass
	
	else:
		err = str("Empty Request Body.")
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

@router.post("/lims/dna_extractions", status_code=HTTP_200_OK)
async def admin_create_multiples(
  dna_extraction_bodies: list = [],
  auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	_, _ = await admin_check_lid_before_import(dna_extraction_bodies,auth)

	is_lab_samples_valid = True
	if dna_extraction_bodies:
		data_arrs = []
		for dna_extraction_body in dna_extraction_bodies:
			try:
				dna_extraction_obj	=	AddDnaExtraction(**dna_extraction_body)
				is_lab_sample_id_existed = await existed_lid(dna_extraction_obj.lid)
				if not is_lab_sample_id_existed:
					err = "Lab Samples ID not existed"
					http_code = HTTP_404_NOT_FOUND
					# failed_dna_extraction_bodies = dna_extraction_bodies
					errs = lid_failure_response(err)
					dna_extraction_body['lid_not_found']=True
					raise HTTPException(status_code=http_code, detail=errs)
				data = dna_extraction_obj.dict()
				formated_data = await standardize_dna_extraction_service(data)
				data_arrs.append(formated_data)
			except Exception as err:
				dna_extraction_body["is_valid"] = False
				is_lab_samples_valid = False
        
		if is_lab_samples_valid:
			try:
				data, err = await create_many_dna_extractions(data_arrs)
				if err:
					http_code = HTTP_400_BAD_REQUEST
					errs = failure_response(err)
					raise HTTPException(status_code=http_code, detail=errs)
        
				return success_response(data), None
			# CATCH DB INSERT ERROR - CONNECTION ERROR 
			except Exception as e:
				err = str(e)
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(err)
				raise HTTPException(status_code=http_code, detail=errs)
		else:
			err = "Missing required fields / Lab Sample ID NOT FOUND in lab_sample"
			http_code = HTTP_422_UNPROCESSABLE_ENTITY
			failed_dna_extraction_bodies = [row for row in dna_extraction_bodies if row['is_valid'] == False]
			# failed_dna_extraction_bodies = dna_extraction_bodies
			errs = lid_failure_response(err,data=failed_dna_extraction_bodies)
			raise HTTPException(status_code=http_code, detail=errs)
				
	else:
		err = str("Empty Request Body.")
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


@router.post("/lims/dna_extraction", status_code=HTTP_200_OK)
async def admin_create(
	dna_extraction_body: AddDnaExtraction,
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	data = dna_extraction_body.dict()
	is_lab_sample_id_existed = await existed_lid(dna_extraction_body.lid)
	if not is_lab_sample_id_existed:
		err = "Lab Samples ID not existed"
		http_code = HTTP_404_NOT_FOUND
		errs = lid_failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)
	
	if not data:
			err = "Empty Request Body"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
	try:
		data = await standardize_dna_extraction_service(data)
		 
		logger.info(f"Create a dna_extraction code with data {data}")
		
		data, err = await create_dna_extraction(data)
		if err:
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		else:
			return success_response(data), None
	except Exception as e:
		err = str(e)
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/lims/dna_extractions", status_code=HTTP_200_OK)
async def admin_get_dna_extractions(
	page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
	page_number: int = Query(1, ge=1),
	lid: Optional[str] = Query(None),
	start_dna_extraction_date: Optional[str]=Query(None),
	end_dna_extraction_date: Optional[str]=Query(None),
	agarose_gel: Optional[str] = Query(None),
	dna_qc_status: Optional[str] = Query(None),
	note: Optional[str] = Query(None),
	auth=Depends(Auth),
):
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)


	start_dna_extraction_date = await convert_start_dna_extraction_date(start_dna_extraction_date) if start_dna_extraction_date else await convert_start_dna_extraction_date('1970-01-01') 
	end_dna_extraction_date = await convert_end_dna_extraction_date(end_dna_extraction_date) if end_dna_extraction_date else await convert_end_dna_extraction_date(datetime.max.strftime('%Y-%m-%d'))
	current_date = get_current_date_time_utc_7()

	logger.info(f"Filter dna_extractions info")
	size = page_size
	offset = (page_number - 1) * page_size
	total = await count_matched_dna_extractions()
	if offset > 0 and offset >= total:
			err = "Offset number is too large."
			http_code = HTTP_422_UNPROCESSABLE_ENTITY
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
	try:
			# results, total_match = await get_all_dna_extractions(
			#     offset=offset,
			#     size=size,
			#     lid=lid,
			#     start_dna_extraction_date=start_dna_extraction_date,
			#     end_dna_extraction_date=end_dna_extraction_date,
			#     agarose_gel=agarose_gel,
			#     dna_qc_status=dna_qc_status,
			#     note=note.lower() if note else None,
			#     order_by="created_at"
			# )

			results, total_match = await get_all_dna_extractions(
					offset=offset,
					size=size,
					lid=lid,
					start_dna_extraction_date=start_dna_extraction_date,
					end_dna_extraction_date=end_dna_extraction_date,
					agarose_gel=agarose_gel,
					dna_qc_status=dna_qc_status,
					note=note.lower() if note else None,
					order_by="created_at"
			)

			if not total_match:
					total_match = total

			# field_names = [field for field in DnaExtraction.__dict__.keys() if '_' != field[0]]

			res_data = [res.to_dict() for res in results]
			# res_data = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in results]
			# print('Here res: \n',res_data)
			res = {
					"data": res_data,
					'pagination': {
							'page_size': page_size,
							'page_number': page_number,
							'total': total_match
					},
					"detail": []
			}
			return res
	except Exception as err:
			http_code = HTTP_500_INTERNAL_SERVER_ERROR
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/lims/dna_extractions_w_chip", status_code=HTTP_200_OK)
async def admin_get_dna_extractions(
	page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
	page_number: int = Query(1, ge=1),
	barcode: Optional[str] = Query(None),
	lid: Optional[str] = Query(None),
	chip_id: Optional[str] = Query(None),
	start_dna_extraction_date: Optional[str]=Query(None),
	end_dna_extraction_date: Optional[str]=Query(None),
	agarose_gel: Optional[str] = Query(None),
	dna_qc_status: Optional[str] = Query(None),
	technology: Optional[str] = Query(None),
	note: Optional[str] = Query(None),
	auth=Depends(Auth),
):
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)


	start_dna_extraction_date = await convert_start_dna_extraction_date(start_dna_extraction_date) if start_dna_extraction_date else await convert_start_dna_extraction_date('1970-01-01') 
	end_dna_extraction_date = await convert_end_dna_extraction_date(end_dna_extraction_date) if end_dna_extraction_date else await convert_end_dna_extraction_date(datetime.max.strftime('%Y-%m-%d'))
	current_date = get_current_date_time_utc_7()

	logger.info(f"Filter dna_extractions info")
	size = page_size
	offset = (page_number - 1) * page_size
	total = await count_matched_dna_extractions_join_lab_sample()
	if offset > 0 and offset >= total:
			err = "Offset number is too large."
			http_code = HTTP_422_UNPROCESSABLE_ENTITY
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
	try:
			# results, total_match = await get_all_dna_extractions(
			#     offset=offset,
			#     size=size,
			#     lid=lid,
			#     start_dna_extraction_date=start_dna_extraction_date,
			#     end_dna_extraction_date=end_dna_extraction_date,
			#     agarose_gel=agarose_gel,
			#     dna_qc_status=dna_qc_status,
			#     note=note.lower() if note else None,
			#     order_by="created_at"
			# )

			results, total_match = await get_all_dna_extractions_join_lab_sample(
					offset=offset,
					size=size,
					lid=lid,
					chip_id=chip_id,
					barcode=barcode,
					start_dna_extraction_date=start_dna_extraction_date,
					end_dna_extraction_date=end_dna_extraction_date,
					agarose_gel=agarose_gel,
					dna_qc_status=dna_qc_status,
					technology=technology,
					note=note.lower() if note else None,
					order_by="created_at"
			)

			if not total_match:
					total_match = total

			field_names = [field for field in DnaExtraction.__dict__.keys() if '_' != field[0]]
			field_names.append('barcode')
			field_names.append('technology')
			field_names.append('chip_id')

			# res_data = [res.to_dict() for res in results]
			res_data = [{field_names[idx]: res[idx] for idx in range(len(field_names))} for res in results]
			# print('Here res: \n',res_data)
			res = {
					"data": res_data,
					'pagination': {
							'page_size': page_size,
							'page_number': page_number,
							'total': total_match
					},
					"detail": []
			}
			return res
	except Exception as err:
			http_code = HTTP_500_INTERNAL_SERVER_ERROR
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/lims/dna_extraction/{id}", status_code=HTTP_200_OK)
async def admin_get_dna_extraction_info_by_id(
	id: int,
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	dna_extraction = await get_dna_extraction(id)
	if dna_extraction:
		res_dna_extraction = dna_extraction.to_dict()
		return success_response(res_dna_extraction)
	else:
		err = f"Kit with id {id} can not be found"
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.put("/lims/dna_extraction/{id}", status_code=HTTP_200_OK)
async def admin_update_dna_extraction_info_by_id(
	id: int,
	body: UpdateDnaExtraction,
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	data = body.dict()
	data = {k: v for k, v in data.items() if v is not None}
	if not data:
			err = "No changed field to update"
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

	
	result, _ = await get_all_sample_mappings(dna_extraction_id=id)
	if len(result) > 0:
		err = "DNA_EXTRACTION ALREADY ADDED TO PLATE"
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

	try:
	
		current_time = get_current_date_time_utc_7()
		new_dna_extraction_info = {
			**data,
			'updated_at': current_time
		}
		new_dna_extraction_info = await standardize_dna_extraction_service(new_dna_extraction_info)

		dna_extraction = await get_dna_extraction(id)
		if not dna_extraction:
				err = f"Kit with id {id} can not be found"
				http_code = HTTP_404_NOT_FOUND
				errs = failure_response(err)
				raise HTTPException(status_code=http_code, detail=errs)

		err = await update_dna_extraction(dna_extraction,new_dna_extraction_info)
		if err:
			http_code = HTTP_400_BAD_REQUEST
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
		else:
			updated_dna_extraction_info = await get_dna_extraction(id)
			return success_response(updated_dna_extraction_info.to_dict())
	except Exception as e:
		err = str(e)
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.delete("/lims/dna_extraction/{id}", status_code=HTTP_200_OK)
async def admin_delete_dna_extraction_by_id(
	id: int,
	auth=Depends(Auth),
) -> dict:

	logger.info(f"Delete an sale dna_extraction with id {id}")
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	dna_extraction = await get_dna_extraction(id)
	if not dna_extraction:
			err = f"Kit with id {id} can not be found"
			http_code = HTTP_404_NOT_FOUND
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)

	res, err = await delete_dna_extraction(dna_extraction)
	if err:
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)
	else:
		return success_response(res)

def init_app(app: FastAPI):
		app.include_router(router, tags=["lims_dna_extraction"])