import requests
from fastapi import APIRouter, FastAPI, HTTPException, Query, Depends, Body, File, UploadFile
from typing import List, Optional
from starlette.status import (
	HTTP_200_OK,
	HTTP_400_BAD_REQUEST,
	HTTP_403_FORBIDDEN,
	HTTP_404_NOT_FOUND,
	HTTP_408_REQUEST_TIMEOUT,
	HTTP_409_CONFLICT,
	HTTP_422_UNPROCESSABLE_ENTITY,
	HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
	get_s3_client,
	get_aws_upload_presigned_post,
	get_aws_upload_presigned_for_a_kit,
	success_response,
	failure_response,
	get_current_date_time,
	validate_email,
	convert_rowproxy_to_dict
)
import os
import sys
from datetime import datetime, timedelta
import os
import sys
import threading
from botocore.exceptions import ClientError
import logging
from tempfile import NamedTemporaryFile

from ...services.adn_integration import (
    get_all_adn_integrations_service
)

from ...cruds.request import (
	get_request_by_id
)

router = APIRouter()

# Native Pre-signed URL
@router.get("/lims/raw/upload/{technology}/{name}", status_code=HTTP_200_OK)
async def admin_create(
	technology: str,
	name: str,
	auth=Depends(Auth),
) -> dict:
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	try:
		if technology.upper() == 'PCR':
			if name[:3] != 'PCR':
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response("FILE NAME needs to start with PCR !")
				raise HTTPException(status_code=http_code, detail=errs)

		response = get_aws_upload_presigned_post(technology.upper(), name)
		return success_response(response), None

	except Exception:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(f"FAILED GENERATE PRESIGNED POST")
		raise HTTPException(status_code=http_code, detail=errs)
	

@router.get("/lims/raw/upload/adn/{barcode}/{name}", status_code=HTTP_200_OK)
async def upload_adn_result(
	barcode: str,
	name: str,
	auth=Depends(Auth),
) -> dict:
    
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)

	try:

		# check barcode exist
		logger.info(f"Get kit info with id {barcode}")
		adn_integration_detail, _ = await get_all_adn_integrations_service(barcode=barcode)
		if not adn_integration_detail[0]:
			http_code = HTTP_404_NOT_FOUND
			errs = failure_response(f"FAILED GENERATE PRESIGNED POST - ADN INTEGRATION with barcode {barcode} not found!")
			raise HTTPException(status_code=http_code, detail=errs)
		
		adn_integration = convert_rowproxy_to_dict(adn_integration_detail[0])
		adn_type= adn_integration.get('adn_type')
		request = await get_request_by_id(id=adn_integration.get('request_id'))
		identifier_code = request.to_dict().get('identifier_code')

		if adn_type not in config['AGENCY_STORAGE']['SUPPORT_EXTENSION'].keys():
			http_code = HTTP_409_CONFLICT
			errs = failure_response(f"ADN {adn_type} not supported!")
			raise HTTPException(status_code=http_code, detail=errs)

		# return success_response(kit)
		extension = name.split('.')[-1]
		if extension.upper() not in config['AGENCY_STORAGE']['SUPPORT_EXTENSION'][adn_type]:
			http_code = HTTP_422_UNPROCESSABLE_ENTITY
			errs = failure_response(f"Extension {extension} not supported!")
			raise HTTPException(status_code=http_code, detail=errs)
			
  
		# get presigned upload link 
		response = get_aws_upload_presigned_for_a_kit(identifier_code=identifier_code,barcode=barcode, adn_type=adn_type, file_name=name)
		return success_response(response)

	except Exception as err:
		http_code = HTTP_400_BAD_REQUEST
		errs = failure_response(f"FAILED GENERATE PRESIGNED POST: ", str(err))
		raise HTTPException(status_code=http_code, detail=errs)

	

def init_app(app: FastAPI):
	app.include_router(router, tags=["lims_raw"])