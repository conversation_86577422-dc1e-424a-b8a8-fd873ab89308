import requests
from fastapi import API<PERSON>out<PERSON>, <PERSON><PERSON><PERSON>, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.chip import (
  existed_chip_id,
  count_chips,
  get_chip,
  get_chip_by_chip_id,
  get_all_chip_by_chip_id,
  count_matched_chips,
  get_all_chips,
  create_chip,
  update_chip,
  delete_chip
)

from ...services.chip import (
    standardize_chip_service,
    convert_start_chip_date,
    convert_end_chip_date,
    convert_current_date_to_chip_id_prefix,
    get_next_pcr_chip_id
)

from ...models.models import Chip, db
from ...schemas.chip import *
from datetime import datetime, timedelta

router = APIRouter()

# @router.post("/operation/lims/chip", status_code=HTTP_200_OK)
# @router.get("/operation/lims/chips", status_code=HTTP_200_OK)
# @router.get("/operation/lims/chip/{chip_id}", status_code=HTTP_200_OK)
# @router.put("/operation/lims/chip/{chip_id}", status_code=HTTP_200_OK)
# @router.delete("/operation/lims/chip/{chip_id}", status_code=HTTP_200_OK)

router = APIRouter()

@router.post("/lims/chip/{technology}", status_code=HTTP_200_OK)
async def admin_create(
  technology: str,
  chip_body: AddChip,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)
  if technology.lower() == 'pcr':
    response = await admin_get_next_chip_id(technology=technology,auth=auth)
    next_chip_id = response[0]['data']
    chip_body = AddChip(chip_id=next_chip_id,type='GSAv3',technology=technology.upper(),status='CREATED')

  data = chip_body.dict()
  
  if not data:
      err = "Empty Request Body"
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)

  data = await standardize_chip_service(data)
    
  logger.info(f"Create a chip code with data {data}")
  
  data, err = await create_chip(data)
  if err:
    http_code = HTTP_400_BAD_REQUEST
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)
  else:
    return success_response(data), None



#DONE
@router.get("/lims/chips", status_code=HTTP_200_OK)
async def admin_get_chips(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  chip_id: Optional[str] = Query(None),
  type: Optional[str] = Query(None),
  technology: Optional[str] = Query(None),
  auth=Depends(Auth),
):
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)


  current_date = get_current_date_time_utc_7()

  logger.info(f"Filter chips info")
  size = page_size
  offset = (page_number - 1) * page_size
  total = await count_matched_chips()
  total = total if total else 0
  if offset > 0 and offset >= total:
      err = "Offset number is too large."
      http_code = HTTP_422_UNPROCESSABLE_ENTITY
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
  try:

      results, total_match = await get_all_chips(
          offset=offset,
          size=size,
          chip_id=chip_id,
          type=type,
          technology=technology,
          order_by="created_at"
      )

      if not total_match:
          total_match = total

      res_data = [res.to_dict() for res in results]
      
      res = {
          "data": res_data,
          'pagination': {
              'page_size': page_size,
              'page_number': page_number,
              'total': total_match
          },
          "detail": []
      }
      return res
  except Exception as err:
      http_code = HTTP_500_INTERNAL_SERVER_ERROR
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/lims/chip/{chip_id}", status_code=HTTP_200_OK)
async def admin_get_chip_info_by_chip_id(
  chip_id: str,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  chip = await get_chip_by_chip_id(chip_id)

  if chip:
    res_chip = chip.to_dict()
    return success_response(res_chip)
  else:
    err = f"Chip with id {id} can not be found"
    http_code = HTTP_404_NOT_FOUND
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)



# DONE
@router.get("/lims/next_chip_id/{technology}", status_code=HTTP_200_OK)
async def admin_get_next_chip_id(
	technology: str,
	auth=Depends(Auth)
):
  
	_, err = await auth.get_token_claims()
	if err:
		errs = failure_response(err['err_msg'])
		raise HTTPException(status_code=err['err_code'], detail=errs)
	if technology:
		if technology == "pcr":
			current_prefix = await convert_current_date_to_chip_id_prefix() # e.g: 2023/02/28 --> 230228
			number_of_chips_created_today = 0
			results, total_match = await get_all_chips(
				chip_id=current_prefix,
				order_by="created_at"
				)
            
			next_pcr_chip_id, err = await get_next_pcr_chip_id(results,current_prefix)
			if err:
				http_code = HTTP_400_BAD_REQUEST
				errs = failure_response(err)
				raise HTTPException(status_code=http_code, detail=errs)
			
			return success_response(next_pcr_chip_id),None
                
		else:
			err = f"{technology} is not supported"
			http_code = HTTP_404_NOT_FOUND
			errs = failure_response(err)
			raise HTTPException(status_code=http_code, detail=errs)
	else:
		err = f"No {technology} typed in"
		http_code = HTTP_404_NOT_FOUND
		errs = failure_response(err)
		raise HTTPException(status_code=http_code, detail=errs)


# #DONE
# @router.put("/lims/chip/{chip_id}", status_code=HTTP_200_OK)
# async def admin_update_chip_info_by_chip_id(
#   chip_id: str,
#   body: UpdateChip,
#   auth=Depends(Auth),
# ) -> dict:
#   _, err = await auth.get_token_claims()
#   if err:
#     errs = failure_response(err['err_msg'])
#     raise HTTPException(status_code=err['err_code'], detail=errs)

#   data = body.dict()
#   data = {k: v for k, v in data.items() if v is not None}
#   if not data:
#       err = "No changed field to update"
#       http_code = HTTP_400_BAD_REQUEST
#       errs = failure_response(err)
#       raise HTTPException(status_code=http_code, detail=errs)


#   try:
#     current_time = get_current_date_time_utc_7()
#     new_chip_info = {
#       **data,
#       'updated_at': current_time
#     }
#     new_chip_info = await standardize_chip_service(new_chip_info)

#     chip = await get_chip_by_chip_id(chip_id)

#     if not chip:
#         err = f"Chip with chip_id {id} can not be found"
#         http_code = HTTP_404_NOT_FOUND
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)

#     err = await update_chip(chip,new_chip_info)
#     if err:
#       http_code = HTTP_400_BAD_REQUEST
#       errs = failure_response(err)
#       raise HTTPException(status_code=http_code, detail=errs)
#     else:
#       updated_chip_info = await get_chip_by_chip_id(chip_id)
#       return success_response(updated_chip_info.to_dict())
#   except Exception as e:
#     err = str(e)
#     http_code = HTTP_400_BAD_REQUEST
#     errs = failure_response(err)
#     raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.delete("/lims/chip/{chip_id}", status_code=HTTP_200_OK)
async def admin_delete_chip_by_chip_id(
  chip_id: str,
  auth=Depends(Auth),
) -> dict:

  logger.info(f"Delete an sale chip with id {chip_id}")
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  chip = await get_chip_by_chip_id(chip_id)
  if not chip:
      err = f"Chip with id {chip_id} can not be found"
      http_code = HTTP_404_NOT_FOUND
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)

  current_time = get_current_date_time_utc_7()
  data = {
    'updated_at': current_time,
    'deleted_at': current_time,
  }

  chip, err = await delete_chip(chip,data)
  if err:
    http_code = HTTP_404_NOT_FOUND
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)
  else:
    updated_chip_info = await get_chip(chip.id)
    return success_response(updated_chip_info.to_dict())

def init_app(app: FastAPI):
    app.include_router(router, tags=["lims_chip"])