from typing import Optional, List
from io import BytesIO

import requests
from fastapi import APIRouter, Body, Depends, FastAPI, HTTPException, Query
from fastapi.responses import StreamingResponse, FileResponse, Response
from starlette.responses import JSONResponse
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ... import logger
from ...auth import Auth
from ...config import config
from ...cruds.identity_card import existed_identity_card
from ...schemas.kit import (
    IdentityCardUpdate,
    OperatorRegisterKitWithCCCDV3,
    addIdentityCardRegistration,
    onboardIdentityCardRegistration, OperatorRegisterKitWithCCCDV4,
)
from ...schemas.tracking_collect_session import (
    AddCollectSession,
    AddSamplesToCollectSession,
    UpdateCollectSessionDetail,
)

# from ...schemas.tracking_collect_session import ()
from ...services.identity_card import (
    get_adn_kit_mapping_by_cccd_v3,
    get_identity_card_detail_via_cccd_v3,
    get_identity_card_detail_via_cccd_v3_w_orm,
    get_identity_cards_list_v3,
    get_identity_cards_require_registration_v3,
    update_identity_card_detail_via_cccd_v3,
    validate_checkin_infor_against_id_card,
    export_identity_card_to_docx,
    export_identity_card_to_excel,
    delete_identity_card_by_id_card,
)
from ...services.kit import (
    register_identity_card_v3,
    register_kit_service_w_cccd_v3,
    update_onboarding_identity_card_v3,
    # recollect_kit_service_w_cccd_v3,
    # upgrade_kit_service_w_cccd_v3
)
from ...services.tracking_collect_session import (
    create_collect_session_service,
    get_all_collect_session_samples_service,
    get_all_collect_sessions_service,
    get_collect_session_by_id_service,
    update_collect_session_service,
    upsert_collect_session_sample_service,
)
from ...utils.utils import (
    compare_identity_details,
    failure_response,
    get_role_by_brearer_token,
    success_response,
    validate_access_token, number_to_vietnamese_words,
)

CCCD_ROLE_SET = {"admin", "cs", "cccd_admin"}

router = APIRouter()


@router.post("/cccd/registrations", status_code=HTTP_200_OK)
async def register_cccd(
    registration_body: addIdentityCardRegistration,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """

    logger.info("Operator register a kit with data!")
    # get cs's user id from token claim
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")
    registration_body.customer_support_id = cs_id
    registration_body.customer_support_name = cs_name

    try:
        res = await register_identity_card_v3(register_req=registration_body)

        return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/cccd/registrations", status_code=HTTP_200_OK)
async def update_onboarding_cccd_with_sample_and_kit(
    registration_body: onboardIdentityCardRegistration,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """

    logger.info("Operator register a kit with data!")
    # get cs's user id from token claim
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")
    registration_body.customer_support_id = cs_id
    registration_body.customer_support_name = cs_name

    try:
        res = await update_onboarding_identity_card_v3(
            register_kit_req=registration_body
        )

        return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# list all identity cards
@router.get("/cccds", status_code=HTTP_200_OK)
async def get_all_identity_card(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    identifier_code: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    order_by: Optional[str] = "created_time",
    gender: Optional[str] = Query(None),
    dob: Optional[str] = Query(None),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    auth=Depends(Auth),
):
    try:
        token_claims, err = await auth.get_token_claims()

    except Exception:
        logger.info("no access token provided")

    sub = token_claims.get("sub")
    bearer_token_credential = auth.bearer_token.credentials
    role_list = await get_role_by_brearer_token(
        sub=sub, credential=bearer_token_credential
    )
    customer_support_id = sub
    if set(role_list).intersection(CCCD_ROLE_SET):
        customer_support_id = None

    logger.info(f"Getting kit list with role list {role_list} ")
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_identity_cards_require_registration_v3(
            offset=offset,
            size=size,
            order_by=order_by,
            identifier_code=identifier_code,
            full_name=full_name,
            gender=gender,
            dob=dob,
            customer_support_id=customer_support_id,
            start_date=start_date,
            end_date=end_date,
        )

        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    pass

@router.delete("/cccd/{identifier_code}", status_code=HTTP_200_OK)
async def delete_identity_card(
    identifier_code: str,
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()

    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        logger.info(f"Delete Identity Card with id {identifier_code}")
        await delete_identity_card_by_id_card(
            identifier_code=identifier_code
        )
        return success_response({"message": "Identity card deleted successfully"})
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

# delete multiple identity cards
@router.delete("/cccds", status_code=HTTP_200_OK)
async def delete_multiple_identity_cards(
    identifier_codes: list = Body(..., embed=True),
    auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()

    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        logger.info(f"Delete Identity Cards with ids {identifier_codes}")
        for identifier_code in identifier_codes:
            await delete_identity_card_by_id_card(
                identifier_code=identifier_code
            )
        return success_response({"message": "Identity cards deleted successfully"})
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



@router.post("/cccd/kits/registrations", status_code=HTTP_200_OK)
async def register_kit_w_cccdv3(
    registration_body: OperatorRegisterKitWithCCCDV4,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = registration_body.dict()
    logger.info("Operator register a kit with data!")
    # get cs's user id from token claim
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")
    registration_body.customer_support_id = cs_id
    registration_body.customer_support_name = cs_name

    try:
        res, errs = await register_kit_service_w_cccd_v3(
            register_kit_req=registration_body
        )
        if errs:
            raise HTTPException(status_code=errs[0], detail=errs[1])
        else:
            return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/cccd/kits/check-duplicate/{cccd}")
async def check_duplicate_cccd(cccd: str):
    is_existed_cccd = await existed_identity_card(cccd)

    if is_existed_cccd:
        return JSONResponse(
            content={"message": f"CCCD/Số định danh {cccd} đã tồn tại trong hệ thống"},
            status_code=400
        )

    return JSONResponse(
        content={"message": "CCCD/Số định danh hợp lệ"},
        status_code=200
    )


# @router.post("/cccd/kits/recollection/{first_barcode}", status_code=HTTP_200_OK)
# async def recollect_kit_w_cccd_v3(
#     first_barcode: str,
#     recollection_body: OperatorRecollectKitWithCCCDV3,
#     auth=Depends(Auth),
# ) -> dict:
#     """
#         Register a new kit with its associated information, do not allow to
#         register a registered kit, and an user can register multiple kits
#         :param registration_body: Request body
#         :return: Kit info with an initial status
#     """
#     data = recollection_body.dict()
#     logger.info("Operator register a kit with data!")
#     # get cs's user id from token claim

#     try:
#         token_claims, err = await auth.get_token_claims()
#         # print("token claims: ", token_claims)
#     except Exception as e:
#         logger.info("no access token provided")

#     try:
#         res, errs = await recollect_kit_service_w_cccd_v3(first_barcode=first_barcode, recollect_kit_req=recollection_body)
#         if errs:
#             raise HTTPException(status_code=errs[0], detail=errs[1])
#         else:
#             return success_response(res)

#     except requests.exceptions.ConnectionError as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.Timeout as err:
#         http_code = HTTP_408_REQUEST_TIMEOUT
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.RequestException as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except ValueError as err:
#         http_code = HTTP_400_BAD_REQUEST
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except Exception as err:
#         http_code = HTTP_422_UNPROCESSABLE_ENTITY
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)


# @router.post("/cccd/kits/upgrade/{version}", status_code=HTTP_200_OK)
# async def upgrade_kit_w_cccd_v3(
#     upgradation_body: OperatorUpgradeKitWithCCCDV3,
#     version: int,
#     auth=Depends(Auth),
# ) -> dict:
#     """
#         Register a new kit with its associated information, do not allow to
#         register a registered kit, and an user can register multiple kits
#         :param registration_body: Request body
#         :return: Kit info with an initial status
#     """
#     data = upgradation_body.dict()
#     logger.info("Operator register a kit with data!")
#     # get cs's user id from token claim


#     try:
#         _, err = await auth.get_token_claims()
#     except Exception as e:
#         logger.info("no access token provided")

#     try:
#         # print("Valid Upgrade Body !")
#         res, errs = await upgrade_kit_service_w_cccd_v3(upgrade_kit_req=upgradation_body,version=version)
#         if errs:
#             raise HTTPException(status_code=errs[0], detail=errs[1])
#         else:
#             return success_response(res)

#     except requests.exceptions.ConnectionError as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.Timeout as err:
#         http_code = HTTP_408_REQUEST_TIMEOUT
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.RequestException as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except ValueError as err:
#         http_code = HTTP_400_BAD_REQUEST
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except Exception as err:
#         http_code = HTTP_422_UNPROCESSABLE_ENTITY
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)


# list all identity cards
@router.get("/cccd/all", status_code=HTTP_200_OK)
async def get_all_identity_card_extended(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    identifier_code: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    order_by: Optional[str] = "created_time",
    gender: Optional[str] = Query(None),
    dob: Optional[str] = Query(None),
    tenDonViThuNhanMau: Optional[str] = Query(None),
    idDonViThuNhanMau: Optional[str] = Query(None),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    is_scanned: Optional[bool] = Query(None),
    sample_collector_name: Optional[str] = None,
    customer_support_ids: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    auth=Depends(Auth),
):
    try:
        token_claims, err = await auth.get_token_claims()

    except Exception:
        logger.info("no access token provided")

    sub = token_claims.get("sub")
    bearer_token_credential = auth.bearer_token.credentials
    role_list = await get_role_by_brearer_token(
        sub=sub, credential=bearer_token_credential
    )
    customer_support_id = sub
    if set(role_list).intersection(CCCD_ROLE_SET):
        customer_support_id = None

    logger.info(f"Getting kit list with role list {role_list} ")
    size = page_size
    offset = (page_number - 1) * page_size

    customer_support_ids = customer_support_ids.split(",") if customer_support_ids else []
    idDonViThuNhanMau = idDonViThuNhanMau.split(",") if idDonViThuNhanMau else []

    try:
        data, total = await get_identity_cards_list_v3(
            offset=offset,
            size=size,
            order_by=order_by,
            identifier_code=identifier_code,
            samplecode=samplecode,
            full_name=full_name,
            gender=gender,
            dob=dob,
            customer_support_id=customer_support_id,
            tenDonViThuNhanMau=tenDonViThuNhanMau,
            start_date=start_date,
            end_date=end_date,
            is_scanned=is_scanned,
            sample_collector_name=sample_collector_name,
            customer_support_ids=customer_support_ids,
            idDonViThuNhanMau=idDonViThuNhanMau,
            phone_number=phone_number,
            bearer_token_credential=bearer_token_credential
        )

        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    pass

@router.get("/cccd/export-docx", status_code=HTTP_200_OK)
async def export_cccd(
    order_by: Optional[str] = "created_time",
    tenDonViThuNhanMau: Optional[str] = Query(None),
    idDonViThuNhanMau: Optional[str] = Query(None),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    identifier_code: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    gender: Optional[str] = Query(None),
    dob: Optional[str] = Query(None),
    is_scanned: Optional[bool] = Query(None),
    sample_collector_name: Optional[str] = None,
    customer_support_ids: Optional[str] = Query(None),
    auth=Depends(Auth),
):
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    sub = token_claims.get("sub")
    bearer_token_credential = auth.bearer_token.credentials
    role_list = await get_role_by_brearer_token(
        sub=sub, credential=bearer_token_credential
    )
    customer_support_id = sub
    if set(role_list).intersection(CCCD_ROLE_SET):
        customer_support_id = None

    customer_support_ids = customer_support_ids.split(",") if customer_support_ids else []
    idDonViThuNhanMau = idDonViThuNhanMau.split(",") if idDonViThuNhanMau else []

    try:
        doc_io: BytesIO = await export_identity_card_to_docx(
            order_by=order_by,
            customer_support_id=customer_support_id,
            tenDonViThuNhanMau=tenDonViThuNhanMau,
            idDonViThuNhanMau=idDonViThuNhanMau,
            start_date=start_date,
            end_date=end_date,
            identifier_code=identifier_code,
            full_name=full_name,
            gender=gender,
            dob=dob,
            is_scanned=is_scanned,
            sample_collector_name=sample_collector_name,
            customer_support_ids=customer_support_ids,
            bearer_token_credential=bearer_token_credential
        )

        doc_io.seek(0)

        return Response(
            content=doc_io.getvalue(),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={
                "Content-Disposition": "attachment; filename=BBNT.docx"
            }
        )


    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

# get identity card detail

@router.get("/cccd/export", status_code=HTTP_200_OK)
async def export_identity_card(
    identifier_code: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    order_by: Optional[str] = "created_time",
    gender: Optional[str] = Query(None),
    dob: Optional[str] = Query(None),
    tenDonViThuNhanMau: Optional[str] = Query(None),
    idDonViThuNhanMau: Optional[str] = Query(None),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    is_scanned: Optional[bool] = Query(None),
    customer_support_ids: Optional[str] = Query(None),
    phone_number: Optional[str] = None,
    auth=Depends(Auth),
):
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    sub = token_claims.get("sub")
    bearer_token_credential = auth.bearer_token.credentials
    role_list = await get_role_by_brearer_token(
        sub=sub, credential=bearer_token_credential
    )
    customer_support_id = sub
    if set(role_list).intersection(CCCD_ROLE_SET):
        customer_support_id = None
    customer_support_ids = customer_support_ids.split(",") if customer_support_ids else []
    idDonViThuNhanMau = idDonViThuNhanMau.split(",") if idDonViThuNhanMau else []

    try:
        data, total = await get_identity_cards_list_v3(
            offset=None,
            size=None,
            order_by=order_by,
            identifier_code=identifier_code,
            full_name=full_name,
            gender=gender,
            dob=dob,
            customer_support_id=customer_support_id,
            tenDonViThuNhanMau=tenDonViThuNhanMau,
            idDonViThuNhanMau=idDonViThuNhanMau,
            start_date=start_date,
            end_date=end_date,
            is_scanned=is_scanned,
            customer_support_ids=customer_support_ids,
            phone_number=phone_number,
            bearer_token_credential=bearer_token_credential
        )

        excel_io = export_identity_card_to_excel(data)
        return StreamingResponse(
            excel_io,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=CCCD.xlsx"},
        )
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/cccd/{identifier_code}", status_code=HTTP_200_OK)
async def get_id_card_info_via_identity_code_v3(
    identifier_code: str,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()

    except Exception:
        logger.info("no access token provided")

    try:
        logger.info(f"Get Identity Card info with id {identifier_code}")
        id_card_info = await get_identity_card_detail_via_cccd_v3_w_orm(
            identifier_code=identifier_code
        )
        return success_response(id_card_info)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# update identity card contact info
@router.put("/cccd/{identifier_code}", status_code=HTTP_200_OK)
async def update_id_card_info_via_identity_code_v3(
    identifier_code: str,
    update_req: IdentityCardUpdate,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()

    except Exception:
        logger.info("no access token provided")

    try:
        logger.info(f"Get Identity Card info with id {identifier_code}")
        current_cccd_detail = await get_identity_card_detail_via_cccd_v3(
            identifier_code=identifier_code
        )
        current_cccd_detail = current_cccd_detail.to_dict()
        current_cccd_detail.pop("avatar_image")
        current_cccd_detail.pop("fingerprint_image")
        current_cccd_detail["dob"] = current_cccd_detail["dob"].isoformat()
        data = update_req.dict()
        if update_req.martyr_name is not None:
            martyr_name = update_req.martyr_name
        else:
            martyr_name = None
        if not data.get("NewIdentityCard"):
            data = await update_identity_card_detail_via_cccd_v3(
                identifier_code=identifier_code,
                phone_number=update_req.phone_number,
                email=update_req.email,
                residence=update_req.residence,
                martyr_name=martyr_name,
                martyr_relationships=update_req.martyr_relationships,
                guardian_name=update_req.guardian_name,
                guardian_gender=update_req.guardian_gender,
                guardian_phone_number=update_req.guardian_phone_number,
                guardian_identifier_code=update_req.guardian_identifier_code,
            )

            return data

        else:
            new_avatar_image = data["NewIdentityCard"].pop("avatar_image")
            new_fingerprint_image = data["NewIdentityCard"].pop("fingerprint_image")

            difference = compare_identity_details(
                current_cccd_detail, data["NewIdentityCard"]
            )

            if difference:
                return failure_response(difference)
            if update_req.martyr_name is not None:
                martyr_name = update_req.martyr_name
            else:
                martyr_name = None

            data = await update_identity_card_detail_via_cccd_v3(
                identifier_code=identifier_code,
                phone_number=update_req.phone_number,
                email=update_req.email,
                residence=update_req.residence,
                new_avatar_image=new_avatar_image,
                new_fingerprint_image=new_fingerprint_image,
                martyr_name=martyr_name,
                martyr_relationships=update_req.martyr_relationships,
                guardian_name=update_req.guardian_name,
                guardian_gender=update_req.guardian_gender,
                guardian_phone_number=update_req.guardian_phone_number,
                guardian_identifier_code=update_req.guardian_identifier_code,
            )

            return success_response(data.to_dict())

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# validate card info
@router.get("/cccd/validate/{identifier_code}", status_code=HTTP_200_OK)
async def validate_checkin_info_against_id_card_data(
    identifier_code: str,
    full_name: Optional[str] = Query(None),
    dob: Optional[str] = Query(None),
    gender: Optional[str] = Query(None),
    nationality: Optional[str] = Query(None),
    origin: Optional[str] = Query(None),
    residence: Optional[str] = Query(None),
    ethnic: Optional[str] = Query(None),
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()

    except Exception:
        logger.info("no access token provided")

    try:
        logger.info(f"Get Identity Card info with id {identifier_code}")
        result = await validate_checkin_infor_against_id_card(
            identifier_code=identifier_code,
            full_name=full_name,
            dob=dob,
            gender=gender,
            nationality=nationality,
            origin=origin,
            residence=residence,
            ethnic=ethnic,
        )
        return success_response(result)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/cccd/integrations/{identifier_code}", status_code=HTTP_200_OK)
async def get_available_adn_integrations_by_cccd(
    identifier_code: str, is_concise: Optional[bool] = False, auth=Depends(Auth)
) -> dict:
    try:
        _, err = await auth.get_token_claims()

    except Exception:
        logger.info("no access token provided")

    try:
        logger.info(f"Get Identity Card info with id {identifier_code}")
        result, _ = await get_adn_kit_mapping_by_cccd_v3(
            identifier_code=identifier_code, is_concise=is_concise
        )
        return success_response(result)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/cccd/collect-sessions", status_code=HTTP_200_OK)
@validate_access_token
async def create_collect_session(
    req_body: AddCollectSession = Body(...),
    auth=Depends(Auth),
) -> dict:
    """
    Create a new collect session
    """

    logger.info("Operator create a new collect session!")
    token_claims, _ = await auth.get_token_claims()
    # get cs's user id from token claim

    cs_id = token_claims["sub"]
    cs_phone_number = token_claims["phone_number"]

    req_body.user_id = cs_id
    req_body.phone_number = cs_phone_number.replace("+84", "0")
    data = req_body.dict()

    res = await create_collect_session_service(data=data)
    return success_response(res)


@router.get("/cccd/collect-sessions/all", status_code=HTTP_200_OK)
@validate_access_token
async def get_collect_sessions(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    unit_id: Optional[str] = Query(None),
    tenDonVi: Optional[str] = Query(None),
    location_name: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    employee_name: Optional[str] = Query(None),
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    auth=Depends(Auth),
) -> dict:
    token_claims, _ = await auth.get_token_claims()
    sub = token_claims.get("sub")
    bearer_token_credential = auth.bearer_token.credentials
    role_list = await get_role_by_brearer_token(
        sub=sub, credential=bearer_token_credential
    )
    customer_support_id = sub
    if set(role_list).intersection(CCCD_ROLE_SET):
        customer_support_id = None

    logger.info(f"Getting kit list with role list {role_list} ")
    size = page_size
    offset = (page_number - 1) * page_size

    data, total = await get_all_collect_sessions_service(
        offset=offset,
        size=size,
        unit_id=unit_id,
        tenDonVi=tenDonVi,
        location_name=location_name,
        phone_number=phone_number,
        employee_name=employee_name,
        customer_support_id=customer_support_id,
        collection_start_date=collection_start_date,
        collection_end_date=collection_end_date,
    )

    res = {
        "data": data,
        "pagination": {
            "page_size": page_size,
            "page_number": page_number,
            "total": total,
        },
        "detail": [],
    }

    return res


@router.get("/cccd/collect-sessions/{id}", status_code=HTTP_200_OK)
@validate_access_token
async def get_collect_session_detail(
    id: str,
    auth=Depends(Auth),
) -> dict:
    logger.info(f"Get Collect Session with id {id}")
    data = await get_collect_session_by_id_service(id=id)
    return success_response(data)


@router.put("/cccd/collect-sessions/{id}", status_code=HTTP_200_OK)
@validate_access_token
async def update_collect_session_detail(
    id: str,
    update_req: UpdateCollectSessionDetail,
    auth=Depends(Auth),
) -> dict:
    logger.info(f"Get Collect Session with id {id}")
    body = update_req.dict()
    result = await update_collect_session_service(id=id, data=body)
    return success_response(result)


@router.post("/cccd/collect-sessions/{session_id}/samples", status_code=HTTP_200_OK)
@validate_access_token
async def add_collect_session_sample(
    session_id: str,
    req_body: AddSamplesToCollectSession,
    auth=Depends(Auth),
) -> dict:
    logger.info(f"Get Collect Session with id {session_id}")
    result = await upsert_collect_session_sample_service(
        id=session_id, req_body=req_body
    )
    return success_response(result)


@router.get("/cccd/collect-sessions/{id}/samples/all", status_code=HTTP_200_OK)
@validate_access_token
async def get_all_collect_session_samples(
    id: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    identifier_code: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    order_by: Optional[str] = "tcs.created_at",
    gender: Optional[str] = Query(None),
    dob: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    auth=Depends(Auth),
) -> dict:
    size = page_size
    offset = (page_number - 1) * page_size

    data, total = await get_all_collect_session_samples_service(
        offset=offset,
        size=size,
        order_by=order_by,
        session_id=id,
        identifier_code=identifier_code,
        full_name=full_name,
        gender=gender,
        dob=dob,
        samplecode=samplecode,
    )

    res = {
        "data": data,
        "pagination": {
            "page_size": page_size,
            "page_number": page_number,
            "total": total,
        },
        "detail": [],
    }
    return res


def init_app(app: FastAPI):
    app.include_router(router, tags=["user_id_card"])
