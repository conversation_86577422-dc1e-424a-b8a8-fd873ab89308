import requests
from io import Bytes<PERSON>
from datetime import datetime

from fastapi import APIRouter, Body, Depends, FastAPI, HTTPException, Query
from fastapi.responses import StreamingResponse
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ... import logger
from ...auth import Auth
from ...config import config
from ...cruds.attempts import (
    count_attempts,
    get_all_attempts,
    is_block,
    unblock_kit_registration,
)
from ...cruds.identity_card import get_all_kits_identity_card_via_id_code_v3
from ...cruds.kit import (
    get_kit_detail,
    get_kit_detail_via_barcode_v3,
    get_kit_detail_via_kit_uuid_v3,
    get_kit_list_detail_v3,
    get_latest_kit_detail_via_samplecode_v3,
)
from ...cruds.register_kit import (
    count_kits,
    delete_kit_status,
    delete_kit_v3,
    get_kit_v3,
    get_status,
)
from ...cruds.sample import get_sample_detail_via_samplecode_v3
from ...cruds.subject import get_all_kits_subject_via_subject_id_v3
from ...models.models import IdentityCard, Status, Subject
from ...schemas.kit import *
from ...schemas.notification import NotificationReq
from ...services.kit import (
    create_new_kit_status,
    get_kit_by_meta_data_v3,
    get_kit_list_by_sample_info,
    get_kit_list_v3,
    export_all_kit,
    internal_register_kit,
    migrate_kit_service_v3,
    recollect_kit_service_v3,
    register_kit_service,
    register_kit_service_v2,
    register_kit_service_v3,
    renew_kit_default_link,
    scan_samplecode_service_v3,
    send_kit_info_to_lab,
    send_kit_info_to_lab_v3,
    update_kit_info,
    update_kit_info_v3_w_barcode,
    update_kit_info_w_uuid_v3,
    update_sample_info_w_samplecode_v3,
    upgrade_kit_service_v3, export_all_kit_send_to_lab,
)
from ...services.notification import send_notification_to_group
from ...services.sample import get_sample_list_v4
from ...services.sample_meta import (
    attach_sample_to_user,
)
from ...utils.utils import (
    convert_rowproxy_to_dict,
    failure_response,
    parse_object_from_dict,
    success_response,
)

router = APIRouter()


@router.post("/kits/registrations/v1", status_code=HTTP_200_OK)
async def create(
    registration_body: RegisterKit,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = registration_body.dict()
    logger.info(f"Register a kit with data {data}")
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    apply_user_id = token_claims["sub"]
    if await is_block(str(apply_user_id)):
        err = "Your account is temporarily blocked from registering for the kit, \
        please contact the website administrator to resolve this issue."
        http_code = HTTP_403_FORBIDDEN
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    res, errs = await register_kit_service(data, registration_body, apply_user_id)
    if errs:
        raise HTTPException(status_code=errs[0], detail=errs[1])
    else:
        return success_response(res)


@router.post("/kits/registrations/v2", status_code=HTTP_200_OK)
async def register_kit_v2(
    registration_body: OperatorRegisterKit,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = registration_body.dict()
    logger.info(f"Operator register a kit with data {data}")
    # get cs's user id from token claim
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")
    registration_body.customer_support_id = cs_id
    registration_body.customer_support_name = cs_name

    try:
        res, errs = await register_kit_service_v2(register_kit_req=registration_body)
        if errs:
            raise HTTPException(status_code=errs[0], detail=errs[1])
        else:
            return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/internal/kits/registrations", status_code=HTTP_200_OK)
async def register_kit_internal(
    registration_body: OperatorRegisterKit,
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = registration_body.dict()
    logger.info(f"Operator register a kit with data {data}")
    # get cs's user id from token claim
    cs_id = None
    cs_name = None
    registration_body.customer_support_id = cs_id
    registration_body.customer_support_name = cs_name

    try:
        res, errs = await internal_register_kit(register_kit_req=registration_body)
        if errs:
            raise HTTPException(status_code=errs[0], detail=errs[1])
        else:
            return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/check_by_sample/v1", status_code=HTTP_200_OK)
async def check_kit_list_by_sample_info(
    name: str = Query(None),
    gender: Literal["male", "female"] = Query(None),
    dob: str = Query(None),
    yob: str = Query(None),
    account_id: str = Query(None),
    product_code: str = Query(None),
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    logger.info("Check kit list by sample info")
    try:
        data = await get_kit_list_by_sample_info(
            name=name,
            gender=gender,
            dob=dob,
            yob=yob,
            account_id=account_id,
            product_code=product_code,
        )
        return success_response(data)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/registrations/link", status_code=HTTP_200_OK)
async def get_internal_kit_info_link(
    kit_link_body: OperatorKitLink,
    auth=Depends(Auth),
) -> dict:
    try:
        token_claims, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err["err_msg"])
            raise HTTPException(status_code=err["err_code"], detail=errs)
        data = kit_link_body.dict()
        logger.info(f"Renew presigned report pdf link of kit {data.get('barcode')}")
        res = await renew_kit_default_link(data)
        return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    pass


@router.post("/operator/kits/registrations", status_code=HTTP_200_OK)
async def operator_create(
    registration_body: OperatorRegisterKit,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = registration_body.dict()
    logger.info(f"Operator register a kit with data {data}")
    # get cs's user id from token claim
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    cs_id = token_claims["sub"]
    try:
        if registration_body.validate_account:
            request_headers = {
                "Content-type": "application/json",
                "Accept": "text/plain",
            }
            response = requests.get(
                config["AUTHEN_URL"][config["ENV"]] + "/internal/users/uuid/validate",
                headers=request_headers,
                params={
                    "phone_number": data["phone_number"],
                    "email": data["email"],
                },
                timeout=3,
            )
            user_info = response.json()
            response.raise_for_status()
            if user_info["error"]["code"] == 400:
                http_code = HTTP_400_BAD_REQUEST
                errs = failure_response(user_info["error"]["message"])
                raise HTTPException(status_code=http_code, detail=errs)
            apply_user_id = user_info["data"]["uuid"]

            res, errs = await register_kit_service(
                data, registration_body, apply_user_id
            )
            if errs:
                raise HTTPException(status_code=errs[0], detail=errs[1])
            else:
                return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/{id}/v2", status_code=HTTP_200_OK)
async def get_internal_kit_info_v2(id: str) -> dict:
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    try:
        logger.info(f"Get kit info with id {id}")
        kit_info = await get_kit_detail(id)
        return success_response(kit_info)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/{id}/exist", status_code=HTTP_200_OK)
async def check_kit_exist(
    id: str,
    auth=Depends(Auth),
) -> bool:
    """
    Check whether the kit exist in our database or not, this API is
    used when an user registers a new kit to check whether that kit is registered or not
    :param id: Kit  id (a barcode)
    :return: True/False
    """
    logger.info(f"Check if kit with id {id} existed")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        kit_info = await get_kit_v3(id)
        if kit_info:
            return success_response(True)
        else:
            return success_response(False)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/kits/registrations/unblock", status_code=HTTP_200_OK)
async def unblock(
    userid: str,
    auth=Depends(Auth),
):
    """
    Unblock kit registration for requested user
    """
    token_claims, err = await auth.get_token_claims()
    logger.info(f"Unblock kit registration for requested user {token_claims['sub']} ")
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        await unblock_kit_registration(userid)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/{id}/status", status_code=HTTP_200_OK)
async def get_kit_status(
    id: str,
    auth=Depends(Auth),
) -> list:
    """
    Get all status of a kit
    :param id: Kit  id (a barcode)
    :return: a list of kit status
    """
    logger.info(f"Get status of kit with id {id} ")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    kit_info = await get_kit_v3(id)
    if not kit_info:
        err = f"Kit with id {id} does not exist"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    kit_status = await get_status(id)
    all_status = []
    if kit_status:
        for status in kit_status:
            if status:
                for r in status:
                    if isinstance(r, Status):
                        all_status.append(r.to_dict())
        return success_response(all_status)
    else:
        err = f"Status of the kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# check
@router.put("/kits/registrations/{id}/status", status_code=HTTP_200_OK)
async def create_new_kit_status_external(id: str, kit_status: NewKitStatus) -> list:
    """
    Update current status_id of a kit and create new status in database
    """
    logger.info(f"Create new status of a kit with id {id}")
    res, errs = await create_new_kit_status(id, kit_status)
    if errs:
        raise HTTPException(status_code=errs[0], detail=errs[1])
    else:
        return success_response(res)


@router.put("/kits/registrations/{id}/v2", status_code=HTTP_200_OK)
async def update_kit_data_v2(
    id: str,
    body: UpdateKit,
    auth=Depends(Auth),
) -> list:
    """
    Update kit data
    :param id: Kit  id (a barcode)
    :param body: request body which contains a new kit info
    :return: New kit info
    """
    logger.info(f"Update kit with id {id}, updating data {body}")
    try:
        body.barcode = id
        await update_kit_info(body)

        return success_response(body)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/kits/registrations/{barcode}", status_code=HTTP_200_OK)
async def update_kit_data_v3(
    barcode: str,
    body: UpdateKitV3viaBarcode,
    auth=Depends(Auth),
) -> list:
    """
    Update kit data
    :param id: Kit  barcode (a barcode)
    :param body: request body which contains a new kit info
    :return: New kit info
    """
    logger.info(f"Update kit with barcode {barcode}, updating data {body}")
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")

    body.customer_support_id = cs_id
    body.customer_support_name = cs_name

    try:
        if body.barcode != barcode:
            msg = f"Inconsistent kit barcode {barcode}"
            logger.info(msg)
            raise msg

        await update_kit_info_v3_w_barcode(body)

        return success_response(body)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/kits/registrations/uuid/{kit_uuid}", status_code=HTTP_200_OK)
async def update_kit_data_by_uuid_v3(
    kit_uuid: str,
    body: UpdateKitV3viaUUID,
    auth=Depends(Auth),
) -> list:
    """
    Update kit data
    :param id: Kit  uuid (a kit_uuid)
    :param body: request body which contains a new kit info
    :return: New kit info
    """
    logger.info(f"Update kit with uuid {kit_uuid}, updating data {body}")
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")

    body.customer_support_id = cs_id
    body.customer_support_name = cs_name

    try:
        if body.kit_uuid != kit_uuid:
            msg = f"Inconsistent kit uuid {kit_uuid}"
            logger.info(msg)
            raise msg

        await update_kit_info_w_uuid_v3(body)

        return success_response(body)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/kits/registrations/sample/{samplecode}", status_code=HTTP_200_OK)
async def update_kit_data_by_samplecode_v3(
    samplecode: str,
    body: UpdateSampleV3,
    auth=Depends(Auth),
) -> list:
    """
    Update Sample data
    :param id: Sample  samplecode (a samplecode)
    :param body: request body which contains a new kit info
    :return: New Sample info
    """
    logger.info(f"Update Sample with samplecode {samplecode}, updating data {body}")
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")

    body.customer_support_id = cs_id
    body.customer_support_name = cs_name

    try:
        if body.samplecode != samplecode:
            msg = f"Inconsistent kit samplecode {samplecode}"
            logger.info(msg)
            raise msg

        await update_sample_info_w_samplecode_v3(body)

        return success_response(body)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.delete("/kits/registrations/{id}", status_code=HTTP_200_OK)
async def delete_kit_by_id(
    id: str,
    auth=Depends(Auth),
) -> dict:
    """
    Delete a Kit and its status
    :param id: Kit  id (a barcode)
    :return: Deleted kit
    """
    logger.info(f"Delete a kit with id {id}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        res = await delete_kit_v3(id)
        await delete_kit_status(id)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(str(err))
        raise HTTPException(status_code=http_code, detail=errs)
    return success_response(res)


@router.get("/admin/kits/registrations", status_code=HTTP_200_OK)
async def get_kits(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    userid: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    myself: Optional[bool] = Query(None),
    name: Optional[str] = Query(None),
    gender: Optional[str] = Query(None),
    nickname: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    exclude_status: Optional[str] = Query(None),
):
    """
    Get all Kits with pagination
    :param page_size: Number of rows should be taken
    :param page_number: the current page number
    :param userid: ID of request user
    :param barcode: ID of kit
    :param email: User's email
    :param myself: Type of kit, user buys it for themself, or for their relative
    :param name: Name of customer, who use the kit
    :param gender: Gender of customer, who use the kit
    :return: A list of Kits
    """
    logger.info("Filter kit info")
    size = page_size
    offset = (page_number - 1) * page_size
    total = await count_kits()
    if offset > 0 and offset >= total:
        err = "Offset number is too large."
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        kits, total_match = await get_kit_list_v3(
            size=size,
            offset=offset,
            user_id=userid,
            barcode=barcode,
            email=email,
            phone_number=phone_number,
            name=name,
            gender=gender,
            nickname=nickname,
            current_status=current_status,
            exclude_status=exclude_status,
            order_by="created_time",
        )
        if not total_match:
            total_match = total
        res = {
            "data": kits,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/admin/kits/registrations/ids", status_code=HTTP_200_OK)
async def get_kit_ids(
    barcodes: List[str] = Body(..., min_items=1),
):
    """
    Return a list of information for a specific list of kit_ids (barcodes)
    """
    logger.info("Get a list of kit information")
    kit_infos = {}
    failed_list = []
    for id in barcodes:
        try:
            kit_info = await get_kit_v3(id)
            if not kit_info:
                failed_list.append(id)
            else:
                kit_info = kit_info.to_dict()
                kit_infos.update(
                    {
                        id: {
                            "name": kit_info["name"],
                            "userid": kit_info["userid"],
                            "gender": kit_info["gender"],
                        }
                    }
                )
        except Exception as e:
            logger.error(e)
            failed_list.append(id)

    response = {"data": kit_infos, "err_data": failed_list}
    return response


@router.get("/user/kits/registrations", status_code=HTTP_200_OK)
async def get_kits_list(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    name: Optional[str] = Query(None),
    gender: Optional[str] = Query(None),
    nickname: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    exclude_status: Optional[str] = Query("DISABLED"),
    auth=Depends(Auth),
):
    """
    Get all customers Kits with pagination
    :param page_size: Number of rows should be taken
    :param page_number: the current page number
    :param userid: ID of request user
    :param barcode: ID of kit
    :param myself: Type of kit, user buys it for themself, or for their relative
    :param name: Name of customer, who use the kit
    :param gender: Gender of customer, who use the kit
    :return: A list of Kits
    """
    logger.info("Filter kit info")
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        userid = token_claims["sub"]
        data, total = await get_kit_list_v3(
            offset=offset,
            size=size,
            order_by=None,
            barcode=barcode,
            email=email,
            phone_number=phone_number,
            name=name,
            gender=gender,
            nickname=nickname,
            current_status=current_status,
            exclude_status=exclude_status,
            user_id=userid,
        )
        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/attempts", status_code=HTTP_200_OK)
async def get_user_attempts(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    userid: Optional[str] = Query(None),
    block: Optional[bool] = Query(None),
    attempt: Optional[int] = Query(None),
    auth=Depends(Auth),
):
    """
    Get all attempts with pagination
    """
    logger.info("Filter attempt info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size
    total = await count_attempts()
    if offset > 0 and offset >= total:
        err = "Offset number is too large."
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        attempts, total_match = await get_all_attempts(
            size=size,
            offset=offset,
            userid=userid,
            block=block,
            attempt=attempt,
            order_by="created_time",
        )
        if not total_match:
            total_match = total
        res_data = [r.to_dict() for r in attempts]
        res = {
            "data": res_data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/report/groups", status_code=HTTP_200_OK)
async def get_kit_report_groups(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    auth=Depends(Auth),
):
    """
    Get all report groups of all kits which are belong to an user
    :return: A list of Kit information with report groups
    """
    logger.info("Get kits and report groups which are belong to an user")
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        size = page_size
        offset = (page_number - 1) * page_size
        userid = token_claims["sub"]
        kits, _ = await get_kit_list_detail_v3(
            size=size,
            offset=offset,
            userid=userid,
            order_by="created_time",
            exclude_status=config["DISABLED_KIT_STATUS"],
        )
        if not kits:
            return {"data": [], "detail": []}
        res_data = {r["barcode"]: r for r in kits}
        kit_list_data = [
            {"kit_id": r["barcode"], "gender": r["gender"], "age": 20} for r in kits
        ]
        kit_report_groups_url = config["KIT_REPORT_GROUPS_URL"]

        data = {"kit_list": kit_list_data}

        logger.info("Get kit report groups with data: ", data)
        request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
        try:
            res = requests.post(
                kit_report_groups_url, json=data, headers=request_headers, timeout=30
            )
            res.raise_for_status()
        except requests.exceptions.ConnectionError as err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        except requests.exceptions.Timeout as err:
            http_code = HTTP_408_REQUEST_TIMEOUT
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        except requests.exceptions.RequestException as err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        if res:
            response = res.json()
            final_return_result = []
            for r in response["data"]:
                r.update(res_data[r["kit_id"]])
                final_return_result.append(r)
            res = {"data": final_return_result, "detail": []}
            return res
        else:
            return {"data": [], "detail": []}
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/status/timerange", status_code=HTTP_200_OK)
async def get_status_time_range(
    status: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    days: int = Query(0, ge=0, le=365),
):
    from ...cruds.register_kit import get_status_with_days_ago

    size = page_size
    offset = (page_number - 1) * page_size
    data, total = await get_status_with_days_ago(status, days, size=size, offset=offset)
    res = {
        "data": data,
        "detail": [],
        "pagination": {
            "page_size": page_size,
            "page_number": page_number,
            "total": total,
        },
    }

    return res


@router.post("/kits/assign-to-user", status_code=HTTP_200_OK)
async def attach_to_user(
    user_sample_body: UserSampleBody,
    auth=Depends(Auth),
) -> dict:
    try:
        results = await attach_sample_to_user(
            userid=user_sample_body.userid,
            sample_meta_id=user_sample_body.sample_meta_id,
        )
        if errs:
            raise HTTPException(status_code=errs[0], detail=errs[1])
        else:
            return success_response(results)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/send-to-lab/v2")
async def send_samples_to_lab_v2(
    req: SendToLabUpdate,
    auth=Depends(Auth),
) -> dict:
    try:
        token_claims, err = await auth.get_token_claims()
        user_id = token_claims.get("sub")
        failed_kits, updated_kits = await send_kit_info_to_lab(
            kit_list=req.kit_list, lab_receipt_date=req.lab_receipt_date
        )
        await send_notification_to_group(
            req=NotificationReq(
                group_id="lims",
                sender_id=user_id,
                # recipient_id=
                title=f"CS bàn giao {len(req.kit_list)} mẫu cho Lab",
                content=f"{req.kit_list}",
            )
        )
        res = {
            "data": {
                "failed_kits": failed_kits,
                "updated_kits": updated_kits,
            },
            "detail": [],
        }

        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/back-to-cs")
async def send_samples_to_lab(
    req: BatchUpdateKitStatus,
    auth=Depends(Auth),
) -> dict:
    try:
        token_claims, err = await auth.get_token_claims()
        user_id = token_claims.get("sub")
        await send_notification_to_group(
            req=NotificationReq(
                group_id="cs",
                sender_id=user_id,
                # recipient_id=
                title=f"Lab trả lại {len(req.kit_list)} mẫu, cần CS kiểm tra lại",
                content=f"{req.kit_list}",
            )
        )
        res = {
            "data": "Kits are sent to CS",
            "detail": [],
        }

        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/registrations", status_code=HTTP_200_OK)
async def register_kit_v3(
    registration_body: OperatorRegisterKitV3,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = registration_body.dict()
    logger.info("Operator register a kit with data!")
    # get cs's user id from token claim
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")
    registration_body.customer_support_id = cs_id
    registration_body.customer_support_name = cs_name

    try:
        res, errs = await register_kit_service_v3(register_kit_req=registration_body)
        if errs:
            raise HTTPException(status_code=errs[0], detail=errs[1])
        else:
            return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/migration", status_code=HTTP_200_OK)
async def migrate_kit_v3(
    migrate_body: OperatorMigrateKitV3,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """

    logger.info(f"Operator migrate a kit with data! {migrate_body}")
    # get cs's user id from token claim
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")

    try:
        res = await migrate_kit_service_v3(migrate_kit_req=migrate_body)

        return success_response(res)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/kits/scan-sample/{samplecode}/{version}", status_code=HTTP_200_OK)
async def scan_samplecode_v3(
    samplecode: str,
    version: int,
    product_codes: Optional[List[str]] = Query([]),
    auth=Depends(Auth),
):
    try:
        _, err = await auth.get_token_claims()
    except Exception:
        logger.info("no access token provided")

    try:
        _ = await scan_samplecode_service_v3(
            samplecode=samplecode, version=version, product_codes=product_codes
        )
        kit_info = await get_latest_kit_detail_via_samplecode_v3(samplecode=samplecode)
        return success_response(kit_info)
        # return success_response(data)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/check_by_sample", status_code=HTTP_200_OK)
async def check_kit_list_by_sample_info_v3(
    name: str = Query(None),
    gender: Literal["male", "female"] = Query(None),
    dob: str = Query(None),
    yob: str = Query(None),
    account_id: str = Query(None),
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    try:
        _, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)

    except Exception:
        logger.info("no access token provided")

    logger.info("Check kit list by sample info")
    try:
        data = await get_kit_by_meta_data_v3(
            name=name,
            gender=gender,
            dob=dob,
            yob=yob,
            account_id=account_id,
        )
        return success_response(data)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/all/sub/{subject_id}", status_code=HTTP_200_OK)
async def get_internal_kit_info_v3(
    subject_id: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    order_by: Optional[str] = "created_at",
    auth=Depends(Auth),
) -> dict:
    """
    Get information of a kit with a specific subject_id (id)
    :param id: subject  id (a subject_id)
    :return: Kit information
    """
    """
    Get information of a kit with a specific subject_id (id)
    :param id: subject  id (a subject_id)
    :return: all Kits information belong to subject id
    """
    try:
        try:
            _, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)

        except Exception:
            logger.info("no access token provided")
        logger.info(f"Get all kits belong to subject id {subject_id}")
        size = page_size
        offset = (page_number - 1) * page_size

        kits_list, total = await get_all_kits_subject_via_subject_id_v3(
            subject_id=subject_id,
            size=size,
            offset=offset,
            order_by=order_by,
        )

        subject = None
        for kit in kits_list:
            temp_sub = parse_object_from_dict(Subject, kit)
            temp_sub.pop("created_at")
            temp_sub.pop("updated_at")
            temp_sub.pop("deleted_at")

            if subject and subject != temp_sub:
                logger.info(f"expected sub: {subject}")
                logger.info(f"current sub: {temp_sub}")
                msg = "Inconsistent Subject information !"
                logger.info(msg)
                raise ValueError(msg)
            else:
                subject = temp_sub

        # parsed_data = group_kits_subject_by_sample_collection_time_v3(data)

        res = {
            "data": kits_list,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/samplecode/{samplecode}/cccd", status_code=HTTP_200_OK)
async def get_cccd_via_samplecode(
    samplecode: str,
    auth=Depends(Auth),
):
    try:
        try:
            _, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)

        except Exception:
            logger.info("no access token provided")

        logger.info(f"Get Sample info with id {samplecode}")
        sam_info = await get_sample_detail_via_samplecode_v3(samplecode=samplecode)
        sam_info_dict = convert_rowproxy_to_dict(sam_info)
        data = {"identifier_code": sam_info_dict.get("identifier_code")}
        return success_response(data)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# get all kits belong to identity card given identifier_code
@router.get("/kits/all/cccd/{identifier_code}", status_code=HTTP_200_OK)
async def get_all_kits_belong_to_id_card_v3(
    identifier_code: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    order_by: Optional[str] = "created_at",
    auth=Depends(Auth),
) -> dict:
    try:
        try:
            _, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)

        except Exception:
            logger.info("no access token provided")
        logger.info(f"Get all kits belong to id card {identifier_code}")
        size = page_size
        offset = (page_number - 1) * page_size

        kits_list, total = await get_all_kits_identity_card_via_id_code_v3(
            identifier_code=identifier_code,
            size=size,
            offset=offset,
            order_by=order_by,
        )

        subject = None
        for kit in kits_list:
            temp_sub = parse_object_from_dict(IdentityCard, kit)
            temp_sub.pop("created_at")
            temp_sub.pop("updated_at")
            temp_sub.pop("deleted_at")
            temp_sub.pop("customer_support_id")
            temp_sub.pop("customer_support_name")

            if subject and subject != temp_sub:
                logger.info(f"expected sub: {subject}")
                logger.info(f"current sub: {temp_sub}")
                msg = "Inconsistent Subject information !"
                logger.info(msg)
                raise ValueError(msg)
            else:
                subject = temp_sub

        # parsed_data = group_kits_subject_by_sample_collection_time_v3(data)

        res = {
            "data": kits_list,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/all", status_code=HTTP_200_OK)
async def get_kit_list_ctl(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    gender: Literal["male", "female"] = Query(None),
    nickname: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    workflow: Optional[str] = Query(None),
    product_code: Optional[List[str]] = Query(None),
    sale_pic: Optional[str] = Query(None),
    sale_pic_id: Optional[str] = Query(None),
    account_name: Optional[List[str]] = Query(None),
    batch_number: Optional[int] = Query(None),
    technology: Optional[str] = Query(None),
    sample_collector_name: Optional[str] = Query(None),
    sample_receiver_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    release_start_date: Optional[str] = Query(None),
    release_end_date: Optional[str] = Query(None),
    collection_start_date: Optional[str] = Query(None),
    collection_end_date: Optional[str] = Query(None),
    actual_release_start_date: Optional[str] = Query(None),
    actual_release_end_date: Optional[str] = Query(None),
    include_deleted: Optional[bool] = Query(False),
    nominator: Optional[str] = Query(None),
    order_by: Optional[str] = "created_time",
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    # print("samplecode: ", samplecode)
    logger.info("Getting kit list")
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        data, total = await get_kit_list_v3(
            offset=offset,
            size=size,
            order_by=order_by,
            barcode=barcode,
            samplecode=samplecode,
            email=email,
            phone_number=phone_number,
            name=full_name,
            gender=gender,
            nickname=nickname,
            current_status=current_status,
            workflow=workflow,
            product_code=product_code,
            sale_pic=sale_pic,
            sale_pic_id=sale_pic_id,
            account_name=account_name,
            nominator=nominator,
            batch=batch_number,
            technology=technology,
            sample_collector_name=sample_collector_name,
            sample_receiver_name=sample_receiver_name,
            receipt_start_date=start_date,
            receipt_end_date=end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            collection_start_date=collection_start_date,
            collection_end_date=collection_end_date,
            actual_report_release_start_date=actual_release_start_date,
            actual_report_release_end_date=actual_release_end_date,
            include_deleted=include_deleted,
        )
        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/kits/export", status_code=HTTP_200_OK)
async def export_kit_list_ctl(
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    gender: Literal["male", "female"] = Query(None),
    nickname: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    workflow: Optional[str] = Query(None),
    product_code: Optional[List[str]] = Query(None),
    sale_pic: Optional[str] = Query(None),
    sale_pic_id: Optional[str] = Query(None),
    account_name: Optional[List[str]] = Query(None),
    batch_number: Optional[int] = Query(None),
    technology: Optional[str] = Query(None),
    sample_collector_name: Optional[str] = Query(None),
    sample_receiver_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    release_start_date: Optional[str] = Query(None),
    release_end_date: Optional[str] = Query(None),
    collection_start_date: Optional[str] = Query(None),
    collection_end_date: Optional[str] = Query(None),
    actual_release_start_date: Optional[str] = Query(None),
    actual_release_end_date: Optional[str] = Query(None),
    include_deleted: Optional[bool] = Query(False),
    nominator: Optional[str] = Query(None),
    order_by: Optional[str] = "created_time",
    auth=Depends(Auth),
) -> dict:
    logger.info("Exporting kit list")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        data, total = await get_kit_list_v3(
            offset=None,
            size=None,
            order_by=order_by,
            barcode=barcode,
            samplecode=samplecode,
            email=email,
            phone_number=phone_number,
            name=full_name,
            gender=gender,
            nickname=nickname,
            current_status=current_status,
            workflow=workflow,
            product_code=product_code,
            sale_pic=sale_pic,
            sale_pic_id=sale_pic_id,
            account_name=account_name,
            nominator=nominator,
            batch=batch_number,
            technology=technology,
            sample_collector_name=sample_collector_name,
            sample_receiver_name=sample_receiver_name,
            receipt_start_date=start_date,
            receipt_end_date=end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            collection_start_date=collection_start_date,
            collection_end_date=collection_end_date,
            actual_report_release_start_date=actual_release_start_date,
            actual_report_release_end_date=actual_release_end_date,
            include_deleted=include_deleted,
        )
        excel_io: BytesIO = export_all_kit(data)
        return StreamingResponse(
            excel_io,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=kits.xlsx"},
        )
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/recollection/{first_barcode}", status_code=HTTP_200_OK)
async def recollect_kit_v3(
    first_barcode: str,
    recollection_body: OperatorRecollectKitV3,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = recollection_body.dict()
    logger.info("Operator register a kit with data!")
    # get cs's user id from token claim

    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
    except Exception:
        logger.info("no access token provided")

    try:
        res, errs = await recollect_kit_service_v3(
            first_barcode=first_barcode, recollect_kit_req=recollection_body
        )
        if errs:
            raise HTTPException(status_code=errs[0], detail=errs[1])
        else:
            return success_response(res)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/sample/{samplecode}", status_code=HTTP_200_OK)
async def get_internal_kit_info_via_samplecode_v3(samplecode: str) -> dict:
    """
    Get information of a Sample with a specific samplecode (id)
    :param id: Sample  id (a samplecode)
    :return: Sample information
    """
    """
    Get information of a Sample with a specific samplecode (id)
    :param id: Sample  id (a samplecode)
    :return: Sample information
    """
    try:
        logger.info(f"Get Sample info with id {samplecode}")
        sam_info = await get_sample_detail_via_samplecode_v3(samplecode=samplecode)
        return success_response(sam_info)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/uuid/{kit_uuid}", status_code=HTTP_200_OK)
async def get_internal_kit_info_v3(kit_uuid: str) -> dict:
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    try:
        logger.info(f"Get kit info with id {kit_uuid}")
        kit_info = await get_kit_detail_via_kit_uuid_v3(kit_uuid=kit_uuid)
        return success_response(kit_info)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/barcode/{barcode}", status_code=HTTP_200_OK)
async def get_internal_kit_info_v3(barcode: str) -> dict:
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    try:
        logger.info(f"Get kit info with id {barcode}")
        kit_info = await get_kit_detail_via_barcode_v3(barcode=barcode)
        return success_response(kit_info)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/kits/registrations/{barcode}", status_code=HTTP_200_OK)
async def get_internal_kit_info_v3(barcode: str) -> dict:
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    """
    Get information of a kit with a specific barcode (id)
    :param id: Kit  id (a barcode)
    :return: Kit information
    """
    try:
        logger.info(f"Get kit info with id {barcode}")
        kit_info = await get_kit_detail_via_barcode_v3(barcode=barcode)
        return success_response(kit_info)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/upgrade/{version}", status_code=HTTP_200_OK)
async def upgrade_kit_v3(
    upgradation_body: OperatorUpgradeKitV3,
    version: int,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new kit with its associated information, do not allow to
    register a registered kit, and an user can register multiple kits
    :param registration_body: Request body
    :return: Kit info with an initial status
    """
    data = upgradation_body.dict()
    logger.info("Operator register a kit with data!")
    # get cs's user id from token claim

    try:
        _, err = await auth.get_token_claims()
    except Exception:
        logger.info("no access token provided")

    try:
        # print("Valid Upgrade Body !")
        res, errs = await upgrade_kit_service_v3(
            upgrade_kit_req=upgradation_body, version=version
        )
        if errs:
            raise HTTPException(status_code=errs[0], detail=errs[1])
        else:
            return success_response(res)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/kits/export/send-to-lab", status_code=HTTP_200_OK)
async def export_kit_list_ctl(
    req: SendToLabUpdate,
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    gender: Literal["male", "female"] = Query(None),
    nickname: Optional[str] = Query(None),
    current_status: Optional[str] = Query(None),
    workflow: Optional[str] = Query(None),
    product_code: Optional[List[str]] = Query(None),
    sale_pic: Optional[str] = Query(None),
    sale_pic_id: Optional[str] = Query(None),
    account_name: Optional[List[str]] = Query(None),
    batch_number: Optional[int] = Query(None),
    technology: Optional[str] = Query(None),
    sample_collector_name: Optional[str] = Query(None),
    sample_receiver_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    release_start_date: Optional[str] = Query(None),
    release_end_date: Optional[str] = Query(None),
    collection_start_date: Optional[str] = Query(None),
    collection_end_date: Optional[str] = Query(None),
    actual_release_start_date: Optional[str] = Query(None),
    actual_release_end_date: Optional[str] = Query(None),
    include_deleted: Optional[bool] = Query(False),
    nominator: Optional[str] = Query(None),
    order_by: Optional[str] = "created_time",
    auth=Depends(Auth),
) -> dict:
    logger.info("Exporting kit list")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        data, total = await get_kit_list_v3(
            offset=None,
            size=None,
            order_by=order_by,
            barcode=barcode,
            samplecode=samplecode,
            email=email,
            phone_number=phone_number,
            name=full_name,
            gender=gender,
            nickname=nickname,
            current_status=current_status,
            workflow=workflow,
            product_code=product_code,
            sale_pic=sale_pic,
            sale_pic_id=sale_pic_id,
            account_name=account_name,
            nominator=nominator,
            batch=batch_number,
            technology=technology,
            sample_collector_name=sample_collector_name,
            sample_receiver_name=sample_receiver_name,
            receipt_start_date=start_date,
            receipt_end_date=end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            collection_start_date=collection_start_date,
            collection_end_date=collection_end_date,
            actual_report_release_start_date=actual_release_start_date,
            actual_report_release_end_date=actual_release_end_date,
            include_deleted=include_deleted,
        )
        if req.kit_list:
            export_data = [item for item in data if item.get('barcode') in req.kit_list]
            excel_io: BytesIO = export_all_kit_send_to_lab(export_data)
        else:
            excel_io: BytesIO = export_all_kit_send_to_lab(data)
        today_str = datetime.now().strftime("%d_%m_%Y")
        filename = f"Send_to_lab_{today_str}.xlsx"

        return StreamingResponse(
            excel_io,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"},
        )
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/kits/send-to-lab")
async def send_samples_to_lab_v3(
    req: SendToLabUpdate,
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    gender: Literal["male", "female"] = Query(None),
    nickname: Optional[str] = Query(None),
    filter_status_list: Optional[List[str]] = Query(None),
    current_status: Optional[str] = Query(None),
    workflow: Optional[str] = Query(None),
    product_code: Optional[List[str]] = Query(None),
    sale_pic: Optional[str] = Query(None),
    sale_pic_id: Optional[str] = Query(None),
    account_name: Optional[List[str]] = Query(None),
    batch_number: Optional[int] = Query(None),
    technology: Optional[str] = Query(None),
    sample_collector_name: Optional[str] = Query(None),
    sample_receiver_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    release_start_date: Optional[str] = Query(None),
    release_end_date: Optional[str] = Query(None),
    collection_start_date: Optional[str] = Query(None),
    collection_end_date: Optional[str] = Query(None),
    actual_release_start_date: Optional[str] = Query(None),
    actual_release_end_date: Optional[str] = Query(None),
    sample_collector_unit_name: Optional[str] = Query(None),
    include_deleted: Optional[bool] = Query(False),
    nominator: Optional[str] = Query(None),
    order_by: Optional[str] = "updated_at",
    is_return_page: Optional[int] = Query(None),
    auth=Depends(Auth),
) -> dict:
    """
    # Query sampleV3 -- from -- barcode of kitV3 -- to validate barcode
    """
    try:
        token_claims, err = await auth.get_token_claims()
        user_id = token_claims.get("sub")
        if len(req.kit_list) == 0 and is_return_page == 0:
            kit_list = await get_sample_list_v4(
            order_by=order_by,
            barcode=barcode,
            samplecode=samplecode,
            email=email,
            phone_number=phone_number,
            name=full_name,
            gender=gender,
            nickname=nickname,
            filter_status_list=filter_status_list,
            current_status=current_status,
            workflow=workflow,
            product_code=product_code,
            sale_pic=sale_pic,
            sale_pic_id=sale_pic_id,
            account_name=account_name,
            nominator=nominator,
            batch=batch_number,
            technology=technology,
            sample_collector_name=sample_collector_name,
            sample_receiver_name=sample_receiver_name,
            receipt_start_date=start_date,
            receipt_end_date=end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            collection_start_date=collection_start_date,
            collection_end_date=collection_end_date,
            actual_report_release_start_date=actual_release_start_date,
            actual_report_release_end_date=actual_release_end_date,
            sample_collector_unit_name=sample_collector_unit_name,
            include_deleted=include_deleted,
        )
        else:
            kit_list = req.kit_list

        failed_kits, updated_kits = await send_kit_info_to_lab_v3(
            kit_list=kit_list, lab_receipt_date=req.lab_receipt_date
        )
        await send_notification_to_group(
            req=NotificationReq(
                group_id="lims",
                sender_id=user_id,
                # recipient_id=
                title=f"CS bàn giao {len(kit_list)} mẫu cho Lab",
                content=f"{kit_list}",
            )
        )
        res = {
            "data": {
                "failed_kits": failed_kits,
                "updated_kits": updated_kits,
            },
            "detail": [],
        }

        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, tags=["user_kit"])
