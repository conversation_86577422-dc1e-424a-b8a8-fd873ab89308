{"ngayGioGiaoDich": "20042024190424", "maGiaoDich": "efzdea29-4abc-55e9-zzzz-8849ztt88899", "loaiYeuCau": "1", "mucDichYeuCau": "them moi adn", "quyTacDoiSanh": "CODIS-2017", "maQuyTacDoiSanh": "efzdea29-4abc-55e9-zzzz-8849ztt88899", "data": {"hoTenCongDan": "Phan AAAAA HNNNNNNN", "ngayThangNamSinh": "25031981", "vanTay": "base64", "anhMat": "base64", "mongMat": "base64", "gioiTinh": "1", "soDinhDanh": "187951678899", "thongTinXuLyMau": {"thuNhanMau": {"maThuNhan": "189651678699", "donViThuNhanMau": "GST", "loaiXetNghiem": "loai 1", "ngayGioThuThapMau": "20042024190434", "noiThuThapMau": "HN AAAAAAAAAA", "nhanVienLayMau": {"hoTenNhanVien": "ng<PERSON><PERSON> van a", "chucVu": "QA", "hocVi": "dai hoc", "soDinhDanh": "188651680665"}, "nhanVienGhiHoSo": {"hoTenNhanVien": "ng<PERSON><PERSON> van a", "chucVu": "QA", "hocVi": "dai hoc", "soDinhDanh": "188651680665"}, "nhanVienLuuMau": {"hoTenNhanVien": "ng<PERSON><PERSON> van a", "chucVu": "QA", "hocVi": "dai hoc", "soDinhDanh": "188651680665"}}, "vanChuyenMau": {"maVanChuyen": "12345678", "nhietDoChuyenGiao": "", "tinhTrangNiemPhong": true, "ngayGioChuyenGiao": "20042024190434", "diaDiemChuyenGiao": "Quang Nam", "donViBanGiao": "GST", "donViNhanMau": "GST", "nhanVienBanGiao": {"hoTenNhanVien": "ng<PERSON><PERSON> van a", "chucVu": "QA", "hocVi": "dai hoc", "soDinhDanh": "188651680665"}, "nhanVienNhanMau": {"hoTenNhanVien": "ng<PERSON><PERSON> van a", "chucVu": "QA", "hocVi": "dai hoc", "soDinhDanh": "188651680665"}, "khac": "ghi chu khac"}, "quyTrinhXetNghiem": {"maXetNghiem": "212312312123", "nhietDoLuuTru": "37", "cacBuocXetNghiem": {"cacbuocthuchien": [{"tenBuoc": "buoc 1", "nhanVienThucHien": {"hoTenNhanVien": "ng<PERSON><PERSON> van a", "chucVu": "QA", "hocVi": "dai hoc", "soDinhDanh": "188651680665"}}, {"tenBuoc": "buoc 2", "nhanVienThucHien": {"hoTenNhanVien": "ng<PERSON><PERSON> van a", "chucVu": "QA", "hocVi": "dai hoc", "soDinhDanh": "188651680665"}}]}}}, "duLieuADN": {"tenFileADN": "sample_486703086125/data.json", "congNghe": "STR", "duLieuChinh": true, "sangerTenFilePrimer": "ten file", "microarrayTenKit": "ten kit", "ceTenKit": "ten kit", "ceTenFileLadder": "ten kit", "ngsTenFileAdapter": "ten file ", "ngsTenFileCapturekit": "ten file "}, "mauTrung": {"soDinhDanh": ""}}}