from urllib import response
import requests
from fastapi import API<PERSON>outer, <PERSON><PERSON><PERSON>, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
    HTTP_200_OK,
    HTTP_202_ACCEPTED,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Request - model 
# /platform/request
# /platform/requests/{id}
# _requests - plural
# _request - function name
# request - single
# create_request_service
# update_request_service
# delete_request_by_id_service
# platform_request

from ...models.models import Request

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email,
    get_role_by_brearer_token
)

from ...cruds.request import *


from ...schemas.request import *

from ...schemas.adn_result_validator import *

from ...services.request import *

from ...services.adn_integration import *

from ...services.adn_result import *

router = APIRouter()

@router.post("/platform/request/get-gene-data", status_code=HTTP_200_OK)
async def admin_create_request(
  request_body: addVNeIDRequest,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = request_body.dict()
        data['customer_support_name'] = cs_name
        data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Request with data {data}")

        response = await init_vneid_request_service(requestBody=data)
        return response
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.post("/platform/request/internal/get-gene-data", status_code=HTTP_200_OK)
async def admin_create_request(
  request_body: addInternalRequest,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = request_body.dict()
        data['customer_support_name'] = cs_name
        data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Request with data {data}")

        response = await init_vneid_request_service(requestBody=data)
        return response
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


async def generate_adn_result(data: dict):
    """
    "request_id":"1db99129-292c-4395-b850-cb2ff7537f1c",
    "samplecode":"0FZ9F8",
    "barcode":"882998210109",
    "adn_type": "STR",
    "returnRawADN": false
    """
    tracking_detail = await get_kit_tracking_detail_w_samplecode_and_barcode(samplecode=data.get('samplecode'), 
                                                                                barcode=data.get('barcode'))
    
    duLieuADN, adn_integration_dict = await generate_result_with_codes_and_type_service(**data)
    
    request_detail, identity_card_detail = await get_request_and_cccd_detail_w_request_id(request_id=data.get('request_id'))
    
    adn_result = {
        **request_detail,
        "data":{
            **identity_card_detail,
            "thongTinHanhTrinhMau": tracking_detail,
            "duLieuADN": duLieuADN
        }
    }
    
    try:
        _ = ADNResultValidator(**adn_result)
        logger.info(f"Validate ADN Result successful!")

    except ValidationError as e:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(str(e))
        raise HTTPException(status_code=http_code, detail=errs)
    
    return adn_result

async def generate_request_result_for_a_specific_sample(request_id: str, collect_date: str, adn_types: List[str]):
    
    # Check for valid and complete status of adn_results
    adn_integration_dicts, samplecode, not_ready_lst = await get_adn_integrations_for_a_specific_sample(
                                                                request_id=request_id,
                                                                collect_date=collect_date,
                                                                adn_types=adn_types)
    
    if not samplecode:
        msg = "No sample has been found!"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(msg)
        raise HTTPException(status_code=http_code, detail=errs)
    
    if len(not_ready_lst) > 0:
        msg = f"The request has been accepted for processing, but adn results for following barcodes have not fully available yet: {not_ready_lst}"
        http_code = HTTP_409_CONFLICT
        errs = failure_response(msg)
        raise HTTPException(status_code=http_code, detail=errs)
    
    logger.info(f"ADN Result for request with samplecode {samplecode} is ready to generate!")
        
    adn_results = []
    adn_type_lst = []
    for adn_integration in adn_integration_dicts:

        if adn_integration.get('samplecode') != samplecode:
            msg = f"Adn Integration with samplecode {adn_integration.get('samplecode')}, but request's samplecode is {samplecode}!"
            http_code = HTTP_409_CONFLICT
            errs = failure_response(msg)
            raise HTTPException(status_code=http_code, detail=errs)
        
        data = {
            "request_id": request_id,
            "samplecode":samplecode,
            "barcode":adn_integration.get('barcode'),
            "adn_type": adn_integration.get('adn_type'),
            "returnRawADN": False
        } 
        
        adn_result = await generate_adn_result(data=data)
        adn_results.append(adn_result)
        adn_type_lst.append(adn_integration.get('adn_type'))
        logger.info(f"ADN Result for samplecode {samplecode} - barcode {adn_integration.get('barcode')} - adn_type {adn_integration.get('adn_type')} is generated!")
    
    ## combine adn results  ##
    loaiXetnghiem = '_'.join(sorted(adn_type_lst))
    
    request_result = await combine_all_adn_results_for_a_specific_sample(
        adn_results=adn_results,
        samplecode=samplecode,
        collect_date=collect_date,
        loaiXetnghiem=loaiXetnghiem
    )
    
    return request_result

@router.post("/platform/requests/result", status_code=HTTP_200_OK)
async def admin_create_request_result_for_a_specific_sample(
    request_body: requestResult,
    auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        # return response
        request_result = await generate_request_result_for_a_specific_sample(
                                    request_id=request_body.request_id, 
                                    collect_date=request_body.collect_date, 
                                    adn_types=request_body.adn_types)

        return success_response(request_result)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    

@router.post("/platform/request/adn-result", status_code=HTTP_200_OK)
async def admin_create_request_adn_result(
  request_body: returnADNResult,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = request_body.dict()
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Request with data {data}")
        # return response
        adn_result = await generate_adn_result(data=data)

        return success_response(adn_result)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



@router.post("/platform/request", status_code=HTTP_200_OK)
async def admin_create_request(
  request_body: AddRequest,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = request_body.dict()
        data['customer_support_name'] = cs_name
        data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Request with data {data}")

        data = await create_request_service(data)
        return success_response(data)
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.get("/platform/requests/all", status_code=HTTP_200_OK)
async def admin_get_requests(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  id: Optional[str] = Query(None),
  agency_name: Optional[str] = Query(None),
  request_code: Optional[str] = Query(None),
  identifier_code: Optional[str] = Query(None),
  customer_name: Optional[str] = Query(None),
  customer_phone: Optional[str] = Query(None),
  payment_status: Optional[str] = Query(None),
  status: Optional[str] = Query(None),
  start_collect_date: Optional[str] = Query(None),
  end_collect_date: Optional[str] = Query(None),
  start_request_date: Optional[str] = Query(None),
  end_request_date: Optional[str] = Query(None),
  start_response_date: Optional[str] = Query(None),
  end_response_date: Optional[str] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  auth=Depends(Auth),
):
        
    logger.info(f"Filter request info")
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size
    
    sub = token_claims.get('sub')
    bearer_token_credential=auth.bearer_token.credentials
    role_list = await get_role_by_brearer_token(sub=sub,credential=bearer_token_credential)
    customer_support_id = sub
    if 'admin' in role_list:
        customer_support_id = None

    try:
        data, total = await get_all_requests_service(
            size=size,
            offset=offset,
            id=id,
            agency_name=agency_name,
            request_code=request_code,
            identifier_code=identifier_code,
            customer_name=customer_name,
            customer_phone=customer_phone,
            payment_status=payment_status,
            status=status,
            customer_support_id=customer_support_id,
            start_collect_date=start_collect_date,
            end_collect_date=end_collect_date,
            start_request_date=start_request_date,
            end_request_date=end_request_date,
            start_response_date=start_response_date,
            end_response_date=end_response_date,
            include_deleted=include_deleted
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.put("/platform/requests/{id}/review/{adn_integration_id}", status_code=HTTP_200_OK)
async def admin_review_adn_result(
    id: str,
    adn_integration_id: str,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        response = await review_request_result_by_ids_service(request_id=id, adn_integration_id=adn_integration_id)
        
        if not response:
            err = "Invalid adn integration id!"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        
        return success_response(response)
            
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.post("/platform/requests/{id}/sent/{adn_integration_id}/{adn_type}/{exclude_raw}", status_code=HTTP_200_OK)
async def admin_send_adn_result_to_partner(
    id: str,
    adn_integration_id: str,
    adn_type: str,
    exclude_raw: int,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)
        """
        "request_id":"1db99129-292c-4395-b850-cb2ff7537f1c",
        "samplecode":"0FZ9F8",
        "barcode":"882998210109",
        "adn_type": "STR",
        "returnRawADN": false 
        
        1 - false
        0 - true
        """
        
        curr_adn_integration = await get_adn_integration_by_id_service(id=adn_integration_id) 
        
        data = {
            "request_id": id,
            "samplecode": curr_adn_integration.samplecode,
            "barcode": curr_adn_integration.barcode,
            "adn_type": config['AGENCY_CONNECTION']['ADN_TYPE'][adn_type.upper()],
            "returnRawADN": False if exclude_raw else True
        }
                
        adn_result = await generate_adn_result(data=data)

        response_entity = await send_request_result_by_ids_service(request_id=id, adn_integration_id=adn_integration_id, adn_result=adn_result)
        
        if not response_entity:
            err = "Invalid adn integration id!"
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        
        return adn_result
        
    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/platform/requests/{id}", status_code=HTTP_200_OK)
async def admin_get_request_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        request_detail = await get_request_by_id_service(id)
        
        return success_response(request_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.put("/platform/requests/{id}", status_code=HTTP_200_OK)
async def admin_update_request_info_by_id(
  id: str,
  body: UpdateRequestDetail,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        updated_request = await update_request_service(id=id, data=data)
        
        return success_response(updated_request)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.put("/platform/requests/id/{request_id}/sync-kit", status_code=HTTP_200_OK)
async def admin_update_request_info_by_id(
  request_id: str,
  auth=Depends(Auth),
) -> dict:
    
    """
    IF no request created with identifier_code, return 404 NOT FOUND
    
    IF request's status is "SAMPLE_NOT_FOUND", then check whether new kits are created for request
    SAMPLE_NOT_FOUND -> SAMPLE_FOUND
    
    IF releated kits' status are different than "REGISTERED", then update request's status
    SAMPLE_FOUND -> IN_PROGRESS
    
    IF releated kits' status are "REVIEWED" or "COMPLETED", then update request's status
    IN_PROGRESS -> REQUIRE_REVIEW -> REVIEWED
    
    IF request's presigned links are not empty/null, call TECA request & update request's status:
    REVIEWED -> SENT
    
    """

    try:
        token_claims, err = await auth.get_token_claims()
    except Exception as e:
        logger.info("no access token provided")
        
    
    try:
            
        sync_kit_request = await sync_kit_request_w_request_id_service(request_id=request_id)
        
        return success_response(sync_kit_request)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# ALL SKIP IT
# CCCD -> ALL REQUESTs sync
#DONE
@router.put("/platform/requests/{identifier_code}/sync-kit", status_code=HTTP_200_OK)
async def admin_update_request_info_by_id(
  identifier_code: str,
  auth=Depends(Auth),
) -> dict:
    
    """
    IF no request created with identifier_code, return 404 NOT FOUND
    
    IF request's status is "SAMPLE_NOT_FOUND", then check whether new kits are created for request
    SAMPLE_NOT_FOUND -> SAMPLE_FOUND
    
    IF releated kits' status are different than "REGISTERED", then update request's status
    SAMPLE_FOUND -> IN_PROGRESS
    
    IF releated kits' status are "REVIEWED" or "COMPLETED", then update request's status
    IN_PROGRESS -> REQUIRE_REVIEW -> REVIEWED
    
    IF request's presigned links are not empty/null, call TECA request & update request's status:
    REVIEWED -> SENT
    
    """

    try:
        token_claims, err = await auth.get_token_claims()
    except Exception as e:
        logger.info("no access token provided")
        
    
    try:
            
        sync_kit_requests = await sync_kit_request_service(identifier_code=identifier_code)
        
        return success_response(sync_kit_requests)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



# #DONE
# @router.delete("/platform/requests/{id}", status_code=HTTP_200_OK)
# async def admin_delete_request_by_id(
#   id: str,
#   auth=Depends(Auth),
# ) -> dict:

#     try:
#         logger.info(f"Delete a adn integration type with id {id}")
#         _, err = await auth.get_token_claims()
#         if err:
#             errs = failure_response(err['err_msg'])
#             raise HTTPException(status_code=err['err_code'], detail=errs)


#         res = await delete_request_by_id_service(id)
#         return success_response(res)

    
#     except HTTPException as err:
#         raise err
#     except requests.exceptions.ConnectionError as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.Timeout as err:
#         http_code = HTTP_408_REQUEST_TIMEOUT
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.RequestException as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except ValueError as err:
#         http_code = HTTP_400_BAD_REQUEST
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except Exception as err:
#         http_code = HTTP_422_UNPROCESSABLE_ENTITY
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, tags=["platform_request"])