import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON>, FastAP<PERSON>, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# AdnIntegration - model 
# /platform/adn-integration
# /platform/adn-integrations/{id}
# _adn_integrations - plural
# _adn_integration - function name
# adn_integration - single
# create_adn_integration_service
# update_adn_integration_service
# delete_adn_integration_by_id_service
# platform_adn_integration

from ...models.models import AdnIntegration

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.adn_integration import *


from ...schemas.adn_integration import *

from ...services.adn_integration import *

router = APIRouter()

@router.post("/platform/adn-integration", status_code=HTTP_200_OK)
async def admin_create_adn_integration(
  agency_body: AddAdnIntegration,
  auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        data = agency_body.dict()
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Sale AdnIntegration with data {data}")

        data, err = await create_adn_integration_service(data)
        return success_response(data)
        
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.get("/platform/adn-integrations/all", status_code=HTTP_200_OK)
async def admin_get_adn_integrations(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    order_by: Optional[str]="ai.created_at",
    adn_integration_id: Optional[str] = None,
    samplecode: Optional[str] = None,
    barcode: Optional[str] = None,
    method: Optional[str] = None,
    status: Optional[str] = None,
    include_deleted: Optional[bool] = False,
    auth=Depends(Auth),
):
    
    logger.info(f"Filter adn integration info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size # 10
    offset = (page_number - 1) * page_size # 0

    try:
        data, total = await get_all_adn_integrations_service(
            size=size,
            offset=offset,
            order_by=order_by,
            adn_integration_id=adn_integration_id,
            samplecode=samplecode,
            barcode=barcode,
            method=method,
            status=status,
            include_deleted=include_deleted
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# get_adn_integration_detail_via_barcode_v3
@router.get("/platform/adn-integrations/barcode/{barcode}", status_code=HTTP_200_OK)
async def admin_get_adn_integration_info_by_barcode(
  barcode: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        adn_integration_detail, _ = await get_all_adn_integrations_service(barcode=barcode)
        return success_response(adn_integration_detail[0])

    
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    pass

#DONE
@router.get("/platform/adn-integrations/id/{id}", status_code=HTTP_200_OK)
async def admin_get_adn_integration_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        adn_integration_detail, _ = await get_all_adn_integrations_service(adn_integration_id=id)
        return success_response(adn_integration_detail[0])

    
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
 
#DONE
@router.put("/platform/adn-integrations/{id}/status", status_code=HTTP_200_OK)
async def admin_update_adn_integration_info_by_id(
  id: str,
  body: UpdateAdnIntegerationStatus,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        _ = await update_adn_integration_service(id, data)
        
        adn_integration_detail, _ = await get_all_adn_integrations_service(adn_integration_id=id)
        return success_response(adn_integration_detail[0])
        
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.delete("/platform/adn-integrations/{id}", status_code=HTTP_200_OK)
async def admin_delete_adn_integration_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:

    try:
        logger.info(f"Delete a adn integration type with id {id}")
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)


        res = await delete_adn_integration_by_id_service(id)
        return success_response(res)

    
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

def init_app(app: FastAPI):
    app.include_router(router, tags=["platform_adn_integration"])