import requests
from fastapi import <PERSON><PERSON><PERSON><PERSON>, FastAP<PERSON>, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Agency - model 
# /platform/agency
# /platform/agencies/{id}
# _agencies - plural
# _agency - function name
# agency - single
# create_agency_service
# update_agency_service
# delete_agency_by_id_service
# platform_agency

from ...models.models import Agency

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.agency import *


from ...schemas.agency import *

from ...services.agency import *


router = APIRouter()

@router.post("/platform/agency", status_code=HTTP_200_OK)
async def admin_create_agency(
  agency_body: AddAgency,
  auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        data = agency_body.dict()
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Partner Agency with data {data}")

        data = await create_agency_service(data)
        return success_response(data)
        
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/platform/agencies/all", status_code=HTTP_200_OK)
async def admin_get_agencies(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  name: Optional[str] = Query(None),
  type: Optional[str] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  auth=Depends(Auth),
):
    
    logger.info(f"Filter agencies info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_all_agencies_service(
            size=size,
            offset=offset,
            name=name,
            type=type,
            include_deleted=include_deleted
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/platform/agencies/{id}", status_code=HTTP_200_OK)
async def admin_get_agency_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        agency_detail = await get_agency_by_id_service(id=id)
        return success_response(agency_detail)

    
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.put("/platform/agencies/{id}", status_code=HTTP_200_OK)
async def admin_update_agency_info_by_id(
  id: str,
  body: UpdateAgencyDetail,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        updated_agency = await update_agency_service(id, data)
        
        return success_response(updated_agency)
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.delete("/platform/agencies/{id}", status_code=HTTP_200_OK)
async def admin_delete_agency_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:

    try:
        logger.info(f"Delete a sale agency with id {id}")
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)


        res = await delete_agency_by_id_service(id)
        return success_response(res)

    
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

def init_app(app: FastAPI):
    app.include_router(router, tags=["platform_agency"])