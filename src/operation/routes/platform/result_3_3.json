{
    "ngayGioGiaoDich": "27062024152400",
    "maGiaoDich": "1db99129-292c-4395-b850-cb2ff7537f1c",
    "data": {
        "hoTenCongDan": "<PERSON>uy<PERSON><PERSON>",
        "ngayThangNamSinh": "05/04/1997",
        "soDinhDanh": "",
        "maDvXN": 1,
        "anhMat": "",
        "thongTinHanhTrinhMau": {
            "thuNhanMau": {
                "maThuNhan": "0FZ9F8",
                "donViThuNhanMau": 5,
                "loaiXetNghiem": "Dịch vụ định danh công dân",
                "ngayGioThuThapMau": "24062024150700",
                "noiThuThapMau": 48,
                "nhanVienLayMau": {
                    "hoTenNhanVien": "Trần Đà Nẵng",
                    "soDinhDanh": "052190000222"
                },
                "nhanVienGhiHoSo": {
                    "hoTenNhanVien": "Trần <PERSON>ă<PERSON>",
                    "soDinhDanh": "052190000223"
                },
                "nhanVienLuuMau": {
                    "hoTenNhanVien": "Trần Đà Nẵng",
                    "soDinhDanh": "052190000222"
                }
            },
            "vanChuyenMau": {
                "maVanChuyen": "893050028378893",
                "nhietDoChuyenGiao": "3",
                "tinhTrangNiemPhong": false,
                "ngayGioChuyenGiao": "01082024100800",
                "diaDiemChuyenGiao": 79,
                "donViBanGiao": "Bệnh viện Sư Vạn Hạnh",
                "donViVanChuyen": "GeneStory - Hà Nội",
                "donViNhanMau": "VSK ĐÀ NẴNG",
                "nhanVienBanGiao": {
                    "hoTenNhanVien": "Nguyễn Hồng Ngọc",
                    "soDinhDanh": "032009008802"
                },
                "nhanVienNhanMau": {
                    "hoTenNhanVien": "Trần Văn Đà",
                    "soDinhDanh": "052190000223"
                },
                "khac": "Updated"
            },
            "quyTrinhXetNghiem": {
                "maXetNghiem": "240036",
                "nhietDoLuuTru": "4",
                "cacBuocXetNghiem": {
                    "cacBuocThucHien": [
                        {
                            "tenBuoc": "Nhận mẫu đầu vào",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Nguyễn Thị Ly",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008803"
                            }
                        },
                        {
                            "tenBuoc": "Bảo quản mẫu",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Nguyễn Thị Ly",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008803"
                            }
                        },
                        {
                            "tenBuoc": "Tách chiết",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Dương Thị Lệ",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008804"
                            }
                        },
                        {
                            "tenBuoc": "Chạy PCR nhân đoạn",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Triệu Thị Nguyệt",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008805"
                            }
                        },
                        {
                            "tenBuoc": "Chạy PCR sequencing",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Triệu Thị Nguyệt",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008805"
                            }
                        },
                        {
                            "tenBuoc": "Chạy Sanger",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Triệu Thị Nguyệt",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008805"
                            }
                        },
                        {
                            "tenBuoc": "Chạy phân tích mtADN",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Phạm Ngọc Nam",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008806"
                            }
                        },
                        {
                            "tenBuoc": "Phê duyệt Kết Qủa ",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Phê Văn Duyệt",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008807"
                            }
                        },
                        {
                            "tenBuoc": "Lưu trữ mẫu",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Triệu Thị Nguyệt",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008805"
                            }
                        }
                    ]
                }
            }
        },
        "duLieuADN": {
            "tenFileADN": "240036.zip",
            "noiDungFileADN": "UEsDBBQACAAIAHCJBlkAAAAAAAAAAIcFAAAeACAAMjQwMDM2LnN0YXRpc3RpY192YXJpYW50cy5qc29uVVQNAAfU9rFm1PaxZt32sWZ1eAsAAQToAwAABOgDAACr5lIAAiUjEwMDYzMlK4VqMB8s5hFmiCIAFizOKygGikajiIJANYYIWH1BPki5oZmBpbEOdhVFqWlAFUohSjjki1MLQfLOuOTTMnNSsboIrsLIAOg9cyPjeIgv44Eec1PCqjwWQ7SWC7t8rQ5yQBlRK6CMzAgEkyOBYHKnZjAZUTmYjKkVTCaWhviDCWdqgQYTztRGTjAZB5EZTKhuUEpJzUktyczPIz08TI0oTDa6AxQemLYS8KcJZfE+UP7ElT24IGQtAFBLBwi9I0/MxQAAAIcFAABQSwMEFAAIAAgAWYkGWQAAAAAAAAAA/wIAABkAIAAyNDAwMzYuY29uc2Vuc3VzX2h2LmZhc3RhVVQNAAeq9rFmqvaxZsD2sWZ1eAsAAQToAwAABOgDAADNkjsOAjEMRPu9CwXQrzSawlRU1t7/KPjZEVSsKHES5zNO4sxkfxzX7Xlmlm0pUpkh58yrE9XhAgALqa6QchkVQiMypN5b64GrJbHkPo8YsNkrrO9LEK45Te6fbdsfx+2c26+WcFIEFUOZbj4TNiCbCt/R1BWpiRgNFOnhUDPpFkIoR6nRu4VbjoihfCpntFZczNkISOQoUdPRY0KxBXkkw35g5L7xbfoQr7ckW3Ea33kTkvPofty6I+bzEdBfTKtcLuQaXnFqvKmwPinChryyrcH2AlBLBwjyKSxT1AAAAP8CAABQSwECFAMUAAgACABwiQZZvSNPzMUAAACHBQAAHgAgAAAAAAAAAAAAtIEAAAAAMjQwMDM2LnN0YXRpc3RpY192YXJpYW50cy5qc29uVVQNAAfU9rFm1PaxZt32sWZ1eAsAAQToAwAABOgDAABQSwECFAMUAAgACABZiQZZ8iksU9QAAAD/AgAAGQAgAAAAAAAAAAAAtIExAQAAMjQwMDM2LmNvbnNlbnN1c19odi5mYXN0YVVUDQAHqvaxZqr2sWbA9rFmdXgLAAEE6AMAAAToAwAAUEsFBgAAAAACAAIA0wAAAGwCAAAAAA==",
            "congNghe": "Sanger",
            "tenKit": "BigDye™ Terminator v3.1 Cycle Sequencing Kit",
            "loaiDuLieu": "mtADN"
        }
    }
},
{
    "ngayGioGiaoDich": "27062024152400",
    "maGiaoDich": "1db99129-292c-4395-b850-cb2ff7537f1c",
    "data": {
        "hoTenCongDan": "Nguyễn Phúc Hiệu",
        "ngayThangNamSinh": "05/04/1997",
        "soDinhDanh": "",
        "maDvXN": 1,
        "anhMat": "",
        "thongTinHanhTrinhMau": {
            "thuNhanMau": {
                "maThuNhan": "0FZ9F8",
                "donViThuNhanMau": 5,
                "loaiXetNghiem": "Dịch vụ định danh công dân",
                "ngayGioThuThapMau": "24062024150700",
                "noiThuThapMau": 48,
                "nhanVienLayMau": {
                    "hoTenNhanVien": "Trần Đà Nẵng",
                    "soDinhDanh": "052190000222"
                },
                "nhanVienGhiHoSo": {
                    "hoTenNhanVien": "Trần Văn Đà",
                    "soDinhDanh": "052190000223"
                },
                "nhanVienLuuMau": {
                    "hoTenNhanVien": "Trần Đà Nẵng",
                    "soDinhDanh": "052190000222"
                }
            },
            "vanChuyenMau": {
                "maVanChuyen": "893050028378893",
                "nhietDoChuyenGiao": "3",
                "tinhTrangNiemPhong": false,
                "ngayGioChuyenGiao": "01082024100800",
                "diaDiemChuyenGiao": 79,
                "donViBanGiao": "Bệnh viện Sư Vạn Hạnh",
                "donViVanChuyen": "GeneStory - Hà Nội",
                "donViNhanMau": "VSK ĐÀ NẴNG",
                "nhanVienBanGiao": {
                    "hoTenNhanVien": "Nguyễn Hồng Ngọc",
                    "soDinhDanh": "032009008802"
                },
                "nhanVienNhanMau": {
                    "hoTenNhanVien": "Trần Văn Đà",
                    "soDinhDanh": "052190000223"
                },
                "khac": "Updated"
            },
            "quyTrinhXetNghiem": {
                "maXetNghiem": "240027",
                "nhietDoLuuTru": "6",
                "cacBuocXetNghiem": {
                    "cacBuocThucHien": [
                        {
                            "tenBuoc": "Nhận mẫu đầu vào",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Nguyễn Thị Ly",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008803"
                            }
                        },
                        {
                            "tenBuoc": "Bảo quản mẫu",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Nguyễn Thị Ly",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008803"
                            }
                        },
                        {
                            "tenBuoc": "Tách chiết",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Nguyễn Thị Ly",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008803"
                            }
                        },
                        {
                            "tenBuoc": "Chạy PCR",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Dương Thị Lệ",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008804"
                            }
                        },
                        {
                            "tenBuoc": "Chạy CE",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Dương Thị Lệ",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008804"
                            }
                        },
                        {
                            "tenBuoc": "Phân tích và Kiểm soát chất lượng Genemapper",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Triệu Thị Nguyệt",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008805"
                            }
                        },
                        {
                            "tenBuoc": "Phân tích và Kiểm soát chất lượng Osiris",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Phạm Ngọc Nam",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008806"
                            }
                        },
                        {
                            "tenBuoc": "Phê duyệt Kết Qủa ",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Phê Văn Duyệt",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008807"
                            }
                        },
                        {
                            "tenBuoc": "Lưu trữ mẫu",
                            "nhanVienThucHien": {
                                "hoTenNhanVien": "Nguyễn Thị Ly",
                                "chucVu": "Kĩ Thuật Viên",
                                "hocVi": null,
                                "soDinhDanh": "032009008803"
                            }
                        }
                    ]
                }
            }
        },
        "duLieuADN": {
            "tenFileADN": "240027.zip",
            "noiDungFileADN": "UEsDBBQAAAAAAE2L21gAAAAAAAAAAAAAAAAHACAAMjQwMDI3L1VUDQAHUj59ZlZIfWZWSH1mdXgLAAEE6AMAAAToAwAAUEsDBBQAAAAAADKR21gAAAAAAAAAAAAAAAALACAAMjQwMDI3L2Fkbi9VVA0AB3FIfWZ3SH1mcUh9ZnV4CwABBOgDAAAE6AMAAFBLAwQUAAgACAAAittYAAAAAAAAAABxBwAAFgAgADI0MDAyNy9hZG4vMjQwMDI3Lmpzb25VVA0AB+A7fWZQSH1mcUh9ZnV4CwABBOgDAAAE6AMAAK2US2vDMBCE74X+h6BzMRqtZEu9hTppD20T2JD2FnrIzedeQv575Qb6AO9ATY3BYtd8jGYfp+urRX3c/vngcfC+viG528XpEh9TvSgk5V/BMf42DMfhWMMOyd1MpPCZ69xX6vz9l3t/WTJiJsQ8TezRapLCqJ5QwzT1TtfYbhgUf4futptXgmSXxzRx+bR6ZCIJ0XAzK9BRNxlUDGpAxRKokBKJJRVZE4MiEqXRUqoxUigaYQY0Ru17FI0i/yx39+CZWNJRpTFqtb5nExqIyGB6GhQ+JnZ3tkqSgU2aQffTjBHtISp1ec3y1NpQnebg5y4oo/t1RZspjI1oV4q0qVeESG2dM1Z1/tvUMiqRa3YAgkqh01qICZYFtVtFqAMds/YH9XKqn/MHUEsHCDXzKPooAQAAcQcAAFBLAQIUAxQAAAAAAE2L21gAAAAAAAAAAAAAAAAHACAAAAAAAAAAAAD9QQAAAAAyNDAwMjcvVVQNAAdSPn1mVkh9ZlZIfWZ1eAsAAQToAwAABOgDAABQSwECFAMUAAAAAAAykdtYAAAAAAAAAAAAAAAACwAgAAAAAAAAAAAA/UFFAAAAMjQwMDI3L2Fkbi9VVA0AB3FIfWZ3SH1mcUh9ZnV4CwABBOgDAAAE6AMAAFBLAQIUAxQACAAIAACK21g18yj6KAEAAHEHAAAWACAAAAAAAAAAAAC0gY4AAAAyNDAwMjcvYWRuLzI0MDAyNy5qc29uVVQNAAfgO31mUEh9ZnFIfWZ1eAsAAQToAwAABOgDAABQSwUGAAAAAAMAAwASAQAAGgIAAAAA",
            "congNghe": "CE",
            "tenKit": "GlobalFiler",
            "loaiDuLieu": "STR"
        }
    }
}