from fastapi import APIRouter

from src.operation.routes.lims.batch import router as lims_batch_router
from src.operation.routes.lims.card import router as lims_card_router
from src.operation.routes.lims.chip import router as lims_chip_router
from src.operation.routes.lims.dna_box import router as lims_dna_box_router
from src.operation.routes.lims.dna_extraction import (
    router as lims_dna_extraction_router,
)
from src.operation.routes.lims.lab_sample import router as lims_lab_sample_router
from src.operation.routes.lims.plate import router as lims_plate_router
from src.operation.routes.lims.raw import router as lims_raw_router
from src.operation.routes.lims.sample_management import (
    router as lims_sample_management_router,
)
from src.operation.routes.operator.billcode import router as operator_billcode_router
from src.operation.routes.operator.code import router as operator_code_router
from src.operation.routes.operator.promotion import router as operator_promotion_router
from src.operation.routes.operator.sample import router as operator_sample_router
from src.operation.routes.operator.samplecode import (
    router as operator_samplecode_router,
)
from src.operation.routes.partner.account import router as partner_account_router
from src.operation.routes.partner.partner_booking import (
    router as partner_booking_router,
)
from src.operation.routes.partner.sale_account_history import (
    router as partner_sale_account_history_router,
)
from src.operation.routes.partner.staff import router as partner_staff_router
from src.operation.routes.platform.adn_integration import (
    router as platform_adn_integration_router,
)
from src.operation.routes.platform.adn_integration_type import (
    router as platform_adn_integration_type_router,
)
from src.operation.routes.platform.agency import router as platform_agency_router
from src.operation.routes.platform.request import router as platform_request_router
from src.operation.routes.platform.result import router as platform_result_router
from src.operation.routes.sponsor.sponsor import router as sponsor_router
from src.operation.routes.tracking.collection import (
    router as tracking_collection_router,
)
from src.operation.routes.tracking.delivery import (
    router as tracking_delivery_router,
)
from src.operation.routes.tracking.employee import (
    router as tracking_employee_router,
)
from src.operation.routes.tracking.procedure import (
    router as tracking_procedure_router,
)
from src.operation.routes.tracking.sample import (
    router as tracking_sample_router,
)
from src.operation.routes.tracking.step import router as tracking_step_router
from src.operation.routes.tracking.template import router as tracking_template_router
from src.operation.routes.tracking.unit import router as tracking_unit_router
from src.operation.routes.tracking.unit_location import (
    router as tracking_unit_location_router,
)
from src.operation.routes.user.identity_card import (
    router as user_identity_card_router,
)
from src.operation.routes.user.kit import router as user_kit_router
from src.operation.routes.lims.sample_mapping import router as sample_mapping_router
from src.operation.routes.warehouse.sample_box import router as sample_box_router


router = APIRouter()
router.include_router(lims_batch_router, prefix="/lims/batch", tags=["lims"])
router.include_router(lims_card_router, prefix="/lims/card", tags=["lims"])
router.include_router(lims_chip_router, tags=["lims_chip"])
router.include_router(lims_dna_box_router, tags=["lims_dna_box"])
router.include_router(lims_dna_extraction_router, tags=["lims_dna_extraction"])
router.include_router(
    lims_lab_sample_router, prefix="/lims/lab_sample", tags=["lims_lab_sample"]
)
router.include_router(lims_plate_router, tags=["lims_plate"])
router.include_router(lims_raw_router, tags=["lims_raw"])
router.include_router(
    lims_sample_management_router,
    prefix="/lims/sample_management",
    tags=["lims_sample_management"],
)
router.include_router(operator_billcode_router, tags=["operator_billcode"])
router.include_router(operator_code_router, tags=["operator_code"])
router.include_router(operator_promotion_router, tags=["operator_promotion"])
router.include_router(operator_sample_router, tags=["operator_sample"])
router.include_router(operator_samplecode_router, tags=["operator_samplecode"])
router.include_router(partner_account_router, tags=["partner_account"])
router.include_router(partner_booking_router, tags=["partner_booking"])
router.include_router(
    partner_sale_account_history_router, tags=["partner_sale_account_history"]
)
router.include_router(partner_staff_router, tags=["partner_staff"])
router.include_router(
    platform_adn_integration_type_router, tags=["platform_adn_integration_type"]
)
router.include_router(
    platform_adn_integration_router, tags=["platform_adn_integration"]
)
router.include_router(platform_agency_router, tags=["platform_agency"])
router.include_router(platform_request_router, tags=["platform_request"])
router.include_router(platform_result_router, tags=["platform_result"])
router.include_router(sponsor_router, prefix="/sponsor", tags=["sponsor"])
router.include_router(tracking_collection_router, tags=["tracking_collection"])
router.include_router(tracking_delivery_router, tags=["tracking_delivery"])
router.include_router(tracking_employee_router, tags=["tracking_employee"])
router.include_router(tracking_procedure_router, tags=["tracking_procedure"])
router.include_router(tracking_sample_router, tags=["tracking_sample"])
router.include_router(tracking_step_router, tags=["tracking_step"])
router.include_router(tracking_template_router, tags=["tracking_template"])
router.include_router(tracking_unit_location_router, tags=["tracking_unit_location"])
router.include_router(tracking_unit_router, tags=["tracking_unit"])
router.include_router(user_identity_card_router, tags=["user_identity_card"])
router.include_router(user_kit_router, tags=["user_kit"])
router.include_router(sample_mapping_router, tags=["sample_mapping"])
router.include_router(sample_box_router, prefix="/warehouse", tags=["sample_mapping"])
