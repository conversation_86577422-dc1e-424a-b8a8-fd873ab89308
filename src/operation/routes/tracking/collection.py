from urllib import response
import requests
from fastapi import APIRouter, FastAPI, HTTPException, Query, Depends, Body
from typing import List, Optional
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Collection - model 
# /tracking/collection
# /tracking/collections/{id}
# _collections - plural
# _collection - function name
# collection - single
# create_collection_service
# update_collection_service
# delete_collection_by_id_service
# tracking_collection

from ...models.models import TrackingCollection

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.tracking_collection import *


from ...schemas.tracking_collection import *

from ...services.tracking_collection import *

router = APIRouter()

@router.post("/tracking/collection", status_code=HTTP_200_OK)
async def admin_create_collection(
  collection_body: AddCollection,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = collection_body.dict(exclude={'donViThuNhanMau', 'nhanVienLayMau', 'nhanVienGhiHoSo', 'nhanVienLuuMau'})
        # print("data: ", data)
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Collection with data {data}")

        data = await create_collection_service(data)
        return success_response(data)
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/tracking/collections/all", status_code=HTTP_200_OK)
async def admin_get_collections(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  maThuNhan: Optional[str] = Query(None),
  tenDonViThuNhanMau: Optional[str] = Query(None),
  noiThuThapMau: Optional[int] = Query(None),
  ngayGioThuThapMau_start_date: Optional[str] = Query(None),
  ngayGioThuThapMau_end_date: Optional[str] = Query(None),
  hoTenNhanVienLayMau: Optional[str] = Query(None),
  soDinhDanhNhanVienLayMau: Optional[str] = Query(None),
  hoTenNhanVienGhiHoSo: Optional[str] = Query(None),
  soDinhDanhNhanVienGhiHoSo: Optional[str] = Query(None),
  hoTenNhanVienLuuMau: Optional[str] = Query(None),
  soDinhDanhNhanVienLuuMau: Optional[str] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  auth=Depends(Auth),
):
        
    logger.info(f"Filter collection info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_all_collections_service(
            size=size,
            offset=offset,
            maThuNhan=maThuNhan,
            tenDonViThuNhanMau=tenDonViThuNhanMau,
            noiThuThapMau=noiThuThapMau,
            ngayGioThuThapMau_start_date=ngayGioThuThapMau_start_date,
            ngayGioThuThapMau_end_date=ngayGioThuThapMau_end_date,
            hoTenNhanVienLayMau=hoTenNhanVienLayMau,
            soDinhDanhNhanVienLayMau=soDinhDanhNhanVienLayMau,
            hoTenNhanVienGhiHoSo=hoTenNhanVienGhiHoSo,
            soDinhDanhNhanVienGhiHoSo=soDinhDanhNhanVienGhiHoSo,
            hoTenNhanVienLuuMau=hoTenNhanVienLuuMau,
            soDinhDanhNhanVienLuuMau=soDinhDanhNhanVienLuuMau,
            include_deleted=include_deleted,
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/tracking/collections/{id}", status_code=HTTP_200_OK)
async def admin_get_collection_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        collection_detail = await get_collection_by_id_service(id)
        
        return success_response(collection_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.get("/tracking/collections/samplecode/{samplecode}", status_code=HTTP_200_OK)
async def admin_get_collection_info_by_samplecode(
  samplecode: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        collection_detail = await get_collection_by_samplecode_service(samplecode=samplecode)
        
        return success_response(collection_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#TODO
@router.put("/tracking/collections/samplecode/{samplecode}", status_code=HTTP_200_OK)
async def admin_update_collection_info_by_samplecode(
  samplecode: str,
  update_body: UpdateCollectionDetail,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        data = update_body.dict()
        
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin update a Tracking Collection with data {data}")
        data = await update_collection_service_via_samplecode(samplecode,data)
        return success_response(data)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.put("/tracking/collections/{id}", status_code=HTTP_200_OK)
async def admin_update_collection_info_by_id(
  id: str,
  body: UpdateCollectionDetail,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        updated_collection = await update_collection_service(id=id, data=data)
        
        return success_response(updated_collection)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, tags=["tracking_collection"])