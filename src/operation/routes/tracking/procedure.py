from urllib import response
import requests
from fastapi import APIRouter, FastAPI, HTTPException, Query, Depends, Body
from typing import List, Optional
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Procedure - model 
# /tracking/procedure
# /tracking/procedures/{id}
# _procedures - plural
# _procedure - function name
# procedure - single
# create_procedure_service
# update_procedure_service
# delete_procedure_by_id_service
# tracking_procedure

from ...models.models import TrackingProcedure

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.tracking_procedure import *


from ...schemas.tracking_procedure import *

from ...services.tracking_procedure import *
from ...services.lab_sample import *

router = APIRouter()

@router.post("/tracking/procedure", status_code=HTTP_200_OK)
async def admin_create_procedure(
  procedure_body: AddProcedure,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        procedure_body.gs_lid, procedure_body.gs_barcode = await match_barcode_n_lid(procedure_body.gs_lid, procedure_body.gs_barcode)
        print("procedure_body.gs_barcode: ", procedure_body.gs_barcode)
        data = procedure_body.dict()
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        # print("data: ", data)
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Procedure with data ")

        data = await create_procedure_service(data)
        return success_response(data)
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

# get_all_lids_via_samplecode_v3
@router.get("/tracking/procedures/samplecode/{samplecode}/lids/all", status_code=HTTP_200_OK)
async def admin_get_lids_via_samplecode(
  samplecode: str,
  auth=Depends(Auth),
):
        
    logger.info(f"Filter procedure info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        data = await get_all_lids_via_samplecode_v3(samplecode=samplecode
        )
        res = {
            "data": data,
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
#DONE
@router.get("/tracking/procedures/samplecode/{samplecode}/all", status_code=HTTP_200_OK)
async def admin_get_procedures(
  samplecode: str,
  auth=Depends(Auth),
):
        
    logger.info(f"Filter procedure info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        data, total = await get_tracking_procedure_by_samplecode(samplecode=samplecode
        )
        res = {
            "data": data,
            'pagination': {
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.get("/tracking/procedures/all", status_code=HTTP_200_OK)
async def admin_get_procedures(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  maXetNghiem: Optional[str] = Query(None),
  nhietDoLuuTru: Optional[str] = Query(None),
  gs_isComplete: Optional[str] = Query(None),
  gs_barcode: Optional[str] = Query(None),
  gs_lid: Optional[str] = Query(None),
  congNghe: Optional[str] = Query(None),
  gs_template_name: Optional[str] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  auth=Depends(Auth),
):
        
    logger.info(f"Filter procedure info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_all_procedures_service(
            size=size,
            offset=offset,
            maXetNghiem=maXetNghiem,
            nhietDoLuuTru=nhietDoLuuTru,
            gs_isComplete=gs_isComplete,
            gs_barcode=gs_barcode,
            gs_lid=gs_lid,
            congNghe=congNghe,
            gs_template_name=gs_template_name,
            include_deleted=include_deleted
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/tracking/procedures/{id}", status_code=HTTP_200_OK)
async def admin_get_procedure_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        procedure_detail = await get_procedure_by_id_service(id)
        
        return success_response(procedure_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.put("/tracking/procedures/{id}", status_code=HTTP_200_OK)
async def admin_update_procedure_info_by_id(
  id: str,
  body: UpdateProcedureDetail,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        updated_procedure = await update_procedure_service(id=id, data=data)
        
        return success_response(updated_procedure)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, tags=["tracking_procedure"])