from urllib import response
import requests
from fastapi import APIRout<PERSON>, FastAPI, HTTPException, Query, Depends, Body
from typing import List, Optional
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Template - model 
# /tracking/template
# /tracking/templates/{id}
# _templates - plural
# _template - function name
# template - single
# create_template_service
# update_template_service
# delete_template_by_id_service
# tracking_template

from ...models.models import TrackingTemplate

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.tracking_template import *


from ...schemas.tracking_template import *

from ...services.tracking_template import *

router = APIRouter()

@router.post("/tracking/template", status_code=HTTP_200_OK)
async def admin_create_template(
  template_body: AddTemplate,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = template_body.dict()
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Template with data {data}")

        data = await create_template_service(data)
        return success_response(data)
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/tracking/templates/all", status_code=HTTP_200_OK)
async def admin_get_templates(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  congNghe: Optional[str] = Query(None),
  gs_template_name: Optional[str] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  auth=Depends(Auth),
):
        
    logger.info(f"Filter template info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_all_templates_service(
        offset=offset,
        size=size,
        congNghe=congNghe,
        gs_template_name=gs_template_name,
        include_deleted=include_deleted,
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/tracking/templates/{id}", status_code=HTTP_200_OK)
async def admin_get_template_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        template_detail = await get_template_by_id_service(id)
        
        return success_response(template_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
# @router.put("/tracking/templates/{id}", status_code=HTTP_200_OK)
# async def admin_update_template_info_by_id(
#   id: str,
#   body: UpdateTemplateDetail,
#   auth=Depends(Auth),
# ) -> dict:
    

#     cs_id = None
#     cs_name = None
#     try:
#         token_claims, err = await auth.get_token_claims()
#         # print("token claims: ", token_claims)
#         cs_id = token_claims["sub"]
#         cs_name = token_claims["name"]
#     except Exception as e:
#         logger.info("no access token provided")
        
#     # body.customer_support_id = cs_id
#     # body.customer_support_name = cs_name
#     data = body.dict()
#     try:
            
#         updated_template = await update_template_service(id=id, data=data)
        
#         return success_response(updated_template)
#     except HTTPException as err:
#         raise err
#     except requests.exceptions.ConnectionError as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except requests.exceptions.Timeout as err:
#         http_code = HTTP_408_REQUEST_TIMEOUT
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)
#     except ValueError as e:
#         errs = failure_response(e)
#         raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
#     except Exception as err:
#         http_code = HTTP_500_INTERNAL_SERVER_ERROR
#         errs = failure_response(err)
#         raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, tags=["tracking_template"])