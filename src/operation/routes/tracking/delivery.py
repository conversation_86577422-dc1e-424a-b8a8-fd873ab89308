from urllib import response
import requests
from fastapi import APIRouter, FastAP<PERSON>, HTTPException, Query, Depends, Body
from typing import List, Optional
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Delivery - model 
# /tracking/delivery
# /tracking/deliveries/{id}
# _deliveries - plural
# _delivery - function name
# delivery - single
# create_delivery_service
# update_delivery_service
# delete_delivery_by_id_service
# tracking_delivery

from ...models.models import TrackingDelivery

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.tracking_delivery import *


from ...schemas.tracking_delivery import *

from ...services.tracking_delivery import *

router = APIRouter()

@router.post("/tracking/delivery", status_code=HTTP_200_OK)
async def admin_create_delivery(
  agency_body: AddDelivery,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = agency_body.dict(exclude={'donViBanGiao','donViVanChuyen','donViNhanMau','nhanVienBanGiao','nhanVienNhanMau'})
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Delivery with data {data}")

        data = await create_delivery_service(data)
        return success_response(data)
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/tracking/deliveries/all", status_code=HTTP_200_OK)
async def admin_get_deliveries(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  maVanChuyen: Optional[str] = Query(None),
  nhietDoChuyenGiao: Optional[str] = Query(None),
  tinhTrangNiemPhong: Optional[bool] = Query(None),
  ngayGioChuyenGiao_start_date: Optional[str] = Query(None),
  ngayGioChuyenGiao_end_date: Optional[str] = Query(None),
  diaDiemChuyenGiao: Optional[int] = Query(None),
  tenDonViBanGiao: Optional[str] = Query(None),
  tenDonViVanChuyen: Optional[str] = Query(None),
  tenDonViNhanMau: Optional[str] = Query(None),
  hoTenNhanVienBanGiao: Optional[str] = Query(None),
  soDinhDanhNhanVienBanGiao: Optional[str] = Query(None),
  hoTenNhanVienNhanMau: Optional[str] = Query(None),
  soDinhDanhNhanVienNhanMau: Optional[str] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  auth=Depends(Auth),
):
        
    logger.info(f"Filter delivery info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_all_deliveries_service(
            size=size,
            offset=offset,
            maVanChuyen=maVanChuyen,
            nhietDoChuyenGiao=nhietDoChuyenGiao,
            tinhTrangNiemPhong=tinhTrangNiemPhong,
            ngayGioChuyenGiao_start_date=ngayGioChuyenGiao_start_date,
            ngayGioChuyenGiao_end_date=ngayGioChuyenGiao_end_date,
            diaDiemChuyenGiao=diaDiemChuyenGiao,
            tenDonViBanGiao=tenDonViBanGiao,
            tenDonViVanChuyen=tenDonViVanChuyen,
            tenDonViNhanMau=tenDonViNhanMau,
            hoTenNhanVienBanGiao=hoTenNhanVienBanGiao,
            soDinhDanhNhanVienBanGiao=soDinhDanhNhanVienBanGiao,
            hoTenNhanVienNhanMau=hoTenNhanVienNhanMau,
            soDinhDanhNhanVienNhanMau=soDinhDanhNhanVienNhanMau,
            include_deleted=include_deleted
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/tracking/deliveries/{id}", status_code=HTTP_200_OK)
async def admin_get_delivery_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        delivery_detail = await get_delivery_by_id_service(id)
        
        return success_response(delivery_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



#DONE
@router.get("/tracking/deliveries/samplecode/{samplecode}", status_code=HTTP_200_OK)
async def admin_get_delivery_info_by_id(
  samplecode: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        delivery_detail = await get_delivery_by_samplecode_service(samplecode=samplecode)
        
        return success_response(delivery_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.put("/tracking/deliveries/{id}", status_code=HTTP_200_OK)
async def admin_update_delivery_info_by_id(
  id: str,
  body: UpdateDeliveryDetail,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        updated_delivery = await update_delivery_service(id=id, data=data)
        
        return success_response(updated_delivery)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#TODO
@router.put("/tracking/deliveries/samplecode/{samplecode}", status_code=HTTP_200_OK)
async def admin_update_delivery_info_by_samplecode(
  samplecode: str,
  update_body: UpdateDeliveryDetail,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        data = update_body.dict()
        
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin update a Tracking Delivery with data {data}")
        data = await update_delivery_service_via_samplecode(samplecode,data)
        return success_response(data)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, tags=["tracking_delivery"])