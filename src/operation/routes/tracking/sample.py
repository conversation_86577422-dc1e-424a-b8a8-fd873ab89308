from typing import Optional

import requests
from fastapi import <PERSON>Rout<PERSON>, Depends, FastAPI, HTTPException, Query
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ... import logger
from ...auth import Auth

# Tracking - model
# /tracking/sample
# /tracking/samples/{id}
# _trackings - plural
# _tracking - function name
# tracking - single
# create_tracking_service
# update_tracking_service
# delete_tracking_by_id_service
# tracking_tracking
from ...config import config
from ...cruds.tracking import *
from ...schemas.tracking import *
from ...services.tracking import *
from ...utils.utils import failure_response, success_response

router = APIRouter()


@router.post("/tracking/sample", status_code=HTTP_200_OK)
async def admin_create_tracking(
    tracking_body: AddTracking,
    auth=Depends(Auth),
) -> dict:
    try:
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception:
            logger.info("no access token provided")

        data = tracking_body.dict()
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Tracking Tracking with data {data}")

        data = await create_tracking_service(data)
        return success_response(data)

    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# DONE
@router.get("/tracking/samples/all", status_code=HTTP_200_OK)
async def admin_get_trackings(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    samplecode: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    include_deleted: Optional[bool] = Query(False),
    auth=Depends(Auth),
):
    logger.info("Filter tracking info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_all_trackings_service(
            size=size,
            offset=offset,
            samplecode=samplecode,
            barcode=barcode,
            include_deleted=include_deleted,
        )
        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res

    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get(
    "/tracking/samples/samplecode/{samplecode}/kits/{barcode}", status_code=HTTP_200_OK
)
async def admin_get_kit_tracking_detail_w_samplecode_and_barcode(
    samplecode: str,
    barcode: str,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err["err_msg"])
            raise HTTPException(status_code=err["err_code"], detail=errs)

        tracking_detail = await get_kit_tracking_detail_w_samplecode_and_barcode(
            samplecode=samplecode, barcode=barcode
        )

        return success_response(tracking_detail)

    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/tracking/samples/samplecode/{samplecode}", status_code=HTTP_200_OK)
async def admin_get_all_kits_tracking_releated_to_samplecode(
    samplecode: str,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err["err_msg"])
            raise HTTPException(status_code=err["err_code"], detail=errs)

        tracking_detail = await get_all_kits_tracking_releated_to_samplecode(
            samplecode=samplecode
        )

        return success_response(tracking_detail)

    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    pass


@router.get("/tracking/samples/barcode/{barcode}", status_code=HTTP_200_OK)
async def admin_get_adn_result_metadata_via_barcode(
    barcode: str,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err["err_msg"])
            raise HTTPException(status_code=err["err_code"], detail=errs)

        tracking_detail = await get_adn_result_metadata_via_barcode(barcode=barcode)

        return success_response(tracking_detail)

    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# DONE
@router.get("/tracking/samples/{id}", status_code=HTTP_200_OK)
async def admin_get_tracking_info_by_id(
    id: str,
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err["err_msg"])
            raise HTTPException(status_code=err["err_code"], detail=errs)

        tracking_detail = await get_tracking_by_id_service(id)

        return success_response(tracking_detail)

    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


# DONE
@router.put("/tracking/samples/{id}", status_code=HTTP_200_OK)
async def admin_update_tracking_info_by_id(
    id: str,
    body: UpdateTrackingDetail,
    auth=Depends(Auth),
) -> dict:
    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception:
        logger.info("no access token provided")

    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
        updated_tracking = await update_tracking_service(id=id, data=data)

        return success_response(updated_tracking)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, tags=["tracking_tracking"])
