from urllib import response
import requests
from fastapi import APIRouter, FastAPI, HTTPException, Query, Depends, Body
from typing import List, Optional
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Unit - model 
# /tracking/unit
# /tracking/units/{id}
# _units - plural
# _unit - function name
# unit - single
# create_unit_service
# update_unit_service
# delete_unit_by_id_service
# tracking_unit

from ...models.models import TrackingUnit

from ...config import config
from ... import logger
from ...auth import Auth
from ...dependencies.pagination import PaginationParams

from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.tracking_unit import *


from ...schemas.tracking_unit import *

from ...services.tracking_unit import *

router = APIRouter()

@router.post("/tracking/unit", status_code=HTTP_200_OK)
async def admin_create_unit(
  unit_body: AddUnit,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = unit_body.dict()
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Tracking Unit with data {data}")

        data = await create_unit_service(data)
        return success_response(data)
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/tracking/units/all", status_code=HTTP_200_OK)
async def admin_get_units(
  tenDonVi: Optional[str] = Query(None),
  gs_area: Optional[str] = Query(None),
  gs_area_code: Optional[int] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  pagination: PaginationParams = Depends(),
  auth=Depends(Auth),
):
        
    logger.info(f"Filter unit info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        data, total = await get_all_units_service(
            size=pagination.size,
            offset=pagination.offset,
            tenDonVi=tenDonVi,
            gs_area=gs_area,
            gs_area_code=gs_area_code,
            include_deleted=include_deleted
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': pagination.page_size,
                'page_number': pagination.page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/tracking/units/account", status_code=HTTP_200_OK)
async def admin_get_unit_info_by_id(
    id: str,
    auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        unit_detail = await get_unit_by_id_service(id=id, credential=auth.bearer_token.credentials)
        
        if unit_detail:
            return success_response(unit_detail)
        
        else:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(f"Unit with id {id} not found")
            raise HTTPException(status_code=http_code, detail=errs)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.put("/tracking/units/{id}/phone-account", status_code=HTTP_200_OK)
async def admin_link_unit_with_phone_number_by_id(
    id: int,
    body: LinkUnitPhoneNumber,
    auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        linked_unit = await link_unit_with_phone_number_service(id=id, data=data, credential=auth.bearer_token.credentials)
        
        return success_response(linked_unit.to_dict())
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    pass


@router.put("/tracking/units/{id}/phone-account/unlink", status_code=HTTP_200_OK)
async def admin_unlink_unit_with_phone_number_by_id(
        id: int,
        body: UnLinkUnitPhoneNumber,
        auth=Depends(Auth),
) -> dict:
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:

        linked_unit = await unlink_unit_with_phone_number_service(id=id, phone_number=body.gs_phone_number)

        return success_response(linked_unit.to_dict())
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    pass


@router.get("/tracking/units/phone-account/{phone_number}/check-exist", status_code=HTTP_200_OK)
async def check_account_exist(
        phone_number: str,
        # auth=Depends(Auth),
) -> dict:
    # token_claims, err = await auth.get_token_claims()
    # if err:
    #     errs = failure_response(err["err_msg"])
    #     raise HTTPException(status_code=err["err_code"], detail=errs)

    logger.info("Check if phone number exists in the system")

    try:
        logger.info(f"Check if phone number {phone_number} exists in the system")
        phone_number_existed = await check_account_service(phone_number)
        logger.info(f"Phone number {phone_number} exists: {phone_number_existed}")
        tracking_unit = await get_unit_by_phone_number_service(phone_number)
        if tracking_unit:
            err = f"Số điện thoại {phone_number} đã được liên kết với đơn vị: {tracking_unit.tenDonVi}"
            http_code = HTTP_409_CONFLICT
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            return success_response({"phone_number_existed": phone_number_existed})
    except HTTPException as err:
        errs = failure_response(err)
        logger.error(f"HTTPException: {errs}")
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/tracking/units/phone-account/{phone_number}", status_code=HTTP_200_OK)
async def admin_get_linked_unit_via_phone_number(
    phone_number: str,
    auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
    
    try:
        tracking_unit = await get_unit_by_phone_number_service(phone_number)
        if tracking_unit:
            return success_response(tracking_unit.to_dict())
        else:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(f"Unit with phone number {phone_number} not found")
            raise HTTPException(status_code=http_code, detail=errs)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.put("/tracking/units/{id}", status_code=HTTP_200_OK)
async def admin_update_unit_info_by_id(
  id: str,
  body: UpdateUnitDetail,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
        updated_unit = await update_unit_service(id=id, data=data)
        
        return success_response(updated_unit)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, tags=["tracking_unit"])