from urllib import response
import requests
from fastapi import APIRouter, FastAPI, HTTPException, Query, Depends, Body
from typing import List, Optional
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Employee - model 
# /tracking/employee
# /tracking/employees/{id}
# _employees - plural
# _employee - function name
# employee - single
# create_employee_service
# update_employee_service
# delete_employee_by_id_service
# tracking_employee

from ...models.models import TrackingEmployee

from ...config import config
from ... import logger
from ...auth import Auth
from ...dependencies.pagination import PaginationParams

from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.tracking_employee import *


from ...schemas.tracking_employee import *

from ...services.tracking_employee import *

router = APIRouter()

@router.post("/tracking/employee", status_code=HTTP_200_OK)
async def admin_create_employee(
  employee_body: AddEmployee,
  auth=Depends(Auth),
) -> dict:
    try:
        
        cs_id = None
        cs_name = None
        try:
            token_claims, err = await auth.get_token_claims()
            # print("token claims: ", token_claims)
            cs_id = token_claims["sub"]
            cs_name = token_claims["name"]
        except Exception as e:
            logger.info("no access token provided")

        data = employee_body.dict()
        # data['customer_support_name'] = cs_name
        # data['customer_support_id'] = cs_id
        if not data:
            err = "No changed field to update"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        logger.info(f"Admin create a Integration Employee with data {data}")

        data = await create_employee_service(data)
        return success_response(data)
        
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/tracking/employees/all", status_code=HTTP_200_OK)
async def admin_get_employees(
  hoTenNhanVien: Optional[str] = Query(None),
  tenDonVi: Optional[str] = Query(None),
  soDinhDanh: Optional[str] = Query(None),
  chucVu: Optional[str] = Query(None),
  gs_area: Optional[str] = Query(None),
  include_deleted: Optional[bool] = Query(False),
  pagination: PaginationParams = Depends(),
  auth=Depends(Auth),
):
        
    logger.info(f"Filter employee info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        data, total = await get_all_employees_service(
            offset=pagination.offset,
            size=pagination.size,
            hoTenNhanVien=hoTenNhanVien,
            tenDonVi=tenDonVi,
            soDinhDanh=soDinhDanh,
            chucVu=chucVu,
            gs_area=gs_area,
            include_deleted=include_deleted,
        )
        res = {
            "data": data,
            'pagination': {
                'page_size': pagination.page_size,
                'page_number': pagination.page_number,
                'total': total
            },
            "detail": []
        }
        return res
    
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.get("/tracking/employees/{id}", status_code=HTTP_200_OK)
async def admin_get_employee_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
    
    try:
        _, err = await auth.get_token_claims()
        if err:
            errs = failure_response(err['err_msg'])
            raise HTTPException(status_code=err['err_code'], detail=errs)

        employee_detail = await get_employee_by_id_service(id)
        
        return success_response(employee_detail)

    
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.put("/tracking/employees/{id}", status_code=HTTP_200_OK)
async def admin_update_employee_info_by_id(
  id: str,
  body: UpdateEmployeeDetail,
  auth=Depends(Auth),
) -> dict:
    

    cs_id = None
    cs_name = None
    try:
        token_claims, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)
        cs_id = token_claims["sub"]
        cs_name = token_claims["name"]
    except Exception as e:
        logger.info("no access token provided")
        
    # body.customer_support_id = cs_id
    # body.customer_support_name = cs_name
    data = body.dict()
    try:
            
        updated_employee = await update_employee_service(id=id, data=data)
        
        return success_response(updated_employee)
    except HTTPException as err:
        raise err
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as e:
        errs = failure_response(e)
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)



def init_app(app: FastAPI):
    app.include_router(router, tags=["tracking_employee"])