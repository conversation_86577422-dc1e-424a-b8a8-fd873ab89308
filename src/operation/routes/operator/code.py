import uuid

from asyncpg.exceptions import UniqueViolationError
from typing import List, Optional
from fastapi import APIRouter, Depends, FastAPI, HTTPException, Query
from pydantic import BaseModel
import requests
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...utils.utils import (
    success_response,
    failure_response,
    format_date,
    DEFAULT_DATE_NO_TIME_ZONE
)
from ...auth import Auth
from ... import logger
from ...config import config
from ...cruds.codes import (
    generate_code,
    generate_barcode_v2,
    get_code,
    update_code,
    update_printed,
    delete_code,
    delete_codes,
    save_code,
    count_codes,
    get_all_codes,
)

from ...schemas.code import (
    DeleteBarcodes,
    UpdateBarcodeInfo,
)


router = APIRouter()


class NewState(BaseModel):
    state: str
    id: str

class NewPrintedStatus(BaseModel):
    id: str
    is_printed: bool
    
@router.post("/internal/codes", status_code=HTTP_200_OK)
async def create(
    product_code: str,
    size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"]),
    version: int = 1,
) -> list:
    logger.info(f"Generate {size} code with product_code is {product_code}")
    
    try:
        request_headers = {'Content-type': 'application/json', 'Accept': 'text/plain'}
        response = requests.get(config["SHOP_URL"][config["ENV"]] + '/admin/product/',
                    headers=request_headers,
                    timeout=3)
        response.raise_for_status()
        
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
      
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
      
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    product_list = response.json().get("data")
    
    if not product_list or product_code not in [p.get('code') for p in product_list]:
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=f"Cannot retrieve product list")    
        
    product_map = {p.get('code'): p for p in product_list}
    if product_code not in product_map:
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=f"Product code {product_code} isn't existed")
    product_detail = product_map.get(product_code)
    
    codes = []
    i = 0
    
    while i < size:
        if version == 2:
            code = generate_barcode_v2(product_code=product_code, product_name=product_detail.get('name'))
        else:
            code = generate_code(product_code, product_name=product_detail.get('name'))
        if not await get_code(code['barcode']):
            await save_code(code)
            codes.append(code)
            i += 1

    return success_response(codes)

@router.post("/codes", status_code=HTTP_200_OK)
async def create_batch_barcodes(
    product_code: str,
    size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"]),
    version: int = 1,
    auth=Depends(Auth),
) -> list:
    """
    Create a list of code object (barcode, qrcode, printed status, state)
    :return: A list of codes
    """
    logger.info(f"Generate {size} code with product_code is {product_code}")
    
    try:
        request_headers = {'Content-type': 'application/json', 'Accept': 'text/plain', 'Authorization': f'Bearer {auth.bearer_token}'}
        response = requests.get(config["SHOP_URL"][config["ENV"]] + '/admin/product/',
                    headers=request_headers,
                    timeout=3)
        response.raise_for_status()
        
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
      
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
      
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    product_list = response.json().get("data")
    
    if not product_list or product_code not in [p.get('code') for p in product_list]:
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=f"Cannot retrieve product list")    
        
    product_map = {p.get('code'): p for p in product_list}
    if product_code not in product_map:
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=f"Product code {product_code} isn't existed")
    product_detail = product_map.get(product_code)
    
    codes = []
    i = 0
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    while i < size:
        if version == 2:
            code = generate_barcode_v2(product_code=product_code, product_name=product_detail.get('name'))
        else:
            code = generate_code(product_code, product_name=product_detail.get('name'))
        if not await get_code(code['barcode']):
            await save_code(code)
            codes.append(code)
            i += 1

    return success_response(codes)


@router.post("/codes/manual", status_code=HTTP_200_OK)
async def create_manually(
        barcodes: List[str],
        auth=Depends(Auth),
):
    """
    Manually create a list of code object by providing a list of barcode
    """
    logger.info("Manually create a list of code object by providing a list of barcode")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    for barcode in barcodes:
        try:
            product_code = int(barcode[-2:])
        except Exception as err:
            err_msg = f"The barcode {barcode} doesn't contain the correct product code"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err_msg)
            raise HTTPException(status_code=http_code, detail=errs)
        if len(barcode) != 12:
            err_msg = "The length of barcodes must be 12"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err_msg)
            raise HTTPException(status_code=http_code, detail=errs)
        if not barcode.isnumeric():
            err_msg = f"The barcode {barcode} is not a numeric string"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err_msg)
            raise HTTPException(status_code=http_code, detail=errs)
        try:
            code = generate_code(product_code, barcode=barcode)
            if not await get_code(code['barcode']):
                await save_code(code)
            else:
                logger.info(f"The barcode {barcode} was already inserted to database")
        except Exception as err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

    success_msg = "Finished to insert given codes"
    return success_response(success_msg)


@router.get("/codes", status_code=HTTP_200_OK)
async def retrieve_codes(
    auth = Depends(Auth),
    page_size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    product_code: Optional[str] = Query(None),
    note: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    state: Optional[List[str]] = Query(None),
) -> dict:
    """
    Get all codes with filtering, searching, and sorting
    :param page_size: Size of page
    :param page_number: number of page
    :param barcode: id of code
    :param state: State of the code
    :return:
    """
    logger.info("Get code info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        if start_date:
            start_date_std = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date:
            end_date_std = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
            if end_date_std < start_date_std:
                raise ValueError("end date cannot be after start date")
        codes, total_match = await get_all_codes(
            size=size,
            offset=offset,
            barcode=barcode,
            product_code=product_code,
            note=note,
            start_date=start_date_std if start_date is not None else None,
            end_date=end_date_std if end_date is not None else None,
            state=state,
            order_by="updated_time",
        )
        
        # if not total_match:
        #     total_match = total
        res = {
            "data": codes,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total_match
            },
            "detail": []
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/codes/{id}", status_code=HTTP_200_OK)
async def retrieve(
        id: str,
        auth=Depends(Auth)
) -> dict:
    """
    Get/Search a code object (barcode, qrcode, printed status, state)
    :param id: a barcode
    :return: a code object
    """
    logger.info(f"Get a code with id {id}")
    code = None
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        code = await get_code(id)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    if code:
        return success_response(code.to_dict())
    else:
        err = f"A code with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/codes/{id}", status_code=HTTP_200_OK)
async def update(
        id: str,
        body: UpdateBarcodeInfo,
        auth=Depends(Auth),
) -> dict:
    """
    Update a code object with a new state (barcode, qrcode, state)
    :param id: a barcode
    :param state: a new code state
    :return: a newly updated code object
    """
    logger.info(f"Update a code with id {id}, updating state {body.state}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    allowed_states = config["ALLOWED_CODE_STATE"]
    if body.state not in allowed_states:
        err = f"Status '{body.state}' is not an allowed code states, the correct list is: ({allowed_states})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        await update_code(id, body.state, body.note)
        code = await get_code(id)
        return success_response(code.to_dict())
    except Exception as err:
        if err.status_code == 404:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        elif err.status_code == 422:
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)


@router.put("/codes/status/printed", status_code=HTTP_200_OK)
async def update(
        printed_statuses: List[NewPrintedStatus],
        auth=Depends(Auth),
) -> dict:
    """
    Update printed status of a code object
    :param id: a barcode
    :param is_printed: a printed boolean
    :return: a newly updated code object
    """
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    data_arr = []
    for printed_status in printed_statuses:
        data = printed_status.dict()
        data_arr.append(data)
    
    data_arr, err = await update_printed(data_arr)
    return success_response(data_arr)


@router.put("/codes", status_code=HTTP_200_OK)
async def bulk_update(
        body: List[NewState],
        auth=Depends(Auth)
) -> dict:
    """
    Update a bulk of codes state
    """
    logger.info("Update a bulk of codes state")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    allowed_states = config["ALLOWED_CODE_STATE"]
    data_res = []
    for state_obj in body:
        state = state_obj.state
        barcode = state_obj.id
        if state not in allowed_states:
            item_res = {
                'code': HTTP_422_UNPROCESSABLE_ENTITY,
                'id': barcode,
                'data': {},
                'msg': f"Status '{state}' is not an allowed code states, the correct list is: ({allowed_states})"
            }
            data_res.append(item_res)
        else:
            try:
                await update_code(barcode, state)
                code = await get_code(barcode)
                item_res = {
                    'code': HTTP_200_OK,
                    'id': barcode,
                    'data': code.to_dict(),
                    'msg': ''
                }
                data_res.append(item_res)
            except Exception as err:
                if err.status_code == 404:
                    http_code = HTTP_404_NOT_FOUND
                    item_res = {
                        'code': http_code,
                        'id': barcode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
                elif err.status_code == 422:
                    http_code = HTTP_422_UNPROCESSABLE_ENTITY
                    item_res = {
                        'code': http_code,
                        'id': barcode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
                else:
                    http_code = HTTP_500_INTERNAL_SERVER_ERROR
                    item_res = {
                        'code': http_code,
                        'id': barcode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
    return success_response(data_res)

# delete barcode by list
@router.delete("/codes", status_code=HTTP_200_OK)
async def delete(
    body: DeleteBarcodes,
    auth=Depends(Auth),
) -> dict:
    logger.info(f"Delete barcode list: {body}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        barcode_list = await delete_codes(barcode_list=body.barcode_list)
        return success_response(barcode_list)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.delete("/codes/{id}", status_code=HTTP_200_OK)
async def delete(
        id: str,
        auth=Depends(Auth),
) -> dict:
    """
    Delete a code object (barcode, qrcode, printed status, state)
    :param id: a barcode
    :return: a deleted code object
    """
    logger.info(f"Delete a code with id {id}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    err = "Unknown errors"
    try:
        code, err = await delete_code(id)
        if err:
            raise HTTPException(status_code=404, detail=err)
        else:
            final_res = {
                'data': "Deleted",
                'detail': []
            }
            return final_res
    except Exception as excp:
        if excp.status_code == 404:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        elif excp.status_code == 422:
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(excp)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(excp)
            raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, tags=["operator_code"])