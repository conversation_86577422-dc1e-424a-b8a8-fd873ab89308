

from asyncpg.exceptions import UniqueViolationError
from typing import List, Optional
from fastapi import APIRouter, Depends, FastAPI, HTTPException, Query
from pydantic import BaseModel

from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    # HTTP_408_REQUEST_TIMEOUT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...utils.utils import (
    success_response,
    failure_response,
    format_date,
    DEFAULT_DATE_NO_TIME_ZONE
)
from ...auth import Auth
from ... import logger
from ...config import config
from ...cruds.billcode import (
    generate_billcode,
    get_billcode,
    update_billcode,
    update_printed,
    delete_billcode,
    delete_billcodes,
    save_billcode,
    # count_billcodes,
    get_all_billcodes,
)

from ...schemas.billcode import (
    DeleteBillcodes,
    UpdateBillcodeInfo,
)


router = APIRouter()


class NewState(BaseModel):
    status: str
    billcode: str

class NewPrintedStatus(BaseModel):
    billcode: str
    is_printed: bool

@router.post("/billcodes", status_code=HTTP_200_OK)
async def create(
    size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"]),
    auth=Depends(Auth),
) -> list:
    logger.info("Generate billcode_objs")
    """
    Create a list of billcode_obj object (billcode, qrcode, printed status, status)
    :return: A list of billcode_objs
    """

    billcode_objs = []
    i = 0
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    while i < size:
        billcode_obj = await generate_billcode()
        if not await get_billcode(billcode_obj['billcode']):
            await save_billcode(billcode_obj)
            billcode_objs.append(billcode_obj)
            i += 1

    return success_response(billcode_objs)


@router.get("/billcodes", status_code=HTTP_200_OK)
async def retrieve_billcodes(
    page_size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    billcode: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    status: Optional[List[str]] = Query(None),
    auth = Depends(Auth),
) -> dict:
    """
    Get all billcode_objs with filtering, searching, and sorting
    :param page_size: Size of page
    :param page_number: number of page
    :param billcode: billcode of billcode_obj
    :param status: State of the billcode_obj
    :return:
    """
    logger.info("Get billcode_obj info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        if start_date:
            start_date_std = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date:
            end_date_std = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
            if end_date_std < start_date_std:
                raise ValueError("end date cannot be after start date")
        billcode_objs, total_match = await get_all_billcodes(
            size=size,
            offset=offset,
            billcode=billcode,
            start_date=start_date_std if start_date is not None else None,
            end_date=end_date_std if end_date is not None else None,
            status=status,
            order_by="created_at",
        )
        
        # if not total_match:
        #     total_match = total
        res = {
            "data": billcode_objs,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total_match
            },
            "detail": []
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs) from err


@router.get("/billcodes/{billcode}", status_code=HTTP_200_OK)
async def retrieve(
        billcode: str,
        auth=Depends(Auth)
) -> dict:
    """
    Get/Search a billcode_obj object (billcode, printed status, status)
    :param billcode: a billcode
    :return: a billcode_obj object
    """
    logger.info("Get a billcode_obj with billcode %s", billcode)
    billcode_obj = None
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    try:
        billcode_obj = await get_billcode(billcode)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    if billcode_obj:
        return success_response(billcode_obj.to_dict())
    else:
        err = f"A billcode_obj with billcode {billcode} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/billcodes/{billcode}", status_code=HTTP_200_OK)
async def update_billcode_infor(
        billcode: str,
        body: UpdateBillcodeInfo,
        auth=Depends(Auth),
) -> dict:
    """
    Update a billcode_obj object with a new status (billcode, status)
    :param billcode: a billcode
    :param status: a new billcode_obj status
    :return: a newly updated billcode_obj object
    """
    logger.info(f"Update a billcode_obj with billcode {billcode}, updating status {body.status}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    allowed_statuss = config["ALLOWED_BILLCODE_STATE"]
    if body.status not in allowed_statuss:
        err = f"Status '{body.status}' is not an allowed billcode_obj statuss, the correct list is: ({allowed_statuss})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        await update_billcode(billcode, body.status)
        billcode_obj = await get_billcode(billcode)
        return success_response(billcode_obj.to_dict())
    except HTTPException as err:
        if err.status_code == 404:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        elif err.status_code == 422:
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/billcodes/status/printed", status_code=HTTP_200_OK)
async def update_billcode_print_status(
        printed_statuses: List[NewPrintedStatus],
        auth=Depends(Auth),
) -> dict:
    """
    Update printed status of a billcode_obj object
    :param billcode: a billcode
    :param is_printed: a printed boolean
    :return: a newly updated billcode_obj object
    """
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    data_arr = []
    for printed_status in printed_statuses:
        data = printed_status.dict()
        data_arr.append(data)

    data_arr, err = await update_printed(data_arr)
    return success_response(data_arr)


@router.put("/billcodes", status_code=HTTP_200_OK)
async def bulk_update_status(
        body: List[NewState],
        auth=Depends(Auth)
) -> dict:
    """
    Update a bulk of billcode_objs status
    """
    logger.info("Update a bulk of billcode_objs status")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    allowed_statuss = config["ALLOWED_BILLCODE_STATE"]
    data_res = []
    for status_obj in body:
        status = status_obj.status
        billcode = status_obj.billcode
        if status not in allowed_statuss:
            item_res = {
                'http_code': HTTP_422_UNPROCESSABLE_ENTITY,
                'billcode': billcode,
                'data': {},
                'msg': f"Status '{status}' is not an allowed billcode_obj statuss, the correct list is: ({allowed_statuss})"
            }
            data_res.append(item_res)
        else:
            try:
                await update_billcode(billcode, status)
                billcode_obj = await get_billcode(billcode)
                item_res = {
                    'http_code': HTTP_200_OK,
                    'billcode': billcode,
                    'data': billcode_obj.to_dict(),
                    'msg': ''
                }
                data_res.append(item_res)
            except HTTPException as err:
                if err.status_code == 404:
                    http_code = HTTP_404_NOT_FOUND
                    item_res = {
                        'http_code': http_code,
                        'billcode': billcode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
                elif err.status_code == 422:
                    http_code = HTTP_422_UNPROCESSABLE_ENTITY
                    item_res = {
                        'http_code': http_code,
                        'billcode': billcode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
                else:
                    http_code = HTTP_500_INTERNAL_SERVER_ERROR
                    item_res = {
                        'http_code': http_code,
                        'billcode': billcode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
            
            except Exception as err:
                http_code = HTTP_500_INTERNAL_SERVER_ERROR
                item_res = {
                    'http_code': http_code,
                    'billcode': billcode,
                    'data': {},
                    'msg': str(err)
                }
                data_res.append(item_res)
                
    return success_response(data_res)

# delete billcode by list
@router.delete("/billcodes", status_code=HTTP_200_OK)
async def delete_batch_billcodes(
    body: DeleteBillcodes,
    auth=Depends(Auth),
) -> dict:
    logger.info("Delete billcode list: %s", body)
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    try:
        billcode_list = await delete_billcodes(billcode_list=body.billcode_list)
        return success_response(billcode_list)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs) from err
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs) from err

@router.delete("/billcodes/{billcode}", status_code=HTTP_200_OK)
async def delete(
        billcode: str,
        auth=Depends(Auth),
) -> dict:
    """
    Delete a billcode_obj object (billcode, qrcode, printed status, status)
    :param billcode: a billcode
    :return: a deleted billcode_obj object
    """
    logger.info("Delete a billcode_obj with billcode %s", billcode)
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_billcode'], detail=errs)
    err = "Unknown errors"
    try:
        await delete_billcode(billcode)

        final_res = {
            'data': "Deleted",
            'detail': []
        }
        return final_res
    except HTTPException as err:
        if err.status_code == 404:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err
        elif err.status_code == 422:
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err
    except Exception as err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err


def init_app(app: FastAPI):
    app.include_router(router, tags=["billcode_obj"])