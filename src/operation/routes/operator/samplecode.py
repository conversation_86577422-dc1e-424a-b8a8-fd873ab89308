

from asyncpg.exceptions import UniqueViolationError
from typing import List, Optional
from fastapi import APIRouter, Depends, FastAPI, HTTPException, Query
from pydantic import BaseModel

from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    # HTTP_408_REQUEST_TIMEOUT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...utils.utils import (
    success_response,
    failure_response,
    format_date,
    DEFAULT_DATE_NO_TIME_ZONE
)
from ...auth import Auth
from ... import logger
from ...config import config
from ...cruds.samplecodes import (
    generate_samplecode,
    get_samplecode,
    update_samplecode,
    update_printed,
    delete_samplecode,
    delete_samplecodes,
    save_samplecode,
    # count_samplecodes,
    get_all_samplecodes,
)

from ...schemas.samplecode import (
    DeleteSamplecodes,
    UpdateSamplecodeInfo,
)


router = APIRouter()


class NewState(BaseModel):
    state: str
    samplecode: str

class NewPrintedStatus(BaseModel):
    samplecode: str
    is_printed: bool

@router.post("/internal/samplecodes", status_code=HTTP_200_OK)
async def internal_create(
    size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"])
) -> list:

    samplecode_objs = []
    i = 0

    while i < size:
        samplecode_obj = generate_samplecode()
        if not await get_samplecode(samplecode_obj['samplecode']):
            await save_samplecode(samplecode_obj)
            samplecode_objs.append(samplecode_obj)
            i += 1

    return success_response(samplecode_objs)

@router.post("/samplecodes", status_code=HTTP_200_OK)
async def create(
    size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"]),
    auth=Depends(Auth),
) -> list:
    logger.info("Generate samplecode_objs")
    """
    Create a list of samplecode_obj object (samplecode, qrcode, printed status, state)
    :return: A list of samplecode_objs
    """

    samplecode_objs = []
    i = 0
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    while i < size:
        samplecode_obj = generate_samplecode()
        if not await get_samplecode(samplecode_obj['samplecode']):
            await save_samplecode(samplecode_obj)
            samplecode_objs.append(samplecode_obj)
            i += 1

    return success_response(samplecode_objs)


@router.get("/samplecodes", status_code=HTTP_200_OK)
async def retrieve_samplecodes(
    page_size: int = Query(10, ge=1, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    samplecode: Optional[str] = Query(None),
    note: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    state: Optional[List[str]] = Query(None),
    auth = Depends(Auth),
) -> dict:
    """
    Get all samplecode_objs with filtering, searching, and sorting
    :param page_size: Size of page
    :param page_number: number of page
    :param samplecode: samplecode of samplecode_obj
    :param state: State of the samplecode_obj
    :return:
    """
    logger.info("Get samplecode_obj info")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        if start_date:
            start_date_std = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date:
            end_date_std = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
            if end_date_std < start_date_std:
                raise ValueError("end date cannot be after start date")
        samplecode_objs, total_match = await get_all_samplecodes(
            size=size,
            offset=offset,
            samplecode=samplecode,
            note=note,
            start_date=start_date_std if start_date is not None else None,
            end_date=end_date_std if end_date is not None else None,
            state=state,
            order_by="updated_at",
        )
        
        # if not total_match:
        #     total_match = total
        res = {
            "data": samplecode_objs,
            'pagination': {
                'page_size': page_size,
                'page_number': page_number,
                'total': total_match
            },
            "detail": []
        }
        return res
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs) from err


@router.get("/samplecodes/{samplecode}", status_code=HTTP_200_OK)
async def retrieve(
        samplecode: str,
        auth=Depends(Auth)
) -> dict:
    """
    Get/Search a samplecode_obj object (samplecode, printed status, state)
    :param samplecode: a samplecode
    :return: a samplecode_obj object
    """
    logger.info("Get a samplecode_obj with samplecode %s", samplecode)
    samplecode_obj = None
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    try:
        samplecode_obj = await get_samplecode(samplecode)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    if samplecode_obj:
        return success_response(samplecode_obj.to_dict())
    else:
        err = f"A samplecode_obj with samplecode {samplecode} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/samplecodes/{samplecode}", status_code=HTTP_200_OK)
async def update_samplecode_infor(
        samplecode: str,
        body: UpdateSamplecodeInfo,
        auth=Depends(Auth),
) -> dict:
    """
    Update a samplecode_obj object with a new state (samplecode, state)
    :param samplecode: a samplecode
    :param state: a new samplecode_obj state
    :return: a newly updated samplecode_obj object
    """
    logger.info(f"Update a samplecode_obj with samplecode {samplecode}, updating state {body.state}")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    allowed_states = config["ALLOWED_SAMPLECODE_STATE"]
    if body.state not in allowed_states:
        err = f"Status '{body.state}' is not an allowed samplecode_obj states, the correct list is: ({allowed_states})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    try:
        await update_samplecode(samplecode, body.state, body.note)
        samplecode_obj = await get_samplecode(samplecode)
        return success_response(samplecode_obj.to_dict())
    except HTTPException as err:
        if err.status_code == 404:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        elif err.status_code == 422:
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/samplecodes/status/printed", status_code=HTTP_200_OK)
async def update_samplecode_print_status(
        printed_statuses: List[NewPrintedStatus],
        auth=Depends(Auth),
) -> dict:
    """
    Update printed status of a samplecode_obj object
    :param samplecode: a samplecode
    :param is_printed: a printed boolean
    :return: a newly updated samplecode_obj object
    """
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    data_arr = []
    for printed_status in printed_statuses:
        data = printed_status.dict()
        data_arr.append(data)

    data_arr, err = await update_printed(data_arr)
    return success_response(data_arr)


@router.put("/samplecodes", status_code=HTTP_200_OK)
async def bulk_update_state(
        body: List[NewState],
        auth=Depends(Auth)
) -> dict:
    """
    Update a bulk of samplecode_objs state
    """
    logger.info("Update a bulk of samplecode_objs state")
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    allowed_states = config["ALLOWED_SAMPLECODE_STATE"]
    data_res = []
    for state_obj in body:
        state = state_obj.state
        samplecode = state_obj.samplecode
        if state not in allowed_states:
            item_res = {
                'http_code': HTTP_422_UNPROCESSABLE_ENTITY,
                'samplecode': samplecode,
                'data': {},
                'msg': f"Status '{state}' is not an allowed samplecode_obj states, the correct list is: ({allowed_states})"
            }
            data_res.append(item_res)
        else:
            try:
                await update_samplecode(samplecode, state)
                samplecode_obj = await get_samplecode(samplecode)
                item_res = {
                    'http_code': HTTP_200_OK,
                    'samplecode': samplecode,
                    'data': samplecode_obj.to_dict(),
                    'msg': ''
                }
                data_res.append(item_res)
            except HTTPException as err:
                if err.status_code == 404:
                    http_code = HTTP_404_NOT_FOUND
                    item_res = {
                        'http_code': http_code,
                        'samplecode': samplecode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
                elif err.status_code == 422:
                    http_code = HTTP_422_UNPROCESSABLE_ENTITY
                    item_res = {
                        'http_code': http_code,
                        'samplecode': samplecode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
                else:
                    http_code = HTTP_500_INTERNAL_SERVER_ERROR
                    item_res = {
                        'http_code': http_code,
                        'samplecode': samplecode,
                        'data': {},
                        'msg': str(err)
                    }
                    data_res.append(item_res)
            
            except Exception as err:
                http_code = HTTP_500_INTERNAL_SERVER_ERROR
                item_res = {
                    'http_code': http_code,
                    'samplecode': samplecode,
                    'data': {},
                    'msg': str(err)
                }
                data_res.append(item_res)
                
    return success_response(data_res)

# delete samplecode by list
@router.delete("/samplecodes", status_code=HTTP_200_OK)
async def delete_batch_samplecodes(
    body: DeleteSamplecodes,
    auth=Depends(Auth),
) -> dict:
    logger.info("Delete samplecode list: %s", body)
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    try:
        samplecode_list = await delete_samplecodes(samplecode_list=body.samplecode_list)
        return success_response(samplecode_list)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs) from err
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs) from err

@router.delete("/samplecodes/{samplecode}", status_code=HTTP_200_OK)
async def delete(
        samplecode: str,
        auth=Depends(Auth),
) -> dict:
    """
    Delete a samplecode_obj object (samplecode, qrcode, printed status, state)
    :param samplecode: a samplecode
    :return: a deleted samplecode_obj object
    """
    logger.info("Delete a samplecode_obj with samplecode %s", samplecode)
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_samplecode'], detail=errs)
    err = "Unknown errors"
    try:
        await delete_samplecode(samplecode)

        final_res = {
            'data': "Deleted",
            'detail': []
        }
        return final_res
    except HTTPException as err:
        if err.status_code == 404:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err
        elif err.status_code == 422:
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err
    except Exception as err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs) from err


def init_app(app: FastAPI):
    app.include_router(router, tags=["samplecode_obj"])