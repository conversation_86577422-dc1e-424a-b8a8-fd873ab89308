from typing import List, Literal, Optional

import requests
from fastapi import APIRouter, Depends, FastAPI, HTTPException, Query
from pydantic import BaseModel
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ... import logger
from ...auth import Auth
from ...config import config
from ...cruds.register_kit import save_status
from ...cruds.register_sample import (
    get_all_samples,
    get_distinct_batch_codes,
    get_new_batch_code,
    get_samples_with_kit_info,
    register,
    update_sample_status,
)
from ...services.sample import get_sample_list_v3
from ...utils.utils import failure_response, success_response

router = APIRouter()


class RegisterSample(BaseModel):
    barcode: str
    batch_barcode: str
    vinmec_id: str
    chip_id: str
    chip_type: Optional[str] = None
    qc_status: str


class UpdateSample(BaseModel):
    id: int
    qc_status: Optional[str] = None
    gender: Optional[str] = None
    positive_tested: Optional[bool] = None
    chip_id: Optional[str] = None
    chip_type: Optional[str] = None
    position: Optional[str] = None


class AnalysisSample(BaseModel):
    batch_barcode: Optional[str] = None
    chip_id: Optional[str] = None


class UpdateStatusAfterScan(BaseModel):
    barcode: str
    status: str
    note: Optional[str] = None


@router.post("/samples", status_code=HTTP_200_OK)
async def create(
    registration_body: RegisterSample,
    auth=Depends(Auth),
) -> dict:
    """
    Register a new sample with its associated information, do not allow to
    register a registered sample, and an user can register multiple samples
    :param registration_body: Request body
    :return: Sample info with an initial status
    """
    data = registration_body.dict()
    logger.info(f"Register a sample with data {data}")
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    if token_claims.get("email"):
        data["technician_name"] = token_claims["email"]
    elif token_claims.get("phone_number"):
        data["technician_name"] = token_claims["phone_number"]
    else:
        data["technician_name"] = ""
    allowed_statuses = config["ALLOWED_SAMPLE_STATUS"]
    if data.get("qc_status") and (data["qc_status"].upper() not in allowed_statuses):
        err = f"Status '{data['qc_status']}' is not an allowed request status ({allowed_statuses})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    allowed_chip_types = config["ALLOWED_CHIP_TYPES"]
    if data.get("chip_type") and (data["chip_type"].upper() not in allowed_chip_types):
        err = f"Chip type '{data['chip_type']}' is not an allowed type ({allowed_chip_types})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    data, err = await register(data)
    if err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        return success_response(registration_body)


@router.get("/samples", status_code=HTTP_200_OK)
async def get_samples(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_NUM_SAMPLES"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    batch_barcode: Optional[str] = Query(None),
    technology: Optional[str] = Query(None),
    chip_id: Optional[str] = Query(None),
):
    """
    Get all Samples with pagination
    :param page_size: Number of rows should be taken
    :param page_number: the current page number
    :param position: the position of sample
    :param barcode: ID of sample
    :param batch_barcode: ID of batch sample
    :param chip_id: chip ID
    :return: A list of Samples
    """
    logger.info("Filter sample info")
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        samples, total_match = await get_all_samples(
            offset=offset,
            size=size,
            barcode=barcode,
            batch_barcode=batch_barcode,
            chip_id=chip_id,
        )
        res_data = [r.to_dict() for r in samples]
        res = {
            "data": res_data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/samples/kit_info", status_code=HTTP_200_OK)
async def get_samples(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_NUM_SAMPLES"]),
    page_number: int = Query(1, ge=1),
    barcode: Optional[str] = Query(None),
    batch_barcode: Optional[str] = Query(None),
    technology: Optional[str] = Query(None),
    chip_id: Optional[str] = Query(None),
):
    """
    Get all Samples Kit info and pagination
    :param page_size: Number of rows should be taken
    :param page_number: the current page number
    :param position: the position of sample
    :param barcode: ID of sample
    :param batch_barcode: ID of batch sample
    :param vinmec_id: vinmec ID
    :param chip_id: chip ID
    :param technician_name: technician name
    :param qc_status: Status of sample
    :return: A list of Samples
    """
    logger.info("Filter sample with kit info")
    size = page_size
    offset = (page_number - 1) * page_size
    try:
        samples, total_match = await get_samples_with_kit_info(
            offset=offset,
            size=size,
            barcode=barcode,
            batch_barcode=batch_barcode,
            chip_id=chip_id,
        )
        res = {
            "data": samples,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total_match,
            },
            "detail": [],
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/samples/new_batch", status_code=HTTP_200_OK)
async def get_new_batch(
    auth=Depends(Auth),
):
    """
    Get unique new batch code
    """
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        batch_code = await get_new_batch_code()
        return batch_code
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/samples/batch_codes", status_code=HTTP_200_OK)
async def get_new_batch(
    auth=Depends(Auth),
):
    """
    Get unique new batch code
    """
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)
    try:
        batch_codes = await get_distinct_batch_codes()
        return batch_codes
    except Exception as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.put("/samples", status_code=HTTP_200_OK)
async def create_new_sample_status(
    update_samples: List[UpdateSample],
    auth=Depends(Auth),
) -> list:
    """
    Update sample
    """

    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err["err_msg"])
        raise HTTPException(status_code=err["err_code"], detail=errs)

    data_arr = []
    allowed_statuses = config["ALLOWED_SAMPLE_STATUS"]
    for update_sample in update_samples:
        data = update_sample.dict()
        data = {k: v for k, v in data.items() if v is not None}

        if data.get("qc_status") and (
            data["qc_status"].upper() not in allowed_statuses
        ):
            err = f"Status '{data['qc_status']}' with id '{data['id']}' is not an allowed request status ({allowed_statuses})"
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        data_arr.append(data)
    data_arr, err = await update_sample_status(data_arr)
    return success_response(data_arr)


@router.post("/samples/status", status_code=HTTP_200_OK)
async def update_status_when_scan_barcode(body: UpdateStatusAfterScan) -> dict:
    data = body.dict()
    res, err = await save_status(data)
    if err:
        errs = failure_response(err)
        raise HTTPException(status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail=errs)

    return success_response(res.to_dict())


@router.get("/samples/all", status_code=HTTP_200_OK)
async def get_sample_list_ctl(
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    gender: Literal["male", "female"] = Query(None),
    nickname: Optional[str] = Query(None),
    filter_status_list: Optional[List[str]] = Query(None),
    current_status: Optional[str] = Query(None),
    workflow: Optional[str] = Query(None),
    product_code: Optional[List[str]] = Query(None),
    sale_pic: Optional[str] = Query(None),
    sale_pic_id: Optional[str] = Query(None),
    account_name: Optional[List[str]] = Query(None),
    batch_number: Optional[int] = Query(None),
    technology: Optional[str] = Query(None),
    sample_collector_name: Optional[str] = Query(None),
    sample_receiver_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    release_start_date: Optional[str] = Query(None),
    release_end_date: Optional[str] = Query(None),
    collection_start_date: Optional[str] = Query(None),
    collection_end_date: Optional[str] = Query(None),
    actual_release_start_date: Optional[str] = Query(None),
    actual_release_end_date: Optional[str] = Query(None),
    sample_collector_unit_name: Optional[str] = Query(None),
    include_deleted: Optional[bool] = Query(False),
    nominator: Optional[str] = Query(None),
    order_by: Optional[str] = "updated_at",
    auth=Depends(Auth),
) -> dict:
    try:
        _, err = await auth.get_token_claims()
        # print("token claims: ", token_claims)

    except Exception:
        logger.info("no access token provided")

    # Get role by access token
    # If role == sale, fitler by pic_id

    logger.info("Getting kit list")
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_sample_list_v3(
            offset=offset,
            size=size,
            order_by=order_by,
            barcode=barcode,
            samplecode=samplecode,
            email=email,
            phone_number=phone_number,
            name=full_name,
            gender=gender,
            nickname=nickname,
            filter_status_list=filter_status_list,
            current_status=current_status,
            workflow=workflow,
            product_code=product_code,
            sale_pic=sale_pic,
            sale_pic_id=sale_pic_id,
            account_name=account_name,
            nominator=nominator,
            batch=batch_number,
            technology=technology,
            sample_collector_name=sample_collector_name,
            sample_receiver_name=sample_receiver_name,
            receipt_start_date=start_date,
            receipt_end_date=end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            collection_start_date=collection_start_date,
            collection_end_date=collection_end_date,
            actual_report_release_start_date=actual_release_start_date,
            actual_report_release_end_date=actual_release_end_date,
            sample_collector_unit_name=sample_collector_unit_name,
            include_deleted=include_deleted,
        )
        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@router.get("/samples/all/pic/{pic_phone_number}", status_code=HTTP_200_OK)
async def get_sample_list_ctl(
    pic_phone_number: str,
    page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
    page_number: int = Query(1, ge=1),
    email: Optional[str] = Query(None),
    phone_number: Optional[str] = Query(None),
    barcode: Optional[str] = Query(None),
    samplecode: Optional[str] = Query(None),
    full_name: Optional[str] = Query(None),
    gender: Literal["male", "female"] = Query(None),
    nickname: Optional[str] = Query(None),
    filter_status_list: Optional[List[str]] = Query(None),
    current_status: Optional[str] = Query(None),
    workflow: Optional[str] = Query(None),
    product_code: Optional[List[str]] = Query(None),
    sale_pic: Optional[str] = Query(None),
    account_name: Optional[List[str]] = Query(None),
    batch_number: Optional[int] = Query(None),
    technology: Optional[str] = Query(None),
    sample_collector_name: Optional[str] = Query(None),
    sample_receiver_name: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    release_start_date: Optional[str] = Query(None),
    release_end_date: Optional[str] = Query(None),
    collection_start_date: Optional[str] = Query(None),
    collection_end_date: Optional[str] = Query(None),
    actual_release_start_date: Optional[str] = Query(None),
    actual_release_end_date: Optional[str] = Query(None),
    sample_collector_unit_name: Optional[str] = Query(None),
    include_deleted: Optional[bool] = Query(False),
    nominator: Optional[str] = Query(None),
    order_by: Optional[str] = "updated_at",
    auth=Depends(Auth),
) -> dict:
    try:
        token_claims, err = await auth.get_token_claims()
    except Exception:
        logger.info("no access token provided")
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED, detail="no access token provided"
        )

    logger.info("Getting kit list")
    size = page_size
    offset = (page_number - 1) * page_size

    try:
        data, total = await get_sample_list_v3(
            offset=offset,
            size=size,
            order_by=order_by,
            barcode=barcode,
            samplecode=samplecode,
            email=email,
            phone_number=phone_number,
            name=full_name,
            gender=gender,
            nickname=nickname,
            filter_status_list=filter_status_list,
            current_status=current_status,
            workflow=workflow,
            product_code=product_code,
            sale_pic=sale_pic,
            account_name=account_name,
            nominator=nominator,
            batch=batch_number,
            technology=technology,
            sample_collector_name=sample_collector_name,
            sample_receiver_name=sample_receiver_name,
            receipt_start_date=start_date,
            receipt_end_date=end_date,
            release_start_date=release_start_date,
            release_end_date=release_end_date,
            collection_start_date=collection_start_date,
            collection_end_date=collection_end_date,
            actual_report_release_start_date=actual_release_start_date,
            actual_report_release_end_date=actual_release_end_date,
            sample_collector_unit_name=sample_collector_unit_name,
            include_deleted=include_deleted,
            pic_phone_number=pic_phone_number,
        )
        res = {
            "data": data,
            "pagination": {
                "page_size": page_size,
                "page_number": page_number,
                "total": total,
            },
            "detail": [],
        }
        return res
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def init_app(app: FastAPI):
    app.include_router(router, tags=["operator_sample"])
