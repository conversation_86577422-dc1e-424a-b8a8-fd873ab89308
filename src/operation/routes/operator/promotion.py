import requests
from fastapi import API<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HTTPException, Query, Depends, Body
from typing import List
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...config import config
from ... import logger
from ...auth import Auth
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email
)

from ...cruds.promotion import (
  count_promotions,
  get_promotion,
  count_matched_promotions,
  get_all_promotions,
  create_promotion,
  update_promotion,
  delete_promotion
)

from ...services.promotion import (
    standardize_promotion_service,
    convert_start_promotion_date,
    convert_end_promotion_date
)

from ...models.models import Promotion
from ...schemas.promotion import *
from datetime import datetime, timedelta

router = APIRouter()

# @router.post("/operation/promotion", status_code=HTTP_200_OK)
# @router.get("/operation/promotions", status_code=HTTP_200_OK)
# @router.get("/operation/promotion/{id}", status_code=HTTP_200_OK)
# @router.put("/operation/promotion/{id}", status_code=HTTP_200_OK)
# @router.delete("/operation/promotion/{id}", status_code=HTTP_200_OK)

router = APIRouter()

@router.post("/promotion", status_code=HTTP_200_OK)
async def admin_create(
  promotion_body: AddPromotion,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  data = promotion_body.dict()
  if not data:
      err = "No changed field to update"
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
  try:
    data = await standardize_promotion_service(data)
     
    logger.info(f"Create a promotion code with data {data}")
    
    data, err = await create_promotion(data)
    if err:
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
    else:
      return success_response(data), None
  except Exception as e:
    err = str(e)
    http_code = HTTP_400_BAD_REQUEST
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/promotions", status_code=HTTP_200_OK)
async def admin_get_promotions(
  page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
  page_number: int = Query(1, ge=1),
  code: Optional[str] = Query(None),
  name: Optional[str] = Query(None),
  discount: Optional[int] = Query(None),
  department: Optional[str] = Query(None),
  s_start_date: Optional[str]=Query(None),
  e_start_date: Optional[str]=Query(None),
  s_end_date: Optional[str]=Query(None),
  e_end_date: Optional[str]=Query(None),
  is_active: Optional[bool]=Query(None),
  auth=Depends(Auth),
):
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)


  s_start_date = await convert_start_promotion_date(s_start_date) if s_start_date else await convert_start_promotion_date('1970-01-01') 
  e_start_date = await convert_end_promotion_date(e_start_date) if e_start_date else await convert_end_promotion_date(datetime.max.strftime('%Y-%m-%d'))
  s_end_date = await convert_start_promotion_date(s_end_date) if s_end_date else await convert_start_promotion_date('1970-01-01') 
  e_end_date = await convert_end_promotion_date(e_end_date) if e_end_date else await convert_end_promotion_date(datetime.max.strftime('%Y-%m-%d'))
  current_date = get_current_date_time_utc_7()

  logger.info(f"Filter promotions info")
  size = page_size
  offset = (page_number - 1) * page_size
  total = await count_promotions()
  if offset > 0 and offset >= total:
      err = "Offset number is too large."
      http_code = HTTP_422_UNPROCESSABLE_ENTITY
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
  try:
      results, total_match = await get_all_promotions(
          offset=offset,
          size=size,
          code=code.lower() if code else None,
          name=name.lower() if name else None,
          discount=discount,
          department=department,
          s_start_date=s_start_date,
          e_start_date=e_start_date,
          s_end_date=s_end_date,
          e_end_date=e_end_date,
          is_active=is_active,
          current_date=current_date,
          order_by="created_at"
      )

      if not total_match:
          total_match = total

      res_data = [res.to_dict() for res in results]
      res = {
          "data": res_data,
          'pagination': {
              'page_size': page_size,
              'page_number': page_number,
              'total': total_match
          },
          "detail": []
      }
      return res
  except Exception as err:
      http_code = HTTP_500_INTERNAL_SERVER_ERROR
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)


#DONE
@router.get("/promotion/{id}", status_code=HTTP_200_OK)
async def admin_get_promotion_info_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  promotion = await get_promotion(id)
  if promotion:
    res_promotion = promotion.to_dict()
    return success_response(res_promotion)
  else:
    err = f"Kit with id {id} can not be found"
    http_code = HTTP_404_NOT_FOUND
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.put("/promotion/{id}", status_code=HTTP_200_OK)
async def admin_update_promotion_info_by_id(
  id: str,
  body: UpdatePromotion,
  auth=Depends(Auth),
) -> dict:
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  data = body.dict()
  data = {k: v for k, v in data.items() if v is not None}
  if not data:
      err = "No changed field to update"
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)


  try:
  
    current_time = get_current_date_time_utc_7()
    new_promotion_info = {
      **data,
      'updated_at': current_time
    }
    new_promotion_info = await standardize_promotion_service(new_promotion_info)

    promotion = await get_promotion(id)
    if not promotion:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    err = await update_promotion(promotion,new_promotion_info)
    if err:
      http_code = HTTP_400_BAD_REQUEST
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)
    else:
      updated_promotion_info = await get_promotion(id)
      return success_response(updated_promotion_info.to_dict())
  except Exception as e:
    err = str(e)
    http_code = HTTP_400_BAD_REQUEST
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)

#DONE
@router.delete("/promotion/{id}", status_code=HTTP_200_OK)
async def admin_delete_promotion_by_id(
  id: str,
  auth=Depends(Auth),
) -> dict:

  logger.info(f"Delete an sale promotion with id {id}")
  _, err = await auth.get_token_claims()
  if err:
    errs = failure_response(err['err_msg'])
    raise HTTPException(status_code=err['err_code'], detail=errs)

  promotion = await get_promotion(id)
  if not promotion:
      err = f"Kit with id {id} can not be found"
      http_code = HTTP_404_NOT_FOUND
      errs = failure_response(err)
      raise HTTPException(status_code=http_code, detail=errs)

  current_time = get_current_date_time_utc_7()
  data = {
    'updated_at': current_time,
    'deleted_at': current_time,
  }

  promotion, err = await delete_promotion(promotion,data)
  if err:
    http_code = HTTP_404_NOT_FOUND
    errs = failure_response(err)
    raise HTTPException(status_code=http_code, detail=errs)
  else:
    updated_promotion_info = await get_promotion(id)
    return success_response(updated_promotion_info.to_dict())

def init_app(app: FastAPI):
    app.include_router(router, tags=["operator_promotion"])