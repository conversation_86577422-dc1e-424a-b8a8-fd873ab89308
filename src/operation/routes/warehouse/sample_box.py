from urllib import response
import requests
from fastapi import APIRout<PERSON>, FastAP<PERSON>, HTTPException, Query, Depends, Body
from typing import List, Optional

from starlette.responses import JSONResponse, StreamingResponse
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from ...cruds.sample_box import export_sample_box_to_excel, export_sample_box_postion_to_excel
from ...dependencies.pagination import PaginationParams
# Collection - model
# /tracking/collection
# /tracking/collections/{id}
# _collections - plural
# _collection - function name
# collection - single
# create_collection_service
# update_collection_service
# delete_collection_by_id_service
# tracking_collection

from ...models.models import TrackingCollection

from ...config import config
from ... import logger
from ...auth import Auth
from ...services.warehouse import WarehouseService
from ...schemas.warehouse import SetSamplePosition
from ...utils.utils import (
    convert_current_utc_to_tz,
    convert_str_to_datetime,
    convert_str_to_iso_datetime,
    success_response,
    failure_response,
    get_current_date_time_utc_7,
    validate_email, success_response_w_pagination, get_role_by_brearer_token
)

from ...cruds.tracking_collection import *


from ...schemas.tracking_collection import *

from ...services.tracking_collection import *

router = APIRouter()


@router.get("/sample-boxes", tags=["warehouse"])
async def get_all_sample_boxes(
    box_code: Optional[str] = Query(None, description="Filter by code"),
    rack_code: Optional[str] = Query(None, description="Filter by rack_code"),
    total: Optional[str] = Query(None, description="Filter by total_sample"),
    created_by: Optional[str] = Query(None, description="Filter by created_by"),
    updated_by: Optional[str] = Query(None, description="Filter by updated_by"),
    from_date: Optional[str] = Query(None, description="Filter by start date"),
    to_date: Optional[str] = Query(None, description="Filter by end date"),
    order_by: Optional[str] = Query("created_at", description="Order by field"),
    order_direction: Optional[str] = Query(
        "desc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
    pagination: PaginationParams = Depends(),

):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        data, total = await WarehouseService.get_sample_box_list(
            box_code=box_code,
            rack_code=rack_code,
            total=total,
            created_by=created_by,
            updated_by=updated_by,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            order_direction=order_direction,
            page_number=pagination.page_number,
            page_size=pagination.page_size
        )
        return success_response_w_pagination(
            body=data,
            pagination={
                "page_size": pagination.page_size,
                "page_number": pagination.page_number,
                "total": total,
            },
        )
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))

@router.get("/sample-boxes/export", status_code=HTTP_200_OK)
async def export_sample_boxes(
    box_code: Optional[str] = Query(None, description="Filter by code"),
    rack_code: Optional[str] = Query(None, description="Filter by rack_code"),
    total: Optional[int] = Query(None, description="Filter by total_sample"),
    created_by: Optional[str] = Query(None, description="Filter by created_by"),
    updated_by: Optional[str] = Query(None, description="Filter by updated_by"),
    from_date: Optional[str] = Query(None, description="Filter by start date"),
    to_date: Optional[str] = Query(None, description="Filter by end date"),
    order_by: Optional[str] = Query("created_at", description="Order by field"),
    order_direction: Optional[str] = Query(
        "desc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
    pagination: PaginationParams = Depends(),
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        data = await WarehouseService.get_sample_box_list_all(
            box_code=box_code,
            rack_code=rack_code,
            total=total,
            created_by=created_by,
            updated_by=updated_by,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            # order_direction=order_direction,
        )

        excel_io = export_sample_box_to_excel(data)
        return StreamingResponse(
            excel_io,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=Danh_sach_hop_luu_mau.xlsx"},
        )
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

@router.get("/sample-boxes/{box_code}", tags=["warehouse"])
async def get_sample_box_detail(
    box_code: str,
    auth: Auth = Depends(Auth),

):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        data = await WarehouseService.get_sample_box_detail(
            box_code=box_code,
        )
        return success_response(data)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.post("/sample-boxes", tags=["warehouse"])
async def set_sample_position(
    req: SetSamplePosition,
    auth: Auth = Depends(Auth),
):
    token_claims, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    sub_name = token_claims.get("name") if token_claims else None

    try:
        data = await WarehouseService.set_sample_positions(
            box_code=req.box_code,
            rack_code=req.rack_code,
            positions=req.sample_positions,
            sub_name=sub_name
        )
        return success_response(data)
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))


@router.get("/sample-box-position", tags=["warehouse"])
async def get_all_sample_boxes(
    barcode: Optional[str] = Query(None, description="Filter by code"),
    position: Optional[str] = Query(None, description="Filter by position"),
    box_code: Optional[str] = Query(None, description="Filter by box_code"),
    rack_code: Optional[str] = Query(None, description="Filter by rack_code"),
    from_date: Optional[str] = Query(None, description="Filter by start date"),
    to_date: Optional[str] = Query(None, description="Filter by end date"),
    order_by: Optional[str] = Query("created_at", description="Order by field"),
    order_direction: Optional[str] = Query(
        "desc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
    pagination: PaginationParams = Depends(),

):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)
    try:
        data, total = await WarehouseService.get_sample_box_position_list(
            barcode=barcode,
            position=position,
            box_code=box_code,
            rack_code=rack_code,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            # order_direction=order_direction,
            page_number=pagination.page_number,
            page_size=pagination.page_size
        )
        return success_response_w_pagination(
            body=data,
            pagination={
                "page_size": pagination.page_size,
                "page_number": pagination.page_number,
                "total": total,
            },
        )
    except HTTPException as e:
        return JSONResponse(
            status_code=e.status_code, content=failure_response(e.detail)
        )
    except Exception as e:
        logger.error(f"Error: {e}")
        return JSONResponse(status_code=500, content=failure_response(str(e)))

@router.get("/sample-box-position/export", status_code=HTTP_200_OK)
async def export_sample_boxes(
    barcode: Optional[str] = Query(None, description="Filter by code"),
    position: Optional[str] = Query(None, description="Filter by position"),
    box_code: Optional[str] = Query(None, description="Filter by box_code"),
    rack_code: Optional[str] = Query(None, description="Filter by rack_code"),
    from_date: Optional[str] = Query(None, description="Filter by start date"),
    to_date: Optional[str] = Query(None, description="Filter by end date"),
    order_by: Optional[str] = Query("created_at", description="Order by field"),
    order_direction: Optional[str] = Query(
        "desc", description="Order direction (asc/desc)"
    ),
    auth: Auth = Depends(Auth),
):
    _, err = await auth.get_token_claims()
    if err:
        errs = failure_response(err['err_msg'])
        raise HTTPException(status_code=err['err_code'], detail=errs)

    try:
        data, total = await WarehouseService.get_sample_box_position_list_all(
            barcode=barcode,
            position=position,
            box_code=box_code,
            rack_code=rack_code,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            # order_direction=order_direction,
        )

        excel_io = export_sample_box_postion_to_excel(data)
        return StreamingResponse(
            excel_io,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=Danh_sach_mau_trong_hop_luu_mau.xlsx"},
        )
    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.Timeout as err:
        http_code = HTTP_408_REQUEST_TIMEOUT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except requests.exceptions.RequestException as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except ValueError as err:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    except Exception as err:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


