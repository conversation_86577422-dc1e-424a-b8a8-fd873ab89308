import time
import uuid
from fastapi import <PERSON><PERSON><PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware

from .. import logger

class RequestStatisticsMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        request_id = str(uuid.uuid4())

        logger.info(f"[RequestID {request_id}] Start {request.method} {request.url.path}")
        start_time = time.time()
        response = await call_next(request)
        duration = time.time() - start_time
        logger.info(f"[RequestID {request_id}] End {request.method} {request.url.path} | Status: {response.status_code} | Duration: {duration:.4f}s")
        return response