import uuid
from typing import Optional
from ..models.models import db, Source
from ..utils.utils import get_current_date_time

SOURCE_QUERY = '''
    select
        s.id,
        s.account_history_id,
        s.nominator_id,
        s.freelancer_id,
        a.id as account_id,
        a.name as account_name
    from
        source s
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
    where s.id = 
'''

async def get_source_by_id(id: uuid.uuid4):
    source = await Source.get(id)
    return source

async def create_source(source: Source):
    # source.id = uuid.uuid4()
    await source.create()

async def create_source_by_staffs_n_account(staff_id, account_history_id, freelancer_id):
    results = Source.query
    if staff_id == "":
        staff_id = None
    results = results.where(Source.freelancer_id==freelancer_id)
    results = results.where(Source.nominator_id==staff_id)
    results = results.where(Source.account_history_id==account_history_id)
    source = [r.to_dict() for r in await results.gino.all()]
    if len(source) > 1:
        raise ValueError("invalid source")
    if len(source) == 0:
        source = Source(
            id=uuid.uuid4(),
            account_history_id=account_history_id,
            nominator_id=staff_id,
            freelancer_id=freelancer_id
        )
        await create_source(source=source)
        return source.to_dict()
    return source[0]
    pass

async def create_source_by_staff_and_account(staff_id, account_history_id):
    results = Source.query
    if staff_id == "":
        staff_id = None
    results = results.where(Source.nominator_id==staff_id)
    results = results.where(Source.account_history_id==account_history_id)
    results = results.where(Source.freelancer_id is None)
    source = [r.to_dict() for r in await results.gino.all()]
    if len(source) > 1:
        raise ValueError("invalid source")
    if len(source) == 0:
        source = Source(
            id=uuid.uuid4(),
            account_history_id=account_history_id,
            nominator_id=staff_id,
        )
        await create_source(source=source)
        return source.to_dict()
    return source[0]

async def get_source_detail_by_id(id):
    raw_query = f"{SOURCE_QUERY}'{id}'"
    results = await db.all(db.text(raw_query))
    if len(results) != 1:
        raise ValueError(f"source detail not found for id: {id}")
    return results[0]
