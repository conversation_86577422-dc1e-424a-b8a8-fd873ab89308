import uuid
from typing import Optional
from ..models.models import db, Promotion
from ..utils.utils import get_current_date_time_utc_7
from .. import logger
from gino.loader import <PERSON>umnLoader
from sqlalchemy import Date, cast
from datetime import datetime, timedelta

async def count_promotions():
    total = db.select([db.func.count(Promotion.id)])
    return await total.gino.scalar()

async def get_promotion(id: uuid.uuid4) -> Promotion:
    promotion = await Promotion.get(id)
    return promotion


async def count_matched_promotions(
    code: Optional[str] = None,
    name: Optional[str] = None,
    discount: Optional[int] = None,
    department: Optional[str] = None,
    s_start_date: Optional[str] = None,
    e_start_date: Optional[str] = None,
    s_end_date: Optional[str] = None, # using VAR in ConfigMap
    e_end_date: Optional[str] = None, # using VAR in ConfigMap
    is_active: Optional[bool] = None,
    current_date: Optional[str] = None
):
    results = db.select([db.func.count()])
    results = results.where(Promotion.deleted_at == None)
    results = results.where(Promotion.start_date.between(s_start_date,e_start_date))
    results = results.where(Promotion.end_date.between(s_end_date,e_end_date))
    if is_active:
        # results = results.where(cast(Promotion.start_date, Date) <= current_date)
        # results = results.where(cast(Promotion.end_date, Date) + timedelta(days=10) >= current_date)
        results = results.where(Promotion.start_date <= current_date)
        results = results.where(Promotion.end_date + timedelta(days=10)>= current_date)
    if code:
        results = results.where(db.func.lower(Promotion.code).contains(f"%{code}"))
    if name:
        results = results.where(db.func.lower(Promotion.name).contains(f"%{name}") )
    if discount:
        results = results.where(Promotion.discount == discount)
    if department:
        results = results.where(Promotion.department.contains(f"%{department}"))

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0
    
async def get_all_promotions(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    code: Optional[str] = None,
    name: Optional[str] = None,
    discount: Optional[int] = None,
    department: Optional[str] = None,
    s_start_date: Optional[str] = None,
    e_start_date: Optional[str] = None,
    s_end_date: Optional[str] = None, # using VAR in ConfigMap
    e_end_date: Optional[str] = None, # using VAR in ConfigMap
    is_active: Optional[bool] = None,
    current_date: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = Promotion.query
    results = results.where(Promotion.deleted_at == None)
    results = results.where(Promotion.start_date.between(s_start_date,e_start_date))
    results = results.where(Promotion.end_date.between(s_end_date,e_end_date))
    if is_active:
        # results = results.where(cast(Promotion.start_date, Date) <= current_date)
        # results = results.where(cast(Promotion.end_date, Date) >= current_date)
        results = results.where(Promotion.start_date <= current_date)
        results = results.where(Promotion.end_date + timedelta(days=10) >= current_date)
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if code:
        results = results.where(db.func.lower(Promotion.code).contains(f"%{code}"))
    if name:
        results = results.where(db.func.lower(Promotion.name).contains(f"%{name}"))
    if discount:
        results = results.where(Promotion.discount == discount)
    if department:
        results = results.where(Promotion.department.contains(f"%{department}"))
    if order_by:
        results = results.order_by(getattr(Promotion, order_by).desc())
    total = await count_matched_promotions(
        code=code,
        name=name,
        discount=discount,
        department=department,
        s_start_date=s_start_date,
        e_start_date=e_start_date,
        s_end_date=s_end_date,
        e_end_date=e_end_date,
        is_active=is_active,
        current_date=current_date
    )
    return [r for r in await results.gino.all()], total

async def existed_name(code: str) -> bool:
    total = await db.select([db.func.count()]).where(Promotion.code == code).where(Promotion.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_promotion(data: dict):
    if await existed_name(data['code']):
        err = f"Error: Promotion with code {data['code']} is already existed"
        return data, err
    current_time = get_current_date_time_utc_7()

    data = {
        'id': uuid.uuid4(),
        'code': data['code'],
        'name': data['name'],
        'discount': data['discount'], # if data.get('discount') else None
        'department': data['department'],
        'start_date': data['start_date'],
        'end_date': data['end_date'],
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await Promotion.create(**data)
    return result.to_dict(), None
    
async def update_promotion(promotion: Promotion, data: dict):
    if promotion.deleted_at:
        err = f"Promotion with if: {promotion.id} already deleted!"
        return err
    await promotion.update(**data).apply()
    logger.info(f"Promotion with name: {promotion.name} updated")
    return None
    
async def delete_promotion(promotion,data):
    current_time = get_current_date_time_utc_7()
    if not promotion:
        err = f"Promotion with id: {promotion.id} cannot be found"
        return None, err
    elif promotion.deleted_at:
        err = f"Promotion with id: {promotion.id} already deleted!"
        return None, err

    await promotion.update(**data).apply()
    logger.info(f"Promotion with name: {promotion.name} deleted")
    return promotion, None
