import os

from typing import Optional, List

from ..utils.utils import get_current_date_time_utc_7
from ..utils.utils import gen_random_unique_string, ensure_trailing_slash, generate_barcode_string_v2
from ..models.models import db, Code as CodeModel
from ..config import config
from sqlalchemy import Date, cast
from .. import logger
from datetime import datetime

CODE_LIST_QUERY = """
    select
        barcode,
        qrcode,
        state,
        created_time,
        updated_time,
        printed,
        product_code,
        product_name,
        note
    from code
"""

CODE_LIST_COUNT_QUERY = """
    select count(barcode)
    from code
"""


async def get_code(barcode: str) -> dict:
    barcode = await CodeModel.get(barcode)
    return barcode


async def update_code(barcode: str, state: str, note: str=None, updated_time: str=None):
    code = await get_code(barcode)
    current_time = get_current_date_time_utc_7()
    update_body = {
        "state": state,
        "updated_time": current_time if updated_time is None else updated_time,
    }
    if note:
        update_body.update(
            {
                "note": note,
            }
        )
    await code.update(**update_body).apply()


async def update_printed(data_arr: list):
    res = []
    current_time = get_current_date_time_utc_7()
    for data in data_arr:
        code = await get_code(data['id'])
        if code is None:
            err = "Cannot find code id in database"
            res.append({"id": data['id'], "err_msg": err})
            continue
        updated_body = {
            'printed': data['is_printed'],
            'updated_time': current_time
        }
        await code.update(**updated_body).apply()
        res.append(code.to_dict())
    return res, None


async def count_codes():
    total = db.select([db.func.count(CodeModel.barcode)])
    return await total.gino.scalar()


async def delete_code(barcode: str):
    return await update_code(barcode=barcode, 
                             state=config['DEACTIVATED_CODE_STATE'],
                             updated_time=datetime.strptime("2000-01-01", "%Y-%m-%d"))
    
async def delete_codes(barcode_list: List[str]):
    try:
        for barcode in barcode_list:
            await delete_code(barcode=barcode)
        return barcode_list
    except Exception as e:
        raise ValueError(f"delete barcode {barcode} with error {e}")

async def get_all_codes_v1(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    barcode: Optional[str] = None,
    product_code: Optional[str] = None,
    state: Optional[str] = None,
    note: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = None,
):
    results = CodeModel.query
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if barcode:
        results = results.where(CodeModel.barcode.like(f"%{barcode}%"))
    if state:
        results = results.where(CodeModel.state == state)
    if product_code:
        results = results.where(CodeModel.barcode.like(f"%{product_code}"))
    if note:
        results = results.where(CodeModel.note.like(f"%{note}%"))
    if start_date:
        results = results.where(cast(CodeModel.created_time, Date) >= start_date)
    if end_date:
        results = results.where(cast(CodeModel.created_time, Date) <= end_date)
    if order_by:
        results = results.order_by(getattr(CodeModel, order_by).desc())
    total = await count_matched_codes(
        barcode=barcode,
        state=state,
        start_date=start_date,
        end_date=end_date,
    )
    return [r for r in await results.gino.all()], total


async def count_matched_codes(
    barcode: Optional[str] = None,
    product_code: Optional[str] = None,
    state: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
):
    results = db.select([db.func.count(CodeModel.barcode)])
    if barcode:
        results = results.where(CodeModel.barcode.like(f"%{barcode}%"))
    if state:
        results = results.where(CodeModel.state == state)
    if product_code:
        results = results.where(CodeModel.barcode.like(f"%{product_code}"))
    if start_date:
        results = results.where(cast(CodeModel.created_time, Date) >= start_date)
    if end_date:
        results = results.where(cast(CodeModel.created_time, Date) <= end_date)
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.exception(e)
        return 0
    
async def get_all_codes(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    barcode: Optional[str] = None,
    product_code: Optional[str] = None,
    state: Optional[List[str]] = None,
    note: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = None,
):
    query_filter = ""
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"barcode = '{barcode}' "
    if state:
        state_filter = ''
        s_str = [f"'{s}'" for s in state]
        state_filter = ','.join(s_str)
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"state in ({state_filter}) "
    if product_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"barcode like '%{product_code}' "
    if note:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(note) like '%{note.lower()}%' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_time::date >= '{start_date}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_time::date <= '{end_date}' "
    raw_count_query = CODE_LIST_COUNT_QUERY + "where " + query_filter if query_filter != "" else CODE_LIST_COUNT_QUERY
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query = ""
    if query_filter.startswith("order") or query_filter.startswith("limit") or query_filter.startswith("offset"):
        raw_query = CODE_LIST_QUERY + query_filter
    else:
        raw_query = CODE_LIST_QUERY + "where " + query_filter if query_filter != "" else CODE_LIST_QUERY
    results = await db.all(db.text(raw_query))
    return results, total

async def duplicated_code(barcode: str) -> bool:
    code = await get_code(barcode)
    if code and code.state == config['USED_CODE_STATE']:
        return True
    return False


async def available_code(barcode: str) -> bool:
    code = await get_code(barcode)
    if code and code.state in (config['DEFAULT_CODE_STATE'], config['SHIPPED_CODE_STATE']):
        return True
    return False


async def deactivated_code(barcode: str) -> bool:
    code = await get_code(barcode)
    if code and code.state == config['DEACTIVATED_CODE_STATE']:
        return True
    return False


async def existed_code(barcode: str) -> bool:
    code = await get_code(barcode)
    if code:
        return True
    return False


async def save_code(data: dict):
    await CodeModel.create(**data)


def generate_code(product_code, product_name=None, barcode=None):
    if not barcode:
        barcode = gen_random_unique_string(product_code)
    current_time = get_current_date_time_utc_7()
    register_url = ensure_trailing_slash(config['REGISTER_URL']) + str(barcode)
    data = {
        'barcode': barcode,
        'qrcode': register_url,
        'printed': False,
        'state': config['DEFAULT_CODE_STATE'],
        'product_code': product_code,
        'product_name': product_name,
        'created_time': current_time,
        'updated_time': current_time,
    }
    return data

def generate_barcode_v2(product_code, product_name=None, barcode=None):
    if not barcode:
        barcode = generate_barcode_string_v2(product_code)
    current_time = get_current_date_time_utc_7()
    register_url = ensure_trailing_slash(config['REGISTER_URL']) + str(barcode)
    data = {
        'barcode': barcode,
        'qrcode': register_url,
        'printed': False,
        'state': config['DEFAULT_CODE_STATE'],
        'product_code': product_code,
        'product_name': product_name,
        'created_time': current_time,
        'updated_time': current_time,
    }
    return data



def generate_used_code(product_code, product_name=None, barcode=None):
    if not barcode:
        barcode = gen_random_unique_string(product_code)
    current_time = get_current_date_time_utc_7()
    register_url = ensure_trailing_slash(config['REGISTER_URL']) + str(barcode)
    data = {
        'barcode': barcode,
        'qrcode': register_url,
        'printed': False,
        'state': config['USED_CODE_STATE'],
        'product_code': product_code,
        'product_name': product_name,
        'created_time': current_time,
        'updated_time': current_time,
    }
    return data

def generate_used_barcode_v2(product_code, product_name=None, barcode=None):
    if not barcode:
        barcode = generate_barcode_string_v2(product_code)
    current_time = get_current_date_time_utc_7()
    register_url = ensure_trailing_slash(config['REGISTER_URL']) + str(barcode)
    data = {
        'barcode': barcode,
        'qrcode': register_url,
        'printed': False,
        'state': config['USED_CODE_STATE'],
        'product_code': product_code,
        'product_name': product_name,
        'created_time': current_time,
        'updated_time': current_time,
    }
    return data
