import uuid
from typing import Optional
from ..models.models import TrackingUnitLocation, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import (
    get_current_date_time_utc_7, 
    failure_response, 
    admin_create_phone_user, 
    parse_user_information
)
from ..utils.message_service import (
    send_mess_queue,
    add_group_cccd_new_user
)
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from .tracking_unit import *

UNIT_LOCATION_LIST_QUERY_V3 = """
    select
        tul.id,
        tul.unit_id,
        tul.name,
        tul.created_at,
        tul.updated_at,
        tul.deleted_at
    from
        tracking_unit_location tul
"""

UNIT_LOCATION_LIST_COUNT_QUERY_V3 = """
    select
        count(tul.id)
    from
        tracking_unit_location tul
"""

async def get_tracking_unit_location_by_id(id: uuid.uuid4):
    tracking_unit_location = await TrackingUnitLocation.get(id)
    return tracking_unit_location

async def get_all_tracking_unit_locations(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="tul.created_at",
    name: Optional[str] = None,
    unit_id: Optional[int] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(tul.'name') like '%{name.lower()}%' """

    if unit_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tul.unit_id = '{unit_id}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tul.deleted_at is null """
    
    raw_count_query = UNIT_LOCATION_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else UNIT_LOCATION_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = UNIT_LOCATION_LIST_QUERY_V3 + query_filter
    else:
        raw_query = UNIT_LOCATION_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else UNIT_LOCATION_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_unit_location_existed(name: str, unit_id: int):
    total = await db.select([db.func.count()]).where(TrackingUnitLocation.name == name).where(TrackingUnitLocation.unit_id == unit_id).where(TrackingUnitLocation.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_tracking_unit_location(data: dict):
    if not await get_tracking_unit_by_id(id=data['unit_id']):
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(f"Unit with id {data['unit_id']} not found")
        raise HTTPException(status_code=http_code, detail=errs)

    if await is_tracking_unit_location_existed(data['name'], data['unit_id']):
        err = f"Error: Integration TrackingUnitLocation with name {data['name']} and unit_id {data['unit_id']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        'name': data['name'],
        'unit_id': data['unit_id'],
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await TrackingUnitLocation.create(**data)
    return result.to_dict()

async def update_tracking_unit_location(tracking_unit_location: TrackingUnitLocation, data: dict):
    if tracking_unit_location.deleted_at:
        err = f"TrackingUnitLocation with name: {tracking_unit_location.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_unit_location.update(**data).apply()
    logger.info(f"TrackingUnitLocation with name: {tracking_unit_location.name} updated")
    return None


async def delete_tracking_unit_location(tracking_unit_location: TrackingUnitLocation):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_unit_location:
        err = f"TrackingUnitLocation with name: {tracking_unit_location.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_unit_location.deleted_at:
        err = f"TrackingUnitLocation with: {tracking_unit_location.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_unit_location.update(**data).apply()
    logger.info(f"TrackingUnitLocation with name: {tracking_unit_location.name} deleted")


