import uuid
from typing import Optional
from ..models.models import AdnResult, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# AdnResult - model 
# RESULT - QUERY adn_integration_id
# _adn_result - function adn_integration_id
# adn_result - single
# results - plural
# adn_integration_id -- search key

RESULT_LIST_QUERY_V3 = """
    select
        ar.id,
        ar.adn_integration_id,
        ar."tenFileAD<PERSON>",
        ar."congNghe",
        ar."tenKit",
        ar."tenThuMucTho",
        ar."loaiDu<PERSON><PERSON>",
        ar.gs_adn_result_s3_url,
        ar.gs_raw_result_s3_url,
        ar.created_at,
        ar.updated_at,
        ar.deleted_at
    from
        adn_result ar
"""

RESULT_LIST_COUNT_QUERY_V3 = """
    select
        count(ar.id)
    from
        adn_result ar
"""

async def get_adn_result_by_id(id: uuid.uuid4):
    adn_result = await AdnResult.get(id)
    return adn_result

async def get_adn_result_via_adn_integration_id(adn_integration_id: str):
    adn_result = await AdnResult.query.where(AdnResult.adn_integration_id == adn_integration_id).where(AdnResult.deleted_at == None).gino.first()
    return adn_result #.to_dict()
    

async def get_all_results(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="ar.created_at",
    tenFileADN: Optional[str] = None,
    congNghe: Optional[str] = None,
    tenKit: Optional[str] = None,
    tenThuMucTho: Optional[str] = None,
    loaiDuLieu: Optional[str] = None,
    adn_integration_id: Optional[str] = None,
    adn_result_id: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if tenFileADN:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(ar.'tenFileADN') like '%{tenFileADN.lower()}%' """
    if congNghe:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""ar.'congNghe' = '{str(congNghe)}' """
    if tenKit:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""ar.'tenKit' = '{str(tenKit)}' """
    if tenThuMucTho:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(ar.'tenThuMucTho') like '%{tenThuMucTho.lower()}%' """
    if loaiDuLieu:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""ar.'loaiDuLieu' = '{str(loaiDuLieu)}' """
    if adn_integration_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""ar.adn_integration_id = '{str(adn_integration_id)}' """
    if adn_result_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""ar.id = '{str(adn_result_id)}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""ar.deleted_at is null """
    
    raw_count_query = RESULT_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else RESULT_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = RESULT_LIST_QUERY_V3 + query_filter
    else:
        raw_query = RESULT_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else RESULT_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_adn_result_existed(adn_integration_id: str):
    total = await db.select([db.func.count()]).where(AdnResult.adn_integration_id == adn_integration_id).where(AdnResult.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_adn_result(data: dict):
    """
    adn_integration_id: str
    tenFileADN: str
    congNghe: Literal[tuple(config['ALLOWED_TECHNOLOGY'])]
    tenKit: str
    tenThuMucTho: Optional[str]
    loaiDuLieu: Literal['STR','mtADN','SNP']
    gs_adn_result_s3_url: Optional[str]
    gs_raw_result_s3_url: Optional[str]
    """
    if await is_adn_result_existed(data['adn_integration_id']):
        err = f"Error: AdnResult with adn_integration_id {data['adn_integration_id']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await AdnResult.create(**data)
    return result.to_dict()

async def update_adn_result(adn_result: AdnResult, data: dict):
    if adn_result.deleted_at:
        err = f"AdnResult with adn_integration_id: {adn_result.adn_integration_id} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await adn_result.update(**data).apply()
    logger.info(f"AdnResult with adn_integration_id: {adn_result.adn_integration_id} updated")
    return None


async def delete_adn_result(adn_result: AdnResult):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not adn_result:
        err = f"AdnResult with adn_integration_id: {adn_result.adn_integration_id} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif adn_result.deleted_at:
        err = f"AdnResult with: {adn_result.adn_integration_id} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await adn_result.update(**data).apply()
    logger.info(f"AdnResult with adn_integration_id: {adn_result.adn_integration_id} deleted")
