from ..models.models import db, Chip, SampleMapping, Batch<PERSON>apping, Batch, Plate
from ..utils.utils import get_current_date_time_utc_7
from .. import logger
from typing import Optional
from ..schemas.batch import *
from ..schemas.batch_mapping import *

async def create_batch_mapping(batch_mapping_req: AddBatchMapping):
    batch_mapping = BatchMapping(
        plate_id=batch_mapping_req.plate_id,
        batch_id=batch_mapping_req.batch_id,
        created_at=get_current_date_time_utc_7(),
        updated_at=get_current_date_time_utc_7(),
        wetlab_date= batch_mapping_req.wetlab_date,
        drylab_date= batch_mapping_req.drylab_date,
        raw_data_uploaded_date= batch_mapping_req.raw_data_uploaded_date,
        raw_report_to_reviewers= batch_mapping_req.raw_report_to_reviewers
    )
    await batch_mapping.create()
    return batch_mapping.to_dict()


async def get_batch_by_number(type: str, number: int) -> Batch:
    batch = await Batch.query.where(Batch.type==type.upper()).where(Batch.number==number).gino.first()
    batch_mapping = await BatchMapping.query.where(BatchMapping.batch_id == batch.id).gino.first()
    return batch_mapping

async def get_batch_mapping_by_plate_id(plate_id: str) -> BatchMapping:
    batch_mapping = await BatchMapping.query.where(BatchMapping.plate_id == plate_id).gino.first()
    return batch_mapping

async def update_batch_mapping_w_number(type: str, number: int, data: dict):

    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    # await batch.update(**update_data.dict()).apply()
    batch_mapping = await get_batch_by_number(type,number)
    await batch_mapping.update(**data).apply()
    return batch_mapping.to_dict()
