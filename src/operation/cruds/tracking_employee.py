import uuid
from typing import Optional

from httpx import get
from ..models.models import TrackingEmployee, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import (get_current_date_time_utc_7, failure_response, standardize_full_name, standardize_name)
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# TrackingEmployee - model 
# EMPLOYEE - QUERY NAME
# _tracking_employee - function name
# tracking_employee - single
# employees - plural
# name -- search key

EMPLOYEE_LIST_QUERY_V3 = """
    select
        te.id,
        te."hoTen<PERSON><PERSON><PERSON><PERSON>",
        te."hocVi",
        te."soDinhDanh",
        te."chucVu",
        te.unit_id,
        tu."tenDonVi",
        tu.gs_area,
        tu.gs_area_code,
        te.created_at,
        te.updated_at,
        te.deleted_at
    from
        tracking_employee te
        INNER JOIN tracking_unit tu ON te.unit_id = tu.id
"""

EMPLOYEE_LIST_COUNT_QUERY_V3 = """
    select
        count(te.id)
    from
        tracking_employee te
        INNER JOIN tracking_unit tu ON te.unit_id = tu.id
"""

async def get_tracking_employee_by_id(id: uuid.uuid4):
    tracking_employee = await TrackingEmployee.get(id)
    return tracking_employee

@standardize_name
async def get_tracking_employee_by_name_n_unit_id(
    employee_name: str,
    unit_id:int 
):
    if unit_id is None:
        return None
    tracking_employee = await TrackingEmployee.query.where(TrackingEmployee.hoTenNhanVien == employee_name).where(TrackingEmployee.unit_id == int(unit_id)).gino.first()
    return tracking_employee

@standardize_name
async def get_tracking_employee_dict_by_name_n_unit_id(
    employee_name: str,
    unit_id:int 
):
    tracking_employee = await get_tracking_employee_by_name_n_unit_id(employee_name, unit_id)
    return tracking_employee.to_dict() if tracking_employee else None

async def get_all_employees(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="te.created_at",
    hoTenNhanVien: Optional[str] = None,
    tenDonVi: Optional[str] = None,
    soDinhDanh: Optional[str] = None,
    chucVu: Optional[str] = None,
    gs_area: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if hoTenNhanVien:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(te."hoTenNhanVien") like '%{hoTenNhanVien.lower()}%'"""
    if soDinhDanh:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""te."soDinhDanh" = '{str(soDinhDanh)}' """
    if tenDonVi:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(tu."tenDonVi") = '{tenDonVi.lower()}' """
    if chucVu:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""te."chucVu" = '{str(chucVu)}' """
    if gs_area:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tu."gs_area" = '{str(gs_area)}'"""
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"te.deleted_at is null "
    
    print("query_filter: ", query_filter)
    raw_count_query = EMPLOYEE_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else EMPLOYEE_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = EMPLOYEE_LIST_QUERY_V3 + query_filter
    else:
        raw_query = EMPLOYEE_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else EMPLOYEE_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_employee_existed(soDinhDanh: str, unit_id: int):
    total = await db.select([db.func.count()]).where(TrackingEmployee.soDinhDanh == soDinhDanh).where(TrackingEmployee.unit_id == int(unit_id)).where(TrackingEmployee.deleted_at == None).gino.scalar()
    return True if total > 0 else False

@standardize_name
async def is_tracking_employee_existed_via_name_and_unit_id(hoTenNhanVien: str, unit_id: int):
    total = await db.select([db.func.count()]).where(TrackingEmployee.hoTenNhanVien == hoTenNhanVien).where(TrackingEmployee.unit_id == int(unit_id)).where(TrackingEmployee.deleted_at == None).gino.scalar()
    return True if total > 0 else False


async def create_tracking_employee(data: dict):
    """
    hoTenNhanVien: str
    hocVi: str
    soDinhDanh: str
    chucVu: str
    unit_id: int
    """
    if await is_tracking_employee_existed(data['soDinhDanh'], data['unit_id']):
        err = f"Error: TrackingEmployee with CCCD {data['soDinhDanh']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    if await is_tracking_employee_existed_via_name_and_unit_id(data['hoTenNhanVien'], data['unit_id']):
        err = f"Error: TrackingEmployee with name {data['hoTenNhanVien']} and unit_id {data['unit_id']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data['hoTenNhanVien'] = standardize_full_name(data['hoTenNhanVien'])
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await TrackingEmployee.create(**data)
    return result.to_dict()

async def update_tracking_employee(tracking_employee: TrackingEmployee, data: dict):
    if tracking_employee.deleted_at:
        err = f"TrackingEmployee with hoTenNhanVien: {tracking_employee.hoTenNhanVien} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_employee.update(**data).apply()
    logger.info(f"TrackingEmployee with hoTenNhanVien: {tracking_employee.hoTenNhanVien} updated")
    return None


async def delete_tracking_employee(tracking_employee: TrackingEmployee):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_employee:
        err = f"TrackingEmployee with name: {tracking_employee.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_employee.deleted_at:
        err = f"TrackingEmployee with: {tracking_employee.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_employee.update(**data).apply()
    logger.info(f"TrackingEmployee with name: {tracking_employee.name} deleted")
