from typing import List, Optional
from datetime import datetime

from ..utils.utils import get_current_date_time_utc_7
from ..models.models import db, <PERSON>ple, Kit, SampleMeta
from ..config import config
from .. import logger
from .register_kit import get_kit
from .aws import validate_files_s3, run_pipeline, BUCKET, run_pipeline_extended, get_files_from_s3, validate_files_s3_from_list
from .sample_meta import get_sample_meta_by_id

MAPPING_POSITION = [
    'R01C01', 'R02C01', 'R03C01', 'R04C01', 'R05C01', 'R06C01', 'R07C01', 'R08C01',
    'R09C01', 'R10C01', 'R11C01', 'R12C01', 'R01C02', 'R02C02', 'R03C02', 'R04C02',
    'R05C02', 'R06C02', 'R07C02', 'R08C02', 'R09C02', 'R10C02', 'R11C02', 'R12C02',
]

async def get_sample_by_id(id: int) -> dict:
    sample = await Sample.get(id)
    return sample

async def get_chip_id(chip_id: str) -> dict:
    chip = await Sample.query.where(Sample.chip_id == chip_id).gino.all()
    return chip

async def get_batch(batch_barcode: str) -> dict:
    batch = await Sample.query.where(Sample.batch_barcode == batch_barcode).gino.all()
    return batch


async def get_latest_sample(batch_barcode: str) -> dict:
    sample = await Sample.query.where(Sample.batch_barcode == batch_barcode).order_by(Sample.id.desc()).gino.all()
    return sample[0]


async def get_new_batch_code() -> str:
    samples = await Sample.query.distinct(Sample.batch_barcode).order_by(Sample.batch_barcode.desc()).gino.all()
    if len(samples) == 0:
        return '1'
    return str(int(samples[0].batch_barcode) + 1)


async def get_distinct_batch_codes() -> list:
    samples = await Sample.query.distinct(Sample.batch_barcode).gino.all()
    return [s.batch_barcode for s in samples]


async def get_all_samples(
        offset: Optional[int] = None,
        size: Optional[int] = None,
        position: Optional[str] = None,
        barcode: Optional[str] = None,
        batch_barcode: Optional[str] = None,
        vinmec_id: Optional[str] = None,
        chip_id: Optional[str] = None,
        technician_name: Optional[str] = None,
        qc_status: Optional[str] = None,
        order_by: Optional[str] = None,
):
    results = Sample.query
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if position:
        results = results.where(Sample.position.like(f"%{position}%"))
    if barcode:
        results = results.where(Sample.barcode.like(f"%{barcode}%"))
    if batch_barcode:
        results = results.where(Sample.batch_barcode == batch_barcode)
    if vinmec_id:
        results = results.where(Sample.vinmec_id.like(f"%{vinmec_id}%"))
    if chip_id:
        results = results.where(Sample.chip_id.like(f"%{chip_id}%"))
    if technician_name:
        results = results.where(Sample.technician_name.like(f"%{technician_name}%"))
    if qc_status:
        results = results.where(Sample.qc_status.like(f"%{qc_status}%"))
    if order_by:
        results = results.order_by(getattr(Sample, order_by).desc())
    total = await count_matched_samples(
        position=position,
        barcode=barcode,
        batch_barcode=batch_barcode,
        vinmec_id=vinmec_id,
        chip_id=chip_id,
        technician_name=technician_name,
        qc_status=qc_status,
    )
    return [r for r in await results.gino.all()], total


async def get_samples_with_kit_info(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    position: Optional[str] = None,
    barcode: Optional[str] = None,
    batch_barcode: Optional[str] = None,
    vinmec_id: Optional[str] = None,
    chip_id: Optional[str] = None,
    technician_name: Optional[str] = None,
    qc_status: Optional[str] = None,
    order_by: Optional[str] = None,
):
    results = Sample.join(Kit, Kit.barcode == Sample.barcode)
    samples = Sample.query.select_from(results)
    kits = Kit.query.select_from(results)
    if size:
        samples = samples.limit(size)
        kits = kits.limit(size)
    if offset:
        samples = samples.offset(offset)
        kits = kits.offset(offset)
    if position:
        samples = samples.where(Sample.position.like(f"%{position}%"))
        kits = kits.where(Sample.position.like(f"%{position}%"))
    if barcode:
        samples = samples.where(Sample.barcode.like(f"%{barcode}%"))
        kits = kits.where(Sample.barcode.like(f"%{barcode}%"))
    if batch_barcode:
        samples = samples.where(Sample.batch_barcode == batch_barcode)
        kits = kits.where(Sample.batch_barcode == batch_barcode)
    if vinmec_id:
        samples = samples.where(Sample.vinmec_id.like(f"%{vinmec_id}%"))
        kits = kits.where(Sample.vinmec_id.like(f"%{vinmec_id}%"))
    if chip_id:
        samples = samples.where(Sample.chip_id.like(f"%{chip_id}%"))
        kits = kits.where(Sample.chip_id.like(f"%{chip_id}%"))
    if technician_name:
        samples = samples.where(Sample.technician_name.like(f"%{technician_name}%"))
        kits = kits.where(Sample.technician_name.like(f"%{technician_name}%"))
    if qc_status:
        samples = samples.where(Sample.qc_status.like(f"%{qc_status}%"))
        kits = kits.where(Sample.qc_status.like(f"%{qc_status}%"))
    if order_by:
        samples = samples.order_by(getattr(Sample, order_by).desc())
        kits = kits.order_by(getattr(Sample, order_by).desc())
    total = await count_matched_joined_samples_kits(
        position=position,
        barcode=barcode,
        batch_barcode=batch_barcode,
        vinmec_id=vinmec_id,
        chip_id=chip_id,
        technician_name=technician_name,
        qc_status=qc_status,
    )
    samples = await samples.gino.all()
    kits = await kits.gino.all()
    results = []
    for index, sample in enumerate(samples):
        kit = kits[index].to_dict()
        sample_meta = await get_sample_meta_by_id(kit['sample_meta_id'])
        sample_meta = sample_meta.to_dict()
        results.append({
            **sample.to_dict(),
            "name": sample_meta["full_name"],
            "dob": sample_meta["dob"]
        })

    return results, total


async def count_matched_joined_samples_kits(
        position: Optional[str] = None,
        barcode: Optional[str] = None,
        batch_barcode: Optional[str] = None,
        vinmec_id: Optional[str] = None,
        chip_id: Optional[str] = None,
        technician_name: Optional[bool] = None,
        qc_status: Optional[str] = None,
):
    from_obj = Sample.join(Kit, Kit.barcode == Sample.barcode)
    results = db.select([db.func.count()], from_obj=from_obj)
    if position:
        results = results.where(Sample.position.like(f"%{position}%"))
    if barcode:
        results = results.where(Sample.barcode.like(f"%{barcode}%"))
    if batch_barcode:
        results = results.where(Sample.batch_barcode == batch_barcode)
    if vinmec_id:
        results = results.where(Sample.vinmec_id.like(f"%{vinmec_id}%"))
    if chip_id:
        results = results.where(Sample.chip_id.like(f"%{chip_id}%"))
    if technician_name:
        results = results.where(Sample.technician_name.like(f"%{technician_name}%"))
    if qc_status:
        results = results.where(Sample.qc_status.like(f"%{qc_status}%"))
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.info(e)
        return 0


async def count_matched_samples(
        position: Optional[str] = None,
        barcode: Optional[str] = None,
        batch_barcode: Optional[str] = None,
        vinmec_id: Optional[str] = None,
        chip_id: Optional[str] = None,
        technician_name: Optional[bool] = None,
        qc_status: Optional[str] = None,
):
    results = db.select([db.func.count()])
    if position:
        results = results.where(Sample.position.like(f"%{position}%"))
    if barcode:
        results = results.where(Sample.barcode.like(f"%{barcode}%"))
    if batch_barcode:
        results = results.where(Sample.batch_barcode == batch_barcode)
    if vinmec_id:
        results = results.where(Sample.vinmec_id.like(f"%{vinmec_id}%"))
    if chip_id:
        results = results.where(Sample.chip_id.like(f"%{chip_id}%"))
    if technician_name:
        results = results.where(Sample.technician_name.like(f"%{technician_name}%"))
    if qc_status:
        results = results.where(Sample.qc_status.like(f"%{qc_status}%"))
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.info(e)
        return 0


async def update_sample(sample: Sample, data: dict):
    await sample.update(**data).apply()


async def update_sample_status(data_arr: list):
    res = []
    current_time = get_current_date_time_utc_7()
    for data in data_arr:
        sample = await get_sample_by_id(data['id'])
        if sample is None:
            err = "Cannot find sample id in database"
            res.append({"id": data['id'], "err_msg": err})
            continue

        if data.get('qc_status') is not None:
            data['qc_status'] = data['qc_status'].upper()

        updated_status_body = {
            **data,
            'updated_time': current_time
        }
        await sample.update(**updated_status_body).apply()
        res.append(sample.to_dict())
    return res, None


async def existed_sample_in_batch(barcode: str, batch_barcode: str) -> bool:
    samples = await Sample.query.where(Sample.batch_barcode == batch_barcode).gino.all()
    for sample in samples:
        if sample.barcode == barcode:
            return True
    return False


async def existed_batch(batch_barcode: str) -> bool:
    batch = await get_batch(batch_barcode)
    if batch:
        return True
    return False


async def count_samples():
    total = db.select([db.func.count(Sample.id)])
    return await total.gino.scalar()


async def generate_operation_id(data: dict):
    year = str(datetime.utcnow().year)[-2:]
    week_num = str(datetime.utcnow().strftime("%V"))
    gender = data.get('gender')[0].upper()
    sample_count = await count_samples()
    return f"{year}{week_num}{gender}{format(sample_count + 1, '03d')}"


async def generate_num_position(data: dict):
    sample = await get_latest_sample(data.get('batch_barcode'))

    prev_num = sample.num
    prev_row_ascii = ord(sample.position[0].upper())
    prev_col = int(sample.position[1])

    num = prev_num + 1 if prev_num < (config["MAXIMUM_NUM_SAMPLES"] - 1) else 0
    row = prev_row_ascii
    col = prev_col
    if prev_row_ascii >= 65 and prev_row_ascii < 72:
        row = chr(prev_row_ascii + 1)
    else:
        row = chr(65)
        if prev_col < 12:
            col = prev_col + 1
        else:
            return "A1"
    return num, f"{row}{col}"


async def register(data: dict):
    kit = await get_kit(data.get('barcode'))
    if kit is None:
        err = f"Error: No kit matched with barcode {data['barcode']}"
        return data, err
    sample_meta = await get_sample_meta_by_id(kit.to_dict()['sample_meta_id'])
    sample_meta = sample_meta.to_dict()
    data['gender'] = sample_meta['gender']
    if await existed_batch(data['batch_barcode']):
        if await existed_sample_in_batch(data['barcode'], data['batch_barcode']):
            err = f"Error: Sample with barcode {data['barcode']} has already registered"
            return data, err
        current_time = get_current_date_time_utc_7()
        operation_id = await generate_operation_id(data)
        num, position = await generate_num_position(data)
        data = {
            'num': num,
            'operation_id': operation_id,
            'position': position,
            'physical_position': MAPPING_POSITION[num % 24],
            'barcode': data['barcode'],
            'batch_barcode': data['batch_barcode'],
            'vinmec_id': data['vinmec_id'],
            'chip_id': data['chip_id'],
            'chip_type': data['chip_type'],
            'gender': data['gender'],
            'technician_name': data['technician_name'],
            'qc_status': data['qc_status'].upper(),
            'created_time': current_time,
            'updated_time': current_time
        }
    else:
        current_time = get_current_date_time_utc_7()
        operation_id = await generate_operation_id(data)
        data = {
            'num': 0,
            'operation_id': operation_id,
            'position': 'A1',
            'physical_position': 'R01C01',
            'barcode': data['barcode'],
            'batch_barcode': data['batch_barcode'],
            'vinmec_id': data['vinmec_id'],
            'chip_id': data['chip_id'],
            'chip_type': data['chip_type'],
            'gender': data['gender'],
            'technician_name': data['technician_name'],
            'qc_status': data['qc_status'].upper(),
            'created_time': current_time,
            'updated_time': current_time
        }
    result = await Sample.create(**data)
    return result.to_dict(), None


async def analyze_samples(batch: str):
    samples = await get_batch(batch)
    if not samples:
        err = f"Cant find samples with matching batch {batch}"
        return None, err
    logger.info(f"Getting all samples with bath {batch}")

    data_pipeline = []
    missing_files = []
    list_files = None

    # validating files
    for sample in samples:
        if sample.positive_tested:
            continue
        if list_files is None:
            chip_id = sample.chip_id
            prefix = f"{chip_id}/"
            list_files = get_files_from_s3(prefix)
        files = validate_files_s3_from_list(sample.chip_id, sample.physical_position, list_files)
        if len(files) > 0:
            missing_files += files

    if len(missing_files) == 0:
        chip_ids = set()
        for sample in samples:
            if sample.chip_id in chip_ids:
                continue
            chip_ids.add(sample.chip_id)
            obj = {
                'chip_id': sample.chip_id,
                'chip_type': sample.chip_type,
                'assembly': sample.assembly,
                'file_paths': f's3://{BUCKET}/{sample.chip_id}/'
            }
            logger.info(f"Step function input {obj}")
            run_pipeline(obj)
            data_pipeline.append(obj)

    if len(missing_files) > 0:
        err = f'Missing files in S3 bucket'
        return missing_files, err
    return data_pipeline, None

async def analyze_samples_via_chip_id(chip_id: str):
    samples = await get_chip_id(chip_id)
    if not samples:
        err = f"Cant find samples with matching chip_id {chip_id}"
        return None, err
    logger.info(f"Getting all samples with chip_id {chip_id}")
    data_pipeline = []
    missing_files = []
    list_files = None

    # validating files
    for sample in samples:
        logger.info(f"Checking sample {sample}")
        if sample.positive_tested:
            continue
        if list_files is None:
            prefix = f"{chip_id}/"
            list_files = get_files_from_s3(prefix)
        files = validate_files_s3_from_list(sample.chip_id, sample.physical_position, list_files)
        if len(files) > 0:
            missing_files += files
            logger.info(f"Current missing_files {missing_files}")

    if len(missing_files) == 0:
        chip_ids = set()
        for sample in samples:
            if sample.chip_id in chip_ids:
                continue
            chip_ids.add(sample.chip_id)
            obj = {
                'chip_id': sample.chip_id,
                'chip_type': sample.chip_type,
                'assembly': sample.assembly,
                'file_paths': f's3://{BUCKET}/{sample.chip_id}/'
            }
            logger.info(f"Step function input {obj}")
            run_pipeline(obj)
            data_pipeline.append(obj)

    if len(missing_files) > 0:
        err = f'Missing files in S3 bucket'
        return missing_files, err
    return data_pipeline, None

