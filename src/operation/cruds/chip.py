from ..models.models import db, Chip, <PERSON>pleMapping, Batch<PERSON><PERSON>ping, Batch, Plate
from ..utils.utils import get_current_date_time_utc_7
from .. import logger
from typing import Optional
from ..schemas.chip import *
from ..schemas.sample_mapping import *


async def count_chips():
    total = db.select([db.func.count(Chip.id)]).where(Chip.deleted_at == None)
    return await total.gino.scalar()

async def get_chip(id: int) -> Chip:
    chip = await Chip.get(id)
    return chip

async def get_all_chip_by_chip_id(chip_id: str) -> Chip:
    results = Chip.query.where(Chip.chip_id == chip_id)
    return [r for r in await results.gino.all()]

async def count_matched_chips(
    chip_id: Optional[str] = None,
    type: Optional[str] = None,
    technology: Optional[str] = None,
):
    results = db.select([db.func.count()])
    results = results.where(Chip.deleted_at == None)
    if chip_id:
        results = results.where(Chip.chip_id == chip_id)
    if type:
        results = results.where(Chip.type == type)
    if technology:
        results = results.where(Chip.technology == technology)

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0

async def get_all_chips(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    chip_id: Optional[str] = None,
    type: Optional[str] = None,
    technology: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = Chip.query
    results = results.where(Chip.deleted_at == None)

    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if chip_id:
        results = results.where(Chip.chip_id.contains(f"%{chip_id}"))
    if type:
        results = results.where(Chip.type == type)
    if technology:
        results = results.where(Chip.technology == technology)
    if order_by:
        results = results.order_by(getattr(Chip, order_by).desc())
    total = await count_matched_chips(
        chip_id=chip_id,
        type=type,
        technology=technology,
    )
    return [r for r in await results.gino.all()], total

async def existed_chip_id(chip_id: str) -> bool:
    total = await db.select([db.func.count()]).where(Chip.deleted_at == None).where(Chip.chip_id == chip_id).gino.scalar()
    return True if total > 0 else False

async def create_chip(data: dict):
    if await existed_chip_id(data['chip_id']):
        err = f"Error: CHIP_ID {data['chip_id']} is already existed"
        return data, err
    current_time = get_current_date_time_utc_7()

    data = {
        'chip_id': data['chip_id'],
        'type': data['type'],
        'technology': data['technology'],
        # 'pipeline_qc_report': data['pipeline_qc_report'] if data.get('pipeline_qc_report') else None,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await Chip.create(**data)
    return result.to_dict(), None

async def create_chips(req_body: FillChipWSamples, technology: str):
    results = []
    async with db.transaction() as tx:
        for chip_id in req_body.chip_ids:
            data = {}
            data['chip_id'] = chip_id
            data['type'] = req_body.type
            data['technology'] = technology.upper()
            # data['pipeline_qc_report'] = None

            result, err = await create_chip(data)
            if err:
                # CHIP_ID already existed!
                # raise ValueError(err)
                return None, err
            else:
                results.append(result)
    return results, None
        
    pass
      
async def update_chip(chip: Chip, data: dict):
    if chip.deleted_at:
        err = f"chip with if: {chip.chip_id} already deleted!"
        return err
    await chip.update(**data).apply()
    logger.info(f"chip with chip_id: {chip.chip_id} updated")
    return None
    
async def delete_chip(chip,data):
    current_time = get_current_date_time_utc_7()
    if not chip:
        err = f"chip with chip_id: {chip.chip_id} cannot be found"
        return None, err
    elif chip.deleted_at:
        err = f"chip with chip_id: {chip.chip_id} already deleted!"
        return None, err

    async with db.transaction() as tx:
        added_batch = await get_batch_by_chip_id(chip_id=chip.chip_id)
        # chip belongs to plate & added to a batch cannot be deleted [TODO] check status or not?
        if added_batch:
            err = f"This chip was already added to a batch with id: {added_batch.id}"
            return None, err
        # remove chip_id & position value from list of sample_mappings
        sm_data = {
            "chip_id": None,
            "position": None,
        }
        sample_mappings = await SampleMapping.query.where(SampleMapping.chip_id == chip.chip_id).gino.all()
        if sample_mappings:
            for sm in sample_mappings:
                await sm.update(**sm_data).apply()

        await chip.update(**data).apply()
        logger.info(f"chip with chip_id: {chip.chip_id} deleted")
        return chip, None

async def get_chip_by_chip_id(chip_id: str) -> Chip:
    chip = await Chip.query.where(Chip.deleted_at == None).where(Chip.chip_id==chip_id).gino.first()
    return chip

async def get_batch_by_chip_id(chip_id: str) -> Batch:
    sample_mapping = await SampleMapping.query.where(SampleMapping.chip_id == chip_id).gino.first()
    if sample_mapping:
        plate = await Plate.query.where(Plate.id == sample_mapping.plate_id).gino.first()
        if plate:
            batch_mapping = await BatchMapping.query.where(BatchMapping.plate_id == plate.id).gino.first()
            if batch_mapping:
                batch = await Batch.query.where(Batch.id == batch_mapping.batch_id).gino.first()
                return batch 
            else:
                return None
        else:
            return None
    else:
        return None