from ..models.models import db, <PERSON>, <PERSON>pleMapping, <PERSON>ch<PERSON>apping, <PERSON><PERSON>, Chip, LabSample
from ..utils.utils import get_current_date_time_utc_7
from .. import logger
from typing import Optional
from ..schemas.plate import *
from .sample_management import *
from ..config import config
import uuid
from sqlalchemy import cast, Integer

async def get_max_plate_name(type: str):
    plate = await Plate.query.where(Plate.deleted_at == None).where(Plate.type == type.upper()).order_by(cast(getattr(Plate, 'name'), Integer).desc()).gino.first()
    return plate.name if plate else "0"

async def count_plates():
    total = db.select([db.func.count(Plate.id)]).where(Plate.deleted_at == None)
    return await total.gino.scalar()

async def get_plate_via_uuid(id: uuid.uuid4) -> Plate:
    plate = await Plate.get(id)
    return plate

async def get_plate(type: str,name: str) -> Plate:
    plate = await Plate.query.where(Plate.type==type.upper()).where(Plate.name==name).order_by(getattr(Plate, 'created_at').desc()).gino.first()
    return plate

async def count_matched_plates(
    name: Optional[str] = None,
    status: Optional[list] = None,
    type: Optional[str] = None,
):
    results = db.select([db.func.count()])
    results = results.where(Plate.deleted_at == None)
    if name:
        results = results.where(Plate.name == name)
    if status:
        results = results.where(Plate.status.in_(status))
    if type:
        results = results.where(Plate.type == type.upper())

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0

async def get_all_plates(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    name: Optional[str] = None,
    status: Optional[list] = None,
    type: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = Plate.query
    results = results.where(Plate.deleted_at == None)

    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if name:
        results = results.where(Plate.name == name)
    if status:
        results = results.where(Plate.status.in_(status))
    if type:
        results = results.where(Plate.type == type.upper())
    if order_by:
        results = results.order_by(cast(getattr(Plate, order_by), Integer).desc())
    total = await count_matched_plates(
        name=name,
        status=status,
        type=type,
    )
    return [r for r in await results.gino.all()], total

async def count_matched_plates_w_tech(
    name: Optional[str] = None,
    technology: Optional[str] = None,
    status: Optional[str] = None,
    is_not_filled_into_chips: Optional[bool] = None,
):
    # results = db.select([db.func.count()])
    results = db.select([
            db.func.count(db.func.distinct(Plate.name))
        ]).select_from(
            Plate.outerjoin(SampleMapping, SampleMapping.plate_id == Plate.id)
        )
    
    if is_not_filled_into_chips:
        results = results.where(SampleMapping.chip_id == None)
    
    if name:
        results = results.where(Plate.name == name)
    if status:
        results = results.where(Plate.status.in_(status))
    if technology:
        results = results.where(Plate.type == technology.upper())
    
    # results = results.distinct(Plate.name).group_by(Plate.name,Plate.id)

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0



async def get_all_plates_w_tech(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    name: Optional[str] = None,
    technology: Optional[str] = None,
    status: Optional[str] = None,
    is_not_filled_into_chips: Optional[bool] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = db.select([
            Plate
        ]).select_from(
            Plate.outerjoin(SampleMapping, SampleMapping.plate_id == Plate.id)
        ).distinct(cast(getattr(Plate, order_by), Integer))
    
    if is_not_filled_into_chips:
        results = results.where(SampleMapping.chip_id == None)

    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if name:
        results = results.where(Plate.name == name)
    if status:
        results = results.where(Plate.status.in_(status))
    if technology:
        results = results.where(Plate.type == technology.upper())
    if order_by:
        results = results.order_by(cast(getattr(Plate, order_by), Integer).desc())

    # results = results.group_by(Plate.name,Plate.id)

    total = await count_matched_plates_w_tech(
        name=name,
        technology=technology,
        status=status,
        is_not_filled_into_chips=is_not_filled_into_chips,
    )
    return [r for r in await results.gino.all()], total

async def existed_plate_name(type: str, name: str) -> bool:
    total = await db.select([db.func.count()]).where(Plate.deleted_at == None).where(Plate.type == type.upper()).where(Plate.name == name).gino.scalar()
    return True if total > 0 else False

async def create_plate(data: dict):
    if await existed_plate_name(data['type'],data['name']):
        err = f"Error: PLATE_ID {data['name']} is already existed"
        return data, err
    current_time = get_current_date_time_utc_7()

    data = {
        'name': data['name'],
        'status': data['status'],
        'type': data['type'].upper(),
        'created_at': current_time,
        'updated_at': current_time
    }
    async with db.transaction() as tx:
        result = await Plate.create(**data)
        total = await db.select([db.func.count()]).where(Plate.deleted_at is None).where(Plate.type == data['type'].upper()).where(Plate.name == data['name']).gino.scalar()
        if total > 1:
            err = f"Error: PLATE_ID {data['name']} is already existed. Your request's too fast!"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)


    return result.to_dict(), None

      
async def update_plate(plate: Plate, data: dict):
    if plate.deleted_at:
        err = f"Plate with if: {plate.name} already deleted!"
        return err
    await plate.update(**data).apply()
    logger.info(f"Plate with name: {plate.name} updated")
    return None
    
async def delete_plate(plate,data):
    current_time = get_current_date_time_utc_7()
    if not plate:
        err = f"Plate with name: {plate.name} cannot be found"
        return None, err
    elif plate.deleted_at:
        err = f"Plate with name: {plate.name} already deleted!"
        return None, err

    await plate.update(**data).apply()
    logger.info(f"Plate with name: {plate.name} deleted")
    return plate, None

async def get_plate_by_name(type:str, name: str) -> Plate:
    plate = await Plate.query.where(Plate.deleted_at == None).where(Plate.type==type.upper()).where(Plate.name==name).gino.first()
    return plate

async def get_no_samples_in_plate(technology: str, plate_name: str):
    
    _, total = await get_filtered_sample_mappings(
                            technology=technology,
                            plate_name=plate_name, 
                            plate_status_filter=config['AVAILABLE_CHIP_STATUS']+config['EXPORT_PLATE_STATUS']
                        )
    return total

async def get_no_samples_in_plate_v3(technology: str, plate_name: str):
    
    _, total = await get_filtered_sample_mappings_v3(
                            technology=technology,
                            plate_name=plate_name, 
                            plate_status_filter=config['AVAILABLE_CHIP_STATUS']+config['EXPORT_PLATE_STATUS']
                        )
    return total