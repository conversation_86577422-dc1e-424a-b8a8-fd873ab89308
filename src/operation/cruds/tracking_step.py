import uuid
from typing import Optional
from ..models.models import TrackingStep, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTT<PERSON>Exception
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# TrackingStep - model 
# STEP - QUERY tenBuoc
# _tracking_step - function tenBuoc
# tracking_step - single
# steps - plural
# tenBuoc -- search key

STEP_LIST_QUERY_V3 = """
    select
        ts.id,
        ts."tenBuoc",
        ts.created_at,
        ts.updated_at,
        ts.deleted_at
    from
        tracking_step ts
"""

STEP_LIST_COUNT_QUERY_V3 = """
    select
        count(ts.id)
    from
        tracking_step ts
"""

async def get_tracking_step_by_id(id: uuid.uuid4):
    tracking_step = await TrackingStep.get(id)
    return tracking_step


async def get_all_steps(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="ts.created_at",
    tenBuoc: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if tenBuoc:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(ts.'tenBuoc') like '%{tenBuoc.lower()}%' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""ts.deleted_at is null """
    
    raw_count_query = STEP_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else STEP_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = STEP_LIST_QUERY_V3 + query_filter
    else:
        raw_query = STEP_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else STEP_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_step_existed(tenBuoc: str):
    total = await db.select([db.func.count()]).where(TrackingStep.tenBuoc == tenBuoc).where(TrackingStep.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_tracking_step(data: dict):
    if await is_tracking_step_existed(data['tenBuoc']):
        err = f"Error: TrackingStep with tenBuoc {data['tenBuoc']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await TrackingStep.create(**data)
    return result.to_dict()

async def update_tracking_step(tracking_step: TrackingStep, data: dict):
    if tracking_step.deleted_at:
        err = f"TrackingStep with tenBuoc: {tracking_step.tenBuoc} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_step.update(**data).apply()
    logger.info(f"TrackingStep with tenBuoc: {tracking_step.tenBuoc} updated")
    return None


async def delete_tracking_step(tracking_step: TrackingStep):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_step:
        err = f"TrackingStep with tenBuoc: {tracking_step.tenBuoc} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_step.deleted_at:
        err = f"TrackingStep with: {tracking_step.tenBuoc} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_step.update(**data).apply()
    logger.info(f"TrackingStep with tenBuoc: {tracking_step.tenBuoc} deleted")
