import uuid
from typing import Optional

from .. import logger
from ..models.models import Account, Staff, db
from ..utils.utils import get_current_date_time_utc_7


# account_name
# sale_pic_name
# year
def query_sale_pic_stats():
    SALE_ACCOUNT_HISTORY_COUNT_KITS = """
        SELECT DISTINCT ON (account_name, sale_pic_name, a.id, sf.id, date_part('month', k.created_at))
            a.id as id,
            a.name as account_name,
            sf.name as sale_pic_name,
            a.id as account_id,
            sf.id as sale_pic_id,
            sale_account_history.created_at,
            date_part('month', k.created_at) AS month,
            date_part('year', k.created_at) AS year,
            COUNT(*) as total_in_month
        FROM 
            sale_account_history
        INNER JOIN account a ON sale_account_history.account_id = a.id
        INNER JOIN staff sf ON sale_account_history.pic_id = sf.id
        INNER JOIN source s ON sale_account_history.id = s.account_history_id
        INNER JOIN sample sam ON s.id = sam.source_id
        INNER JOIN kit k ON sam.samplecode = k.samplecode
    """

    return SALE_ACCOUNT_HISTORY_COUNT_KITS


async def get_sale_pic_stats(
    order_by: Optional[str] = None,
    order_option: Optional[str] = "desc",
    account_name: Optional[str] = None,
    sale_pic_name: Optional[str] = None,
    pic_id: Optional[str] = None,
    pic_phone_number: Optional[str] = None,
    year: Optional[int] = None,
):
    query_filter = ""

    # CURRENT_SALE_ACCOUNT_HISTORY_COUNT_QUERY = query_count_current_accounts_managed_by_sale_pic_id(pic_id)
    # raw_count_query = CURRENT_SALE_ACCOUNT_HISTORY_COUNT_QUERY

    # count = await db.all(db.text(raw_count_query))
    if account_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"a.name like '%{account_name}%' "
    if sale_pic_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sf.name like '%{sale_pic_name}%' "
    if year:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"date_part('year', k.created_at) = {year}"
    if pic_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sale_account_history.pic_id = '{pic_id}' "
    if pic_phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sf.phone_number = '{pic_phone_number}' "
    # [GS-6868]
    query_filter += "and " if query_filter != "" else " "
    query_filter += "sf.role = 'SALE_PIC' "

    SALE_ACCOUNT_HISTORY_COUNT_KITS_QUERY = query_sale_pic_stats()
    raw_query = (
        SALE_ACCOUNT_HISTORY_COUNT_KITS_QUERY + "where " + query_filter
        if query_filter != ""
        else SALE_ACCOUNT_HISTORY_COUNT_KITS_QUERY
    )
    raw_query += "GROUP BY sf.name, a.name, a.id, sf.id, month, year, sale_account_history.created_at, date_part('month', k.created_at) "
    raw_query += f"ORDER BY {order_by} {order_option} "
    results = await db.all(db.text(raw_query))
    return results, None


def query_current_accounts_managed_by_sale_pic_id(pic_id):
    if pic_id:
        PIC_ID_CONDITION = f"WHERE pic_id = '{pic_id}'"
    else:
        PIC_ID_CONDITION = ""

    CURRENT_SALE_ACCOUNT_HISTORY = f"""
        SELECT
            a.name as account_name,
            sale_w_account_histories.account_id,
            sale_w_account_histories.pic_id,
            sale_w_account_histories.created_at
        FROM (
            SELECT DISTINCT ON (account_id) account_id, pic_id, created_at
            FROM public.sale_account_history
            WHERE account_id IN (
                SELECT DISTINCT ON (account_id, pic_id) account_id
                FROM public.sale_account_history
                {PIC_ID_CONDITION}
                ORDER BY account_id, pic_id, created_at DESC
            )
            ORDER BY account_id, created_at DESC
        ) sale_w_account_histories
        inner join account a on sale_w_account_histories.account_id = a.id
        {PIC_ID_CONDITION}
    """

    return CURRENT_SALE_ACCOUNT_HISTORY


def query_count_current_accounts_managed_by_sale_pic_id(pic_id):
    if pic_id:
        PIC_ID_CONDITION = f"WHERE pic_id = '{pic_id}'"
    else:
        PIC_ID_CONDITION = ""

    COUNT_CURRENT_SALE_ACCOUNT_HISTORY = f"""
        SELECT
            count(sale_w_account_histories.account_id)
        FROM (
            SELECT DISTINCT ON (account_id) account_id, pic_id, created_at
            FROM public.sale_account_history
            WHERE account_id IN (
                SELECT DISTINCT ON (account_id, pic_id) account_id
                FROM public.sale_account_history
                {PIC_ID_CONDITION}
                ORDER BY account_id, pic_id, created_at DESC
            )
            ORDER BY account_id, created_at DESC
        ) sale_w_account_histories
        inner join account a on sale_w_account_histories.account_id = a.id
        {PIC_ID_CONDITION}
    """

    return COUNT_CURRENT_SALE_ACCOUNT_HISTORY


async def get_current_sale_account_history(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    pic_id: Optional[str] = None,
):
    query_filter = ""

    CURRENT_SALE_ACCOUNT_HISTORY_COUNT_QUERY = (
        query_count_current_accounts_managed_by_sale_pic_id(pic_id)
    )
    raw_count_query = CURRENT_SALE_ACCOUNT_HISTORY_COUNT_QUERY

    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    CURRENT_SALE_ACCOUNT_HISTORY_QUERY = query_current_accounts_managed_by_sale_pic_id(
        pic_id=pic_id
    )
    raw_query = (
        CURRENT_SALE_ACCOUNT_HISTORY_QUERY + query_filter
        if query_filter != ""
        else CURRENT_SALE_ACCOUNT_HISTORY_QUERY
    )
    results = await db.all(db.text(raw_query))
    return results, total


async def count_staffs():
    total = db.select([db.func.count(Staff.id)])
    return await total.gino.scalar()


async def get_staff(id: uuid.uuid4) -> Staff:
    staff = await Staff.get(id)
    return staff


async def count_matched_staffs(
    account_name: Optional[str] = None,
    role: Optional[str] = None,
    name: Optional[str] = None,
    account_id: Optional[uuid.uuid4] = None,
    userid: Optional[uuid.uuid4] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
):
    results = db.select([db.func.count()]).select_from(
        Staff.join(Account, Staff.account_id == Account.id)
    )
    results = results.where(Staff.deleted_at == None)
    results = results.where(Staff.created_at.between(start_date, end_date))
    if account_name:
        results = results.where(Account.name.contains(f"%{account_name}"))
    if role:
        results = results.where(Staff.role == role)
    if name:
        results = results.where(Staff.name.contains(f"%{name}"))
    if account_id:
        results = results.where(Staff.account_id == account_id)
    if userid:
        results = results.where(Staff.userid == userid)
    if email:
        results = results.where(Staff.email.contains(f"%{email}"))
    if phone_number:
        results = results.where(Staff.phone_number.contains(f"%{phone_number}"))
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


async def get_all_staffs(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    role: Optional[str] = None,
    account_name: Optional[str] = None,
    name: Optional[str] = None,
    account_id: Optional[uuid.uuid4] = None,
    userid: Optional[uuid.uuid4] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # results = Staff.query
    # accounts = Account.alias('sacc')
    results = db.select([Staff, Account.name]).select_from(
        Staff.join(Account, Staff.account_id == Account.id)
    )
    results = results.where(Staff.deleted_at == None)
    # results = results.where(Staff.created_at.between(start_date,end_date))
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if account_name:
        results = results.where(Account.name.contains(f"%{account_name}"))
    if role:
        results = results.where(Staff.role == role)
    if name:
        results = results.where(Staff.name.contains(f"%{name}"))
    if account_id:
        results = results.where(Staff.account_id == account_id)
    if userid:
        results = results.where(Staff.userid == userid)
    if email:
        results = results.where(Staff.email.contains(f"%{email}"))
    if phone_number:
        results = results.where(Staff.phone_number.contains(f"%{phone_number}"))
    if order_by:
        results = results.order_by(getattr(Staff, order_by).desc())
    total = await count_matched_staffs(
        account_name=account_name,
        role=role,
        name=name,
        account_id=account_id,
        userid=userid,
        email=email,
        phone_number=phone_number,
        start_date=start_date,
        end_date=end_date,
    )

    return [r for r in await results.gino.all()], total


async def get_existed_default_sale_pic():
    default_sale_pic = await Staff.query.where(Staff.name == "DEFAULT").gino.all()
    return [r.to_dict() for r in default_sale_pic]


async def existed_name(name: str) -> bool:
    total = (
        await db.select([db.func.count()])
        .where(Staff.name == name)
        .where(Staff.deleted_at == None)
        .gino.scalar()
    )
    return True if total > 0 else False


async def create_staff(data: dict):
    if data["name"] == "DEFAULT" and await existed_name(name=data["name"]):
        err = f"Error: {data['name']} STAFF SALE PIC is already created"
        return data, err
    current_time = get_current_date_time_utc_7()

    data = {
        "id": uuid.uuid4(),
        "name": data["name"],
        "email": data.get("email"),
        "userid": data.get("userid"),  # if data.get('userid') else None
        "account_id": data["account_id"]
        if data["name"] != "DEFAULT"
        else "b0f00eaa-000c-0e0b-aaf0-fe0bbe0e0d00",
        "phone_number": data.get("phone_number"),
        "role": data.get("role") if data["name"] != "DEFAULT" else "SALE_PIC",
        "created_at": current_time,
        "updated_at": current_time,
    }
    result = await Staff.create(**data)
    return result.to_dict(), None


async def update_staff(staff: Staff, data: dict):
    if staff.deleted_at:
        err = f"Staff with if: {staff.id} already deleted!"
        return err
    await staff.update(**data).apply()
    logger.info(f"Staff with name: {staff.name} updated")
    return None


async def delete_staff(staff, data):
    current_time = get_current_date_time_utc_7()
    if not staff:
        err = f"Staff with id: {staff.id} cannot be found"
        return None, err
    elif staff.deleted_at:
        err = f"Staff with id: {staff.id} already deleted!"
        return None, err

    await staff.update(**data).apply()
    logger.info(f"Staff with name: {staff.name} deleted")
    return staff, None
