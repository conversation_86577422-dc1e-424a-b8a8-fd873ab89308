import uuid
from typing import Optional

from sqlalchemy import and_

from .. import logger
from ..models.models import Account, SaleAccountHistory, Staff, db
from ..utils.utils import get_current_date_time_utc_7


async def count_accounts():
    total = db.select([db.func.count(Account.id)])
    return await total.gino.scalar()


async def get_account(id: uuid.uuid4) -> Account:
    sale_account = await Account.get(id)
    return sale_account


async def get_account_by_name(name: str) -> Account:
    sale_account = await Account.query.where(Account.name == name).gino.first()
    return sale_account


async def count_matched_accounts(
    type: Optional[str] = None,
    area: Optional[str] = None,
    name: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
):
    subq = (
        db.select(
            [
                SaleAccountHistory.account_id.label("account_id"),
                db.func.max(SaleAccountHistory.created_at).label("maxdate"),
            ]
        )
        .select_from(SaleAccountHistory)
        .group_by(SaleAccountHistory.account_id)
        .alias()
    )  # .subquery('t2') #.subquery('t2')

    latest_q = (
        db.select(
            [
                SaleAccountHistory.id.label("current_id"),
                SaleAccountHistory.account_id.label("account_id"),
                SaleAccountHistory.pic_id.label("pic_id"),
                SaleAccountHistory.created_at.label("max_created_date"),
            ]
        )
        .select_from(
            SaleAccountHistory.join(
                subq,
                and_(
                    SaleAccountHistory.account_id == subq.c.account_id,
                    SaleAccountHistory.created_at == subq.c.maxdate,
                ),
            )
        )
        .alias()
    )

    # CHECK 1
    latest_w_pic_q = (
        db.select(
            [
                latest_q.c.current_id.label("current_id"),
                latest_q.c.account_id.label("account_id"),
                latest_q.c.pic_id.label("pic_id"),
                Staff.name.label("staff_name"),
            ]
        )
        .select_from(latest_q.join(Staff, latest_q.c.pic_id == Staff.id))
        .alias()
    )

    results = db.select([db.func.count()]).select_from(
        Account.join(latest_w_pic_q, latest_w_pic_q.c.account_id == Account.id)
    )
    results = results.where(Account.deleted_at == None)
    if start_date and end_date:
        results = results.where(Account.created_at.between(start_date, end_date))
    if type:
        results = results.where(Account.type == type)
    if area:
        results = results.where(Account.area == area)
    if name:
        name = name.lower()
        logger.info(name)
        results = results.where(db.func.lower(Account.name).contains(f"%{name}"))
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


async def get_all_accounts(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    name: Optional[str] = None,
    staff_name: Optional[str] = None,
    area: Optional[str] = None,
    type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = None,
    pic_id: Optional[str] = None,
    pic_phone_number: Optional[str] = None,
):
    # results = Account.query
    subq = (
        db.select(
            [
                SaleAccountHistory.account_id.label("account_id"),
                db.func.max(SaleAccountHistory.created_at).label("maxdate"),
            ]
        )
        .select_from(SaleAccountHistory)
        .group_by(SaleAccountHistory.account_id)
        .alias()
    )
    subq = subq.alias()

    latest_q = (
        db.select(
            [
                SaleAccountHistory.id.label("current_id"),
                SaleAccountHistory.account_id.label("account_id"),
                SaleAccountHistory.pic_id.label("pic_id"),
                SaleAccountHistory.created_at.label("max_created_date"),
            ]
        )
        .select_from(
            SaleAccountHistory.join(
                subq,
                and_(
                    SaleAccountHistory.account_id == subq.c.account_id,
                    SaleAccountHistory.created_at == subq.c.maxdate,
                ),
            )
        )
        .alias()
    )

    # CHECK 1
    latest_w_pic_q = db.select(
        [
            latest_q.c.current_id.label("current_id"),
            latest_q.c.account_id.label("account_id"),
            latest_q.c.pic_id.label("pic_id"),
            Staff.name.label("staff_name"),
        ]
    ).select_from(latest_q.join(Staff, latest_q.c.pic_id == Staff.id))

    if pic_id:
        latest_w_pic_q = latest_w_pic_q.where(latest_q.c.pic_id == pic_id)
    if pic_phone_number:
        latest_w_pic_q = latest_w_pic_q.where(Staff.phone_number == pic_phone_number)

    if staff_name:
        latest_w_pic_q = latest_w_pic_q.where(Staff.name.contains(f"%{staff_name}"))

    latest_w_pic_q = latest_w_pic_q.alias()

    results = db.select(
        [
            Account,
            latest_w_pic_q.c.staff_name.label("staff_name"),
            latest_w_pic_q.c.current_id.label("current_history_id"),
            latest_w_pic_q.c.pic_id.label("pic_id"),
        ]
    ).select_from(
        Account.join(latest_w_pic_q, latest_w_pic_q.c.account_id == Account.id)
    )

    results = results.where(Account.deleted_at == None)
    results = results.where(Account.created_at.between(start_date, end_date))
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if type:
        results = results.where(Account.type == type)
    if area:
        results = results.where(Account.area.contains(f"%{area}"))
    if name:
        name = name.lower()
        logger.info(name)
        results = results.where(db.func.lower(Account.name).contains(f"%{name}"))
    if order_by:
        results = results.order_by(getattr(Account, order_by).desc())
    total = await count_matched_accounts(
        type=type,
        area=area,
        name=name,
        start_date=start_date,
        end_date=end_date,
    )
    return [r for r in await results.gino.all()], total


async def existed_name(name: str) -> bool:
    total = await count_matched_accounts(
        name=name,
    )
    return True if total > 0 else False


async def create_account(data: dict):
    if await existed_name(data["name"]):
        err = f"Error: Account with name {data['name']} is already created"
        return data, None, err
    current_time = get_current_date_time_utc_7()

    data = {
        "id": uuid.uuid4(),
        "name": data["name"],
        "address": data["address"],
        "description": data["description"],
        "area": data["area"],
        "type": data["type"],
        "created_at": current_time,
        "updated_at": current_time,
    }
    result = await Account.create(**data)
    return result.to_dict(), data["id"], None


async def update_account(account: Account, data: dict):
    if account.deleted_at:
        err = f"Sale_Account with id: {account.id} already deleted!"
        return err

    await account.update(**data).apply()
    logger.info(f"Sale_Account with name: {account.name} updated")
    return None


async def delete_account(account: Account, data: dict):
    current_time = get_current_date_time_utc_7()
    if not account:
        err = "Please pass Sale_Account with id"
        return None, err
    elif account.deleted_at:
        err = f"Sale_Account with id: {account.id} already deleted!"
        return None, err

    await account.update(**data).apply()
    logger.info(f"Sale_Account with name: {account.name} deleted")
    return account, None
