import os
from typing import Optional

from fastapi import HTTPException

from .. import logger
from ..config import config
from ..models.models import Batch, BatchMapping, Plate, db
from ..schemas.batch import *
from ..schemas.sample_management import *
from ..services.batch import (
    get_today_date_service,
    standardize_date_w_name_service_utc_7,
)
from ..utils.utils import (
    export_list_dicts_to_csv,
    get_current_date_time_utc_7,
    upload_file,
)
from .aws import *
from .batch_mapping import *
from .plate import *
from .sample_management import *


async def get_batch_by_id(batch_id: str) -> Batch:
    batch = (
        await Batch.query.where(Batch.deleted_at == None)
        .where(Batch.id == batch_id)
        .gino.first()
    )
    return batch


async def get_batch_by_number(number: int) -> Batch:
    batch = (
        await Batch.query.where(Batch.deleted_at == None)
        .where(Batch.number == number)
        .gino.first()
    )
    return batch


async def get_batch_by_number_n_tech(type: str, number: int) -> Batch:
    batch = (
        await Batch.query.where(Batch.type == type)
        .where(Batch.number == number)
        .gino.first()
    )
    return batch


async def get_batch_by_max_number(type):
    subq = (
        db.select([db.func.max(Batch.number).label("maxnumber")])
        .select_from(Batch)
        .where(Batch.type == type)
        .alias()
    )  # .subquery('t2') #.subquery('t2')

    results = db.select([Batch]).select_from(
        Batch.join(subq, subq.c.maxnumber == Batch.number)
    )

    batch = await results.gino.first()
    return batch


async def existed_batch_number(type: str, number: int) -> bool:
    total = (
        await db.select([db.func.count()])
        .where(Batch.deleted_at == None)
        .where(Batch.type == type.upper())
        .where(Batch.number == number)
        .gino.scalar()
    )
    return True if total > 0 else False


async def count_matched_batches(
    number: Optional[int] = None,
    name: Optional[str] = None,
    note: Optional[str] = None,
    type: Optional[str] = None,
):
    results = db.select([db.func.count()])
    results = results.where(Batch.deleted_at == None)
    if number:
        results = results.where(Batch.number == number)
    if name:
        results = results.where(Batch.name == name)
    if note:
        results = results.where(db.func.lower(Batch.note).contains(f"%{note}"))
    if type:
        results = results.where(Batch.type == type.upper())

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


async def get_all_batches(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    number: Optional[int] = None,
    name: Optional[str] = None,
    note: Optional[str] = None,
    type: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = Batch.query
    results = results.where(Batch.deleted_at == None)

    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if number:
        results = results.where(Batch.number == number)
    if name:
        results = results.where(Batch.name == name)
    if note:
        results = results.where(db.func.lower(Batch.note).contains(f"%{note}"))
    if type:
        results = results.where(Batch.type == type.upper())
    if order_by:
        results = results.order_by(getattr(Batch, order_by).desc())
    total = await count_matched_batches(
        number=number,
        name=name,
        note=note,
        type=type,
    )
    return [r for r in await results.gino.all()], total


BATCH_COUNT_QUERY = """
    select count(b.number)
    from
        batch b
        inner join batch_mapping bm on b.id = bm.batch_id
        inner join plate p on p.id = bm.plate_id

"""

BATCH_QUERY = """
    select
        b.id,
        b.number,
        b.name,
        b.note,
        b.type,
        b.created_at,
        b.updated_at,
        b.deleted_at,
        p.status
    from
        batch b
        inner join batch_mapping bm on b.id = bm.batch_id
        inner join plate p on p.id = bm.plate_id

"""


async def get_filtered_batch_list(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    number: Optional[int] = None,
    name: Optional[str] = None,
    note: Optional[str] = None,
    type: Optional[str] = None,
    plate_status_filter: Optional[list] = None,
    order_by: Optional[str] = None,
    order_option: Optional[str] = "desc",
):
    query_filter = ""
    if number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.number = '{number}' "
    if type:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.type = '{type}' "
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.name like '%{name}%' "
    if note:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.note like '%{note}%' "
    # if plate_status_filter:
    #     query_filter += "and " if query_filter != "" else " "
    #     query_filter += f"(p.status in {tuple(plate_status_filter)} or p.status is null) "

    raw_count_query = (
        BATCH_COUNT_QUERY + "where " + query_filter
        if query_filter != ""
        else BATCH_COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if order_by:
        query_filter += f"order by {order_by} {order_option} "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    raw_query = (
        BATCH_QUERY + "where " + query_filter if query_filter != "" else BATCH_QUERY
    )
    results = await db.all(db.text(raw_query))
    # REMEMBER to_dict()
    return results, total


# async def delete_batch(type: str, number: int):
#     current_time = get_current_date_time_utc_7()
#     if not await existed_batch_number(type,number):
#         err = f"Batch with number: {number} cannot be found"
#         return None, err

#     # Check SampleMapping.dna_extraction_id exist ? before delete?

#     return await Batch.delete.where(
#         Batch.number==number,
#     ).gino.status()


async def create_batch(batch_req: CreateBatchReq):
    batch = Batch(
        # id=batch_req.id,
        number=batch_req.number,
        name=batch_req.name,
        note=batch_req.note,
        type=batch_req.type,
        created_at=get_current_date_time_utc_7(),
        updated_at=get_current_date_time_utc_7(),
    )

    async with db.transaction() as tx:
        await batch.create()
        total = (
            await db.select([db.func.count()])
            .where(Batch.deleted_at is None)
            .where(Batch.type == batch_req.type)
            .where(Batch.number == batch_req.number)
            .gino.scalar()
        )
        if total > 1:
            err = f"Error: BATCH {batch_req.number} is already existed. Your request's too fast!"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
    return batch.to_dict()


async def update_batch_w_number(type: str, number: int, update_data: BatchUpdate):
    batch = (
        await Batch.query.where(Batch.type == type.upper())
        .where(Batch.number == number)
        .gino.first()
    )
    await batch.update(**update_data.dict()).apply()
    return batch.to_dict()


async def update_batch(update_data: BatchUpdateReq):
    batch = await Batch.query.where(Batch.id == update_data.id).gino.first()
    await batch.update(**update_data.dict()).apply()
    return batch.to_dict()


async def delete_batch(type: str, number: int):
    async with db.transaction() as tx:
        batch = (
            await Batch.query.where(Batch.type == type.upper())
            .where(Batch.number == number)
            .gino.first()
        )
        if batch is None:
            raise ValueError(f"batch with number {number} is not exsisted")

        batch_plate = await BatchMapping.query.where(
            BatchMapping.batch_id == batch.id
        ).gino.first()
        if batch_plate is not None:
            plate = await Plate.query.where(
                Plate.id == batch_plate.plate_id
            ).gino.first()
            if plate.status in ["COMPLETED", "RUNNING"]:
                raise ValueError(
                    f"cannot delete batch: plate {Plate.number} that was added to this batch was already processed"
                )
        # delete batch mapping
        await BatchMapping.delete.where(BatchMapping.batch_id == batch.id).gino.status()
        # delete batch
        await Batch.delete.where(Batch.id == batch.id).gino.status()
        # [TODO] delete all chips in batch?


def validate_raw_data_for_main_pipeline(samples, raw_data_bucket):
    missing_files = []
    list_files = {}
    chip_ids = set()
    # print('First sample: ', samples[0])
    # print('First sample type: ', type(samples[0]))

    for sample in samples:
        # if sample.positive_tested:
        #     continue
        if sample["chip_id"] not in chip_ids:
            chip_id = sample["chip_id"]
            chip_ids.add(chip_id)
            prefix = f"{chip_id}/"
            list_files[chip_id] = get_files_from_s3_w_bucket(raw_data_bucket, prefix)
        files = validate_files_s3_from_list(
            sample["chip_id"],
            sample["physical_position"],
            list_files[sample["chip_id"]],
        )
        if len(files) > 0:
            missing_files += files
    return chip_ids, missing_files


def get_barcode_mapping_list_w_batch_id(samples):
    results = []
    for sample in samples:
        entry = {}
        entry["LabID"] = sample["lid"]
        entry["Barcode"] = sample["barcode"]
        results.append(entry)

    return results
    pass


def parse_input_n_trigger_main_pipeline(
    missing_files, samples, raw_data_bucket, assembly
):
    data_pipeline = []

    if len(missing_files) == 0:
        chip_ids = set()
        for sample in samples:
            if sample["chip_id"] in chip_ids:
                continue
            chip_ids.add(sample["chip_id"])
            obj = {
                "chip_id": sample["chip_id"],
                "chip_type": sample["chip_type"],
                "assembly": assembly,
                "batch_barcode": sample["batch_barcode"],
                "file_paths": f"s3://{raw_data_bucket}/{sample['chip_id']}/",
            }
            logger.info(f"Step function input {obj}")
            run_pipeline(obj)
            data_pipeline.append(obj)

    if len(missing_files) > 0:
        err = "Missing RAW DATA in S3 bucket"
        # raise ValueError(err)
        errs = failure_response(err, data=missing_files)
        raise HTTPException(status_code=422, detail=errs)

    return data_pipeline


def parse_input_n_trigger_px_pipeline(missing_files, samples, payload):
    if len(missing_files) == 0:
        batch_barcodes = set()
        data_pipeline = []
        # pcr_data_bucket = config.get('RAW_DATA_BUCKET').get('PCR')
        # px_output_bucket = config.get('PIPELINE_QC_STATUS').get('OUTPUT_BUCKET').get('PCR')

        for sample in samples:
            if not sample["batch_barcode"]:
                err = "BATCH_NUMBER of given RAW DATA NOT FOUND"
                raise ValueError(err)

            if sample["batch_barcode"] in batch_barcodes:
                continue
            batch_barcode = sample["batch_barcode"]
            batch_barcodes.add(batch_barcode)
            logger.info(f"CURRENT BATCH NUMBER: {batch_barcode}")

            step_function_input = {
                "barcode_mapping_file": f"barcode_mapping_file_{str(batch_barcode)}.csv",
                "gene_kit": payload.gene_kit,
                "batch_id": sample["batch_barcode"],
                # "pcr_data_bucket": pcr_data_bucket,
                # "pcr_output_bucket": px_output_bucket
            }
            logger.info(f"Step function input {step_function_input}")

            # Validate Function
            if is_input_file_px_existed(batch_barcode):
                run_pipeline_extended(step_function_input, "px")
                data_pipeline.append(step_function_input)
            else:
                err = f"Missing files in S3 bucket: PCR{batch_barcode}"
                raise ValueError(err)

        return data_pipeline

    else:
        err = f"Missing RAW DATA {missing_files} in S3 bucket"
        # raise ValueError(err)
        errs = failure_response(err, data=missing_files)
        raise HTTPException(status_code=422, detail=errs)


def validate_uploaded_raw_data_for_px_pipeline(samples):
    batch_numbers = set()
    missing_files = []
    for sample in samples:
        if not sample["batch_barcode"]:
            err = "BATCH_NUMBER of given RAW DATA NOT FOUND"
            raise ValueError(err)

        if sample["batch_barcode"] in batch_numbers:
            continue
        batch_number = sample["batch_barcode"]
        batch_numbers.add(batch_number)
        if is_input_file_px_existed(batch_number):
            logger.info(f"INPUT FILE  PCR{batch_number} for PX PIPELINE existed! ")
        else:
            logger.info(
                f"Missing files in S3 bucket: PCR{batch_number} for PX PIPELINE existed!"
            )
            missing_files.append(f"PCR{batch_number}")

    return batch_number, missing_files


async def analyze_samples_w_tech_n_batch(
    technology: str, number: int, payload: TriggerPipelineReq
):
    batch = await get_batch_by_number_n_tech(technology, number)
    if not batch:
        err = f"Cant find batch with number {batch.number}"
        raise ValueError(err)

    samples, _ = await get_filtered_sample_mappings(batch_id=batch.id)
    if not samples:
        err = f"Cant find samples with matching batch number {batch.number}"
        raise ValueError(err)

    # logger.info(f"Triggering Drylab for batch {batch.number}")
    logger.info(f"Triggering Drylab for batch {batch.number}")

    if technology in config.get("PIPELINE_INPUT").get("MAIN"):
        assembly = config.get("ASSEMBLY").get(technology.upper())
        if not assembly:
            err = f"This technology {technology} doesnt have ASSEMBLY value"
            raise ValueError(err)
        logger.info(f"ASSEMBLY: {assembly}")
        raw_data_bucket = config.get("RAW_DATA_BUCKET").get(technology.upper())
        if not raw_data_bucket:
            err = f"MISSING RAW_DATA_BUCKET for this technology {technology}"
            raise ValueError(err)
        logger.info(f"RAW DATA BUCKET: {raw_data_bucket}")

        chip_ids, missing_files = validate_raw_data_for_main_pipeline(
            samples, raw_data_bucket
        )
        logger.info(f"CURRENT RUNNING CHIPS: {chip_ids}")
        logger.info(f"MISSING FILES: {missing_files}")

        data_pipeline = parse_input_n_trigger_main_pipeline(
            missing_files, samples, raw_data_bucket, assembly
        )
        return data_pipeline

    elif technology in config.get("PIPELINE_INPUT").get("EXTENDED"):
        if technology.upper() in ["PX1", "PCR"]:
            logger.info(f"TECHNOLOGY: {technology} -- NUMBER: {number}")
            BATCH_NUMBER = str(number)
            barcode_mapping_list = get_barcode_mapping_list_w_batch_id(samples=samples)
            logger.info(f"GET BARCODE_MAPPING_LIST: {barcode_mapping_list}")

            barcode_mapping_file_config = config.get("PIPELINE_QC_STATUS").get(
                "BARCODE_MAPPING_FILE"
            )
            barcode_mapping_file_name = (
                barcode_mapping_file_config.get("PREFIX")
                + BATCH_NUMBER
                + barcode_mapping_file_config.get("SUFFIX")
            )
            barcode_mapping_local_path = barcode_mapping_file_config.get("LOCAL_PATH")
            file_path = os.path.join(
                barcode_mapping_local_path, barcode_mapping_file_name
            )
            export_list_dicts_to_csv(barcode_mapping_list, file_path)
            logger.info(
                f"EXPORTED BARCODE_MAPPING_FILE: {barcode_mapping_file_name} && into path: {file_path}"
            )

            input_bucket = config.get("RAW_DATA_BUCKET").get("PCR")
            upload_res = upload_file(
                prefix=BATCH_NUMBER, file_name=file_path, bucket=input_bucket
            )
            if not upload_res:
                err = f"UPLOAD BARCODE MAPPING FILE for TECHNOLOGY: {technology} -- NUMBER: {number} FAILED"
                raise ValueError(err)
            logger.info(
                f"UPLOADED BARCODE_MAPPING_FILE: {barcode_mapping_file_name} && Bucket: {input_bucket}"
            )

            _, missing_files = validate_uploaded_raw_data_for_px_pipeline(samples)
            logger.info(
                f"TRIGGER PCR PIPELINE TECHNOLOGY: {technology} -- NUMBER: {number} -- {payload.gene_kit} w/ {missing_files}"
            )
            data_pipeline = parse_input_n_trigger_px_pipeline(
                missing_files, samples, payload
            )
            return data_pipeline

    else:
        err = f"NOT SUPPORT TECHNOLOGY {technology}"
        raise ValueError(err)


async def check_uploaded_samples_w_tech_n_batch(technology: str, number: int):
    batch = await get_batch_by_number_n_tech(technology, number)
    if not batch:
        err = f"Cant find samples with matching batch number {batch.number}"
        raise ValueError(err)

    samples, _ = await get_filtered_sample_mappings(batch_id=batch.id)
    # logger.info(f"Triggering Drylab for batch {batch.number}")
    logger.info(f"Triggering Drylab for batch {batch.number}")

    if technology in config.get("PIPELINE_INPUT").get("MAIN"):
        raw_data_bucket = config.get("RAW_DATA_BUCKET").get(technology.upper())
        if not raw_data_bucket:
            err = f"MISSING RAW_DATA_BUCKET for this technology {technology}"
            raise ValueError(err)
        logger.info(f"RAW DATA BUCKET: {raw_data_bucket}")

        chip_ids, missing_files = validate_raw_data_for_main_pipeline(
            samples, raw_data_bucket
        )
        logger.info(
            f"CURRENT RUNNING: TECH {technology} BATCH {batch.number} CHIPS {chip_ids} W/ "
        )
        logger.info(f"MISSING FILES: {missing_files}")

        return missing_files

    elif technology in config.get("PIPELINE_INPUT").get("EXTENDED"):
        if technology.upper() in ["PX1", "PCR"]:
            batch_numbers, missing_files = validate_uploaded_raw_data_for_px_pipeline(
                samples
            )
            logger.info(f"CURRENT RUNNING: TECH {technology} BATCH {batch.number} W/ ")
            logger.info(f"MISSING FILES: {missing_files}")

            return missing_files

    else:
        err = f"NOT SUPPORT TECHNOLOGY {technology}"
        raise ValueError(err)


async def get_binded_plate_names_w_tech_n_batch(technology: str, number: int):
    batch = await get_batch_by_number_n_tech(technology, number)
    if not batch:
        err = f"Cant find samples with matching batch number {batch.number}"
        raise ValueError(err)

    samples, _ = await get_filtered_sample_mappings(
        batch_id=batch.id,
        plate_status_filter=config["AVAILABLE_CHIP_STATUS"]
        + config["EXPORT_PLATE_STATUS"],
        is_added_to_batch=True,
    )
    plate_names = set()
    for sample in samples:
        if sample["plate_name"] not in plate_names:
            plate_names.add(sample["plate_name"])
        else:
            continue

    return plate_names


async def update_batch_mapping_wetlab_date(
    technology: str, batch_number: int, body: UpdateBatchMappingWetlabDate
):
    results = []
    async with db.transaction() as tx:
        plate_names = await get_binded_plate_names_w_tech_n_batch(
            technology=technology.upper(), number=batch_number
        )
        for plate_name in plate_names:
            # UPDATE plate STATUS --> RUNNING
            update_plate_body = UpdatePlate(
                status=config["PLATE_STATUS"]["RUN_WETLAB"]
            ).dict()
            current_time = get_current_date_time_utc_7()
            new_plate_info = {**update_plate_body, "updated_at": current_time}
            plate = await get_plate_by_name(technology.upper(), plate_name)
            if not plate:
                errs = f"PLATE {plate_name} is not existed!! Create Plate to proceed!"
                raise ValueError(errs)

            _ = await update_plate(plate, new_plate_info)
            # UPDATE WetLab_Date
            data = body.dict()
            if not data:
                errs = "Empty wetlab_date UPDATE request!"
                raise ValueError(errs)

            formated_data = standardize_date_w_name_service_utc_7(
                data, list(data.keys())[0]
            )
            res = await update_batch_mapping_w_number(
                technology, batch_number, formated_data
            )
            results.append(res)
        pass
    pass
    return results


async def create_batch_w_plate_names(
    technology: str, batch_number: int, body_req: CreateBatchMappingReqs
):
    results = []
    async with db.transaction() as tx:
        if await existed_batch_number(technology, batch_number):
            errs = f"Batch {batch_number} is existed!!"
            raise ValueError(errs)

        batch_res = await create_batch(
            batch_req=CreateBatchReq(
                number=batch_number,
                name=body_req.name,
                note=body_req.note,
                type=technology,
            )
        )
        logger.info(f"Create BATCH w/ number {batch_res['number']}")

        for plate_name in body_req.plate_names:
            plate_name = str(plate_name)
            if not await existed_plate_name(technology, plate_name):
                errs = f"PLATE {plate_name} is not existed!! Create Plate to proceed!"
                raise ValueError(errs)
            if await get_no_samples_in_plate_v3(technology, plate_name) == 0:
                errs = f"PLATE {plate_name} is empty!! Add SAMPLEs to Plate first!"
                raise ValueError(errs)

            logger.info(f"Add PLATE {plate_name} into BATCH {batch_number}")
            plate_res = await get_plate_by_name(technology, plate_name)
            res = await get_batch_mapping_by_plate_id(plate_id=plate_res.id)
            if res:
                used_batch = await get_batch_by_id(res.batch_id)
                errs = f"Plate {plate_name} is already added to another BATCH {used_batch.number}!!"
                raise ValueError(errs)
            else:
                # BATCH_PLATE mapping
                logger.info(
                    f"Create BATCH_MAPPING w/ plate_id {plate_res.id} and batch_id {batch_res['id']}"
                )
                batch_mapping_req = AddBatchMapping(
                    plate_id=str(plate_res.id), batch_id=str(batch_res["id"])
                )
                batch_mapping_res = await create_batch_mapping(batch_mapping_req)
                results.append(batch_mapping_res)

    return results

    pass


async def update_batch_mapping_raw_data_uploaded_date(
    technology: str, batch_number: int, body: UpdateBatchMappingRawDataUploadDate
):
    # CHECK BATCH EXIST
    if not await existed_batch_number(technology, batch_number):
        err = f"BATCH with number {batch_number} NOT existed!!"
        # raise ValueError(err)
        raise ValueError(err)

    # CHECK SAMPLE BEFORE UPDATE RAW_DATA_UPLOAD date
    missing_files = await check_uploaded_samples_w_tech_n_batch(
        technology=technology.upper(), number=batch_number
    )
    if missing_files:
        err = "Missing RAW DATA in S3 bucket"
        # raise ValueError(err)
        raise ValueError(err)

    # UPDATE WetLab_Date
    data = body.dict()
    formated_data = standardize_date_w_name_service_utc_7(data, list(data.keys())[0])
    res = await update_batch_mapping_w_number(technology, batch_number, formated_data)
    return res


async def trigger_drylab_pipeline_w_batch_number(
    technology: str, batch_number: int, payload: TriggerPipelineReq
):
    # CHECK BATCH EXIST
    if not await existed_batch_number(technology, batch_number):
        err = f"BATCH with number {batch_number} NOT existed!!"
        raise ValueError(err)

    # TRIGGER PIPELINE W/ APPROPRIATE INPUT
    inputs = await analyze_samples_w_tech_n_batch(
        technology=technology.upper(), number=batch_number, payload=payload
    )
    # UPDATE PLATE: RUNNING --> COMPLETED
    if inputs:
        results = []
        async with db.transaction() as tx:
            plate_names = await get_binded_plate_names_w_tech_n_batch(
                technology=technology.upper(), number=batch_number
            )
            for plate_name in plate_names:
                update_plate_body = UpdatePlate(status="COMPLETED").dict()
                current_time = get_current_date_time_utc_7()
                new_plate_info = {**update_plate_body, "updated_at": current_time}
                plate = await get_plate_by_name(technology.upper(), plate_name)
                if not plate:
                    errs = (
                        f"PLATE {plate_name} is not existed!! Create Plate to proceed!"
                    )
                    raise ValueError(errs)

                _ = await update_plate(plate, new_plate_info)

                # UPDATE DryLab_Date
                data = UpdateBatchMappingDrylabDate(
                    drylab_date=get_today_date_service()
                ).dict()
                formated_data = standardize_date_w_name_service_utc_7(
                    data, list(data.keys())[0]
                )
                res = await update_batch_mapping_w_number(
                    technology, batch_number, formated_data
                )
                results.append(res)

        return results
    else:
        errs = "CANT TRIGGER PIPELINE!"
        raise ValueError(errs)

    pass


async def manually_update_drylab_date_w_body(
    technology: str, batch_number: int, body_req: UpdateBatchMappingDrylabDate
):
    # GET ALL PLATEs binded to batch number
    results = []
    plate_names = await get_binded_plate_names_w_tech_n_batch(
        technology=technology.upper(), number=batch_number
    )
    for plate_name in plate_names:
        update_plate_body = UpdatePlate(status="COMPLETED").dict()
        current_time = get_current_date_time_utc_7()
        new_plate_info = {**update_plate_body, "updated_at": current_time}
        plate = await get_plate_by_name(technology.upper(), plate_name)
        if not plate:
            errs = f"PLATE {plate_name} is not existed!! Create Plate to proceed!"
            raise ValueError(errs)

        _ = await update_plate(plate, new_plate_info)

        # UPDATE DryLab_Date
        if body_req:
            data = body_req.dict()
            formated_data = standardize_date_w_name_service_utc_7(
                data, list(data.keys())[0]
            )
        else:
            data = UpdateBatchMappingDrylabDate(
                drylab_date=get_today_date_service()
            ).dict()
            formated_data = standardize_date_w_name_service_utc_7(
                data, list(data.keys())[0]
            )
        res = await update_batch_mapping_w_number(
            technology, batch_number, formated_data
        )
        results.append(res)

    return results
    pass


async def get_next_batch_number_w_type(type: str):
    logger.info(f"Get next BATCH ID/NUMBER with technology {type}")
    batch_w_max_number = await get_batch_by_max_number(type.upper())
    if batch_w_max_number:
        logger.info(
            f"NEXT BATCH NUMBER: {batch_w_max_number.number + 1} with technology {type}"
        )
        max_number = batch_w_max_number.number
        return str(max_number + 1)
    else:
        return "1"


async def update_batch_mapping_wetlab_date_v3(
    technology: str, batch_number: int, body: UpdateBatchMappingWetlabDate
):
    results = []
    async with db.transaction() as tx:
        plate_names = await get_binded_plate_names_w_tech_n_batch_v3(
            technology=technology.upper(), number=batch_number
        )
        for plate_name in plate_names:
            # UPDATE plate STATUS --> RUNNING
            update_plate_body = UpdatePlate(
                status=config["PLATE_STATUS"]["RUN_WETLAB"]
            ).dict()
            current_time = get_current_date_time_utc_7()
            new_plate_info = {**update_plate_body, "updated_at": current_time}
            plate = await get_plate_by_name(technology.upper(), plate_name)
            if not plate:
                errs = f"PLATE {plate_name} is not existed!! Create Plate to proceed!"
                raise ValueError(errs)

            _ = await update_plate(plate, new_plate_info)
            # UPDATE WetLab_Date
            data = body.dict()
            if not data:
                errs = "Empty wetlab_date UPDATE request!"
                raise ValueError(errs)

            formated_data = standardize_date_w_name_service_utc_7(
                data, list(data.keys())[0]
            )
            res = await update_batch_mapping_w_number(
                technology, batch_number, formated_data
            )
            results.append(res)
        pass
    pass
    return results


async def get_binded_plate_names_w_tech_n_batch_v3(technology: str, number: int):
    batch = await get_batch_by_number_n_tech(technology, number)
    if not batch:
        err = f"Cant find samples with matching batch number {batch.number}"
        raise ValueError(err)

    samples, _ = await get_filtered_sample_mappings_v3(
        batch_id=batch.id,
        plate_status_filter=config["AVAILABLE_CHIP_STATUS"]
        + config["EXPORT_PLATE_STATUS"],
        is_added_to_batch=True,
    )
    plate_names = set()
    for sample in samples:
        if sample["plate_name"] not in plate_names:
            plate_names.add(sample["plate_name"])
        else:
            continue

    return plate_names


async def update_batch_mapping_raw_data_uploaded_date_v3(
    technology: str, batch_number: int, body: UpdateBatchMappingRawDataUploadDate
):
    # CHECK BATCH EXIST
    if not await existed_batch_number(technology, batch_number):
        err = f"BATCH with number {batch_number} NOT existed!!"
        # raise ValueError(err)
        raise ValueError(err)

    # CHECK SAMPLE BEFORE UPDATE RAW_DATA_UPLOAD date
    missing_files = await check_uploaded_samples_w_tech_n_batch_v3(
        technology=technology.upper(), number=batch_number
    )
    if missing_files:
        err = "Missing RAW DATA in S3 bucket"
        # raise ValueError(err)
        raise ValueError(err)

    # UPDATE WetLab_Date
    data = body.dict()
    formated_data = standardize_date_w_name_service_utc_7(data, list(data.keys())[0])
    res = await update_batch_mapping_w_number(technology, batch_number, formated_data)
    return res


async def check_uploaded_samples_w_tech_n_batch_v3(technology: str, number: int):
    batch = await get_batch_by_number_n_tech(technology, number)
    if not batch:
        err = f"Cant find samples with matching batch number {batch.number}"
        raise ValueError(err)

    samples, _ = await get_filtered_sample_mappings_v3(batch_id=batch.id)
    # logger.info(f"Triggering Drylab for batch {batch.number}")
    logger.info(f"Triggering Drylab for batch {batch.number}")

    if technology in config.get("PIPELINE_INPUT").get("MAIN"):
        raw_data_bucket = config.get("RAW_DATA_BUCKET").get(technology.upper())
        if not raw_data_bucket:
            err = f"MISSING RAW_DATA_BUCKET for this technology {technology}"
            raise ValueError(err)
        logger.info(f"RAW DATA BUCKET: {raw_data_bucket}")

        chip_ids, missing_files = validate_raw_data_for_main_pipeline(
            samples, raw_data_bucket
        )
        logger.info(
            f"CURRENT RUNNING: TECH {technology} BATCH {batch.number} CHIPS {chip_ids} W/ "
        )
        logger.info(f"MISSING FILES: {missing_files}")

        return missing_files

    elif technology in config.get("PIPELINE_INPUT").get("EXTENDED"):
        if technology.upper() in ["PX1", "PCR"]:
            batch_numbers, missing_files = validate_uploaded_raw_data_for_px_pipeline(
                samples
            )
            logger.info(f"CURRENT RUNNING: TECH {technology} BATCH {batch.number} W/ ")
            logger.info(f"MISSING FILES: {missing_files}")

            return missing_files

    else:
        err = f"NOT SUPPORT TECHNOLOGY {technology}"
        raise ValueError(err)


async def analyze_samples_w_tech_n_batch_v3(
    technology: str, number: int, payload: TriggerPipelineReq
):
    batch = await get_batch_by_number_n_tech(technology, number)
    if not batch:
        err = f"Cant find batch with number {batch.number}"
        raise ValueError(err)

    samples, _ = await get_filtered_sample_mappings_v3(batch_id=batch.id)
    if not samples:
        err = f"Cant find samples with matching batch number {batch.number}"
        raise ValueError(err)

    # logger.info(f"Triggering Drylab for batch {batch.number}")
    logger.info(f"Triggering Drylab for batch {batch.number}")

    if technology in config.get("PIPELINE_INPUT").get("MAIN"):
        assembly = config.get("ASSEMBLY").get(technology.upper())
        if not assembly:
            err = f"This technology {technology} doesnt have ASSEMBLY value"
            raise ValueError(err)
        logger.info(f"ASSEMBLY: {assembly}")
        raw_data_bucket = config.get("RAW_DATA_BUCKET").get(technology.upper())
        if not raw_data_bucket:
            err = f"MISSING RAW_DATA_BUCKET for this technology {technology}"
            raise ValueError(err)
        logger.info(f"RAW DATA BUCKET: {raw_data_bucket}")

        chip_ids, missing_files = validate_raw_data_for_main_pipeline(
            samples, raw_data_bucket
        )
        logger.info(f"CURRENT RUNNING CHIPS: {chip_ids}")
        logger.info(f"MISSING FILES: {missing_files}")

        data_pipeline = parse_input_n_trigger_main_pipeline(
            missing_files, samples, raw_data_bucket, assembly
        )
        return data_pipeline

    elif technology in config.get("PIPELINE_INPUT").get("EXTENDED"):
        if technology.upper() in ["PX1", "PCR"]:
            logger.info(f"TECHNOLOGY: {technology} -- NUMBER: {number}")
            BATCH_NUMBER = str(number)
            barcode_mapping_list = get_barcode_mapping_list_w_batch_id(samples=samples)
            logger.info(f"GET BARCODE_MAPPING_LIST: {barcode_mapping_list}")

            barcode_mapping_file_config = config.get("PIPELINE_QC_STATUS").get(
                "BARCODE_MAPPING_FILE"
            )
            barcode_mapping_file_name = (
                barcode_mapping_file_config.get("PREFIX")
                + BATCH_NUMBER
                + barcode_mapping_file_config.get("SUFFIX")
            )
            barcode_mapping_local_path = barcode_mapping_file_config.get("LOCAL_PATH")
            file_path = os.path.join(
                barcode_mapping_local_path, barcode_mapping_file_name
            )
            export_list_dicts_to_csv(barcode_mapping_list, file_path)
            logger.info(
                f"EXPORTED BARCODE_MAPPING_FILE: {barcode_mapping_file_name} && into path: {file_path}"
            )

            input_bucket = config.get("RAW_DATA_BUCKET").get("PCR")
            upload_res = upload_file(
                prefix=BATCH_NUMBER, file_name=file_path, bucket=input_bucket
            )
            if not upload_res:
                err = f"UPLOAD BARCODE MAPPING FILE for TECHNOLOGY: {technology} -- NUMBER: {number} FAILED"
                raise ValueError(err)
            logger.info(
                f"UPLOADED BARCODE_MAPPING_FILE: {barcode_mapping_file_name} && Bucket: {input_bucket}"
            )

            _, missing_files = validate_uploaded_raw_data_for_px_pipeline(samples)
            logger.info(
                f"TRIGGER PCR PIPELINE TECHNOLOGY: {technology} -- NUMBER: {number} -- {payload.gene_kit} w/ {missing_files}"
            )
            data_pipeline = parse_input_n_trigger_px_pipeline(
                missing_files, samples, payload
            )
            return data_pipeline

    else:
        err = f"NOT SUPPORT TECHNOLOGY {technology}"
        raise ValueError(err)


async def trigger_drylab_pipeline_w_batch_number_v3(
    technology: str, batch_number: int, payload: TriggerPipelineReq
):
    # CHECK BATCH EXIST
    if not await existed_batch_number(technology, batch_number):
        err = f"BATCH with number {batch_number} NOT existed!!"
        raise ValueError(err)

    # TRIGGER PIPELINE W/ APPROPRIATE INPUT
    inputs = await analyze_samples_w_tech_n_batch_v3(
        technology=technology.upper(), number=batch_number, payload=payload
    )
    # UPDATE PLATE: RUNNING --> COMPLETED
    if inputs:
        results = []
        async with db.transaction() as tx:
            plate_names = await get_binded_plate_names_w_tech_n_batch_v3(
                technology=technology.upper(), number=batch_number
            )
            for plate_name in plate_names:
                update_plate_body = UpdatePlate(status="COMPLETED").dict()
                current_time = get_current_date_time_utc_7()
                new_plate_info = {**update_plate_body, "updated_at": current_time}
                plate = await get_plate_by_name(technology.upper(), plate_name)
                if not plate:
                    errs = (
                        f"PLATE {plate_name} is not existed!! Create Plate to proceed!"
                    )
                    raise ValueError(errs)

                _ = await update_plate(plate, new_plate_info)

                # UPDATE DryLab_Date
                data = UpdateBatchMappingDrylabDate(
                    drylab_date=get_today_date_service()
                ).dict()
                formated_data = standardize_date_w_name_service_utc_7(
                    data, list(data.keys())[0]
                )
                res = await update_batch_mapping_w_number(
                    technology, batch_number, formated_data
                )
                results.append(res)

        return results
    else:
        errs = "CANT TRIGGER PIPELINE!"
        raise ValueError(errs)

    pass


async def manually_update_drylab_date_w_body_v3(
    technology: str, batch_number: int, body_req: UpdateBatchMappingDrylabDate
):
    # GET ALL PLATEs binded to batch number
    results = []
    plate_names = await get_binded_plate_names_w_tech_n_batch_v3(
        technology=technology.upper(), number=batch_number
    )
    for plate_name in plate_names:
        update_plate_body = UpdatePlate(status="COMPLETED").dict()
        current_time = get_current_date_time_utc_7()
        new_plate_info = {**update_plate_body, "updated_at": current_time}
        plate = await get_plate_by_name(technology.upper(), plate_name)
        if not plate:
            errs = f"PLATE {plate_name} is not existed!! Create Plate to proceed!"
            raise ValueError(errs)

        _ = await update_plate(plate, new_plate_info)

        # UPDATE DryLab_Date
        if body_req:
            data = body_req.dict()
            formated_data = standardize_date_w_name_service_utc_7(
                data, list(data.keys())[0]
            )
        else:
            data = UpdateBatchMappingDrylabDate(
                drylab_date=get_today_date_service()
            ).dict()
            formated_data = standardize_date_w_name_service_utc_7(
                data, list(data.keys())[0]
            )
        res = await update_batch_mapping_w_number(
            technology, batch_number, formated_data
        )
        results.append(res)

    return results
