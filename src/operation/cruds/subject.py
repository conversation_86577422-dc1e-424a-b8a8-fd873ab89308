import uuid
from typing import Optional
from ..models.models import db, Subject
from ..utils.utils import get_current_date_time_utc_7
from .. import logger

async def get_subject(
    user_id: str,
    full_name: Optional[str] = None,
    dob: Optional[str] = None,
    yob: Optional[str] = None,
    gender: Optional[str] = None,
    phone_number: Optional[str] = None,
    identifier_code: Optional[str] = None
):
    results = Subject.query
    if user_id:
        results = results.where(Subject.user_id == user_id)
    if full_name:
        results = results.where(Subject.full_name == (full_name))
    if dob:
        results = results.where(Subject.dob == (dob))
    if yob:
        results = results.where(Subject.yob == (yob))
    if gender:
        results = results.where(Subject.gender == (gender))
    if phone_number:
        results = results.where(Subject.phone_number == (phone_number))
    if identifier_code:
        results = results.where(Subject.identifier_code == (identifier_code))
    else:
        results = results.where(Subject.identifier_code is None)
        
        
    return [r for r in await results.gino.all()]

async def get_subject_by_id(
    subject_id: str,
):
    subject = await Subject.get(subject_id)
    return subject

async def get_subject_by_id_card(
    identifier_code: str,
):
    subject = await Subject.query.where(Subject.identifier_code==identifier_code).gino.first()
    return subject

async def create_subject(
    subject: Subject
):
    # subject.id = uuid.uuid4
    subject.created_at = get_current_date_time_utc_7()
    subject.updated_at = get_current_date_time_utc_7()
    results = await subject.create()
    return results.to_dict()

async def update_subject(
    subject: Subject
):
    subject.updated_at = get_current_date_time_utc_7()
    results = await subject.update(**subject.to_dict()).apply()
    return results

KITS_SUBJECT_QUERY_V3 = """
    select
        k.id as kit_uuid,
        k.barcode,
        k.nickname,
        k.samplecode,
        sub.user_id,
        sub.identifier_code,
        sub.full_name,
        sub.dob::date,
        sub.yob,
        sub.legal_guardian,
        sub.gender,
        sub.phone_number,
        sub.email,
        sub.address,
        k.product_name,
        k.product_code,
        k.product_type,
        k.current_status,
        k.workflow,
        a.id as account_id,
        a.name as account_name,
        s.id as source_id,
        st.name as nominator,
        st.id as nominator_id,
        stf.name as freelancer,
        stf.id as freelancer_id,
        st2.name as sale_pic,
        sam.subject_id,
        sam.sample_collection_date::timestamp,
        sam.sample_collection_time,
        sam.sample_receipt_date::timestamp,
        sam.lab_receipt_date::timestamp,
        sam.sample_collector_name,
        sam.sample_receiver_name,
        k.expected_report_release_date::date,
        k.customer_support_id,
        k.customer_support_name,
        k.free_of_charge,
        k.is_priority,
        sam.scan_status,
        k.note,
        k.promotion as promotion_id,
        p.code as promotion_code,
        b.number as batch_number,
        sub.diagnosis,
        sub.validate_account,
        k.actual_report_release_time::date as actual_report_release_date,
        k.actual_report_release_time,
        k.default_pdf_link,
        sam.sample_type,
        case 
            when k.default_pdf_link is null then 'NOT_AVAILABLE'
            when k.pdf_generation_date > current_date - interval '6 days' then 'AVAILABLE'
            else 'EXPIRED'
        end
        as pdf_status,
        dna_records.lid as dna_extraction_id,
        k.created_at as created_at,
        k.created_at as upgraded_at,
        k.updated_at,
        k.deleted_at
    from
        subject sub
        INNER JOIN sample sam ON sub.id = sam.subject_id
        INNER JOIN kit k ON sam.samplecode = k.samplecode
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join promotion p on k.promotion = p.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id

"""


KITS_SUBJECT_COUNT_QUERY_V3 = """
    select
        count(k.barcode)
    from
        subject sub
        INNER JOIN sample sam ON sub.id = sam.subject_id
        INNER JOIN kit k ON sam.samplecode = k.samplecode
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
"""

async def get_all_kits_subject_via_subject_id_v3(
    subject_id: Optional[str]=None,
    order_by: Optional[str]=None,
    offset: Optional[int]=None,
    size: Optional[int]=None
):
    query_filter = ""
    if subject_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.id = '{subject_id}' "
    
    raw_count_query = KITS_SUBJECT_COUNT_QUERY_V3 + "where " + query_filter if query_filter != "" else KITS_SUBJECT_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    
    if order_by:
        if order_by == 'created_time':
            order_by = 'created_at'
        query_filter += f"order by {order_by} asc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = KITS_SUBJECT_QUERY_V3 + query_filter
    else:
        raw_query = KITS_SUBJECT_QUERY_V3 + "where " + query_filter if query_filter != "" else KITS_SUBJECT_QUERY_V3
        
    results = await db.all(db.text(raw_query))
    
    return results, total