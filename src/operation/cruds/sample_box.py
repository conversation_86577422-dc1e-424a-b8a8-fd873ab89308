from io import BytesIO
from typing import Optional, List
from sqlalchemy import desc, asc, and_
from sqlalchemy import func
import re
from datetime import datetime
from ..models.models import db, SampleRack, SampleBoxPosition, SampleBox
from ..services.promotion import convert_start_promotion_date, convert_end_promotion_date
from typing import Optional
from sqlalchemy import and_, asc, desc

from ..utils.excel_utils import export_to_excel


def create_sample_box(code: str):
    """
    Create a new sample box with the provided data.
    """

    sample_box = SampleBox(
        code=code
    )

    return SampleBox.create(sample_box)


def create_sample_box_if_not_exists(code: str):
    """
    Create a new sample box if it does not already exist.
    """
    existing_box = SampleBox.query.where(SampleBox.code == code).gino.first()
    if existing_box:
        return existing_box
    return create_sample_box(code)

async def get_list_sample_boxes(
    box_code: Optional[str] = None,
    rack_code: Optional[str] = None,
    total: Optional[str] = None,
    created_by: Optional[str] = None,
    updated_by: Optional[str] = None,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    order_by: Optional[str] = "created_at",
    order_direction: Optional[str] = "desc",
    page_number: int = 1,
    page_size: int = 10
):
    subquery_total_samples = (
        db.select([
            SampleBoxPosition.sample_box_id,
            db.func.count(SampleBoxPosition.id).label('total_samples')
        ])
        .group_by(SampleBoxPosition.sample_box_id)
        .alias('sample_counts')
    )

    query = (
        db.select([
            SampleBox.id,
            SampleBox.code.label('box_code'),
            SampleRack.code.label('rack_code'),
            db.func.count(SampleBoxPosition.id).label('total_samples'),
            SampleBox.created_at,
            SampleBox.updated_at,
            SampleBox.created_by,
            SampleBox.updated_by,
        ])
        .select_from(
            SampleBox
            .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
            .outerjoin(SampleBoxPosition, SampleBox.id == SampleBoxPosition.sample_box_id)
        )
        .group_by(SampleBox.id, SampleRack.code)
    )

    count_query = (
        db.select([db.func.count(SampleBox.id.distinct())])
        .select_from(
            SampleBox
            .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
            .outerjoin(SampleBoxPosition, SampleBox.id == SampleBoxPosition.sample_box_id)
        )
    )

    # Apply filters
    filters = []
    if box_code:
        filters.append(SampleBox.code == box_code)
    if rack_code:
        filters.append(SampleRack.code == rack_code)
    if created_by:
        filters.append(SampleBox.created_by == created_by)
    if updated_by:
        filters.append(SampleBox.updated_by == updated_by)
    if from_date:
        from_date = await convert_start_promotion_date(from_date)
        filters.append(SampleBox.created_at >= from_date)
    if to_date:
        to_date = await convert_end_promotion_date(to_date)
        filters.append(SampleBox.created_at <= to_date)

    # Apply filters to main queries
    if filters:
        query = query.where(and_(*filters))
        count_query = count_query.where(and_(*filters))

    if total:
        query = query.having(db.func.count(SampleBoxPosition.id) == int(total))

    if order_by == "created_at":
        order_column = SampleBox.created_at
    elif order_by == "updated_at":
        order_column = SampleBox.updated_at
    elif order_by == "code":
        order_column = SampleBox.code
    elif order_by == "rack_code":
        order_column = SampleRack.code
    else:
        order_column = SampleBox.created_at

    if order_direction.lower() == "desc":
        query = query.order_by(desc(order_column))
    else:
        query = query.order_by(asc(order_column))

    offset = (page_number - 1) * page_size
    limit = page_size
    query = query.offset(offset).limit(limit)

    boxes = await db.all(query)
    total_count = await db.scalar(count_query)

    data = [
        {
            "id": str(box[0]),
            "box_code": box[1],
            "rack_code": box[2] or "",
            "total": box[3],
            "created_at": box[4].isoformat() if box[4] else None,
            "updated_at": box[5].isoformat() if box[5] else None,
            "created_by": box[6],
            "updated_by": box[7],
        }
        for box in boxes
    ]

    return data, total_count

async def get_list_sample_boxes_all(
    box_code: Optional[str] = None,
    rack_code: Optional[str] = None,
    total: Optional[int] = None,
    created_by: Optional[str] = None,
    updated_by: Optional[str] = None,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    order_by: Optional[str] = "created_at",
    order_direction: Optional[str] = "desc"
):
    query = (
        db.select([
            SampleBox.id,
            SampleBox.code.label('box_code'),
            SampleRack.code.label('rack_code'),
            db.func.count(SampleBoxPosition.id).label('total_samples'),
            SampleBox.created_at,
            SampleBox.updated_at,
            SampleBox.created_by,
            SampleBox.updated_by,
        ])
        .select_from(
            SampleBox
            .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
            .outerjoin(SampleBoxPosition, SampleBox.id == SampleBoxPosition.sample_box_id)
        )
        .group_by(SampleBox.id, SampleRack.code)
    )

    # Apply filters
    filters = []
    if box_code:
        filters.append(SampleBox.code == box_code)
    if rack_code:
        filters.append(SampleRack.code == rack_code)
    if created_by:
        filters.append(SampleBox.created_by == created_by)
    if updated_by:
        filters.append(SampleBox.updated_by == updated_by)
    if from_date:
        from_date = await convert_start_promotion_date(from_date)
        filters.append(SampleBox.created_at >= from_date)
    if to_date:
        to_date = await convert_end_promotion_date(to_date)
        filters.append(SampleBox.created_at <= to_date)

    # Apply filters
    if filters:
        query = query.where(and_(*filters))

    # Apply total_sample filter using HAVING
    if total:
        query = query.having(db.func.count(SampleBoxPosition.id) == total)

    # Apply ordering
    if order_by == "created_at":
        order_column = SampleBox.created_at
    elif order_by == "updated_at":
        order_column = SampleBox.updated_at
    elif order_by == "code":
        order_column = SampleBox.code
    elif order_by == "rack_code":
        order_column = SampleRack.code
    else:
        order_column = SampleBox.created_at

    if order_direction.lower() == "desc":
        query = query.order_by(desc(order_column))
    else:
        query = query.order_by(asc(order_column))

    # Execute
    boxes = await db.all(query)

    data = [
        {
            "id": str(box[0]),
            "box_code": box[1],
            "rack_code": box[2] or "",
            "total": box[3],
            "created_at": box[4].isoformat() if box[4] else None,
            "updated_at": box[5].isoformat() if box[5] else None,
            "created_by": box[6],
            "updated_by": box[7],
        }
        for box in boxes
    ]

    return data


def get_position(box_id, box_width_position, box_length_position, box_length):
    if box_id is None:
        return ""

    try:
        box_width_position = int(box_width_position)
        box_length_position = int(box_length_position)
        box_length = int(box_length)
    except (TypeError, ValueError):
        return ""

    num_digits = len(str(abs(box_length)))
    fmt = f"{{:c}}{{:0{num_digits}d}}"

    return fmt.format(65 + box_width_position, box_length_position + 1)


async def get_list_sample_box_position(
    barcode: Optional[str] = None,
    position: Optional[str] = None,
    box_code: Optional[str] = None,
    rack_code: Optional[str] = None,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    order_by: Optional[str] = None,
    order_direction: Optional[str] = "desc",
    page_number: int = 1,
    page_size: int = 10
):
    query = (
        db.select([
            SampleBoxPosition.barcode,
            SampleBoxPosition.x_position,
            SampleBoxPosition.y_position,
            SampleBox.code.label('box_code'),
            SampleRack.code.label('rack_code'),
            SampleBoxPosition.created_at,
            SampleBox.id.label('box_id'),
            db.func.max(SampleBoxPosition.y_position)
                .over(partition_by=SampleBoxPosition.sample_box_id)
                .label('box_length')
        ])
        .select_from(
            SampleBoxPosition
            .join(SampleBox, SampleBox.id == SampleBoxPosition.sample_box_id)
            .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
        )
    )

    count_query = (
        db.select([db.func.count(SampleBoxPosition.id)])
        .select_from(
            SampleBoxPosition
            .join(SampleBox, SampleBox.id == SampleBoxPosition.sample_box_id)
            .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
        )
    )

    filters = []
    if barcode:
        filters.append(SampleBoxPosition.barcode.ilike(f"%{barcode}%"))
    if from_date:
        from_date = await convert_start_promotion_date(from_date)
        filters.append(SampleBoxPosition.created_at >= from_date)
    if to_date:
        to_date = await convert_end_promotion_date(to_date)
        filters.append(SampleBoxPosition.created_at <= to_date)
    if box_code:
        filters.append(SampleBox.code == box_code)
    if rack_code:
        filters.append(SampleRack.code == rack_code)

    if filters:
        query = query.where(and_(*filters))
        count_query = count_query.where(and_(*filters))

    query = query.order_by(
        desc(SampleBoxPosition.created_at),
        asc(SampleBox.code),
        asc(SampleRack.code),
        asc(SampleBoxPosition.y_position),
        asc(SampleBoxPosition.x_position),
        asc(SampleBoxPosition.barcode)
    )

    offset = (page_number - 1) * page_size
    query = query.offset(offset).limit(page_size)

    rows = await db.all(query)
    total_count = await db.scalar(count_query)

    data = []
    for row in rows:
        barcode_val = row[0]
        x = row[1]
        y = row[2]
        box_code_val = row[3]
        rack_code_val = row[4]
        created_at = row[5]
        box_id = row[6]
        box_length = row[7]

        position_str = get_position(box_id, x, y, box_length)

        if position and position_str != position:
            continue

        data.append({
            "barcode": barcode_val,
            "position": position_str,
            "box_code": box_code_val,
            "rack_code": rack_code_val or "",
            "created_at": created_at.isoformat() if created_at else None
        })

    return data, total_count

async def get_list_sample_box_position_all(
    barcode: Optional[str] = None,
    position: Optional[str] = None,
    box_code: Optional[str] = None,
    rack_code: Optional[str] = None,
    from_date: Optional[str] = None,
    to_date: Optional[str] = None,
    order_by: Optional[str] = None,
    order_direction: Optional[str] = "desc"
):
    query = (
        db.select([
            SampleBoxPosition.barcode,
            SampleBoxPosition.x_position,
            SampleBoxPosition.y_position,
            SampleBox.code.label('box_code'),
            SampleRack.code.label('rack_code'),
            SampleBoxPosition.created_at,
            SampleBox.id.label('box_id'),
            db.func.max(SampleBoxPosition.y_position)
                .over(partition_by=SampleBoxPosition.sample_box_id)
                .label('box_length')
        ])
        .select_from(
            SampleBoxPosition
            .join(SampleBox, SampleBox.id == SampleBoxPosition.sample_box_id)
            .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
        )
    )

    count_query = (
        db.select([db.func.count(SampleBoxPosition.id)])
        .select_from(
            SampleBoxPosition
            .join(SampleBox, SampleBox.id == SampleBoxPosition.sample_box_id)
            .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
        )
    )

    filters = []
    if barcode:
        filters.append(SampleBoxPosition.barcode.ilike(f"%{barcode}%"))
    if from_date:
        from_date = await convert_start_promotion_date(from_date)
        filters.append(SampleBoxPosition.created_at >= from_date)
    if to_date:
        to_date = await convert_end_promotion_date(to_date)
        filters.append(SampleBoxPosition.created_at <= to_date)
    if box_code:
        filters.append(SampleBox.code == box_code)
    if rack_code:
        filters.append(SampleRack.code == rack_code)

    if filters:
        query = query.where(and_(*filters))
        count_query = count_query.where(and_(*filters))

    query = query.order_by(
        desc(SampleBoxPosition.created_at),
        asc(SampleBox.code),
        asc(SampleRack.code),
        asc(SampleBoxPosition.y_position),
        asc(SampleBoxPosition.x_position),
        asc(SampleBoxPosition.barcode)
    )

    rows = await db.all(query)
    total_count = await db.scalar(count_query)

    data = []
    for row in rows:
        barcode_val = row[0]
        x = row[1]
        y = row[2]
        box_code_val = row[3]
        rack_code_val = row[4]
        created_at = row[5]
        box_id = row[6]
        box_length = row[7]

        position_str = get_position(box_id, x, y, box_length)

        if position and position_str != position:
            continue

        data.append({
            "barcode": barcode_val,
            "position": position_str,
            "box_code": box_code_val,
            "rack_code": rack_code_val or "",
            "created_at": created_at.isoformat() if created_at else None
        })

    return data, total_count

def preprocess_data_sample_box(data: List) -> List:
    for item in data:
        item["box_code"] = str(item["box_code"])
        item["rack_code"] = str(item["rack_code"])
        item["total"] = str(item["total"])
        item["created_at"] = datetime.fromisoformat(str(item["created_at"])).strftime('%d/%m/%Y')
        item["updated_at"] = datetime.fromisoformat(str(item["updated_at"])).strftime('%d/%m/%Y')
        item["created_by"] = str(item["created_by"])
        item["updated_by"] = str(item["updated_by"])
    return data

def export_sample_box_to_excel(data: List) -> BytesIO:
    data_dicts = [dict(row) for row in data]
    data = preprocess_data_sample_box(data_dicts)
    columns = [
        {"key": "box_code", "title": "Hộp"},
        {"key": "rack_code", "title": "Tủ"},
        {"key": "total", "title": "Tổng số mẫu"},
        {"key": "created_by", "title": "Người tạo"},
        {"key": "created_at", "title": "Ngày tạo"},
        {"key": "updated_by", "title": "Người cập nhật"},
        {"key": "updated_at", "title": "Ngày cập nhật"},
    ]

    return export_to_excel(data=data, columns=columns, include_index=True)


def preprocess_data_sample_box_position(data: List) -> List:
    for item in data:
        item["barcode"] = str(item["barcode"])
        item["position"] = str(item["position"])
        item["box_code"] = str(item["box_code"])
        item["rack_code"] = str(item["rack_code"])
        item["created_at"] = datetime.fromisoformat(str(item["created_at"])).strftime('%d/%m/%Y')
    return data

def export_sample_box_postion_to_excel(data: List) -> BytesIO:
    data_dicts = [dict(row) for row in data]
    data = preprocess_data_sample_box_position(data_dicts)
    columns = [
        {"key": "barcode", "title": "Barcode"},
        {"key": "position", "title": "Vị trí"},
        {"key": "box_code", "title": "Hộp"},
        {"key": "rack_code", "title": "Tủ"},
        {"key": "created_at", "title": "Ngày nhập kho"},
    ]

    return export_to_excel(data=data, columns=columns, include_index=True)



