import uuid
from typing import <PERSON><PERSON>

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from starlette.status import HTTP_400_BAD_REQUEST

from .. import logger
from ..models.models import Tracking, db
from ..utils.utils import failure_response, get_current_date_time_utc_7
from .kit import get_kit_by_barcode_v3

# Tracking - model
# TRACKING - QUERY NAME
# _tracking - function samplecode
# tracking - single
# trackings - plural
# samplecode -- search key

TRACKING_LIST_QUERY_V3 = """
    select
        t.id,
        t.samplecode,
        t.identifier_code,
        t.created_at,
        t.updated_at,
        t.deleted_at
    from
        tracking t
    inner join kit k on t.samplecode = k.samplecode

"""

TRACKING_LIST_COUNT_QUERY_V3 = """
    select
        count(t.id)
    from
        tracking t
    inner join kit k on t.samplecode = k.samplecode
"""


async def get_tracking_by_id(id: uuid.uuid4):
    tracking = await Tracking.get(id)
    return tracking


async def get_tracking_by_barcode(barcode: str):
    kit = await get_kit_by_barcode_v3(barcode=barcode)
    return await get_tracking_by_samplecode(samplecode=kit.samplecode)
    pass


async def get_tracking_by_samplecode(samplecode: str):
    tracking = (
        await Tracking.query.where(Tracking.samplecode == samplecode)
        .where(Tracking.deleted_at == None)
        .gino.first()
    )
    return tracking


async def get_all_trackings(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "t.created_at",
    samplecode: Optional[str] = None,
    barcode: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    query_filter = ""
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""t.samplecode = '{str(samplecode)}' """
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""k.barcode = '{str(barcode)}' """
    # if not include_deleted:
    #     query_filter += "and " if query_filter != "" else " "
    #     query_filter += """t.deleted_at is null """

    raw_count_query = (
        TRACKING_LIST_COUNT_QUERY_V3 + "where" + query_filter
        if query_filter != ""
        else TRACKING_LIST_COUNT_QUERY_V3
    )
    raw_count_query += "group by t.id, t.samplecode, t.identifier_code, t.created_at, t.updated_at, t.deleted_at "
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    page_filter = ""
    if order_by:
        page_filter += f"order by {order_by} desc "
    if size:
        page_filter += f"limit {size} "
        page_filter += f"offset {offset} "

    raw_query = (
        TRACKING_LIST_QUERY_V3 + "where " + query_filter
        if query_filter != ""
        else TRACKING_LIST_QUERY_V3
    )
    raw_query += "group by t.id, t.samplecode, t.identifier_code, t.created_at, t.updated_at, t.deleted_at "
    raw_query += page_filter
    results = await db.all(db.text(raw_query))
    return results, total


async def is_tracking_existed(samplecode: str):
    total = (
        await db.select([db.func.count()])
        .where(Tracking.samplecode == samplecode)
        .where(Tracking.deleted_at == None)
        .gino.scalar()
    )
    return True if total > 0 else False


async def create_tracking(data: dict):
    if await is_tracking_existed(data["samplecode"]):
        err = f"Error: Integration Tracking with samplecode {data['samplecode']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    current_time = get_current_date_time_utc_7()
    data = {**data, "created_at": current_time, "updated_at": current_time}
    # print("create tracking data: ", data)
    result = await Tracking.create(**data)
    return result.to_dict()


async def update_tracking(tracking: Tracking, data: dict):
    if tracking.deleted_at:
        err = f"Tracking with samplecode: {tracking.samplecode} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking.update(**data).apply()
    logger.info(f"Tracking with samplecode: {tracking.samplecode} updated")
    return None


async def delete_tracking(tracking: Tracking):
    current_time = get_current_date_time_utc_7()
    data = {
        "updated_at": current_time,
        "deleted_at": current_time,
    }

    if not tracking:
        err = f"Tracking with samplecode: {tracking.samplecode} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking.deleted_at:
        err = f"Tracking with: {tracking.samplecode} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking.update(**data).apply()
    logger.info(f"Tracking with samplecode: {tracking.samplecode} deleted")


SAMPLE_TRACKING_QUERY_V3 = """
SELECT
    t.id,
    t.samplecode,
    t.status,
    t.identifier_code,
    tc."maThuNhan" AS "thuNhanMau_maThuNhan",
    tc."donViThuNhanMau_id" AS "thuNhanMau_donViThuNhanMau",
    tu."tenDonVi",
    tc."noiThuThapMau" AS "thuNhanMau_noiThuThapMau",
    tc."ngayGioThuThapMau" AS "thuNhanMau_ngayGioThuThapMau",
    tc."nhanVienLayMau_id",
    nvlm."hoTenNhanVien" AS "thuNhanMau_nhanVienLayMau_hoTenNhanVien",
    nvlm."soDinhDanh" AS "thuNhanMau_nhanVienLayMau_soDinhDanh",
    tc."nhanVienGhiHoSo_id",
    nvghs."hoTenNhanVien" AS "thuNhanMau_nhanVienGhiHoSo_hoTenNhanVien",
    nvghs."soDinhDanh" AS "thuNhanMau_nhanVienGhiHoSo_soDinhDanh",
    tc."nhanVienLuuMau_id",
    nvlum."hoTenNhanVien" AS "thuNhanMau_nhanVienLuuMau_hoTenNhanVien",
    nvlum."soDinhDanh" AS "thuNhanMau_nhanVienLuuMau_soDinhDanh",
    td."maVanChuyen" AS "vanChuyenMau_maVanChuyen",
    td."nhietDoChuyenGiao" AS "vanChuyenMau_nhietDoChuyenGiao",
    td."tinhTrangNiemPhong" AS "vanChuyenMau_tinhTrangNiemPhong",
    td."ngayGioChuyenGiao" AS "vanChuyenMau_ngayGioChuyenGiao",
    td."diaDiemChuyenGiao" AS "vanChuyenMau_diaDiemChuyenGiao",
    td."donViBanGiao_id",
    dvbg."tenDonVi" AS "vanChuyenMau_donViBanGiao",
    td."donViVanChuyen_id",
    dvvc."tenDonVi" AS "vanChuyenMau_donViVanChuyen",
    td."donViNhanMau_id",
    dvnm."tenDonVi" AS "vanChuyenMau_donViNhanMau",
    td."nhanVienBanGiao_id",
    nvbg."hoTenNhanVien" AS "vanChuyenMau_nhanVienBanGiao_hoTenNhanVien",
    nvbg."soDinhDanh" AS "vanChuyenMau_nhanVienBanGiao_soDinhDanh",
    td."nhanVienNhanMau_id",
    nvnm."hoTenNhanVien" AS "vanChuyenMau_nhanVienNhanMau_hoTenNhanVien",
    nvnm."soDinhDanh" AS "vanChuyenMau_nhanVienNhanMau_soDinhDanh",
    td.khac,
    t.created_at,
    t.updated_at,
    t.deleted_at
FROM
    tracking t
    INNER JOIN tracking_collection tc ON t.id = tc.tracking_id
    INNER JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
    INNER JOIN tracking_employee nvlm ON tc."nhanVienLayMau_id" = nvlm.id
    INNER JOIN tracking_employee nvghs ON tc."nhanVienGhiHoSo_id" = nvghs.id
    LEFT JOIN tracking_employee nvlum ON tc."nhanVienLuuMau_id" = nvlum.id
    INNER JOIN tracking_delivery td ON t.id = td.tracking_id
    INNER JOIN tracking_unit dvbg ON td."donViBanGiao_id" = dvbg.id
    LEFT JOIN tracking_unit dvvc ON td."donViVanChuyen_id" = dvvc.id
    INNER JOIN tracking_unit dvnm ON td."donViNhanMau_id" = dvnm.id
    INNER JOIN tracking_employee nvbg ON td."nhanVienBanGiao_id" = nvbg.id
    INNER JOIN tracking_employee nvnm ON td."nhanVienNhanMau_id" = nvnm.id

"""

COUNT_SAMPLE_TRACKING_QUERY_V3 = """
    select
        count(t.id)
    from
        tracking t
        INNER JOIN tracking_collection tc ON t.id = tc.tracking_id
        INNER JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
        INNER JOIN tracking_employee nvlm ON tc."nhanVienLayMau_id" = nvlm.id
        INNER JOIN tracking_employee nvghs ON tc."nhanVienGhiHoSo_id" = nvghs.id
        LEFT JOIN tracking_employee nvlum ON tc."nhanVienLuuMau_id" = nvlum.id
        INNER JOIN tracking_delivery td ON t.id = td.tracking_id
        INNER JOIN tracking_unit dvbg ON td."donViBanGiao_id" = dvbg.id
        LEFT JOIN tracking_unit dvvc ON td."donViVanChuyen_id" = dvvc.id
        INNER JOIN tracking_unit dvnm ON td."donViNhanMau_id" = dvnm.id
        INNER JOIN tracking_employee nvbg ON td."nhanVienBanGiao_id" = nvbg.id
        INNER JOIN tracking_employee nvnm ON td."nhanVienNhanMau_id" = nvnm.id
"""


async def get_sample_tracking_detail(samplecode: str):
    query_filter = f"where t.samplecode = '{samplecode}'"
    raw_query = SAMPLE_TRACKING_QUERY_V3 + query_filter
    results = await db.all(db.text(raw_query))
    if len(results) != 1:
        raise ValueError(f"tracking of samplecode {samplecode} not found")
    return results[0]
