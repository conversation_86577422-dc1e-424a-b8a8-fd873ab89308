from typing import Optional

from ..config import config
from ..models.models import <PERSON><PERSON><PERSON>, MissingSample, db
from ..schemas.lab_sample import LabSampleUpdateReq, MissingSampleUpdateReq
from ..utils.utils import get_current_date_time_utc_7
from .register_kit import (
    create_status,
    create_status_v3,
    get_kit,
    get_kit_v3,
    get_kits_with_status,
    get_sample_by_samplecode_v3,
    get_sample_v3,
    update_kit,
    update_kit_v3,
    update_sample_v3,
)

# currently support lid with length of 6
GET_CURRENT_LID_QUERY = """
    SELECT lid 
    FROM lab_sample
    WHERE lid ~ '^[0-9]+$' 
        AND length(lid) >= 6
    ORDER BY lid::integer DESC
    LIMIT 1;
"""

COUNT_LAB_SAMPLE_QUERY = """
    select count(lid) from lab_sample;
"""


def query_lab_samples_w_params(is_all=False, DNA_QC_STATUS="ALL"):
    if is_all:
        if DNA_QC_STATUS in ["PASS", "FAIL"]:
            DNA_EXTRACTION_CONDITION = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"
            DNA_EXTRACTION_JOIN = "inner"
        else:
            DNA_EXTRACTION_CONDITION = ""
            DNA_EXTRACTION_JOIN = "left"

        INCLUDING_LID_QUERY = """
        dna_records.id as dna_extraction_id,
        dna_records.dna_qc_status,
        b.number as batch_barcode,
        p.name as plate_name,
        """
        INCLUDING_LID_JOIN = f"""
        {DNA_EXTRACTION_JOIN} join (
            select de.*
                from dna_extractions de
                inner join (
                    select lid, created_at
                    from dna_extractions
                    {DNA_EXTRACTION_CONDITION}
                ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join plate p on smp.plate_id = p.id
        left join batch_mapping bm on p.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
        """

    else:
        INCLUDING_LID_QUERY = ""
        INCLUDING_LID_JOIN = ""

    LAB_SAMPLE_CHECK_QUERY = f"""
        select
            k.barcode,
            ls.lid,
            ls.technology,
            ls.positive_control,
            k.nickname,
            k.sample_meta_id,
            sm.full_name,
            sm.dob::date,
            sm.yob,
            sm.gender,
            k.product_name,
            k.product_code,
            k.product_type,
            k.current_status,
            k.current_status_id,
            k.lab_check_date,
            ls.lab_receipt_date,
            ls.note as lab_note,
            a.id as account_id,
            a.name as account_name,
            s.id as source_id,
            {INCLUDING_LID_QUERY}
            k.sample_collection_date::date,
            k.sample_collection_time,
            k.sample_receipt_date::date,
            k.expected_report_release_date::date,
            k.customer_support_id,
            k.customer_support_name,
            k.free_of_charge,
            k.is_priority,
            k.note,
            k.sample_type,
            k.created_time,
            k.updated_time,
            k.deleted_at
        from
            kit k
            inner join sample_meta sm on k.sample_meta_id = sm.id
            inner join source s on k.source_id = s.id
            inner join sale_account_history sah on s.account_history_id = sah.id
            inner join account a on sah.account_id = a.id
            left join lab_sample ls on k.barcode = ls.barcode
            {INCLUDING_LID_JOIN}
    """
    return LAB_SAMPLE_CHECK_QUERY


def query_lab_samples_count_w_params(is_all=False, DNA_QC_STATUS="ALL"):
    if is_all:
        if DNA_QC_STATUS in ["PASS", "FAIL"]:
            DNA_EXTRACTION_CONDITION = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"
            DNA_EXTRACTION_JOIN = "inner"
        else:
            DNA_EXTRACTION_CONDITION = ""
            DNA_EXTRACTION_JOIN = "left"

        INCLUDING_LID_JOIN = f"""
        {DNA_EXTRACTION_JOIN} join (
            select de.*
                from dna_extractions de
                inner join (
                    select lid, created_at
                    from dna_extractions
                    {DNA_EXTRACTION_CONDITION}
                ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join plate p on smp.plate_id = p.id
        left join batch_mapping bm on p.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
        """

    else:
        INCLUDING_LID_JOIN = ""

    LAB_SAMPLE_CHECK_COUNT_QUERY = f"""
        select
            count(k.barcode)
        from
            kit k
            inner join sample_meta sm on k.sample_meta_id = sm.id
            inner join source s on k.source_id = s.id
            inner join sale_account_history sah on s.account_history_id = sah.id
            inner join account a on sah.account_id = a.id
            left join lab_sample ls on k.barcode = ls.barcode
            {INCLUDING_LID_JOIN}
    """
    return LAB_SAMPLE_CHECK_COUNT_QUERY


async def get_filtered_lab_sample_list(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[int] = None,
    phone_number: Optional[str] = None,
    lid: Optional[str] = None,
    name: Optional[str] = None,
    product_code: Optional[str] = None,
    account_name: Optional[str] = None,
    filter_status_list: Optional[list] = None,
    current_status: Optional[str] = None,
    lab_check_start_date: Optional[str] = None,
    lab_check_end_date: Optional[str] = None,
    lab_receipt_start_date: Optional[str] = None,
    lab_receipt_end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    batch_barcode: Optional[int] = None,
    plate_name: Optional[int] = None,
    dna_qc_status: Optional[str] = "ALL",
    technology: Optional[str] = None,
    positive_control: Optional[bool] = None,
    is_all: Optional[bool] = False,
):
    query_filter = ""
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology = '{technology}' "
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sm.full_name) like '%{name.lower()}%' "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.barcode like '%{barcode}%' "
    if is_all:
        if batch_barcode:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"b.number = '{batch_barcode}' "
        if plate_name:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"p.name = '{plate_name}' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sm.phone_number like '%{phone_number}%' "
    if product_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.product_code like '%{product_code}%' "
    if lid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lid like '%{lid}%' "
    if account_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"a.name like '%{account_name}%' "
    if filter_status_list:
        query_filter += "and " if query_filter != "" else " "
        if len(filter_status_list) == 1:
            query_filter += f"k.current_status = '{filter_status_list[0]}' "
        else:
            query_filter += f"k.current_status in {tuple(filter_status_list)} "
    if current_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.current_status = '{current_status}' "
    if lab_receipt_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lab_receipt_date::date >= '{lab_receipt_start_date}' "
    if lab_receipt_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lab_receipt_date::date <= '{lab_receipt_end_date}' "
    if lab_check_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.lab_check_date >= '{lab_check_start_date}' "
    if lab_check_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.lab_check_date <= '{lab_check_end_date}' "
    if release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"k.expected_report_release_date::date >= '{release_start_date}' "
        )
    if release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.expected_report_release_date::date <= '{release_end_date}' "
    if positive_control:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.positive_control = '{positive_control}' "

    LAB_SAMPLE_CHECK_COUNT_QUERY = query_lab_samples_count_w_params(
        is_all=is_all, DNA_QC_STATUS=dna_qc_status
    )
    raw_count_query = (
        LAB_SAMPLE_CHECK_COUNT_QUERY + "where " + query_filter
        if query_filter != ""
        else LAB_SAMPLE_CHECK_COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    LAB_SAMPLE_CHECK_QUERY = query_lab_samples_w_params(
        is_all=is_all, DNA_QC_STATUS=dna_qc_status
    )
    raw_query = (
        LAB_SAMPLE_CHECK_QUERY + "where " + query_filter
        if query_filter != ""
        else LAB_SAMPLE_CHECK_QUERY
    )
    results = await db.all(db.text(raw_query))
    return results, total


async def get_lab_sample_w_lid(lid: str):
    return await LabSample.get(lid)


async def get_lab_sample(id: str):
    return await LabSample.get(id)


async def get_lab_sample_by_barcode(barcode: str):
    return await LabSample.query.where(LabSample.barcode == barcode).gino.first()


async def count_lab_sample():
    result = await db.all(db.text(COUNT_LAB_SAMPLE_QUERY))
    if len(result) != 1:
        raise ValueError("invalid count results from database")
    return result[0][0]


async def create_lab_sample(data: LabSample):
    return await data.create()


async def update_lab_sample(update_data: LabSampleUpdateReq):
    lab_sample = await get_lab_sample(update_data.lid)
    await lab_sample.update(**update_data.dict()).apply()
    return lab_sample


async def add_positive_control_sampple(id: str):
    update_data = LabSampleUpdateReq(
        lid=id,
        positive_control=True,
    )
    return await update_lab_sample(update_data=update_data)


async def remove_positive_control_sampple(id: str):
    update_data = LabSampleUpdateReq(
        lid=id,
        positive_control=False,
    )
    return await update_lab_sample(update_data=update_data)


async def get_positive_control_samples(positive_control: bool):
    return await LabSample.query.where(
        LabSample.positive_control == positive_control
    ).gino.all()


async def add_samples_into_positive_control_pool(lab_samples):
    lab_sample_objs = []
    for lab_sample_body in lab_samples:
        lab_sample_obj = LabSampleUpdateReq(**lab_sample_body)
        lab_sample_objs.append(lab_sample_obj)
    results = []
    async with db.transaction() as tx:
        for lab_sample_obj in lab_sample_objs:
            res = await update_lab_sample(lab_sample_obj)
            results.append(res.to_dict())

    return results


async def reset_positive_control_pool():
    lab_samples = await get_positive_control_samples(True)
    results = []
    # lids = []

    async with db.transaction() as tx:
        for lab_sample in lab_samples:
            # lids.append()
            res = await remove_positive_control_sampple(lab_sample.lid)
            results.append(res.to_dict())

    return results


async def delete_lab_sample(id: str):
    update_data = LabSampleUpdateReq(
        lid=id,
        deleted_at=get_current_date_time_utc_7(),
    )
    return await update_lab_sample(update_data=update_data)


async def update_lab_sample_with_new_samplecode(barcode: str, new_samplecode: str):
    UPDATE_LAB_SAMPLE = f"""
    UPDATE lab_sample
    SET samplecode='{new_samplecode}'
    WHERE barcode='{barcode}'
    """
    result = await db.all(db.text(UPDATE_LAB_SAMPLE))
    return result


async def update_missing_sample(update_data: MissingSampleUpdateReq):
    UPDATE_MISSING_SAMPLE = f"""
        UPDATE missing_sample
        SET deleted_at='{update_data.deleted_at}'
        WHERE barcode='{update_data.barcode}'
    """
    results = await db.all(db.text(UPDATE_MISSING_SAMPLE))
    return results


async def handle_missing_sample(barcode: str):
    update_data = MissingSampleUpdateReq(
        barcode=barcode,
        deleted_at=get_current_date_time_utc_7(),
    )
    return await update_missing_sample(update_data=update_data)


async def delete_missing_sample(barcode: str):
    return await MissingSample.delete.where(
        MissingSample.barcode == barcode,
    ).gino.status()


MISSING_SAMPLE_QUERY = """
    select 
        ms.barcode, 
        ms.status, 
        ms.note,
        sm.full_name,
        sm.gender,
        sm.dob,
        k.is_priority,
        k.product_name,
        k.product_code,
        k.sample_type,
        a.name as source,
        k.lab_check_date,
        ls.lab_receipt_date
    from missing_sample ms
    left join lab_sample ls on ls.barcode = ms.barcode
    left join (
        select 
            barcode, 
            product_name, 
            product_code, 
            lab_check_date, 
            sample_meta_id, 
            source_id,
            is_priority,
            sample_type
            from kit 
            where current_status in ('FAILED_LAB_CHECK', 'MISSING_SAMPLE')
        ) k on ms.barcode = k.barcode
    left join sample_meta sm on k.sample_meta_id = sm.id
    left join source s on k.source_id = s.id
    left join sale_account_history sah on s.account_history_id = sah.id
    left join account a on sah.account_id = a.id
"""

MISSING_SAMPLE_COUNT_QUERY = """
    select count(ms.barcode)
    from missing_sample ms
    left join (
        select 
            barcode, 
            product_name, 
            product_code, 
            lab_check_date, 
            sample_meta_id, 
            source_id,
            is_priority,
            sample_type
            from kit 
            where current_status in ('FAILED_LAB_CHECK', 'MISSING_SAMPLE')
        ) k on ms.barcode = k.barcode
"""


async def get_missing_samples(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[str] = None,
    status: Optional[str] = None,
    lab_check_start_date: Optional[str] = None,
    lab_check_end_date: Optional[str] = None,
) -> dict:
    filter = "ms.deleted_at is null "
    if barcode:
        filter += "and " if filter != "" else " "
        filter += f"ms.barcode like '%{barcode}%' "
    if status:
        filter += "and " if filter != "" else " "
        filter += f"ms.status like '%{status}%' "
    if lab_check_start_date:
        filter += "and " if filter != "" else " "
        filter += f"k.lab_check_date >= '{lab_check_start_date}' "
    if lab_check_end_date:
        filter += "and " if filter != "" else " "
        filter += f"k.lab_check_date <= '{lab_check_end_date}' "

    raw_count_query = (
        MISSING_SAMPLE_COUNT_QUERY + "where " + filter
        if filter
        else MISSING_SAMPLE_COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    query_filter = ""
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query = (
        MISSING_SAMPLE_QUERY + "where " + filter
        if filter != ""
        else MISSING_SAMPLE_QUERY
    )
    raw_query = raw_query + query_filter if query_filter != "" else raw_query
    results = await db.all(db.text(raw_query))
    return results, total


HANDLED_MISSING_SAMPLE_QUERY = """
    select 
        ms.barcode, 
        ms.status as previous_status,
        k.current_status,
        ms.note,
        sub.identifier_code,
        sub.full_name,
        sub.gender,
        sub.dob,
        k.is_priority,
        k.product_name,
        k.product_code,
        sam.sample_type,
        a.name as source,
        ls.lab_receipt_date as correction_date
    from missing_sample ms
    left join lab_sample ls on ls.barcode = ms.barcode
    left join kit k on ms.barcode = k.barcode
    left join sample sam on sam.samplecode = ms.samplecode
    left join subject sub on sam.subject_id = sub.id
    left join source s on sam.source_id = s.id
    left join sale_account_history sah on s.account_history_id = sah.id
    left join account a on sah.account_id = a.id
"""

HANDLED_MISSING_SAMPLE_COUNT_QUERY = """
    select count(ms.barcode)
    from missing_sample ms
    left join kit k on ms.barcode = k.barcode
"""


async def get_handled_missing_samples(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[str] = None,
    previous_status: Optional[str] = (None),
    current_status: Optional[str] = (None),
    correction_start_date: Optional[str] = None,
    correction_end_date: Optional[str] = None,
) -> dict:
    filter = "ms.deleted_at is not null "
    if barcode:
        filter += "and " if filter != "" else " "
        filter += f"ms.barcode like '%{barcode}%' "
    if previous_status:
        filter += "and " if filter != "" else " "
        filter += f"ms.status like '%{previous_status}%' "
    if current_status:
        filter += "and " if filter != "" else " "
        filter += f"k.current_status like '%{current_status}%' "
    if correction_start_date:
        filter += "and " if filter != "" else " "
        filter += f"k.lab_check_date >= '{correction_start_date}' "
    if correction_end_date:
        filter += "and " if filter != "" else " "
        filter += f"k.lab_check_date <= '{correction_end_date}' "

    raw_count_query = HANDLED_MISSING_SAMPLE_COUNT_QUERY + "where " + filter
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    query_filter = ""
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query = HANDLED_MISSING_SAMPLE_QUERY + "where " + filter
    raw_query = raw_query + query_filter if query_filter != "" else raw_query
    results = await db.all(db.text(raw_query))
    return results, total


async def get_missing_samples_no_paging():
    return await MissingSample.query.gino.all()


async def get_current_lid():
    result = await db.all(db.text(GET_CURRENT_LID_QUERY))
    if len(result) != 1:
        raise ValueError("cannot get current lid from the database")
    return result[0][0]


async def mark_sample_passed(barcode: str, lab_sample: LabSample):
    async with db.transaction() as tx:
        kit = await get_kit(barcode=barcode)
        update_data = {
            "barcode": barcode,
            "status": config["PASSED_LAB_CHECK_KIT_STATUS"],
        }
        current_time = get_current_date_time_utc_7()
        status = await create_status(update_data)
        new_kit_info = kit
        new_kit_info.current_status = status.status
        new_kit_info.current_status_id = status.id
        new_kit_info.updated_time = current_time
        new_kit_info.lab_check_date = current_time
        await update_kit(new_kit_info)
        lab_sample_by_barcode = await get_lab_sample_by_barcode(barcode=barcode)
        if lab_sample_by_barcode is not None:
            raise ValueError("This sample was already passed lab check")
        await create_lab_sample(lab_sample)
        # mark as hanled
        # [GS-6723] [CASE 1] [MISSING SAMPLE --> PASS_LAB_CHECKED]
        # [GS-6723] [CASE 2] [FAILED_LAB_CHECK --> PASS_LAB_CHECKED]
        await handle_missing_sample(barcode=barcode)


async def mark_sample_failed(barcode: str):
    async with db.transaction() as tx:
        kit = await get_kit(barcode=barcode)
        update_data = {
            "barcode": barcode,
            "status": config["FAILED_LAB_CHECK_KIT_STATUS"],
        }
        existed_kit_status = kit.current_status
        if existed_kit_status == config["PASSED_LAB_CHECK_KIT_STATUS"]:
            await LabSample.delete.where(LabSample.barcode == barcode).gino.status()
        current_time = get_current_date_time_utc_7()
        status = await create_status(update_data)
        new_kit_info = kit
        new_kit_info.current_status = status.status
        new_kit_info.current_status_id = status.id
        new_kit_info.updated_time = current_time
        new_kit_info.lab_check_date = current_time
        await update_kit(new_kit_info)
        await MissingSample(
            barcode=barcode,
            status=config["FAILED_LAB_CHECK_KIT_STATUS"],
            note="",
        ).create()


async def mark_missing_sample(
    barcode: str,
    status: str,
    note: Optional[str] = None,
):
    async with db.transaction() as tx:
        kit = await get_kits_with_status(
            barcode=barcode, status=config["LAB_CHECK_KIT_STATUS"]
        )
        if kit is not None and status == "MISSING_INFO":
            raise ValueError(
                f"cannot move exsited kit with status {kit.current_status} to {status}"
            )
        if status == "MISSING_SAMPLE":
            update_data = {"barcode": barcode, "status": status}
            current_time = get_current_date_time_utc_7()
            status_obj = await create_status(update_data)
            new_kit_info = kit
            new_kit_info.current_status = status_obj.status
            new_kit_info.current_status_id = status_obj.id
            new_kit_info.updated_time = current_time
            new_kit_info.lab_check_date = current_time
            await update_kit(new_kit_info)
        await MissingSample(
            barcode=barcode,
            status=status,
            note=note,
        ).create()


def query_lab_samples_count_w_params_v3(is_all=False, DNA_QC_STATUS="ALL"):
    if is_all:
        if DNA_QC_STATUS in ["PASS", "FAIL"]:
            DNA_EXTRACTION_CONDITION = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"
            DNA_EXTRACTION_JOIN = "inner"
        else:
            DNA_EXTRACTION_CONDITION = ""
            DNA_EXTRACTION_JOIN = "left"

        INCLUDING_LID_JOIN = f"""
        {DNA_EXTRACTION_JOIN} join (
            select de.*
                from dna_extractions de
                inner join (
                    select lid, created_at
                    from dna_extractions
                    {DNA_EXTRACTION_CONDITION}
                ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join plate p on smp.plate_id = p.id
        left join batch_mapping bm on p.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
        """

    else:
        INCLUDING_LID_JOIN = ""

    LAB_SAMPLE_CHECK_COUNT_QUERY = f"""
        select
            count(k.barcode)
        from
            lab_sample ls
            inner join sample sam on ls.samplecode = sam.samplecode
            inner join kit k on ls.barcode = k.barcode
            inner join subject sub on sam.subject_id = sub.id
            inner join source s on sam.source_id = s.id
            inner join sale_account_history sah on s.account_history_id = sah.id
            inner join account a on sah.account_id = a.id
            {INCLUDING_LID_JOIN}
    """
    return LAB_SAMPLE_CHECK_COUNT_QUERY


def query_lab_samples_w_params_v3(is_all=False, DNA_QC_STATUS="ALL"):
    if is_all:
        if DNA_QC_STATUS in ["PASS", "FAIL"]:
            DNA_EXTRACTION_CONDITION = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"
            DNA_EXTRACTION_JOIN = "inner"
        else:
            DNA_EXTRACTION_CONDITION = ""
            DNA_EXTRACTION_JOIN = "left"

        INCLUDING_LID_QUERY = """
        dna_records.id as dna_extraction_id,
        dna_records.dna_qc_status,
        b.number as batch_barcode,
        p.name as plate_name,
        """
        INCLUDING_LID_JOIN = f"""
        {DNA_EXTRACTION_JOIN} join (
            select de.*
                from dna_extractions de
                inner join (
                    select lid, created_at
                    from dna_extractions
                    {DNA_EXTRACTION_CONDITION}
                ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join plate p on smp.plate_id = p.id
        left join batch_mapping bm on p.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
        """

    else:
        INCLUDING_LID_QUERY = ""
        INCLUDING_LID_JOIN = ""

    LAB_SAMPLE_CHECK_QUERY = f"""
        select
            k.barcode,
            ls.lid,
            ls.technology,
            ls.positive_control,
            k.nickname,
            sub.user_id,
            sub.identifier_code,
            sub.full_name,
            sub.dob::date,
            sub.yob,
            sub.legal_guardian,
            sub.gender,
            k.product_name,
            k.product_code,
            k.product_type,
            k.current_status,
            k.current_status_id,
            sam.lab_check_date,
            ls.lab_receipt_date,
            ls.note as lab_note,
            a.id as account_id,
            a.name as account_name,
            s.id as source_id,
            {INCLUDING_LID_QUERY}
            sam.sample_collection_date::date,
            sam.sample_collection_time,
            sam.sample_receipt_date::date,
            k.expected_report_release_date::date,
            k.customer_support_id,
            k.customer_support_name,
            k.free_of_charge,
            k.is_priority,
            k.note,
            sam.sample_type,
            k.created_at,
            k.updated_at,
            k.deleted_at
        from
            lab_sample ls
            inner join sample sam on ls.samplecode = sam.samplecode
            inner join kit k on ls.barcode = k.barcode
            inner join subject sub on sam.subject_id = sub.id
            inner join source s on sam.source_id = s.id
            inner join sale_account_history sah on s.account_history_id = sah.id
            inner join account a on sah.account_id = a.id
            {INCLUDING_LID_JOIN}
    """
    return LAB_SAMPLE_CHECK_QUERY


async def get_filtered_lab_sample_list_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[int] = None,
    phone_number: Optional[str] = None,
    lid: Optional[str] = None,
    name: Optional[str] = None,
    product_code: Optional[str] = None,
    account_name: Optional[str] = None,
    filter_status_list: Optional[list] = None,
    current_status: Optional[str] = None,
    lab_check_start_date: Optional[str] = None,
    lab_check_end_date: Optional[str] = None,
    lab_receipt_start_date: Optional[str] = None,
    lab_receipt_end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    batch_barcode: Optional[int] = None,
    plate_name: Optional[int] = None,
    dna_qc_status: Optional[str] = "ALL",
    technology: Optional[str] = None,
    positive_control: Optional[bool] = None,
    is_all: Optional[bool] = False,
):
    query_filter = ""
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology = '{technology}' "
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sub.full_name) like '%{name.lower()}%' "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.barcode like '%{barcode}%' "
    if is_all:
        if batch_barcode:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"b.number = '{batch_barcode}' "
        if plate_name:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"p.name = '{plate_name}' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.phone_number like '%{phone_number}%' "
    if product_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.product_code like '%{product_code}%' "
    if lid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lid like '%{lid}%' "
    if account_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"a.name like '%{account_name}%' "
    if filter_status_list:
        query_filter += "and " if query_filter != "" else " "
        if len(filter_status_list) == 1:
            query_filter += f"k.current_status = '{filter_status_list[0]}' "
        else:
            query_filter += f"k.current_status in {tuple(filter_status_list)} "
    if current_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.current_status = '{current_status}' "
    if lab_receipt_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lab_receipt_date::date >= '{lab_receipt_start_date}' "
    if lab_receipt_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lab_receipt_date::date <= '{lab_receipt_end_date}' "
    if lab_check_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.lab_check_date >= '{lab_check_start_date}' "
    if lab_check_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.lab_check_date <= '{lab_check_end_date}' "
    if release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"k.expected_report_release_date::date >= '{release_start_date}' "
        )
    if release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.expected_report_release_date::date <= '{release_end_date}' "
    if positive_control:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.positive_control = '{positive_control}' "

    LAB_SAMPLE_CHECK_COUNT_QUERY_V3 = query_lab_samples_count_w_params_v3(
        is_all=is_all, DNA_QC_STATUS=dna_qc_status
    )
    raw_count_query = (
        LAB_SAMPLE_CHECK_COUNT_QUERY_V3 + "where " + query_filter
        if query_filter != ""
        else LAB_SAMPLE_CHECK_COUNT_QUERY_V3
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    LAB_SAMPLE_CHECK_QUERY_V3 = query_lab_samples_w_params_v3(
        is_all=is_all, DNA_QC_STATUS=dna_qc_status
    )
    raw_query = (
        LAB_SAMPLE_CHECK_QUERY_V3 + "where " + query_filter
        if query_filter != ""
        else LAB_SAMPLE_CHECK_QUERY_V3
    )
    print("Query: ", raw_query)
    results = await db.all(db.text(raw_query))
    return results, total


async def mark_sample_passed_v3(barcode: str, lab_sample: LabSample):
    async with db.transaction() as tx:
        kit = await get_kit_v3(barcode=barcode)
        sample = await get_sample_by_samplecode_v3(samplecode=kit.samplecode)
        if sample is None:
            raise ValueError("This sample was not found!")
        update_data = {
            "barcode": barcode,
            "samplecode": kit.samplecode,
            "status": config["PASSED_LAB_CHECK_KIT_STATUS"],
        }
        current_time = get_current_date_time_utc_7()
        status = await create_status_v3(update_data)
        new_sample_info = sample
        new_sample_info.updated_at = current_time
        new_sample_info.lab_check_date = current_time
        await update_sample_v3(new_sample_info)

        new_kit_infor = kit
        new_kit_infor.current_status = status.status
        new_kit_infor.current_status_id = status.id
        await update_kit_v3(new_kit_infor)
        lab_sample_by_barcode = await get_lab_sample_by_barcode(barcode=kit.barcode)
        if lab_sample_by_barcode is not None:
            raise ValueError("This sample was already passed lab check")
        await create_lab_sample(lab_sample)
        # mark as hanled
        # [GS-6723] [CASE 1] [MISSING SAMPLE --> PASS_LAB_CHECKED]
        # [GS-6723] [CASE 2] [FAILED_LAB_CHECK --> PASS_LAB_CHECKED]
        await handle_missing_sample(barcode=barcode)


async def mark_sample_failed_v3(barcode: str):
    async with db.transaction() as tx:
        kit = await get_kit_v3(barcode=barcode)
        sample = await get_sample_by_samplecode_v3(samplecode=kit.samplecode)
        if sample is None:
            raise ValueError("This sample was not found!")
        update_data = {
            "barcode": barcode,
            "samplecode": kit.samplecode,
            "status": config["FAILED_LAB_CHECK_KIT_STATUS"],
        }
        existed_kit_status = kit.current_status
        if existed_kit_status == config["PASSED_LAB_CHECK_KIT_STATUS"]:
            await LabSample.delete.where(LabSample.barcode == kit.barcode).gino.status()
        current_time = get_current_date_time_utc_7()
        status = await create_status_v3(update_data)
        new_sample_info = sample
        new_sample_info.updated_at = current_time
        new_sample_info.lab_check_date = current_time
        await update_sample_v3(new_sample_info)

        new_kit_info = kit
        new_kit_info.current_status = status.status
        new_kit_info.current_status_id = status.id
        await update_kit_v3(new_kit_info)

        await MissingSample(
            barcode=barcode,
            samplecode=kit.samplecode,
            status=config["FAILED_LAB_CHECK_KIT_STATUS"],
            note="",
        ).create()


async def mark_missing_sample_v3(
    barcode: str,
    status: str,
    note: Optional[str] = None,
):
    async with db.transaction() as tx:
        kit = await get_kit_v3(barcode=barcode)
        sample = await get_sample_v3(samplecode=kit.samplecode)
        if sample is not None and status == "MISSING_INFO":
            raise ValueError(
                f"cannot move existed sample with status {kit.current_status} to {status}"
            )
        if status == "MISSING_SAMPLE":
            update_data = {
                "barcode": barcode,
                "samplecode": kit.samplecode,
                "status": status,
            }
            current_time = get_current_date_time_utc_7()
            status_obj = await create_status_v3(update_data)
            new_sample_info = sample
            new_sample_info.updated_at = current_time
            new_sample_info.lab_check_date = current_time
            await update_sample_v3(new_sample_info)

            new_kit_info = kit
            new_kit_info.current_status = status_obj.status
            new_kit_info.current_status_id = status_obj.id
            await update_kit_v3(new_kit_info)

        await MissingSample(
            barcode=barcode,
            samplecode=kit.samplecode,
            status=status,
            note=note,
        ).create()


async def get_all_lab_samples_by_samplecode_v3(samplecode: str):
    return await LabSample.query.where(LabSample.samplecode == samplecode).gino.all()


async def get_lab_sample_by_samplecode_v3(samplecode: str):
    return await LabSample.query.where(LabSample.samplecode == samplecode).gino.first()


MISSING_SAMPLE_QUERY_V3 = """
    select
        ms.samplecode,
        k.barcode, 
        ms.status, 
        ms.note,
        sub.identifier_code,
        sub.full_name,
        sub.gender,
        sub.dob,
        sub.legal_guardian,
        k.is_priority,
        k.product_name,
        k.product_code,
        sam.sample_type,
        a.name as source,
        sam.lab_check_date,
        ls.lab_receipt_date
    from missing_sample ms
    left join lab_sample ls on ls.barcode = ms.barcode
    left join sample sam on sam.samplecode = ms.samplecode
    left join kit k on k.barcode = ms.barcode
    left join subject sub on sam.subject_id = sub.id
    left join source s on sam.source_id = s.id
    left join sale_account_history sah on s.account_history_id = sah.id
    left join account a on sah.account_id = a.id
"""

MISSING_SAMPLE_COUNT_QUERY_V3 = """
    select count(ms.barcode)
    from missing_sample ms
    left join lab_sample ls on ls.barcode = ms.barcode
    left join sample sam on sam.samplecode = ms.samplecode
    left join kit k on k.barcode = ms.barcode
"""


async def get_missing_samples_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[str] = None,
    samplecode: Optional[str] = None,
    status: Optional[str] = None,
    lab_check_start_date: Optional[str] = None,
    lab_check_end_date: Optional[str] = None,
) -> dict:
    filter = "ms.deleted_at is null "
    if samplecode:
        filter += "and " if filter != "" else " "
        filter += f"ms.samplecode like '%{samplecode}%' "
    if barcode:
        filter += "and " if filter != "" else " "
        filter += f"ms.barcode like '%{barcode}%' "
    if status:
        filter += "and " if filter != "" else " "
        filter += f"ms.status like '%{status}%' "
    if lab_check_start_date:
        filter += "and " if filter != "" else " "
        filter += f"sam.lab_check_date >= '{lab_check_start_date}' "
    if lab_check_end_date:
        filter += "and " if filter != "" else " "
        filter += f"sam.lab_check_date <= '{lab_check_end_date}' "

    raw_count_query = (
        MISSING_SAMPLE_COUNT_QUERY_V3 + "where " + filter
        if filter
        else MISSING_SAMPLE_COUNT_QUERY_V3
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    query_filter = ""
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query = (
        MISSING_SAMPLE_QUERY_V3 + "where " + filter
        if filter != ""
        else MISSING_SAMPLE_QUERY_V3
    )
    raw_query = raw_query + query_filter if query_filter != "" else raw_query
    results = await db.all(db.text(raw_query))
    return results, total
