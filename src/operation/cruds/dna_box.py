import uuid
from typing import Optional
from ..models.models import db, DnaBox, LabSample, SampleMapping
from ..utils.utils import get_current_date_time_utc_7
from .. import logger
from gino.loader import <PERSON>umn<PERSON>oader
from sqlalchemy import Date, cast
from datetime import datetime, timedelta
from ..schemas.dna_box import *
from .dna_box_mappings import *

async def count_dna_boxes():
    total = db.select([db.func.count(DnaBox.id)])
    return await total.gino.scalar()

async def get_dna_box(id: str) -> DnaBox:
    dna_box = await DnaBox.get(id)
    return dna_box

async def count_total_dna_boxes(
    id: Optional[str] = None
):
    results = db.select([db.func.count()])
    if id:
        results = results.where(DnaBox.id.contains(f"%{id}"))
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0

async def count_matched_dna_boxes(
    id: Optional[str] = None,
    capacity: Optional[int] = 0 #??
):
    results = db.select([db.func.count()])
    if id:
        results = results.where(DnaBox.id.contains(f"%{id}"))
    if capacity >= 0:
        results = results.where(DnaBox.capacity <= capacity)

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0

async def get_all_dna_boxes(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    id: Optional[str] = None,
    capacity: Optional[int] = 0,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = DnaBox.query
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if id:
        results = results.where(DnaBox.id.contains(f"%{id}"))
    if capacity >= 0:
        results = results.where(DnaBox.capacity <= capacity)
    if order_by:
        results = results.order_by(getattr(DnaBox, order_by).desc())

    total = await count_matched_dna_boxes(
        id=id,
        capacity=capacity
    )
    return [r for r in await results.gino.all()], total

async def existed_id(id: str) -> bool:
    total = await db.select([db.func.count()]).where(DnaBox.id == id).gino.scalar()
    return True if total > 0 else False

async def create_dna_box(data: dict):
    if await existed_id(data.get('id')):
        err = f"Error: DnaBox with code {data.get('id')} is already existed"
        return data, err
    current_time = get_current_date_time_utc_7()

    data = {
        'id': data.get('id'),
        'capacity': data.get('capacity'),
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await DnaBox.create(**data)
    return result.to_dict(), None

        


async def update_dna_box(dna_box: DnaBox, data: dict):
    await dna_box.update(**data).apply()
    logger.info(f"dna_box with id: {dna_box.id} updated")
    return None

async def delete_dna_box(dna_box):
    if not dna_box:
        err = f"dna_box with id: {dna_box.id} cannot be found"
        raise ValueError(err)

    await delete_dna_box_mappings_w_id(dna_box.id)

    await DnaBox.delete.where(
        DnaBox.id==dna_box.id,
    ).gino.status()
    logger.info(f"dna_box with id: {dna_box.id} deleted")
