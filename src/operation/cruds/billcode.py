from datetime import datetime
from typing import List, Optional

from sqlalchemy import Date, cast

from .. import logger
from ..config import config
from ..models.models import Billcode, db
from ..utils.utils import convert_rowproxy_to_dict, get_current_date_time_utc_7

BILLCODE_LIST_QUERY = """
    select
        billcode,
        printed,
        status,
        created_at,
        updated_at
    from billcode
"""

BILLCODE_LIST_COUNT_QUERY = """
    select count(billcode)
    from billcode
"""


async def get_billcode(billcode: str) -> dict:
    billcode_obj = await Billcode.get(billcode)
    return billcode_obj


async def update_billcode(billcode: str, status: str):
    billcode_obj = await get_billcode(billcode)
    current_time = get_current_date_time_utc_7()
    update_body = {
        "status": status,
        "updated_at": current_time,
    }

    await billcode_obj.update(**update_body).apply()


async def update_printed(data_arr: list):
    res = []
    current_time = get_current_date_time_utc_7()
    for data in data_arr:
        billcode_obj = await get_billcode(data["billcode"])
        if billcode_obj is None:
            err = "Cannot find billcode_obj billcode in database"
            res.append({"billcode": data["billcode"], "err_msg": err})
            continue
        updated_body = {"printed": data["is_printed"], "updated_at": current_time}
        await billcode_obj.update(**updated_body).apply()
        res.append(billcode_obj.to_dict())
    return res, None


async def count_billcodes():
    total = db.select([db.func.count(Billcode.billcode)])
    return await total.gino.scalar()


async def delete_billcode(billcode: str):
    return await update_billcode(
        billcode=billcode, status=config["DEACTIVATED_BILLCODE_STATE"]
    )


async def delete_billcodes(billcode_list: List[str]):
    try:
        for billcode in billcode_list:
            await delete_billcode(billcode=billcode)
        return billcode_list
    except Exception as e:
        raise ValueError(f"delete billcode {billcode} with error {e}")


async def count_matched_billcodes(
    billcode: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
):
    results = db.select([db.func.count(Billcode.billcode)])
    if billcode:
        results = results.where(Billcode.billcode.like(f"%{billcode}%"))
    if status:
        results = results.where(Billcode.status == status)
    if start_date:
        results = results.where(cast(Billcode.created_at, Date) >= start_date)
    if end_date:
        results = results.where(cast(Billcode.created_at, Date) <= end_date)
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.exception(e)
        return 0


async def get_all_billcodes(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    billcode: Optional[str] = None,
    status: Optional[List[str]] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = None,
):
    query_filter = ""
    if billcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"billcode = '{billcode}' "
    if status:
        status_filter = ""
        s_str = [f"'{s}'" for s in status]
        status_filter = ",".join(s_str)
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"status in ({status_filter}) "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_at::date >= '{start_date}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_at::date <= '{end_date}' "
    raw_count_query = (
        BILLCODE_LIST_COUNT_QUERY + "where " + query_filter
        if query_filter != ""
        else BILLCODE_LIST_COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query = ""
    if (
        query_filter.startswith("order")
        or query_filter.startswith("limit")
        or query_filter.startswith("offset")
    ):
        raw_query = BILLCODE_LIST_QUERY + query_filter
    else:
        raw_query = (
            BILLCODE_LIST_QUERY + "where " + query_filter
            if query_filter != ""
            else BILLCODE_LIST_QUERY
        )
    results = await db.all(db.text(raw_query))
    return results, total


async def get_latest_billcode_for_today():
    """
    Retrieves the latest billcode generated for today's date from the database.
    """
    today_prefix = datetime.now().strftime("%d%m%y")  # Format date as DDMMYY
    results = await db.all(
        db.text(f"""
        SELECT billcode
        FROM billcode
        WHERE billcode LIKE 'GS-{today_prefix}-%'
        ORDER BY created_at DESC
        LIMIT 1
    """)
    )
    if results:
        billcode_obj = convert_rowproxy_to_dict(results[0])
        return billcode_obj["billcode"]
    else:
        return None


async def duplicated_billcode(billcode: str) -> bool:
    billcode_obj = await get_billcode(billcode)
    if billcode_obj and billcode_obj.status == config["USED_BILLCODE_STATE"]:
        return True
    return False


async def available_billcode(billcode: str) -> bool:
    billcode_obj = await get_billcode(billcode)
    if billcode_obj and billcode_obj.status in (
        config["DEFAULT_BILLCODE_STATE"],
        config["SHIPPED_BILLCODE_STATE"],
    ):
        return True
    return False


async def deactivated_billcode(billcode: str) -> bool:
    billcode_obj = await get_billcode(billcode)
    if billcode_obj and billcode_obj.status == config["DEACTIVATED_BILLCODE_STATE"]:
        return True
    return False


async def existed_billcode(billcode: str) -> bool:
    billcode_obj = await get_billcode(billcode)
    if billcode_obj:
        return True
    return False


async def save_billcode(data: dict):
    await Billcode.create(**data)


async def generate_billcode_string():
    prefix = "GS"
    today_date = datetime.now().strftime("%d%m%y")
    latest_billcode = await get_latest_billcode_for_today()

    # Determine the next order number
    if latest_billcode:
        latest_order_number = int(latest_billcode.split("-")[-1])
        new_order_number = latest_order_number + 1
    else:
        new_order_number = 1

    # Format new order number as a 3-digit string with leading zeros
    new_order_number_str = f"{new_order_number:03}"

    # Create the new billcode
    new_billcode = f"{prefix}-{today_date}-{new_order_number_str}"

    return new_billcode


async def generate_billcode(billcode=None):
    if not billcode:
        billcode = await generate_billcode_string()
    current_time = get_current_date_time_utc_7()
    data = {
        "billcode": billcode,
        "printed": False,
        "status": config["DEFAULT_BILLCODE_STATE"],
        "created_at": current_time,
        "updated_at": current_time,
    }
    return data
