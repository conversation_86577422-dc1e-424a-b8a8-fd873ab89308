import uuid
from typing import Optional
from ..models.models import TrackingDelivery, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# TrackingDelivery - model 
# DELIVERY - QUERY maVanChuyen
# _tracking_delivery - function maVanChuyen
# tracking_delivery - single
# deliveries - plural
# maVanChuyen -- search key

DELIVERY_LIST_QUERY_V3 = """
    select
        td.id,
        td.tracking_id,
        td."maVan<PERSON>huyen",
        td."nhietDoChuyenGiao",
        td."tinhTrangNiemPhong",
        td."ngayGioChuyenGiao",
        td."diaDiemChuyenGiao",
        td."donViBanGiao_id",
        dvbg."tenDonVi" as "tenDonViBanGiao",
        td."donViVanChuyen_id",
        dvvc."tenDonVi" as "tenDonViVanChuyen",
        td."donViNhanMau_id",
        dvnm."tenDonVi" as "tenDonViNhanMau",
        td."nhanVienBanGiao_id",
        nvbg."hoTenNhanVien" as "hoTenNhanVienBanGiao",
        nvbg."hocVi" as "hocViNhanVienBanGiaoBanGiao",
        nvbg."soDinhDanh" as "soDinhDanhNhanVienBanGiao",
        nvbg."chucVu" as "chucVuNhanVienBanGiao",
        td."nhanVienNhanMau_id",
        nvnm."hoTenNhanVien" as "hoTenNhanVienNhanMau",
        nvnm."hocVi" as "hocViNhanVienNhanMau",
        nvnm."soDinhDanh" as "soDinhDanhNhanVienNhanMau",
        nvnm."chucVu" as "chucVuNhanVienNhanMau",
        td.khac,
        td.created_at,
        td.updated_at,
        td.deleted_at
    from
        tracking_delivery td
        INNER JOIN tracking_unit dvbg ON td."donViBanGiao_id" = dvbg.id
        LEFT JOIN tracking_unit dvvc ON td."donViVanChuyen_id" = dvvc.id
        INNER JOIN tracking_unit dvnm ON td."donViNhanMau_id" = dvnm.id
        INNER JOIN tracking_employee nvbg ON td."nhanVienBanGiao_id" = nvbg.id
        INNER JOIN tracking_employee nvnm ON td."nhanVienNhanMau_id" = nvnm.id
"""

DELIVERY_LIST_COUNT_QUERY_V3 = """
    select
        count(td.id)
    from
        tracking_delivery td
"""

async def get_tracking_delivery_by_id(id: uuid.uuid4):
    tracking_delivery = await TrackingDelivery.get(id)
    return tracking_delivery

async def get_tracking_delivery_by_tracking_id(tracking_id: str):
    tracking_delivery = await TrackingDelivery.query.where(TrackingDelivery.tracking_id == tracking_id).gino.first()
    return tracking_delivery

async def get_all_deliveries(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="td.created_at",
    maVanChuyen: Optional[str] = None,
    nhietDoChuyenGiao: Optional[str] = None,
    tinhTrangNiemPhong: Optional[bool] = None,
    ngayGioChuyenGiao_start_date: Optional[str] = None,
    ngayGioChuyenGiao_end_date: Optional[str] = None,
    diaDiemChuyenGiao: Optional[int] = None,
    tenDonViBanGiao: Optional[str] = None,
    tenDonViVanChuyen: Optional[str] = None,
    tenDonViNhanMau: Optional[str] = None,
    hoTenNhanVienBanGiao: Optional[str] = None,
    soDinhDanhNhanVienBanGiao: Optional[str] = None,
    hoTenNhanVienNhanMau: Optional[str] = None,
    soDinhDanhNhanVienNhanMau: Optional[str] = None,
    tracking_delivery_id: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if maVanChuyen:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.'maVanChuyen' = '{str(maVanChuyen)}' """
    if nhietDoChuyenGiao:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.'nhietDoChuyenGiao' = '{str(nhietDoChuyenGiao)}' """
    if tinhTrangNiemPhong:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.'tinhTrangNiemPhong' is '{tinhTrangNiemPhong}' """
    if ngayGioChuyenGiao_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.'ngayGioChuyenGiao'::date >= '{ngayGioChuyenGiao_start_date}' """
    if ngayGioChuyenGiao_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.'ngayGioChuyenGiao'::date <= '{ngayGioChuyenGiao_end_date}' """
    if diaDiemChuyenGiao:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.'diaDiemChuyenGiao' = '{diaDiemChuyenGiao}' """
    if tenDonViBanGiao:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""dvbg.'tenDonVi' = '{str(tenDonViBanGiao)}' """
    if tenDonViVanChuyen:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""dvvc.'tenDonVi' = '{str(tenDonViVanChuyen)}' """
    if tenDonViNhanMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""dvnm.'tenDonVi' = '{str(tenDonViNhanMau)}' """
    if hoTenNhanVienBanGiao:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvbg.'hoTenNhanVien' = '{str(hoTenNhanVienBanGiao)}' """
    if soDinhDanhNhanVienBanGiao:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvbg.'soDinhDanh' = '{str(soDinhDanhNhanVienBanGiao)}' """
    if hoTenNhanVienNhanMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvnm.'hoTenNhanVien' = '{str(hoTenNhanVienNhanMau)}' """
    if soDinhDanhNhanVienNhanMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvnm.'soDinhDanh' = '{str(soDinhDanhNhanVienNhanMau)}' """
    if tracking_delivery_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.id = '{tracking_delivery_id}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""td.deleted_at is null """
    
    raw_count_query = DELIVERY_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else DELIVERY_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = DELIVERY_LIST_QUERY_V3 + query_filter
    else:
        raw_query = DELIVERY_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else DELIVERY_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass


async def is_tracking_delivery_existed_w_tracking_id(tracking_id: str):
    total = await db.select([db.func.count()]).where(TrackingDelivery.tracking_id == tracking_id).where(TrackingDelivery.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_tracking_delivery(data: dict):
    """
    "tracking_id": str
    "maVanChuyen": str
    "nhietDoChuyenGiao": str
    "ngayGioChuyenGiao": str
    "donViBanGiao_id": int
    "diaDiemChuyenGiao": int,
    "donViVanChuyen_id": int
    "nhanVienBanGiao_id": str
    "tinhTrangNiemPhong": boolean,
    "donViNhanMau_id": int
    "nhanVienNhanMau_id": str
    "khac": str
    """
    
    if await is_tracking_delivery_existed_w_tracking_id(data['tracking_id']):
        err = f"Error: TrackingDelivery with tracking_id {data['tracking_id']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await TrackingDelivery.create(**data)
    return result.to_dict()

async def update_tracking_delivery(tracking_delivery: TrackingDelivery, data: dict):
    if tracking_delivery.deleted_at:
        err = f"TrackingDelivery with maVanChuyen: {tracking_delivery.maVanChuyen} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_delivery.update(**data).apply()
    logger.info(f"TrackingDelivery with maVanChuyen: {tracking_delivery.maVanChuyen} updated")
    return None


async def delete_tracking_delivery(tracking_delivery: TrackingDelivery):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_delivery:
        err = f"TrackingDelivery with maVanChuyen: {tracking_delivery.maVanChuyen} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_delivery.deleted_at:
        err = f"TrackingDelivery with: {tracking_delivery.maVanChuyen} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_delivery.update(**data).apply()
    logger.info(f"TrackingDelivery with maVanChuyen: {tracking_delivery.maVanChuyen} deleted")
