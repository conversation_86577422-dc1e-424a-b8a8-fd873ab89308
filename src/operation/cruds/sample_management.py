import uuid

from ..models.models import db, Chip, SampleMapping, BatchMapping, Batch
from typing import Optional
from ..schemas.sample_management import AddChipToBatchReq, CreateBatchReq, BatchUpdateReq
from ..config import config
from ..utils.utils import (
    get_current_date_time,
    failure_response,
    calculate_estimated_gender,
    convert_rowproxy_to_dict,
    filter_personal_infor_from_sample_list
)


def query_samples_w_params(WETLAB_DAYS,DNA_QC_STATUS="PASS"):

    if DNA_QC_STATUS == "ALL":
        DNA_EXTRACTION_CONDITION = ""
    else:
        DNA_EXTRACTION_CONDITION = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"
    
        

    SAMPLE_MAPPING_QUERY = f"""
        select
            k.barcode,
            k.product_name,
            k.product_code,
            k.product_type,
            k.current_status as kit_status,
            k.current_status_id,
            k.lab_check_date,
            k.expected_report_release_date,
            k.sample_type,
            k.sample_meta_id,
            k.is_priority,
            k.nickname,
            ls.lid,
            dna_records.id as dna_extraction_id,
            dna_records.qubit,
            dna_records.dna_extraction_date,
            dna_records.nano_drop,
            dna_records.a260_a280,
            dna_records.agarose_gel,
            dna_records.dna_qc_status,
            dna_records.note as dna_extraction_note,
            db.id as dna_box,
            dbm.position as dna_box_position,
            c.chip_id,
            c.type as chip_type,
            ls.technology,
            ls.note as lab_sample_note,
            ls.lab_receipt_date,
            smp.well_position,
            smp.position as physical_position,
            smp.created_at as plate_added_date,
            smp.call_rate as call_rate,
            smp.qc_status as pipeline_qc_status,
            sm.gender,
            smp.gender_pass as estimated_gender,
            sm.dob,
            bm.wetlab_date,
            bm.wetlab_date + INTERVAL '{WETLAB_DAYS} days' as wetlab_date_end,
            bm.drylab_date,
            bm.raw_data_uploaded_date,
            bm.raw_report_to_reviewers,
            p.name as plate_name,
            p.status as plate_status,
            b.id as batch_id,
            b.number as batch_barcode,
            b.name as batch_name
        from 
            kit k
            inner join sample_meta sm on k.sample_meta_id = sm.id
            inner join lab_sample ls on k.barcode = ls.barcode
            inner join (
                select de.*
                    from dna_extractions de
                    inner join (
                        select lid, created_at
                        from dna_extractions
                        {DNA_EXTRACTION_CONDITION}
                    ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
                ) dna_records on dna_records.lid = ls.lid
            left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
            left join chip c on c.chip_id = smp.chip_id
            left join plate p on smp.plate_id = p.id
            left join batch_mapping bm on p.id = bm.plate_id
            left join batch b on b.id = bm.batch_id
            left join dna_box_mappings dbm on dbm.lid = ls.lid
            left join dna_box db on db.id = dbm.dna_box_id
    """
    return SAMPLE_MAPPING_QUERY

def query_sample_mapping_count_w_params(DNA_QC_STATUS="PASS"):

    if DNA_QC_STATUS == "ALL":
        DNA_EXTRACTION_CONDITION = ""
    else:
        DNA_EXTRACTION_CONDITION = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"

    SAMPLE_MAPPING_COUNT_QUERY = f"""
        select count(k.barcode)
        from 
            kit k
            inner join sample_meta sm on k.sample_meta_id = sm.id
            inner join lab_sample ls on k.barcode = ls.barcode
            inner join (
                select de.*
                    from dna_extractions de
                    inner join (
                        select lid, created_at
                        from dna_extractions
                        {DNA_EXTRACTION_CONDITION}
                    ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
                ) dna_records on dna_records.lid = ls.lid
            left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
            left join chip c on c.chip_id = smp.chip_id
            left join plate p on smp.plate_id = p.id
            left join batch_mapping bm on p.id = bm.plate_id
            left join batch b on b.id = bm.batch_id
            left join dna_box_mappings dbm on dbm.lid = ls.lid
            left join dna_box db on db.id = dbm.dna_box_id
    """
    return SAMPLE_MAPPING_COUNT_QUERY

async def get_filtered_sample_mappings(
    plate_status_filter: Optional[list]=None,
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    order_option: Optional[str]='desc',
    barcode: Optional[int]=None,
    lid: Optional[int]=None,
    dna_extraction_id: Optional[int]=None,
    chip_id: Optional[str]=None,
    plate_name: Optional[str]=None,
    plate_name_null_only: Optional[bool]=False,
    dna_box: Optional[str]=None,
    technology: Optional[str]=None,
    note: Optional[str]=None,
    dna_extraction_date_start: Optional[str] = None,
    dna_extraction_date_end: Optional[str] = None,
	agarose_gel: Optional[str] = None,
	dna_qc_status: Optional[str] = "PASS",
    pipeline_qc_status: Optional[str] = None,
    wetlab_date_start: Optional[str]=None,
    wetlab_date_end: Optional[str]=None,
    drylab_date_start: Optional[str]=None,
    drylab_date_end: Optional[str]=None,
    plate_added_date_start: Optional[str]=None,
    plate_added_date_end: Optional[str]=None,
    batch_id: Optional[str]=None,
    batch_barcode: Optional[str]=None,
    is_added_to_batch: Optional[bool]=False
):
    WETLAB_DAYS = 0
    query_filter = "c.deleted_at is null "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.barcode like '%{barcode}%' "
    if lid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lid like '%{lid}%' "
    if dna_extraction_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.id = '{dna_extraction_id}' "
    if agarose_gel:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.agarose_gel like '%{agarose_gel}%' "
    if chip_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.chip_id like '%{chip_id}%' "
    if plate_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"p.name like '%{plate_name}%' "
    elif plate_name_null_only:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"p.name is null "
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology like '%{technology}%' "
        if technology.upper() in config['WETLAB_SUPPORT_TECHNOLOGY']:
            WETLAB_DAYS = config['WETLAB_DAYS'][technology.upper()]

    if note:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.note like '%{note}%' "
    if plate_status_filter:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"(p.status in {tuple(plate_status_filter)} or p.status is null) "
    if dna_extraction_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.dna_extraction_date::date >= '{dna_extraction_date_start}' "
    if dna_extraction_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.dna_extraction_date::date <= '{dna_extraction_date_end}' "
    if wetlab_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.wetlab_date::date >= '{wetlab_date_start}' "
    if wetlab_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.wetlab_date::date <= '{wetlab_date_end}' "
    if drylab_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.drylab_date::date >= '{drylab_date_start}' "
    if drylab_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.drylab_date::date <= '{drylab_date_end}' "
    if plate_added_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"smp.created_at::date >= '{plate_added_date_start}' "
    if plate_added_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"smp.created_at::date <= '{plate_added_date_end}' "
    if batch_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.id = '{batch_id}' " 
    if batch_barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.number = '{batch_barcode}' " 
    if is_added_to_batch:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.id is not null " 
    if dna_box:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"db.id = '{dna_box}' " 
    # if pipeline_qc_status:
        # query_filter += "and " if query_filter != "" else " "
        # query_filter += f"smp.qc_status = '{pipeline_qc_status}' " 
    if pipeline_qc_status:
        if pipeline_qc_status == 'BOTH':
            pipeline_qc_status_filter=['PASS','FAIL']
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"(smp.qc_status in {tuple(pipeline_qc_status_filter)}) "
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"smp.qc_status = '{pipeline_qc_status}' "
    
    SAMPLE_MAPPING_COUNT_QUERY = query_sample_mapping_count_w_params(DNA_QC_STATUS=dna_qc_status)
    raw_count_query = SAMPLE_MAPPING_COUNT_QUERY + "where " + query_filter if query_filter != "" else SAMPLE_MAPPING_COUNT_QUERY
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by in ['well_position_row','well_position']:
        order_by = 'well_position'

    elif order_by == 'well_position_column':
        order_by = "CONCAT(LPAD(RIGHT(smp.well_position,-1),2,'0'),LEFT(smp.well_position,1))"
        # 'CONCAT(LPAD(RIGHT(smp.well_position,2),2),LEFT(smp.well_position,1))'
        # ,LEFT(smp.well_position,1)
        # CONCAT(LPAD(RIGHT(smp.well_position,2),2),LEFT(smp.well_position,1))

    if order_by:
        query_filter += f"order by {order_by} {order_option} "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    SAMPLE_MAPPING_QUERY = query_samples_w_params(WETLAB_DAYS=WETLAB_DAYS,DNA_QC_STATUS=dna_qc_status)
    raw_query = SAMPLE_MAPPING_QUERY + "where " + query_filter if query_filter != "" else SAMPLE_MAPPING_QUERY
    results = await db.all(db.text(raw_query))

    results = [ convert_rowproxy_to_dict(row) for row in results ]
    results = [ calculate_estimated_gender(sample) for sample in results ] 
    return results, total

# async def create_chip(chip: Chip):
#     await chip.create()
    
# async def get_all_chips():
#     await Chip.query.gino.all()
    
# async def get_batch_chip_by_chip_id(chip_id: str):
#     return await BatchMapping.query.where(BatchMapping.chip_id==chip_id).gino.all()
    
# async def delete_chip(chip_id: str):
#     async with db.transaction() as tx:
#         batch_chips = await get_batch_chip_by_chip_id(chip_id=chip_id)
#         # chip added to a batch cannot be deleted [TODO] check status or not?
#         if len(batch_chips) != 0:
#             batch_id = batch_chips.to_dict()['batch_id']
#             raise ValueError(f"This chip was already added to a batch with id: {batch_id}")
#         # delete sample mapping 
#         await SampleMapping.delete.where(SampleMapping.chip_id==chip_id).gino.status()
#         # delete chip
#         await Chip.delete.where(Chip.id==chip_id).gino.status()
        


async def bulk_add_chip_to_batch(add_chip_to_batch_req: AddChipToBatchReq):
    async with db.transaction() as tx:
        #delete batch_mapping records
        await BatchMapping.delete.where(
            BatchMapping.batch_id==add_chip_to_batch_req.batch_id).gino.status()
        for chip_id in add_chip_to_batch_req.chip_id_list:
            batch_mapping = BatchMapping(
                id=uuid.uuid4(),
                chip_id=chip_id,
                batch_id=add_chip_to_batch_req.batch_id,
                created_at=get_current_date_time(),
                updated_at=get_current_date_time(),
            )
            await batch_mapping.create()
        return add_chip_to_batch_req
    

async def create_sample_mapping(data: SampleMapping):
    # not allow to add sample to chip that was already proccessed
    # batch_mapping = await BatchMapping.query.where(BatchMapping.chip_id==data.chip_id).gino.first()
    # batch = await Batch.query.where(Batch.id==batch_mapping.batch_id).gino.first()
    # if batch is not None and batch.status != e
    
    await data.create()
    
async def delete_sample_mapping(lid: str):
    return await SampleMapping.delete.where(SampleMapping.lab_sample_id==lid)

SAMPLE_ON_CHIP_QUERY = """
    select
        c.id,
        c.chip_id,
        c.type,
        c.technology,
        p.name as plate_name,
        p.status as plate_status,
        bm.wetlab_date,
        bm.drylab_date,
        smp.well_position,
        smp.position,
        smp.lid,
        smp.created_at as plate_added_date,
        ls.barcode,
        ls.lab_receipt_date
    from chip c
    inner join sample_mapping smp on c.chip_id = smp.chip_id
    inner join lab_sample ls on smp.lid = ls.lid
    inner join plate p on smp.plate_id = p.id
    inner join batch_mapping bm on p.id = bm.plate_id
"""


async def get_sample_on_chip(
    chip_id: str
):
    query_filter = "c.deleted_at is null "
    query_filter += f"and c.chip_id like '%{chip_id}%' "
    raw_query = SAMPLE_ON_CHIP_QUERY + "where " + query_filter if query_filter != "" else SAMPLE_ON_CHIP_QUERY
    results = await db.all(db.text(raw_query))
    return results

async def get_filtered_batch_stats(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]='b.number',
    order_option: Optional[str]='desc',
    batch_number: Optional[str]=None,
    product_code: Optional[str]=None,
    wetlab_date_start: Optional[str]=None,
    wetlab_date_end: Optional[str]=None
):
    query_filter = "(de.dna_qc_status = 'PASS') AND (c.deleted_at IS NULL) AND (bm.wetlab_date IS NOT NULL) "
    query_filter = "(de.dna_qc_status = 'PASS') AND (bm.wetlab_date IS NOT NULL) AND (b.type = 'PCR') "

    if wetlab_date_start:
        query_filter += "AND " if query_filter != "" else " "
        query_filter += f"(bm.wetlab_date::date >= '{wetlab_date_start}') "
    if wetlab_date_end:
        query_filter += "AND " if query_filter != "" else " "
        query_filter += f"(bm.wetlab_date::date <= '{wetlab_date_end}') "
    if batch_number:
        query_filter += "AND " if query_filter != "" else " "
        query_filter += f"(p.name = '{batch_number}') "
    if product_code:
        query_filter += "AND " if query_filter != "" else " "
        query_filter += f"(k.product_code = '{product_code}') "

    select_query_exp="SELECT DISTINCT b.number, bm.wetlab_date, k.product_code "
    select_count_query_exp="SELECT COUNT(DISTINCT b.number) "

    from_query_exp = f"""
        FROM batch_mapping bm 
        INNER JOIN batch b ON bm.batch_id=b.id 
        INNER JOIN plate p ON p.id=bm.plate_id 
        INNER JOIN sample_mapping sm ON sm.plate_id=p.id
        INNER JOIN dna_extractions de ON de.id=sm.dna_extraction_id
        INNER JOIN lab_sample ls ON ls.lid=de.lid
        INNER JOIN kit k ON k.barcode=ls.barcode
        WHERE {query_filter}
    """

    query_exp_count=select_count_query_exp+from_query_exp+";"
    query_exp=select_query_exp+from_query_exp

    query_exp+=f'ORDER BY {order_by} {order_option.upper()} '
    
    if size is not None:
        query_exp+=f"LIMIT {size} OFFSET {offset}"

    query_exp+=";"
    
    count = await db.all(db.text(query_exp_count))
    total=count[0][0]
    results = await db.all(db.text(query_exp))
    return results,total

def query_samples_w_params_v3(WETLAB_DAYS,DNA_QC_STATUS="PASS"):

    if DNA_QC_STATUS == "ALL":
        DNA_EXTRACTION_CONDITION_V3 = ""
    else:
        DNA_EXTRACTION_CONDITION_V3 = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"
    
        

    SAMPLE_MAPPING_QUERY_V3 = f"""
        select
            k.barcode,
            k.product_name,
            k.product_code,
            k.product_type,
            k.current_status as kit_status,
            k.current_status_id,
            sam.lab_check_date,
            k.expected_report_release_date,
            sam.sample_type,
            sam.subject_id,
            k.is_priority,
            k.nickname,
            ls.lid,
            dna_records.id as dna_extraction_id,
            dna_records.qubit,
            dna_records.dna_extraction_date,
            dna_records.nano_drop,
            dna_records.a260_a280,
            dna_records.agarose_gel,
            dna_records.dna_qc_status,
            dna_records.note as dna_extraction_note,
            db.id as dna_box,
            dbm.position as dna_box_position,
            c.chip_id,
            c.type as chip_type,
            ls.technology,
            ls.note as lab_sample_note,
            ls.lab_receipt_date,
            smp.well_position,
            smp.position as physical_position,
            smp.created_at as plate_added_date,
            smp.call_rate as call_rate,
            smp.qc_status as pipeline_qc_status,
            sub.gender,
            smp.gender_pass as estimated_gender,
            sub.dob,
            sub.legal_guardian,
            bm.wetlab_date,
            bm.wetlab_date + INTERVAL '{WETLAB_DAYS} days' as wetlab_date_end,
            bm.drylab_date,
            bm.raw_data_uploaded_date,
            bm.raw_report_to_reviewers,
            p.name as plate_name,
            p.status as plate_status,
            b.id as batch_id,
            b.number as batch_barcode,
            b.name as batch_name
        from
            lab_sample ls
            inner join sample sam on ls.samplecode = sam.samplecode
            inner join kit k on ls.barcode = k.barcode
            inner join subject sub ON sam.subject_id = sub.id
            inner join (
                select de.*
                    from dna_extractions de
                    inner join (
                        select lid, created_at
                        from dna_extractions
                        {DNA_EXTRACTION_CONDITION_V3}
                    ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
                ) dna_records on dna_records.lid = ls.lid
            left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
            left join chip c on c.chip_id = smp.chip_id
            left join plate p on smp.plate_id = p.id
            left join batch_mapping bm on p.id = bm.plate_id
            left join batch b on b.id = bm.batch_id
            left join dna_box_mappings dbm on dbm.lid = ls.lid
            left join dna_box db on db.id = dbm.dna_box_id
    """
    return SAMPLE_MAPPING_QUERY_V3

def query_sample_mapping_count_w_params_v3(DNA_QC_STATUS="PASS"):

    if DNA_QC_STATUS == "ALL":
        DNA_EXTRACTION_CONDITION_V3 = ""
    else:
        DNA_EXTRACTION_CONDITION_V3 = f"where dna_qc_status = '{DNA_QC_STATUS}' --select passed dna qc samples"

    SAMPLE_MAPPING_COUNT_QUERY_V3 = f"""
        select count(k.barcode)
        from
            lab_sample ls
            inner join sample sam on ls.samplecode = sam.samplecode
            inner join kit k on ls.barcode = k.barcode
            inner join subject sub ON sam.subject_id = sub.id
            inner join (
                select de.*
                    from dna_extractions de
                    inner join (
                        select lid, created_at
                        from dna_extractions
                        {DNA_EXTRACTION_CONDITION_V3}
                    ) de2 on de.lid = de2.lid and de.created_at = de2.created_at
                ) dna_records on dna_records.lid = ls.lid
            left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
            left join chip c on c.chip_id = smp.chip_id
            left join plate p on smp.plate_id = p.id
            left join batch_mapping bm on p.id = bm.plate_id
            left join batch b on b.id = bm.batch_id
            left join dna_box_mappings dbm on dbm.lid = ls.lid
            left join dna_box db on db.id = dbm.dna_box_id
    """
    return SAMPLE_MAPPING_COUNT_QUERY_V3

async def get_filtered_sample_mappings_v3(
    plate_status_filter: Optional[list]=None,
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    order_option: Optional[str]='desc',
    barcode: Optional[int]=None,
    lid: Optional[int]=None,
    dna_extraction_id: Optional[int]=None,
    chip_id: Optional[str]=None,
    plate_name: Optional[str]=None,
    plate_name_null_only: Optional[bool]=False,
    dna_box: Optional[str]=None,
    technology: Optional[str]=None,
    note: Optional[str]=None,
    dna_extraction_date_start: Optional[str] = None,
    dna_extraction_date_end: Optional[str] = None,
	agarose_gel: Optional[str] = None,
	dna_qc_status: Optional[str] = "PASS",
    pipeline_qc_status: Optional[str] = None,
    wetlab_date_start: Optional[str]=None,
    wetlab_date_end: Optional[str]=None,
    drylab_date_start: Optional[str]=None,
    drylab_date_end: Optional[str]=None,
    plate_added_date_start: Optional[str]=None,
    plate_added_date_end: Optional[str]=None,
    batch_id: Optional[str]=None,
    batch_barcode: Optional[str]=None,
    is_added_to_batch: Optional[bool]=False,
    is_encoded: Optional[bool]=False
):
    WETLAB_DAYS = 0
    query_filter = "c.deleted_at is null "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.barcode like '%{barcode}%' "
    if lid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.lid like '%{lid}%' "
    if dna_extraction_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.id = '{dna_extraction_id}' "
    if agarose_gel:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.agarose_gel like '%{agarose_gel}%' "
    if chip_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.chip_id like '%{chip_id}%' "
    if plate_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"p.name like '%{plate_name}%' "
    elif plate_name_null_only:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"p.name is null "
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology like '%{technology}%' "
        if technology.upper() in config['WETLAB_SUPPORT_TECHNOLOGY']:
            WETLAB_DAYS = config['WETLAB_DAYS'][technology.upper()]

    if note:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.note like '%{note}%' "
    if plate_status_filter:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"(p.status in {tuple(plate_status_filter)} or p.status is null) "
    if dna_extraction_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.dna_extraction_date::date >= '{dna_extraction_date_start}' "
    if dna_extraction_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dna_records.dna_extraction_date::date <= '{dna_extraction_date_end}' "
    if wetlab_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.wetlab_date::date >= '{wetlab_date_start}' "
    if wetlab_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.wetlab_date::date <= '{wetlab_date_end}' "
    if drylab_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.drylab_date::date >= '{drylab_date_start}' "
    if drylab_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"bm.drylab_date::date <= '{drylab_date_end}' "
    if plate_added_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"smp.created_at::date >= '{plate_added_date_start}' "
    if plate_added_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"smp.created_at::date <= '{plate_added_date_end}' "
    if batch_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.id = '{batch_id}' " 
    if batch_barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.number = '{batch_barcode}' " 
    if is_added_to_batch:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.id is not null " 
    if dna_box:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"db.id = '{dna_box}' " 
    # if pipeline_qc_status:
        # query_filter += "and " if query_filter != "" else " "
        # query_filter += f"smp.qc_status = '{pipeline_qc_status}' " 
    if pipeline_qc_status:
        if pipeline_qc_status == 'BOTH':
            pipeline_qc_status_filter=['PASS','FAIL']
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"(smp.qc_status in {tuple(pipeline_qc_status_filter)}) "
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"smp.qc_status = '{pipeline_qc_status}' "
    
    SAMPLE_MAPPING_COUNT_QUERY = query_sample_mapping_count_w_params_v3(DNA_QC_STATUS=dna_qc_status)
    raw_count_query = SAMPLE_MAPPING_COUNT_QUERY + "where " + query_filter if query_filter != "" else SAMPLE_MAPPING_COUNT_QUERY
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by in ['well_position_row','well_position']:
        order_by = 'well_position'

    elif order_by == 'well_position_column':
        order_by = "CONCAT(LPAD(RIGHT(smp.well_position,-1),2,'0'),LEFT(smp.well_position,1))"
        # 'CONCAT(LPAD(RIGHT(smp.well_position,2),2),LEFT(smp.well_position,1))'
        # ,LEFT(smp.well_position,1)
        # CONCAT(LPAD(RIGHT(smp.well_position,2),2),LEFT(smp.well_position,1))
    # Only work when lid is number type. If change lid to another type, it will not work.
    elif order_by == 'lid':
        order_by = "CAST(ls.lid AS INTEGER)"

    if order_by:
        query_filter += f"order by {order_by} {order_option} "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    SAMPLE_MAPPING_QUERY = query_samples_w_params_v3(WETLAB_DAYS=WETLAB_DAYS,DNA_QC_STATUS=dna_qc_status)
    raw_query = SAMPLE_MAPPING_QUERY + "where " + query_filter if query_filter != "" else SAMPLE_MAPPING_QUERY
    results = await db.all(db.text(raw_query))

    results = [ convert_rowproxy_to_dict(row) for row in results ]
    results = [ calculate_estimated_gender(sample) for sample in results ] 
    if is_encoded:
        results = filter_personal_infor_from_sample_list(results)
    return results, total

