import uuid
from typing import Optional
from ..models.models import db
from datetime import datetime
from .codes import update_code, get_code
from ..models.partner_order import *
from ..schemas.partner_booking import PartnerBookingCreationReq, PartnerBookingUpdateReq, ResultUpdateReq
from ..utils.utils import (
    DEFAULT_DATE_STR, DEFAULT_DATE_NO_TIME_ZONE,
    get_product_by_product_code
)
from .. import logger

GET_BOOKING_QUERY = """
    select 
        pb.id,
        pb.customer_support_id,
        pb.partner_name,
        pb.partner_booking_id,
        pb.customer_name,
        pb.customer_phone,
        pb.referral_code,
        pb.payment_amount,
        pb.payment_method,
        pb.payment_status,
        pb.status,
        pb.error_message,
        pb.products,
        pb.note,
        pb.consulted,
        pb.created_at,
        pb.updated_at,
        pb.deleted_at,
        coalesce(jsonb_agg(json_build_object(
            'item_id', pbk.id,
            'booking_id', pbk.booking_id,
            'checkout_price', pbk.checkout_price,
            'barcode', pbk.barcode,
            'status', pbk.status,
            'result_id', pbk.result_id,
            'current_status', k.current_status,
            'use_customer_name', sm.full_name,
            'phone_number', sm.phone_number,
            'dob', sm.dob,
            'email', sm.email,
            'product_code', pbk.product_code,
            'product_id', pbk.product_id,
            'product_name', pbk.product_name,
            'gender', sm.gender,
            'address', sm.address,
            'expected_report_release_date', k.expected_report_release_date,
            'sample_collection_date', k.sample_collection_date,
            'result_url', k.default_pdf_link,
            'actual_release_date', k.actual_report_release_time
        )) filter (where pbk.id is not null), '[]') as item_list
    from partner_booking pb
    left join partner_booking_kit pbk on pb.id = pbk.booking_id
    left join kit k on pbk.barcode = k.barcode
    left join sample_meta sm on k.sample_meta_id = sm.id
"""

COUNT_BOOKING_QUERY = """
    select 
        count(pb.id)
    from partner_booking pb
"""

async def get_all_booking(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    partner_name: Optional[str]=None,
    customer_name: Optional[str]=None,
    customer_phone: Optional[str]=None,
    payment_method: Optional[str]=None,
    payment_status: Optional[str]=None,
    status: Optional[str]=None,
    consulted: Optional[str]=None,
    creation_start_date: Optional[str]=None,
    creation_end_date: Optional[str]=None,
    include_deleted: bool=False,
):
    query_filter = ""
    if partner_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.partner_name like '%{partner_name}%' "
    if customer_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.customer_name like '%{customer_name}%' "
    if customer_phone:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.customer_phone like '%{customer_phone}%' "
    if payment_method:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.payment_method = '{payment_method}' "
    if status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.status = '{status}' "
    if consulted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.consulted = {consulted} "
    if payment_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.payment_status = '{payment_status}' "
    if creation_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.created_at::date >= '{creation_start_date}' "
    if creation_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.created_at::date <= '{creation_end_date}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.deleted_at is null "
    
    raw_count_query = COUNT_BOOKING_QUERY + "where " + query_filter if query_filter != "" else COUNT_BOOKING_QUERY
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    raw_query = GET_BOOKING_QUERY + "where " + query_filter if query_filter != "" else GET_BOOKING_QUERY
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query += "group by pb.id order by pb.created_at desc"
    results = await db.all(db.text(raw_query))
    return results, total
    
async def get_booking_by_id(
    id: UUID
):
    raw_query = GET_BOOKING_QUERY + f"where pb.id = '{id}' group by pb.id"
    result = await db.first(db.text(raw_query))
    return result

async def create_partner_booking(req: PartnerBookingCreationReq):
    async with db.transaction() as tx:
        total = 0
        partner_booking = PartnerBooking(
            id = uuid.uuid4(),
            customer_support_id = req.customer_support_id,
            partner_name = req.partner_name,
            partner_booking_id = req.partner_booking_id,
            customer_name = req.customer_name,
            customer_phone = req.customer_phone,
            referral_code = req.referral_code,
            payment_amount = total,
            payment_method = req.payment_method,
            payment_status = req.payment_status,
            status = req.status,
            consulted = req.consulted,
            error_message = req.error_message,
            products = req.products,
            note = req.note,
            created_at = datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
            updated_at = datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
        )
        booking_kits = []
        barcode_status = BarcodeStatus.RECEIVED.name
        if req.payment_status == PaymentStatus.COMPLETED.name:
            barcode_status = BarcodeStatus.SAMPLE_COLLECTION.name
        if req.item_list is not None:
            for item in req.item_list:
                code = await get_code(item.barcode)
                if code.state not in (config['DEFAULT_CODE_STATE'], config['SHIPPED_CODE_STATE']):
                    logger.info(f"code not in available state: {code}")
                product_detail = get_product_by_product_code(product_code=item.product_code)
                
                partner_booking_kit = PartnerBookingKit(
                    id = uuid.uuid4(),
                    booking_id = partner_booking.id,
                    checkout_price = product_detail['price'],
                    product_code = product_detail['code'],
                    product_name = product_detail['name'],
                    product_id = product_detail['id'],
                    barcode = item.barcode,
                    status = barcode_status,
                    result_id = item.result_id,
                )
                total += partner_booking_kit.checkout_price
                booking_kits.append(partner_booking_kit)
        partner_booking.payment_amount = total
        await partner_booking.create()
        for item in booking_kits:
            await item.create()
            await update_code(item.barcode, config['SHIPPED_CODE_STATE'])
        res = await get_booking_by_id(partner_booking.id)
        return res
    


async def create_partner_booking_v3(req: PartnerBookingCreationReq):
    async with db.transaction() as tx:
        total = 0
        partner_booking = PartnerBooking(
            id = uuid.uuid4(),
            customer_support_id = req.customer_support_id,
            partner_name = req.partner_name,
            partner_booking_id = req.partner_booking_id,
            customer_name = req.customer_name,
            customer_phone = req.customer_phone,
            referral_code = req.referral_code,
            payment_amount = total,
            payment_method = req.payment_method,
            payment_status = req.payment_status,
            status = req.status,
            consulted = req.consulted,
            error_message = req.error_message,
            products = req.products,
            note = req.note,
            created_at = datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
            updated_at = datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
        )
        booking_kits = []
        barcode_status = BarcodeStatus.RECEIVED.name
        if req.payment_status == PaymentStatus.COMPLETED.name:
            barcode_status = BarcodeStatus.SAMPLE_COLLECTION.name
        if req.item_list is not None:
            for item in req.item_list:
                code = await get_code(item.barcode)
                if code.state not in (config['DEFAULT_CODE_STATE'], config['SHIPPED_CODE_STATE']):
                    logger.info(f"code not in available state: {code}")
                product_detail = get_product_by_product_code(product_code=item.product_code)
                
                partner_booking_kit = PartnerBookingKit(
                    id = uuid.uuid4(),
                    booking_id = partner_booking.id,
                    checkout_price = product_detail['price'],
                    product_code = product_detail['code'],
                    product_name = product_detail['name'],
                    product_id = product_detail['id'],
                    barcode = item.barcode,
                    status = barcode_status,
                    result_id = item.result_id,
                )
                total += partner_booking_kit.checkout_price
                booking_kits.append(partner_booking_kit)
        partner_booking.payment_amount = total
        await partner_booking.create()
        for item in booking_kits:
            await item.create()
            await update_code(item.barcode, config['SHIPPED_CODE_STATE'])
        res = await get_booking_by_id_v3(partner_booking.id)
        return res
    


async def update_partner_booking_kit(barcode: str, booking_id: str, update_data: dict):
    partner_booking = await PartnerBookingKit.query.where(PartnerBookingKit.booking_id==booking_id).where(PartnerBookingKit.barcode==barcode).gino.first()
    await partner_booking.update(**update_data).apply()
    return partner_booking.to_dict()
    
async def get_partner_booking_id_by_barcode(barcode: str):
    async with db.transaction() as tx:
        partner_booking = await PartnerBookingKit.query.where(PartnerBookingKit.barcode==barcode).gino.first()
        return partner_booking
        

async def update_partner_booking(
    partner_booking_id: str,
    req: PartnerBookingUpdateReq
):
    async with db.transaction() as tx:
        partner_booking = await PartnerBooking.query.where(PartnerBooking.id==partner_booking_id).gino.first()
        total = 0
        if req.item_list is not None:
            await PartnerBookingKit.delete.where(PartnerBookingKit.booking_id==partner_booking_id).gino.status()
            for item in req.item_list:
                product_detail = get_product_by_product_code(product_code=item.product_code)
                partner_booking_kit = PartnerBookingKit(
                    id = uuid.uuid4(),
                    booking_id = partner_booking.id,
                    checkout_price = product_detail['price'],
                    product_code = product_detail['code'],
                    product_name = product_detail['name'],
                    product_id = product_detail['id'],
                    barcode = item.barcode,
                    status = BarcodeStatus.RECEIVED.name,
                    result_id = item.result_id,
                )
                total += partner_booking_kit.checkout_price
                await partner_booking_kit.create()
        
        req_dict = req.dict()
        update_data = {k: req_dict[k] for k in req_dict.keys() if req_dict[k] is not None and k != "item_list"}
        if total != 0:
            update_data.update({
                "payment_amount": total,
            })
        update_data.update({
            "updated_at": datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
        })
        await partner_booking.update(**update_data).apply()
        
        res = await get_booking_by_id(partner_booking.id)
        return res


async def update_partner_booking_v3(
    partner_booking_id: str,
    req: PartnerBookingUpdateReq
):
    async with db.transaction() as tx:
        partner_booking = await PartnerBooking.query.where(PartnerBooking.id==partner_booking_id).gino.first()
        total = 0
        if req.item_list is not None:
            await PartnerBookingKit.delete.where(PartnerBookingKit.booking_id==partner_booking_id).gino.status()
            for item in req.item_list:
                product_detail = get_product_by_product_code(product_code=item.product_code)
                partner_booking_kit = PartnerBookingKit(
                    id = uuid.uuid4(),
                    booking_id = partner_booking.id,
                    checkout_price = product_detail['price'],
                    product_code = product_detail['code'],
                    product_name = product_detail['name'],
                    product_id = product_detail['id'],
                    barcode = item.barcode,
                    status = BarcodeStatus.RECEIVED.name,
                    result_id = item.result_id,
                )
                total += partner_booking_kit.checkout_price
                await partner_booking_kit.create()
        
        req_dict = req.dict()
        update_data = {k: req_dict[k] for k in req_dict.keys() if req_dict[k] is not None and k != "item_list"}
        if total != 0:
            update_data.update({
                "payment_amount": total,
            })
        update_data.update({
            "updated_at": datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
        })
        await partner_booking.update(**update_data).apply()
        
        res = await get_booking_by_id_v3(partner_booking.id)
        return res
    
async def delete_partner_booking(
    partner_booking_id: str
):
    async with db.transaction() as tx:
        partner_booking = await PartnerBooking.query.where(PartnerBooking.id==partner_booking_id).gino.first()
        update_data = {
            "deleted_at": datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
        }
        await partner_booking.update(**update_data).apply()
        res = await get_booking_by_id(partner_booking.id)
        return res
    
async def delete_partner_booking_v3(
    partner_booking_id: str
):
    async with db.transaction() as tx:
        partner_booking = await PartnerBooking.query.where(PartnerBooking.id==partner_booking_id).gino.first()
        update_data = {
            "deleted_at": datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
        }
        await partner_booking.update(**update_data).apply()
        res = await get_booking_by_id_v3(partner_booking.id)
        return res


async def update_results_by_booking_id(
    booking_id: str,
    results: ResultUpdateReq,
):
    async with db.transaction() as tx:
        for res in results.results:
            item = await PartnerBookingKit.query.where(PartnerBookingKit.booking_id==booking_id).where(PartnerBookingKit.barcode==res.barcode).gino.first()
            update_data = {
                "result_id": res.result_id,
                "updated_at": datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
            }
            await item.update(**update_data).apply()
        resp = await get_booking_by_id(booking_id)
        return resp

GET_BOOKING_QUERY_V3 = """
    select 
        pb.id,
        pb.customer_support_id,
        pb.partner_name,
        pb.partner_booking_id,
        pb.customer_name,
        pb.customer_phone,
        pb.referral_code,
        pb.payment_amount,
        pb.payment_method,
        pb.payment_status,
        pb.status,
        pb.error_message,
        pb.products,
        pb.note,
        pb.consulted,
        pb.created_at,
        pb.updated_at,
        pb.deleted_at,
        coalesce(jsonb_agg(json_build_object(
            'item_id', pbk.id,
            'booking_id', pbk.booking_id,
            'checkout_price', pbk.checkout_price,
            'barcode', pbk.barcode,
            'status', pbk.status,
            'result_id', pbk.result_id,
            'current_status', k.current_status,
            'use_customer_name', sub.full_name,
            'phone_number', sub.phone_number,
            'dob', sub.dob,
            'email', sub.email,
            'product_code', pbk.product_code,
            'product_id', pbk.product_id,
            'product_name', pbk.product_name,
            'gender', sub.gender,
            'address', sub.address,
            'expected_report_release_date', k.expected_report_release_date,
            'sample_collection_date', sam.sample_collection_date,
            'result_url', k.default_pdf_link,
            'actual_release_date', k.actual_report_release_time
        )) filter (where pbk.id is not null), '[]') as item_list
    from partner_booking pb
    left join partner_booking_kit pbk on pb.id = pbk.booking_id
    left join kit k on pbk.barcode = k.barcode
    left join sample sam on k.samplecode = sam.samplecode
    left join subject sub ON sam.subject_id = sub.id
"""

COUNT_BOOKING_QUERY_V3 = """
    select 
        count(pb.id)
    from partner_booking pb
"""

async def get_all_booking_v3(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    partner_name: Optional[str]=None,
    customer_name: Optional[str]=None,
    customer_phone: Optional[str]=None,
    payment_method: Optional[str]=None,
    payment_status: Optional[str]=None,
    status: Optional[str]=None,
    consulted: Optional[str]=None,
    creation_start_date: Optional[str]=None,
    creation_end_date: Optional[str]=None,
    include_deleted: bool=False,
):
    query_filter = ""
    if partner_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.partner_name like '%{partner_name}%' "
    if customer_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.customer_name like '%{customer_name}%' "
    if customer_phone:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.customer_phone like '%{customer_phone}%' "
    if payment_method:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.payment_method = '{payment_method}' "
    if status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.status = '{status}' "
    if consulted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.consulted = {consulted} "
    if payment_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.payment_status = '{payment_status}' "
    if creation_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.created_at::date >= '{creation_start_date}' "
    if creation_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.created_at::date <= '{creation_end_date}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"pb.deleted_at is null "
    
    raw_count_query = COUNT_BOOKING_QUERY_V3 + "where " + query_filter if query_filter != "" else COUNT_BOOKING_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    raw_query = GET_BOOKING_QUERY_V3 + "where " + query_filter if query_filter != "" else GET_BOOKING_QUERY_V3
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query += "group by pb.id order by pb.created_at desc"
    results = await db.all(db.text(raw_query))
    return results, total

async def get_booking_by_id_v3(
    id: UUID
):
    raw_query = GET_BOOKING_QUERY_V3 + f"where pb.id = '{id}' group by pb.id"
    result = await db.first(db.text(raw_query))
    return result

async def update_partner_booking_v3(
    partner_booking_id: str,
    req: PartnerBookingUpdateReq
):
    async with db.transaction() as tx:
        partner_booking = await PartnerBooking.query.where(PartnerBooking.id==partner_booking_id).gino.first()
        total = 0
        if req.item_list is not None:
            await PartnerBookingKit.delete.where(PartnerBookingKit.booking_id==partner_booking_id).gino.status()
            for item in req.item_list:
                product_detail = get_product_by_product_code(product_code=item.product_code)
                partner_booking_kit = PartnerBookingKit(
                    id = uuid.uuid4(),
                    booking_id = partner_booking.id,
                    checkout_price = product_detail['price'],
                    product_code = product_detail['code'],
                    product_name = product_detail['name'],
                    product_id = product_detail['id'],
                    barcode = item.barcode,
                    status = BarcodeStatus.RECEIVED.name,
                    result_id = item.result_id,
                )
                total += partner_booking_kit.checkout_price
                await partner_booking_kit.create()
        
        req_dict = req.dict()
        update_data = {k: req_dict[k] for k in req_dict.keys() if req_dict[k] is not None and k != "item_list"}
        if total != 0:
            update_data.update({
                "payment_amount": total,
            })
        update_data.update({
            "updated_at": datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
        })
        await partner_booking.update(**update_data).apply()
        
        res = await get_booking_by_id_v3(partner_booking.id)
        return res


async def update_results_by_booking_id_v3(
    booking_id: str,
    results: ResultUpdateReq,
):
    async with db.transaction() as tx:
        for res in results.results:
            item = await PartnerBookingKit.query.where(PartnerBookingKit.booking_id==booking_id).where(PartnerBookingKit.barcode==res.barcode).gino.first()
            update_data = {
                "result_id": res.result_id,
                "updated_at": datetime.utcnow().strptime(datetime.utcnow().strftime(DEFAULT_DATE_STR), DEFAULT_DATE_STR),
            }
            await item.update(**update_data).apply()
        resp = await get_booking_by_id_v3(booking_id)
        return resp