from typing import List, Optional

from ..models.models import KitV3, db


def raw_querry_w_batch_number(batch):
    raw_querry = f"""
    SELECT
        b.number AS batch_number,
        k.barcode,
        k.nickname,
        k.sample_meta_id,
        sm.userid,
        sm.full_name,
        sm.dob::date,
        sm.yob,
        sm.gender,
        sm.phone_number,
        sm.email,
        sm.address,
        k.product_name,
        k.product_code,
        k.product_type,
        k.current_status,
        a.id AS account_id,
        a.name AS account_name,
        s.id AS source_id,
        st.name AS nominator,
        st.id AS nominator_id,
        stf.name AS freelancer,
        stf.id AS freelancer_id,
        st2.name AS sale_pic,
        k.sample_collection_date::date,
        k.sample_collection_time,
        k.sample_receipt_date::timestamp,
        k.lab_receipt_date::timestamp,
        k.sample_collector_name,
        k.sample_receiver_name,
        k.expected_report_release_date::date,
        k.customer_support_id,
        k.customer_support_name,
        k.free_of_charge,
        k.is_priority,
        k.note,
        k.promotion AS promotion_id,
        p.code AS promotion_code,
        sm.diagnosis,
        sm.validate_account,
        k.actual_report_release_time::date AS actual_report_release_date,
        k.actual_report_release_time,
        k.default_pdf_link,
        k.sample_type,
        CASE
            WHEN k.default_pdf_link IS NULL THEN 'NOT_AVAILABLE'
            WHEN k.pdf_generation_date > current_date - interval '6 days' THEN 'AVAILABLE'
            ELSE 'EXPIRED'
        END AS pdf_status,
        dna_records.lid AS dna_extraction_id,
        k.created_time,
        k.updated_time,
        k.deleted_at
    FROM
        batch b
        INNER JOIN (
            SELECT bh.*
            FROM batch bh
            WHERE bh.number={batch}
        ) b2 on b.id = b2.id
        INNER JOIN batch_mapping bm ON b.id = bm.batch_id
        INNER JOIN plate pl ON bm.plate_id = pl.id
        INNER JOIN sample_mapping smp ON pl.id = smp.plate_id
        INNER JOIN dna_extractions de ON smp.dna_extraction_id = de.id
        INNER JOIN lab_sample ls ON de.lid = ls.lid
        LEFT JOIN (
            SELECT de.*
            FROM dna_extractions de
            INNER JOIN (
                SELECT
                    dna.lid,
                    dna.id,
                    ROW_NUMBER() OVER (
                        PARTITION BY dna.lid
                        ORDER BY
                            CASE
                                WHEN pl.name IS NULL THEN '0'
                                ELSE pl.name
                            END DESC
                    ) AS rank
                FROM dna_extractions dna
                LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
                LEFT JOIN plate pl ON smp.plate_id = pl.id
                WHERE dna.dna_qc_status = 'PASS'
            ) de2 ON de.id = de2.id AND de.lid = de2.lid
            WHERE de2.rank = 1
        ) dna_records ON dna_records.lid = ls.lid
        INNER JOIN kit k ON ls.barcode = k.barcode
        INNER JOIN sample_meta sm ON k.sample_meta_id = sm.id
        INNER JOIN source s ON k.source_id = s.id
        INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
        INNER JOIN account a ON sah.account_id = a.id
        LEFT JOIN promotion p ON k.promotion = p.id
        LEFT JOIN staff st ON s.nominator_id = st.id
        LEFT JOIN staff stf ON s.freelancer_id = stf.id
        INNER JOIN staff st2 ON sah.pic_id = st2.id
    """
    return raw_querry


def count_raw_querry_w_batch_number(batch):
    raw_querry = f"""
    SELECT
       count(k.barcode)
    FROM
        batch b
        INNER JOIN (
            SELECT bh.*
            FROM batch bh
            WHERE bh.number={batch}
        ) b2 on b.id = b2.id
        INNER JOIN batch_mapping bm ON b.id = bm.batch_id
        INNER JOIN plate pl ON bm.plate_id = pl.id
        INNER JOIN sample_mapping smp ON pl.id = smp.plate_id
        INNER JOIN dna_extractions de ON smp.dna_extraction_id = de.id
        INNER JOIN lab_sample ls ON de.lid = ls.lid
        LEFT JOIN (
            SELECT de.*
            FROM dna_extractions de
            INNER JOIN (
                SELECT
                    dna.lid,
                    dna.id,
                    ROW_NUMBER() OVER (
                        PARTITION BY dna.lid
                        ORDER BY
                            CASE
                                WHEN pl.name IS NULL THEN '0'
                                ELSE pl.name
                            END DESC
                    ) AS rank
                FROM dna_extractions dna
                LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
                LEFT JOIN plate pl ON smp.plate_id = pl.id
                WHERE dna.dna_qc_status = 'PASS'
            ) de2 ON de.id = de2.id AND de.lid = de2.lid
            WHERE de2.rank = 1
        ) dna_records ON dna_records.lid = ls.lid
        INNER JOIN kit k ON ls.barcode = k.barcode
        INNER JOIN sample_meta sm ON k.sample_meta_id = sm.id
        INNER JOIN source s ON k.source_id = s.id
        INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
        INNER JOIN account a ON sah.account_id = a.id
        LEFT JOIN promotion p ON k.promotion = p.id
        LEFT JOIN staff st ON s.nominator_id = st.id
        LEFT JOIN staff stf ON s.freelancer_id = stf.id
        INNER JOIN staff st2 ON sah.pic_id = st2.id
    """
    return raw_querry


KIT_LIST_QUERY = """
    select
        k.barcode,
        k.nickname,
        k.sample_meta_id,
        sm.userid,
        sm.full_name,
        sm.dob::date,
        sm.yob,
        sm.gender,
        sm.phone_number,
        sm.email,
        sm.address,
        k.product_name,
        k.product_code,
        k.product_type,
        k.current_status,
        a.id as account_id,
        a.name as account_name,
        s.id as source_id,
        st.name as nominator,
        st.id as nominator_id,
        stf.name as freelancer,
        stf.id as freelancer_id,
        st2.name as sale_pic,
        k.sample_collection_date::date,
        k.sample_collection_time,
        k.sample_receipt_date::timestamp,
        k.lab_receipt_date::timestamp,
        k.sample_collector_name,
        k.sample_receiver_name,
        k.expected_report_release_date::date,
        k.customer_support_id,
        k.customer_support_name,
        k.free_of_charge,
        k.is_priority,
        k.note,
        k.promotion as promotion_id,
        p.code as promotion_code,
        b.number as batch_number,
        sm.diagnosis,
        sm.validate_account,
        k.actual_report_release_time::date as actual_report_release_date,
        k.actual_report_release_time,
        k.default_pdf_link,
        k.sample_type,
        case 
            when k.default_pdf_link is null then 'NOT_AVAILABLE'
            when k.pdf_generation_date > current_date - interval '6 days' then 'AVAILABLE'
            else 'EXPIRED'
        end
        as pdf_status,
        dna_records.lid as dna_extraction_id,
        k.created_time,
        k.updated_time,
        k.deleted_at
    from
        kit k
        inner join sample_meta sm on k.sample_meta_id = sm.id
        inner join source s on k.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join promotion p on k.promotion = p.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
"""

KIT_LIST_COUNT_QUERY = """
    select
        count(k.barcode)
    from
        kit k
        inner join sample_meta sm on k.sample_meta_id = sm.id
        inner join source s on k.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join promotion p on k.promotion = p.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
"""

# KIT_LIST_BY_SAMPLE_META_QUERY = """
#     select
#         k.barcode,
#         sm.full_name,
#         sm.dob,
#         sm.gender,
#         sm.phone_number,
#         k.product_name,
#         k.product_code,
#         k.current_status,
#         a.name as source,
#         st.name as nominator,
#         st2.name as sale_pic,
#         k.sample_collection_date,
#         k.sample_collection_time,
#         k.sample_receipt_date,
#         k.expected_report_release_date,
#         k.customer_support_id,
#         k.customer_support_name,
#         k.free_of_charge,
#         k.is_priority,
#         k.note,
#         k.promotion,
#         sm.diagnosis,
#         k.created_time,
#         k.updated_time,
#         k.deleted_at
#     from
#         kit k
#         inner join sample_meta sm on k.sample_meta_id = sm.id
#         inner join source s on k.source_id = s.id
#         inner join sale_account_history sah on s.account_history_id = sah.id
#         inner join account a on sah.account_id = a.id
#         left join staff st on s.nominator_id = st.id
#         inner join staff st2 on sah.pic_id = st2.id
# """


async def get_kit_list_detail(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[int] = None,
    userid: Optional[str] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    exclude_status: Optional[str] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    query_filter = ""
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.barcode like '%{barcode}%' "
    if userid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sm.userid = '{str(userid)}' "
    if email:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sm.email like '%{email}%' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sm.phone_number like '%{phone_number}%' "
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sm.full_name) like '%{name.lower()}%' "
    if gender:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sm.gender like '%{gender}%' "
    if nickname:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.nickname like '%{nickname}%' "
    if sample_collector_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.sample_collector_name like '%{sample_collector_name}%' "
    if sample_receiver_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.sample_receiver_name like '%{sample_receiver_name}%' "
    if current_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.current_status like '%{current_status}%' "
    if exclude_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.current_status not like '%{exclude_status}%' "
    if product_code:
        if len(product_code) == 1:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"k.product_code = '{product_code[0]}' "
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"k.product_code in {tuple(product_code)} "
    if sale_pic:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.name like '%{sale_pic}%' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.sample_receipt_date::date >= '{start_date}' "
    if account_name:
        if len(account_name) == 1:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name = '{account_name[0]}'"
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name in {tuple(account_name)} "
    if nominator:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st.name like '%{nominator}%' "
    if batch:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.number = {batch} "
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology = '{technology}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.sample_receipt_date::date <= '{end_date}' "
    if release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"k.expected_report_release_date::date >= '{release_start_date}' "
        )
    if release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.expected_report_release_date::date <= '{release_end_date}' "
    if collection_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.sample_collection_date::date >= '{collection_start_date}' "
    if collection_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.sample_collection_date::date <= '{collection_end_date}' "
    if actual_report_release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.actual_report_release_time::date >= '{actual_report_release_start_date}' "
    if actual_report_release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"k.actual_report_release_time::date <= '{actual_report_release_end_date}' "
        )
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += "k.deleted_at is null "

    if batch:
        COUNT_QUERY = count_raw_querry_w_batch_number(batch)
    else:
        COUNT_QUERY = KIT_LIST_COUNT_QUERY
    raw_count_query = (
        COUNT_QUERY + "where " + query_filter if query_filter != "" else COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if batch:
        KIT_QUERY = raw_querry_w_batch_number(batch)
    else:
        KIT_QUERY = KIT_LIST_QUERY

    if query_filter.startswith("order"):
        raw_query = KIT_QUERY + query_filter
    else:
        raw_query = (
            KIT_QUERY + "where " + query_filter if query_filter != "" else KIT_QUERY
        )
    results = await db.all(db.text(raw_query))
    return results, total


async def get_kit_list_by_sample_meta(
    name: str,
    gender: str,
    dob: Optional[str] = None,
    yob: Optional[str] = None,
    account_id: Optional[str] = None,
    product_code: Optional[str] = None,
):
    query_filter = ""
    query_filter += "and " if query_filter != "" else " "
    query_filter += f"sm.full_name like '%{name}%' "
    query_filter += "and " if query_filter != "" else " "
    query_filter += f"sm.gender like '%{gender}%' "
    if dob:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sm.dob::date = date('{dob}') "
    if yob:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sm.yob = '{yob}' "
    if account_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"a.id = '{account_id}' "
    if product_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.product_code like '%{product_code}%' "
    raw_query = (
        KIT_LIST_QUERY + "where " + query_filter
        if query_filter != ""
        else KIT_LIST_QUERY
    )
    results = await db.all(db.text(raw_query))
    return results


async def get_kit_detail(
    barcode: str,
):
    query_filter = f"where k.barcode = '{barcode}'"
    raw_query = KIT_LIST_QUERY + query_filter
    results = await db.all(db.text(raw_query))
    if len(results) != 1:
        raise ValueError(f"kit with barcode {barcode} not found")
    return results[0]


def raw_querry_w_batch_number_v3(batch):
    raw_querry = f"""
    SELECT
        b.number AS batch_number,
        k.barcode,
        k.nickname,
        k.samplecode,
        k.product_name,
        k.product_code,
        k.product_type,
        k.current_status,
        k.workflow,
        k.expected_report_release_date::date,
        k.customer_support_id,
        k.customer_support_name,
        k.free_of_charge,
        k.is_priority,
        k.actual_report_release_time::date AS actual_report_release_date,
        k.actual_report_release_time,
        k.default_pdf_link,
        k.promotion AS promotion_id,
        tu."tenDonVi" as cs_pic,
        sub.user_id,
        sub.identifier_code,
        sub.full_name,
        sub.dob::date,
        sub.yob,
        sub.legal_guardian,
        sub.gender,
        sub.phone_number,
        sub.email,
        sub.address,
        sub.diagnosis,
        sub.validate_account,
        sub.martyr_name,
        sub.martyr_relationships,
        a.id AS account_id,
        a.name AS account_name,
        s.id AS source_id,
        st.name AS nominator,
        st.id AS nominator_id,
        stf.name AS freelancer,
        stf.id AS freelancer_id,
        st2.name AS sale_pic,
        sam.sample_collection_date::timestamp,
        sam.sample_collection_time,
        sam.sample_receipt_date::timestamp,
        sam.lab_receipt_date::timestamp,
        sam.sample_collector_name,
        sam.sample_receiver_name,
        k.note,
        sam.scan_status,
        sam.sample_type,
        sam.sponsor_id,
        p.code AS promotion_code,
        tc."donViThuNhanMau_id",
        CASE
            WHEN k.default_pdf_link IS NULL THEN 'NOT_AVAILABLE'
            WHEN k.pdf_generation_date > current_date - interval '6 days' THEN 'AVAILABLE'
            ELSE 'EXPIRED'
        END AS pdf_status,
        dna_records.lid AS dna_extraction_id,
        k.created_at,
        k.updated_at,
        k.deleted_at
    FROM
        batch b
        INNER JOIN (
            SELECT bh.*
            FROM batch bh
            WHERE bh.number={batch}
        ) b2 on b.id = b2.id
        INNER JOIN batch_mapping bm ON b.id = bm.batch_id
        INNER JOIN plate pl ON bm.plate_id = pl.id
        INNER JOIN sample_mapping smp ON pl.id = smp.plate_id
        INNER JOIN dna_extractions de ON smp.dna_extraction_id = de.id
        INNER JOIN lab_sample ls ON de.lid = ls.lid
        LEFT JOIN (
            SELECT de.*
            FROM dna_extractions de
            INNER JOIN (
                SELECT
                    dna.lid,
                    dna.id,
                    ROW_NUMBER() OVER (
                        PARTITION BY dna.lid
                        ORDER BY
                            CASE
                                WHEN pl.name IS NULL THEN '0'
                                ELSE pl.name
                            END DESC
                    ) AS rank
                FROM dna_extractions dna
                LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
                LEFT JOIN plate pl ON smp.plate_id = pl.id
                WHERE dna.dna_qc_status = 'PASS'
            ) de2 ON de.id = de2.id AND de.lid = de2.lid
            WHERE de2.rank = 1
        ) dna_records ON dna_records.lid = ls.lid
        INNER JOIN sample sam on ls.samplecode = sam.samplecode
        INNER JOIN kit k ON ls.barcode = k.barcode
        INNER JOIN subject sub ON sam.subject_id = sub.id
        INNER JOIN source s ON sam.source_id = s.id
        INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
        INNER JOIN account a ON sah.account_id = a.id
        LEFT JOIN promotion p ON k.promotion = p.id
        LEFT JOIN staff st ON s.nominator_id = st.id
        LEFT JOIN staff stf ON s.freelancer_id = stf.id
        INNER JOIN staff st2 ON sah.pic_id = st2.id
        left join tracking_collection tc on tc."maThuNhan" = sam.samplecode
        left join tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
           
    """
    return raw_querry


def count_raw_querry_w_batch_number_v3(batch):
    raw_querry = f"""
    SELECT
       count(k.barcode)
    FROM
        batch b
        INNER JOIN (
            SELECT bh.*
            FROM batch bh
            WHERE bh.number={batch}
        ) b2 on b.id = b2.id
        INNER JOIN batch_mapping bm ON b.id = bm.batch_id
        INNER JOIN plate pl ON bm.plate_id = pl.id
        INNER JOIN sample_mapping smp ON pl.id = smp.plate_id
        INNER JOIN dna_extractions de ON smp.dna_extraction_id = de.id
        INNER JOIN lab_sample ls ON de.lid = ls.lid
        LEFT JOIN (
            SELECT de.*
            FROM dna_extractions de
            INNER JOIN (
                SELECT
                    dna.lid,
                    dna.id,
                    ROW_NUMBER() OVER (
                        PARTITION BY dna.lid
                        ORDER BY
                            CASE
                                WHEN pl.name IS NULL THEN '0'
                                ELSE pl.name
                            END DESC
                    ) AS rank
                FROM dna_extractions dna
                LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
                LEFT JOIN plate pl ON smp.plate_id = pl.id
                WHERE dna.dna_qc_status = 'PASS'
            ) de2 ON de.id = de2.id AND de.lid = de2.lid
            WHERE de2.rank = 1
        ) dna_records ON dna_records.lid = ls.lid
        INNER JOIN sample sam on ls.samplecode = sam.samplecode
        INNER JOIN kit k ON ls.barcode = k.barcode
        INNER JOIN subject sub ON sam.subject_id = sub.id
        INNER JOIN source s ON sam.source_id = s.id
        INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
        INNER JOIN account a ON sah.account_id = a.id
        LEFT JOIN promotion p ON k.promotion = p.id
        LEFT JOIN staff st ON s.nominator_id = st.id
        LEFT JOIN staff stf ON s.freelancer_id = stf.id
        INNER JOIN staff st2 ON sah.pic_id = st2.id
    """
    return raw_querry


KIT_LIST_QUERY_V3 = """
    select
        k.id,
        k.id as kit_uuid,
        k.barcode,
        k.nickname,
        k.samplecode,
        sub.user_id,
        sub.identifier_code,
        sub.full_name,
        sub.dob::date,
        sub.yob,
        sub.legal_guardian,
        sub.gender,
        sub.phone_number,
        sub.email,
        sub.address,
        k.product_name,
        k.product_code,
        k.product_type,
        k.current_status,
        k.workflow,
        a.id as account_id,
        a.name as account_name,
        s.id as source_id,
        st.name as nominator,
        st.id as nominator_id,
        stf.name as freelancer,
        stf.id as freelancer_id,
        st2.name as sale_pic,
        sam.subject_id,
        sam.sample_collection_date::timestamp,
        sam.sample_collection_time,
        sam.sample_receipt_date::timestamp,
        sam.lab_receipt_date::timestamp,
        sam.sample_collector_name,
        sam.sample_receiver_name,
        k.expected_report_release_date::date,
        k.customer_support_id,
        k.customer_support_name,
        tu."tenDonVi" as cs_pic,
        k.free_of_charge,
        k.is_priority,
        sam.scan_status,
        k.note,
        k.promotion as promotion_id,
        p.code as promotion_code,
        b.number as batch_number,
        sub.diagnosis,
        sub.validate_account,
        sub.martyr_name,
        sub.martyr_relationships,
        k.actual_report_release_time::date as actual_report_release_date,
        k.actual_report_release_time,
        k.default_pdf_link,
        sam.sample_type,
        sam.sponsor_id,
        tc."donViThuNhanMau_id",
        case 
            when k.default_pdf_link is null then 'NOT_AVAILABLE'
            when k.pdf_generation_date > current_date - interval '6 days' then 'AVAILABLE'
            else 'EXPIRED'
        end
        as pdf_status,
        dna_records.lid as dna_extraction_id,
        k.created_at,
        k.created_at as upgraded_at,
        k.updated_at,
        k.deleted_at
    from
        sample sam
        INNER JOIN kit k ON sam.samplecode = k.samplecode
        INNER JOIN subject sub ON sam.subject_id = sub.id
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join promotion p on k.promotion = p.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join tracking_collection tc on tc."maThuNhan" = sam.samplecode
        left join tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
"""

KIT_LIST_COUNT_QUERY_V3 = """
    select
        count(k.barcode)
    from
        sample sam
        INNER JOIN kit k ON sam.samplecode = k.samplecode
        INNER JOIN subject sub ON sam.subject_id = sub.id
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join promotion p on k.promotion = p.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
"""


async def get_kit_list_detail_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[str] = None,
    samplecode: Optional[str] = None,
    userid: Optional[str] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    workflow: Optional[str] = None,
    exclude_status: Optional[str] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    sale_pic_id: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    query_filter = ""
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.barcode like '%{barcode}%' "
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.samplecode like '%{samplecode}%' "
    if userid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.user_id = '{str(userid)}' "
    if email:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.email like '%{email}%' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.phone_number like '%{phone_number}%' "
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sub.full_name) like '%{name.lower()}%' "
    if gender:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.gender like '%{gender}%' "
    if nickname:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.nickname like '%{nickname}%' "
    if sample_collector_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collector_name like '%{sample_collector_name}%' "
    if sample_receiver_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receiver_name like '%{sample_receiver_name}%' "
    if current_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.current_status like '%{current_status}%' "
    if workflow:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.workflow like '%{workflow}%' "
    if exclude_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.current_status not like '%{exclude_status}%' "
    if product_code:
        if len(product_code) == 1:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"k.product_code = '{product_code[0]}' "
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"k.product_code in {tuple(product_code)} "
    if sale_pic:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.name like '%{sale_pic}%' "
    if sale_pic_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.id = '{sale_pic_id}' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receipt_date::date >= '{start_date}' "
    if account_name:
        if len(account_name) == 1:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name = '{account_name[0]}'"
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name in {tuple(account_name)} "
    if nominator:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st.name like '%{nominator}%' "
    if batch:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.number = {batch} "
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology = '{technology}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receipt_date::date <= '{end_date}' "
    if release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"k.expected_report_release_date::date >= '{release_start_date}' "
        )
    if release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.expected_report_release_date::date <= '{release_end_date}' "
    if collection_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"sam.sample_collection_date::date >= '{collection_start_date}' "
        )
    if collection_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collection_date::date <= '{collection_end_date}' "
    if actual_report_release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.actual_report_release_time::date >= '{actual_report_release_start_date}' "
    if actual_report_release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"k.actual_report_release_time::date <= '{actual_report_release_end_date}' "
        )

    query_filter += "and " if query_filter != "" else " "
    query_filter += "sam.scan_status = 2 "

    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += "k.deleted_at is null "

    if batch:
        COUNT_QUERY_v3 = count_raw_querry_w_batch_number_v3(batch)
    else:
        COUNT_QUERY_v3 = KIT_LIST_COUNT_QUERY_V3
    raw_count_query = (
        COUNT_QUERY_v3 + "where " + query_filter
        if query_filter != ""
        else COUNT_QUERY_v3
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if order_by:
        if order_by == "created_time":
            order_by = "created_at"
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if batch:
        KIT_QUERY_V3 = raw_querry_w_batch_number_v3(batch)
    else:
        KIT_QUERY_V3 = KIT_LIST_QUERY_V3

    if query_filter.startswith("order"):
        raw_query = KIT_QUERY_V3 + query_filter
    else:
        raw_query = (
            KIT_QUERY_V3 + "where " + query_filter
            if query_filter != ""
            else KIT_QUERY_V3
        )

    modified_results = []
    results = await db.all(db.text(raw_query))

    for row in results:
        updated_row = dict(row)
        updated_row["martyr_name"] = parse_field(row["martyr_name"])
        updated_row["martyr_relationships"] = parse_field(row["martyr_relationships"])
        modified_results.append(updated_row)

    return modified_results, total


def parse_field(martyr_raw):
    if martyr_raw and str(martyr_raw).startswith("[") and str(martyr_raw).endswith("]") and "," in str(martyr_raw):
        items = str(martyr_raw)[1:-1].split(",")
        items = [item.strip().strip('"').strip("'") for item in items]
        return ", ".join(f"{item} ({i + 1})" for i, item in enumerate(items))
    elif martyr_raw and str(martyr_raw).startswith("[") and str(martyr_raw).endswith("]") and "," not in str(martyr_raw):
        return str(martyr_raw)[2:-2]
    elif martyr_raw:
        return martyr_raw
    else:
        return ""


async def get_kit_list_by_sample_meta_v3(
    name: str,
    gender: str,
    dob: Optional[str] = None,
    yob: Optional[str] = None,
    account_id: Optional[str] = None,
    product_code: Optional[str] = None,
):
    query_filter = ""
    query_filter += "and " if query_filter != "" else " "
    query_filter += f"sub.full_name like '%{name}%' "
    query_filter += "and " if query_filter != "" else " "
    query_filter += f"sub.gender like '%{gender}%' "
    if dob:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.dob::date = date('{dob}') "
    if yob:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.yob = '{yob}' "
    if account_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"a.id = '{account_id}' "
    if product_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"k.product_code like '%{product_code}%' "
    raw_query = (
        KIT_LIST_QUERY_V3 + "where " + query_filter
        if query_filter != ""
        else KIT_LIST_QUERY_V3
    )
    raw_query += "ORDER BY k.created_at"
    results = await db.all(db.text(raw_query))
    return results


async def get_earliest_kit_by_samplecode_v3(samplecode: str):
    kit = (
        await KitV3.query.where(KitV3.samplecode == samplecode)
        .order_by(KitV3.created_at.asc())
        .gino.first()
    )
    return kit


async def get_all_kits_by_samplecode_v3(samplecode: str):
    kits = (
        await KitV3.query.where(KitV3.samplecode == samplecode)
        .order_by(KitV3.created_at)
        .gino.all()
    )
    return kits


async def get_unscanned_kit_by_samplecode_v3(samplecode: str):
    kit = (
        await KitV3.query.where(KitV3.samplecode == samplecode)
        .where(KitV3.barcode == None)
        .order_by(KitV3.created_at)
        .gino.first()
    )
    return kit


async def get_kit_by_barcode_v3(barcode: str):
    kit = await KitV3.query.where(KitV3.barcode == barcode).gino.first()
    return kit


async def get_kit_detail_v3(
    samplecode: str,
):
    query_filter = f"where k.samplecode = '{samplecode}'"
    raw_query = KIT_LIST_QUERY_V3 + query_filter
    raw_query += "ORDER BY k.created_at"
    results = await db.all(db.text(raw_query))
    if len(results) < 1:
        raise ValueError(f"kit with samplecode {samplecode} not found")
    return results[0]


async def get_kit_detail_via_samplecode_v3(samplecode: str):
    query_filter = f"where sam.samplecode = '{samplecode}'"
    raw_query = KIT_LIST_QUERY_V3 + query_filter
    raw_query += "ORDER BY k.created_at"
    results = await db.all(db.text(raw_query))
    if len(results) != 1:
        raise ValueError(f"kit with samplecode {samplecode} not found")
    return results[0]


async def get_latest_kit_detail_via_samplecode_v3(samplecode: str):
    query_filter = f"where sam.samplecode = '{samplecode}'"
    raw_query = KIT_LIST_QUERY_V3 + query_filter
    raw_query += "ORDER BY k.created_at DESC"
    results = await db.all(db.text(raw_query))
    if len(results) == 0:
        raise ValueError(f"kit with samplecode {samplecode} not found")
    return results[0]


async def get_kit_detail_via_barcode_v3(
    barcode: str,
):
    query_filter = f"where k.barcode = '{barcode}'"
    raw_query = KIT_LIST_QUERY_V3 + query_filter
    raw_query += "ORDER BY k.created_at"
    results = await db.all(db.text(raw_query))
    if len(results) != 1:
        raise ValueError(f"Invalid barcode {barcode}")
    return results[0]


# kit_uuid
async def get_kit_detail_via_kit_uuid_v3(
    kit_uuid: str,
):
    query_filter = f"where k.id = '{kit_uuid}'"
    raw_query = KIT_LIST_QUERY_V3 + query_filter
    raw_query += "ORDER BY k.created_at"
    results = await db.all(db.text(raw_query))
    if len(results) != 1:
        raise ValueError(f"Invalid kit uuid {kit_uuid}")
    return results[0]
