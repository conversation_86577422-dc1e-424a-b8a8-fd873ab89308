from src.operation.models.models import SampleRack


def create_sample_rack(code: str):
    """
    Create a new sample rack with the provided data.
    """

    sample_rack = SampleRack(
        code=code
    )

    return SampleRack.create(sample_rack)

def create_sample_rack_if_not_exists(code: str):
    """
    Create a new sample rack if it does not already exist.
    """
    existing_rack = SampleRack.query.where(SampleRack.code == code).gino.first()
    if existing_rack:
        return existing_rack
    return create_sample_rack(code)