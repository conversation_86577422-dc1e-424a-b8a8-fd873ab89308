from typing import Optional

from .. import logger
from ..config import config
from ..models.models import Chip, DnaExtraction, LabSample, SampleMapping, db
from ..schemas.sample_mapping import *
from ..utils.utils import (
    calculate_qc_status_given_params,
    get_chip_n_pos_from_gtc_file,
    get_current_date_time_utc_7,
    get_field_names_w_additional_cols,
    get_file_name,
    get_position_given_format,
    get_updated_qc_status_for_sample_mapping,
)
from .chip import *
from .dna_extraction import *
from .lab_sample import *
from .plate import *
from .register_kit import *


async def get_sample_mapping_by_id(id: str) -> SampleMapping:
    sample_mapping = await SampleMapping.get(id)
    return sample_mapping


async def get_sample_mapping_by_dna_extraction_id(dna_extraction_id: int):
    result = await SampleMapping.query.where(
        SampleMapping.dna_extraction_id == dna_extraction_id
    ).gino.first()
    return result


async def get_sample_mapping_to_update_qc_status(type, chip_id, position):
    results = db.select([SampleMapping, Plate.type, Plate.name]).select_from(
        SampleMapping.join(Plate, SampleMapping.plate_id == Plate.id)
    )
    results = results.where(Plate.deleted_at == None)
    if type:
        results = results.where(Plate.type == type.upper())
    if chip_id:
        results = results.where(SampleMapping.chip_id == chip_id)
    if position:
        results = results.where(SampleMapping.position == position)

    return [r for r in await results.gino.all()]


async def get_sample_mapping_given_chip_n_pos(TECHNOLOGY, chip_id, position):
    smp_results = await get_sample_mapping_to_update_qc_status(
        TECHNOLOGY, chip_id, position
    )
    field_names = get_field_names_w_additional_cols(
        SampleMapping, ["type", "plate_name"]
    )
    res_data = [
        {field_names[idx]: res[idx] for idx in range(len(field_names))}
        for res in smp_results
    ]
    sample_mapping_id = res_data[0]["id"]

    return sample_mapping_id


async def get_sample_mapping_given_lid_n_plate_name(TECHNOLOGY, lid, plate_name):
    # smp_results = await get_all_sample_mappings_given_lid(lid=lid)
    smp_results = await get_all_sample_mapping_ids_given_lid_and_plate_name(
        type=TECHNOLOGY, lid=lid, plate_name=plate_name
    )
    # field_names = get_field_names_w_additional_cols(SampleMapping, ['lid'])
    field_names = get_field_names_w_additional_cols(None, ["sample_mapping_id"])
    res_data = [
        {field_names[idx]: res[idx] for idx in range(len(field_names))}
        for res in smp_results
    ]
    # sample_mapping_id = res_data[0]['id']
    sample_mapping_id = res_data[0]["sample_mapping_id"]

    return sample_mapping_id


async def count_matched_sample_mappings(
    chip_id: Optional[str] = None,
    position: Optional[str] = None,
    dna_extraction_id: Optional[int] = None,
    type: Optional[str] = None,
    plate_name: Optional[str] = None,
    well_position: Optional[str] = None,
):
    results = db.select([db.func.count()]).select_from(
        SampleMapping.join(Plate, SampleMapping.plate_id == Plate.id)
    )
    results = results.where(Plate.deleted_at == None)

    if type:
        results = results.where(Plate.type == type.upper())
    if plate_name:
        results = results.where(Plate.name.contains(f"%{plate_name}"))
    if well_position:
        results = results.where(
            SampleMapping.well_position.contains(f"%{well_position}")
        )
    if chip_id:
        results = results.where(SampleMapping.chip_id.contains(f"%{chip_id}"))
    if position:
        results = results.where(SampleMapping.position.contains(f"%{position}"))
    if dna_extraction_id:
        results = results.where(SampleMapping.dna_extraction_id == dna_extraction_id)

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


async def get_all_sample_mappings(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    chip_id: Optional[str] = None,
    position: Optional[str] = None,
    dna_extraction_id: Optional[int] = None,
    type: Optional[str] = None,
    plate_name: Optional[str] = None,
    well_position: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    # results = SampleMapping.query
    results = db.select([SampleMapping, Plate.type, Plate.name]).select_from(
        SampleMapping.join(Plate, SampleMapping.plate_id == Plate.id)
    )
    results = results.where(Plate.deleted_at == None)

    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if type:
        results = results.where(Plate.type == type.upper())
    if plate_name:
        results = results.where(Plate.name.contains(f"%{plate_name}"))
    if well_position:
        results = results.where(
            SampleMapping.well_position.contains(f"%{well_position}")
        )
    if chip_id:
        results = results.where(SampleMapping.chip_id.contains(f"%{chip_id}"))
    if position:
        results = results.where(SampleMapping.position.contains(f"%{position}"))
    if dna_extraction_id:
        results = results.where(SampleMapping.dna_extraction_id == dna_extraction_id)
    if order_by:
        results = results.order_by(getattr(SampleMapping, order_by).desc())
    total = await count_matched_sample_mappings(
        chip_id=chip_id,
        position=position,
        dna_extraction_id=dna_extraction_id,
        type=type,
        plate_name=plate_name,
        well_position=well_position,
    )
    return [r for r in await results.gino.all()], total


async def get_all_sample_mappings_given_lid(lid: Optional[str] = None):
    results = db.select([SampleMapping, DnaExtraction.lid.label("lid")]).select_from(
        SampleMapping.join(
            DnaExtraction, SampleMapping.dna_extraction_id == DnaExtraction.id
        )
    )
    if lid:
        results = results.where(DnaExtraction.lid == lid)

    return [r for r in await results.gino.all()]
    pass


async def get_all_sample_mappings_and_plate_status(
    lid: Optional[str] = None,
    type: Optional[str] = None,
    plate_name: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    # results = SampleMapping.query
    sub_q = db.select(
        [
            SampleMapping.dna_extraction_id.label("dna_extraction_id"),
            Plate.type.label("type"),
            Plate.name.label("plate_name"),
            Plate.status.label("status"),
        ]
    ).select_from(SampleMapping.outerjoin(Plate, SampleMapping.plate_id == Plate.id))
    sub_q = sub_q.where(Plate.deleted_at == None)

    if type:
        sub_q = sub_q.where(Plate.type == type.upper())
    if plate_name:
        sub_q = sub_q.where(Plate.name.contains(f"%{plate_name}"))

    sub_q = sub_q.alias()

    results = db.select(
        [
            DnaExtraction.lid.label("lid"),
            DnaExtraction.id.label("dna_extraction_id"),
            sub_q.c.type,
            sub_q.c.plate_name,
            sub_q.c.status,
        ]
    ).select_from(
        DnaExtraction.outerjoin(sub_q, sub_q.c.dna_extraction_id == DnaExtraction.id)
    )

    if lid:
        results = results.where(DnaExtraction.lid.contains(f"%{lid}"))

    return [r for r in await results.gino.all()]


async def get_all_sample_mapping_ids_given_lid_and_plate_name(
    lid: Optional[str] = None,
    type: Optional[str] = None,
    plate_name: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    # results = SampleMapping.query
    sub_q = db.select(
        [
            SampleMapping.id.label("sample_mapping_id"),
            SampleMapping.dna_extraction_id.label("dna_extraction_id"),
            Plate.type.label("type"),
            Plate.name.label("plate_name"),
            Plate.status.label("status"),
        ]
    ).select_from(SampleMapping.join(Plate, SampleMapping.plate_id == Plate.id))
    sub_q = sub_q.where(Plate.deleted_at == None)

    if type:
        sub_q = sub_q.where(Plate.type == type.upper())
    if plate_name:
        sub_q = sub_q.where(Plate.name.contains(f"%{plate_name}"))

    sub_q = sub_q.alias()

    results = db.select(
        [
            sub_q.c.sample_mapping_id,
        ]
    ).select_from(
        DnaExtraction.join(sub_q, sub_q.c.dna_extraction_id == DnaExtraction.id)
    )

    if lid:
        results = results.where(DnaExtraction.lid.contains(f"%{lid}"))

    return [r for r in await results.gino.all()]


async def get_all_chip_id_bind_to_plate_name(
    type: Optional[str] = None,
    plate_name: Optional[str] = None,
):
    results = db.select([SampleMapping.chip_id, Plate.type, Plate.name]).select_from(
        SampleMapping.join(Plate, SampleMapping.plate_id == Plate.id)
    )
    results = results.where(Plate.deleted_at == None)

    if type:
        results = results.where(Plate.type == type.upper())

    if plate_name:
        results = results.where(Plate.name.contains(f"%{plate_name}"))

    results = results.distinct(SampleMapping.chip_id).group_by(
        SampleMapping.chip_id, Plate.type, Plate.name
    )

    return [r for r in await results.gino.all()]


async def existed_sample_mapping_dna_extraction_id(dna_extraction_id: int) -> bool:
    total = (
        await db.select([db.func.count()])
        .where(SampleMapping.dna_extraction_id == dna_extraction_id)
        .gino.scalar()
    )
    return True if total > 0 else False


# LabSample
async def get_tech_via_lab_sample(lid: str) -> LabSample:
    lab_sample = await LabSample.get(lid)
    print("Tech of Sample: ", lab_sample.technology)
    return lab_sample.technology


async def get_chip_by_chip_id(chip_id: str) -> Chip:
    results = Chip.query.where(Chip.chip_id == chip_id)
    chip = [r for r in await results.gino.all()][0]
    print("Tech of Chip: ", chip.technology)
    return chip.technology


async def matching_tech(lid, chip_id):
    sample_technology = await get_tech_via_lab_sample(lid)
    chip_technology = await get_chip_by_chip_id(chip_id)
    return sample_technology.lower() == chip_technology.lower()


async def create_sample_mapping(technology: str, data: dict):
    if not await existed_plate_name(technology, data.get("plate_name")):
        err = f"Error: Plate with name {data.get('plate_name')} using technology {technology} is not existed"
        return data, err
    current_time = get_current_date_time_utc_7()
    adding_plate = await get_plate_by_name(technology, data.get("plate_name"))

    added_data = {
        "plate_id": adding_plate.id,
        "well_position": data.get("well_position"),
        "dna_extraction_id": data.get("dna_extraction_id"),
        "created_at": current_time,
        "updated_at": current_time,
    }
    result = await SampleMapping.create(**added_data)
    return result.to_dict(), None


async def create_sample_mapping_n_pc_sample_mapping_w_plate_id(
    data: dict, plate_id: str, is_pc_sample: bool
):
    # CHECK IF current DNA_EXTRACTION_ID
    sample_mapping = await get_sample_mapping_by_dna_extraction_id(
        dna_extraction_id=data.get("dna_extraction_id")
    )
    if sample_mapping:
        curr_plate = await get_plate_via_uuid(sample_mapping.plate_id)
        if curr_plate.status and is_pc_sample:
            logger.info(f"Replicate DNA_EXTRACTION_ID {data['dna_extraction_id']}")
            dna_extraction, _ = await replicate_positive_control_dna_extraction_w_id(
                dna_extraction_id=data["dna_extraction_id"]
            )
            data["dna_extraction_id"] = dna_extraction.get("id")
        else:
            if curr_plate.status in config["EXPORT_PLATE_STATUS"]:
                err = f"Error: DNA_EXTRACTION_ID belong to {curr_plate.status} PLATE w/ name {curr_plate.name}!"
                raise ValueError(err)
            if curr_plate.status in config["AVAILABLE_CHIP_STATUS"]:
                err = f"Error: DNA_EXTRACTION_ID AREADY ADDED TO ANNOTHER {curr_plate.status} PLATE w/ name {curr_plate.name}!"
                raise ValueError(err)

        if await existed_sample_mapping_w_dna_extraction_id_and_plate_id(
            dna_extraction_id=data.get("dna_extraction_id"), plate_id=plate_id
        ):
            err = f"Error: SAMPLE with {data.get('dna_extraction_id')} already belonged to PLATE w/ name {curr_plate.name}!"
            raise ValueError(err)

    if not data:
        err = "Error: Create sample_mapping w/ empty data!"
        raise ValueError(err)

    current_time = get_current_date_time_utc_7()

    added_data = {
        "plate_id": plate_id,
        "well_position": data.get("well_position"),
        "dna_extraction_id": data.get("dna_extraction_id"),
        "chip_id": data.get("chip_id"),
        "position": data.get("position"),
        "created_at": current_time,
        "updated_at": current_time,
    }
    result = await SampleMapping.create(**added_data)
    return result.to_dict(), None


async def existed_sample_mapping_w_dna_extraction_id_and_plate_id(
    dna_extraction_id: int, plate_id: str
) -> bool:
    total = (
        await db.select([db.func.count()])
        .where(SampleMapping.dna_extraction_id == dna_extraction_id)
        .where(SampleMapping.plate_id == plate_id)
        .gino.scalar()
    )
    return True if total > 0 else False


async def create_sample_mapping_w_plate_id(data: dict, plate_id: str):
    # CHECK IF current DNA_EXTRACTION_ID
    sample_mapping = await get_sample_mapping_by_dna_extraction_id(
        dna_extraction_id=data.get("dna_extraction_id")
    )
    if sample_mapping:
        curr_plate = await get_plate_via_uuid(sample_mapping.plate_id)
        if curr_plate.status in config["EXPORT_PLATE_STATUS"]:
            err = f"Error: DNA_EXTRACTION_ID belong to {curr_plate.status} PLATE w/ name {curr_plate.name}!"
            raise ValueError(err)
        if curr_plate.status in config["AVAILABLE_CHIP_STATUS"]:
            err = f"Error: DNA_EXTRACTION_ID AREADY ADDED TO ANNOTHER {curr_plate.status} PLATE w/ name {curr_plate.name}!"
            raise ValueError(err)

        if existed_sample_mapping_w_dna_extraction_id_and_plate_id(
            dna_extraction_id=data.get("dna_extraction_id"), plate_id=plate_id
        ):
            err = f"Error: SAMPLE with {data.get('dna_extraction_id')} already belonged to PLATE w/ name {curr_plate.name}!"
            raise ValueError(err)

    if not data:
        err = "Error: Create sample_mapping w/ empty data!"
        raise ValueError(err)

    current_time = get_current_date_time_utc_7()

    added_data = {
        "plate_id": plate_id,
        "well_position": data.get("well_position"),
        "dna_extraction_id": data.get("dna_extraction_id"),
        "chip_id": data.get("chip_id"),
        "position": data.get("position"),
        "created_at": current_time,
        "updated_at": current_time,
    }
    result = await SampleMapping.create(**added_data)
    return result.to_dict(), None


async def create_many_sample_mappings(data_arrs):
    results = []
    async with db.transaction() as tx:
        for data in data_arrs:
            logger.info(f"Add sample into chip with date formated data {data}")
            data, err = await create_sample_mapping(data)
            if err:
                return None, err
            results.append(data)
    return results, None


async def create_many_sample_mappings_w_plate_name_n_pc_sample_idx(
    technology, data_arrs, pc_sample_idx, plate_name
):
    results = []
    if not await existed_plate_name(technology, plate_name):
        err = f"Error: Plate with name {plate_name} using {technology} is not existed"
        raise ValueError(err)
    curr_plate = await get_plate_by_name(technology, plate_name)
    async with db.transaction() as tx:
        for idx, data in enumerate(data_arrs):
            if idx != pc_sample_idx and data.get("well_position") == "H12":
                err = "Error: Only PC sample can be placed in H12 position"
                raise ValueError(err)
            if idx == pc_sample_idx and data.get("well_position") != "H12":
                err = "Error: Only PC sample can be placed in H12 position"
                raise ValueError(err)

            logger.info(f"Add sample into chip with date formated data {data}")
            if idx == pc_sample_idx:
                data = await create_sample_mapping_n_pc_sample_mapping_w_plate_id(
                    data, curr_plate.id, True
                )
            else:
                data = await create_sample_mapping_n_pc_sample_mapping_w_plate_id(
                    data, curr_plate.id, False
                )
            results.append(data)
    return results


async def create_many_sample_mappings_w_plate_name(technology, data_arrs, plate_name):
    results = []
    if not await existed_plate_name(technology, plate_name):
        err = f"Error: Plate with name {plate_name} using {technology} is not existed"
        raise ValueError(err)
    curr_plate = await get_plate_by_name(technology, plate_name)
    async with db.transaction() as tx:
        for data in data_arrs:
            logger.info(f"Add sample into chip with date formated data {data}")
            data = await create_sample_mapping_w_plate_id(data, curr_plate.id)
            results.append(data)
    return results


async def transfer_samples_to_chip_an(chip_id, curr_samples_in_chip, technology, type):
    results = []
    async with db.transaction() as tx:
        for sm in curr_samples_in_chip:
            err = await add_sample_to_chip_an(sm, chip_id)
            sm["chip_id"] = chip_id
            # TRANSFER SAMPLE TO CHIP WITH SAME POSITION ON PLATE
            sm["position"] = sm.get("well_position")
            results.append(sm)
            if err:
                return None, err
    return results, None


async def add_sample_to_chip_an(data, chip_id):
    # TRANSFER SAMPLE TO CHIP WITH SAME POSITION ON PLATE
    position = data.get("well_position")
    dna_extraction_id = data.get("dna_extraction_id")
    if dna_extraction_id:
        sample_mapping = await get_sample_mapping_by_dna_extraction_id(
            dna_extraction_id=dna_extraction_id
        )
        if sample_mapping:
            update_info = {"chip_id": chip_id, "position": position}
            # print('Updated info: ', update_info)
            await sample_mapping.update(**update_info).apply()
            logger.info(
                f"Sample Mapping with DNA_EXTRACTION_ID {dna_extraction_id} updated with {chip_id} and {position}"
            )
            return None
        else:
            err = f"Error: Sample mapping with DNA_EXTRACTION_ID {dna_extraction_id} not found!!!"
            return err
    else:
        err = "Error: Sample without DNA_EXTRACTION_ID !!!"
        return err


async def transfer_samples_to_chip_rc(
    chip_id, col_index, sample_mappings, ROW_FORMAT, COL_FORMAT
):
    results = []
    async with db.transaction() as tx:
        for row_index, sm in enumerate(sample_mappings):
            position = get_position_given_format(
                row_index, col_index, ROW_FORMAT, COL_FORMAT
            )
            # print('position: ',position)
            # print('sample_mapping: ',sm)
            # print('type: ',type(sm))
            err = await add_sample_to_chip_rc(sm, chip_id, position)
            if err:
                return None, err
            sm["chip_id"] = chip_id
            sm["position"] = position
            results.append(sm)
    # print('FINISHING TRANSFERRING: ', results)
    return results, None


async def add_sample_to_chip_rc(data, chip_id, position):
    dna_extraction_id = data.get("dna_extraction_id")
    if dna_extraction_id:
        sample_mapping = await get_sample_mapping_by_dna_extraction_id(
            dna_extraction_id=dna_extraction_id
        )
        if sample_mapping:
            update_info = {"chip_id": chip_id, "position": position}
            # print('Updated info: ', update_info)
            await sample_mapping.update(**update_info).apply()
            logger.info(
                f"Sample Mapping with DNA_EXTRACTION_ID {dna_extraction_id} updated with {chip_id} and {position}"
            )
            return None
        else:
            err = f"Error: Sample mapping with DNA_EXTRACTION_ID {dna_extraction_id} not found!!!"
            return err
    else:
        err = "Error: Sample without DNA_EXTRACTION_ID !!!"
        return err


async def update_sample_mapping_w_id(id: str, data: dict):
    sample_mapping = await get_sample_mapping_by_id(id=id)
    if not sample_mapping:
        err = f"Error: NOT FOUND sample_mapping w/ id {id}!"
        raise ValueError(err)

    current_time = get_current_date_time_utc_7()
    update_info = {**data, "created_at": current_time, "updated_at": current_time}
    await sample_mapping.update(**update_info).apply()
    logger.info(f"UPDATE sample_mapping with ID {id} with data {data}")


async def update_kit_status_w_sample_mapping_ids(sample_mappings):
    async with db.transaction() as tx:
        for id, qc_status in sample_mappings:
            sample_mapping = await get_sample_mapping_by_id(id=id)
            dna_extraction = await get_dna_extraction(
                id=sample_mapping.dna_extraction_id
            )
            lab_sample = await get_lab_sample_w_lid(lid=dna_extraction.lid)
            kit = await get_kit(barcode=lab_sample.barcode)
            current_time = get_current_date_time_utc_7()
            data = {
                "current_status": "PASSED_WELAB"
                if qc_status == "PASS"
                else "FAILED_WELAB",
                "created_time": current_time,
                "updated_time": current_time,
            }
            logger.info(
                f"Update sample_mapping_id {id} of KIT barcode {lab_sample.barcode}: COMPLETED"
            )
            _ = await update_kit_with_default_link(kit, data)
            pass
    pass


async def update_sample_mappings_qc_status_w_params(
    TECHNOLOGY, identity_col, rows, updated_col_names, plate_name
):
    results = []

    async with db.transaction() as tx:
        for row in rows:
            sample_mapping_id = None
            try:
                if TECHNOLOGY == "MICROARRAY":
                    chip_id, position = get_chip_n_pos_from_gtc_file(
                        gtc_file_name=get_file_name(row[identity_col])
                    )
                    sample_mapping_id = await get_sample_mapping_given_chip_n_pos(
                        TECHNOLOGY, chip_id, position
                    )

                elif TECHNOLOGY == "PCR":
                    print(f"LID {row[identity_col]} PLATE_NAME {plate_name}")
                    sample_mapping_id = await get_sample_mapping_given_lid_n_plate_name(
                        TECHNOLOGY=TECHNOLOGY,
                        lid=row[identity_col],
                        plate_name=plate_name,
                    )
            except IndexError:
                err = f"CHIP {chip_id} not filled with position {position}"
                logger.warning(err)
                continue

            if not sample_mapping_id:
                err = "Error: SAMPLE_MAPPING_ID NOT FOUND!!"
                raise ValueError(err)

            qc_status = calculate_qc_status_given_params(TECHNOLOGY, row.values())
            data = get_updated_qc_status_for_sample_mapping(
                TECHNOLOGY=TECHNOLOGY,
                row=row,
                updated_col_names=updated_col_names,
                qc_status=qc_status,
            )
            await update_sample_mapping_w_id(id=sample_mapping_id, data=data)
            results.append((sample_mapping_id, qc_status))
            # logger.info(f"Update sample_mapping id {sample_mapping_id}: {data}")

        pass

    return results
    pass


async def delete_sample_mapping(dna_extraction_id: int):
    current_time = get_current_date_time_utc_7()
    if not await existed_sample_mapping_dna_extraction_id(dna_extraction_id):
        err = f"chip with chip_id: {dna_extraction_id} cannot be found"
        return None, err

    # Check SampleMapping.dna_extraction_id exist ? before delete?

    return await SampleMapping.delete.where(
        SampleMapping.dna_extraction_id == dna_extraction_id,
    ).gino.status()


async def delete_many_sample_mappings(existed_sample_mappings):
    results = []
    async with db.transaction() as tx:
        for data in existed_sample_mappings:
            logger.info(f"Deleted sample_mapping data {data}")
            _, err = await delete_sample_mapping(data.get("dna_extraction_id"))
            if err:
                raise ValueError(err)
            results.append(data)
    return results, None


async def delete_sample_mappings_w_plate_name(technology, plate_name: str):
    if not await existed_plate_name(technology, plate_name):
        err = f"Error: Plate with name {plate_name} using {technology} is not existed"
        raise ValueError(err)
    curr_plate = await get_plate_by_name(technology, plate_name)
    return await SampleMapping.delete.where(
        SampleMapping.plate_id == curr_plate.id,
    ).gino.status()


async def delete_sample_mappings_w_plate_name_n_chips(
    technology, plate_name: str, chip_ids: list
):
    if not await existed_plate_name(technology, plate_name):
        err = f"Error: Plate with name {plate_name} using {technology} is not existed"
        raise ValueError(err)
    if not chip_ids:
        err = "Error: Passing empty CHIP IDs"
        raise ValueError(err)

    async with db.transaction() as tx:
        for chip_id in chip_ids:
            logger.info(f"Deleted chip_id {chip_id}")
            await Chip.delete.where(Chip.chip_id == chip_id).gino.status()
        pass

    curr_plate = await get_plate_by_name(technology, plate_name)
    return await SampleMapping.delete.where(
        SampleMapping.plate_id == curr_plate.id,
    ).gino.status()
