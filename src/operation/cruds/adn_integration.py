import uuid
from typing import Optional
from ..models.models import AdnIntegration, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)
from .request_adn_integration import *
# AdnIntegration - model 
# ADN_INTEGRATION - QUERY NAME
# _adn_integration - function name
# adn_integration ai - single
# ai. - SQL alias
# adnIntegration - single
# adn_integrations - plural
# _adn_integrations
# name -- search key

ADN_INTEGRATION_LIST_EXTEND_QUERY_V3 = """
    select
        ai.id,
        rai.request_id,
        ai.samplecode,
        ai.barcode,
        ls.lid,
        ai.type_id,
        ai.status,
        k.current_status,
        ai.presigned_s3_url,
        ai.raw_adn_s3_obj_key,
        ai.review_required,
        ait.name,
        ait.adn_type,
        ait.method,
        ai.response_date,
        ai.created_at,
        ai.updated_at,
        ai.deleted_at
    from
        adn_integration ai
        INNER JOIN adn_integration_type ait ON ai.type_id = ait.id
        LEFT JOIN kit k ON ai.barcode = k.barcode
        LEFT JOIN lab_sample ls ON k.barcode = ls.barcode
        LEFT JOIN request_adn_integration rai on ai.id = rai.adn_integration_id
        LEFT JOIN request r on rai.request_id = r.id 
"""

ADN_INTEGRATION_LIST_EXTEND_COUNT_QUERY_V3 = """
    select
        count(ai.id)
    from
        adn_integration ai
        INNER JOIN adn_integration_type ait ON ai.type_id = ait.id
        LEFT JOIN kit k ON ai.barcode = k.barcode
        LEFT JOIN lab_sample ls ON k.barcode = ls.barcode
        LEFT JOIN request_adn_integration rai on ai.id = rai.adn_integration_id
        LEFT JOIN request r on rai.request_id = r.id 
"""

async def get_adn_integration_by_id(id: uuid.uuid4):
    adnIntegration = await AdnIntegration.get(id)
    return adnIntegration


async def get_all_adn_integrations(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="ai.created_at",
    adn_integration_id: Optional[str] = None, 
    request_id: Optional[str] = None, 
    samplecode: Optional[str] = None,
    barcode: Optional[str] = None,
    method: Optional[str] = None,
    status: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    query_filter = ""
    if adn_integration_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.id = '{str(adn_integration_id)}' "
    if request_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"rai.request_id = '{str(request_id)}' "
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.samplecode = '{str(samplecode)}' "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.barcode = '{str(barcode)}' "
    if method:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.method = '{str(method)}' "
    if status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.status = '{str(status)}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.deleted_at is null "
    
    raw_count_query = ADN_INTEGRATION_LIST_EXTEND_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else ADN_INTEGRATION_LIST_EXTEND_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = ADN_INTEGRATION_LIST_EXTEND_QUERY_V3 + query_filter
    else:
        raw_query = ADN_INTEGRATION_LIST_EXTEND_QUERY_V3 + "where " + query_filter if query_filter != "" else ADN_INTEGRATION_LIST_EXTEND_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

ADN_INTEGRATION_LIST_ONLY_QUERY_V3 = """
    select
        ai.id,
        ai.samplecode,
        ai.barcode,
        ai.type_id,
        ai.status,
        k.current_status,
        ai.presigned_s3_url,
        ai.raw_adn_s3_obj_key,
        ai.review_required,
        ait.name,
        ait.adn_type,
        ait.method,
        ai.response_date,
        ai.created_at,
        ai.updated_at,
        ai.deleted_at
    from
        adn_integration ai
        INNER JOIN adn_integration_type ait ON ai.type_id = ait.id
        LEFT JOIN kit k ON ai.barcode = k.barcode
"""

ADN_INTEGRATION_LIST_ONLY_COUNT_QUERY_V3 = """
    select
        count(ai.id)
    from
        adn_integration ai
"""

async def get_adn_integrations_w_codes_and_type(
    samplecode: Optional[str] = None,
    barcode: Optional[str] = None,
    adn_type: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    query_filter = ""
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.samplecode = '{str(samplecode)}' "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.barcode = '{str(barcode)}' "
    if adn_type:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ait.adn_type = '{str(adn_type)}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ai.deleted_at is null "
    
    raw_query = ADN_INTEGRATION_LIST_ONLY_QUERY_V3 + "where " + query_filter if query_filter != "" else ADN_INTEGRATION_LIST_ONLY_QUERY_V3
    query_filter += f"order by ai.created_at desc "
    results = await db.all(db.text(raw_query))
    return results[0]

async def is_adn_integration_existed(samplecode: str, barcode: str, type_id: str):
    total = await db.select([db.func.count()]).where(AdnIntegration.samplecode == samplecode).where(AdnIntegration.barcode == barcode).where(AdnIntegration.type_id == type_id).where(AdnIntegration.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def get_adn_integration_by_samplecode_and_type_id(samplecode: str, barcode: str, type_id: str):
    # integrationReq: Request
    adn_integration = await AdnIntegration.query.where(AdnIntegration.samplecode == samplecode).where(AdnIntegration.barcode == barcode).where(AdnIntegration.type_id == type_id).where(AdnIntegration.deleted_at == None).gino.first()
    return adn_integration

async def create_adn_integration_w_request_id(data: dict, request_id: str):
    
    """
    samplecode: str
    barcode: Optional[str]    
    type_id: Optional[str]
    status: str
    presigned_s3_url: Optional[str]
    raw_adn_s3_obj_key: Optional[str]
    review_required: bool = True
    
    """
    # print(f"Befoooooooooooo {request_id}: ", data)
    if await is_adn_integration_existed(samplecode=data['samplecode'], barcode=data['barcode'], type_id=data['type_id']):
        adn_integration = await get_adn_integration_by_samplecode_and_type_id(samplecode=data['samplecode'], barcode=data['barcode'], type_id=data['type_id'])
        if await is_request_adn_integration_existed(request_id=request_id,adn_integration_id=adn_integration.id):
            err = f" AdnIntegration {data['samplecode']} and barcode {data['barcode']} with Request {request_id} is already created"
            http_code = HTTP_409_CONFLICT
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        else:
            adn_integration = adn_integration.to_dict()
            request_adn_integration = {
                "request_id": request_id,
                "adn_integration_id": adn_integration.get('id')
            }
            
            request_adn_integration = await create_request_adn_integration(data=request_adn_integration)
            return adn_integration, request_adn_integration
    else:
        
        current_time = get_current_date_time_utc_7()
        data = {
            'samplecode': data['samplecode'],
            'barcode': data['barcode'],
            'type_id': data['type_id'], 
            'status': data['status'],
            'presigned_s3_url': data['presigned_s3_url'],
            'raw_adn_s3_obj_key': data['raw_adn_s3_obj_key'],
            'review_required': data['review_required'],
            'created_at': current_time,
            'updated_at': current_time
        }
        result = await AdnIntegration.create(**data)
        adn_integration = result.to_dict()
        
        request_adn_integration = {
            "request_id": request_id,
            "adn_integration_id": adn_integration.get('id')
        }
        
        request_adn_integration = await create_request_adn_integration(data=request_adn_integration)
        return adn_integration, request_adn_integration

async def create_adn_integration(data: dict):
    
    """
    samplecode: str
    barcode: Optional[str]    
    type_id: Optional[str]
    status: str
    presigned_s3_url: Optional[str]
    raw_adn_s3_obj_key: Optional[str]
    review_required: bool = True
    
    """
    
    if await is_adn_integration_existed(samplecode=data['samplecode'], barcode=data['barcode'], type_id=data['type_id']):
        err = f"Integration AdnIntegration with samplecode {data['samplecode']} and barcode {data['barcode']} is already created"
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        'samplecode': data['samplecode'],
        'barcode': data['barcode'],
        'type_id': data['type_id'], 
        'status': data['status'],
        'presigned_s3_url': data['presigned_s3_url'],
        'raw_adn_s3_obj_key': data['raw_adn_s3_obj_key'],
        'review_required': data['review_required'],
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await AdnIntegration.create(**data)
    return result.to_dict()
    pass

async def update_adn_integration(adnIntegration: AdnIntegration, data: dict):
    if adnIntegration.deleted_at:
        err = f"Integration request with barcode: {adnIntegration.barcode} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await adnIntegration.update(**data).apply()
    logger.info(f"Integration request with barcode: {adnIntegration.barcode} updated")
    return None


async def delete_adn_integration(adnIntegration: AdnIntegration):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not adnIntegration:
        err = f"Integration request with name: {adnIntegration.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    elif adnIntegration.deleted_at:
        err = f"Integration request with: {adnIntegration.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await adnIntegration.update(**data).apply()
    logger.info(f"Integration request with name: {adnIntegration.name} deleted")
