import re
from typing import Optional, List

from sqlalchemy import and_

from ..models.models import IdentityCard, db, Subject
from ..schemas.kit import IdentityCardResponse
from ..utils.utils import get_current_date_time_utc_7, get_account_by_cs_id

ID_CARD_QUERY_V3 = """
    SELECT
        identifier_code,
        full_name,
        dob::date,
        gender,
        nationality,
        origin,
        residence,
        ethnic,
        email,
        phone_number,
        manual_input,
        created_at,
        deleted_at,
        samplecode,
        barcode,
        "tenDonViThuNhan<PERSON>au",
        idDonViThuNhanMau,
        customer_support_id,
        customer_support_name,
        note,
        sample_collector_name,
        martyr_name,
        martyr_relationships,
        sponsor_name,
        kit_status,
        scan_status
    FROM (
        SELECT
            idc.identifier_code,
            idc.full_name,
            idc.dob,
            idc.gender,
            idc.nationality,
            idc.origin,
            idc.residence,
            idc.ethnic,
            idc.email,
            idc.phone_number,
            idc.manual_input,
            idc.created_at,
            idc.deleted_at,
            k.samplecode,
            k.barcode,
            k.current_status as kit_status,
            sam.sample_collector_name,
            sam.scan_status,
            k.note,
            tu."tenDonVi" as "tenDonViThu<PERSON>han<PERSON>au",
            tu."id" as idDonViThu<PERSON>hanMau,
            idc.customer_support_id,
            idc.customer_support_name,
            sub.martyr_name,
            sub.martyr_relationships,
            sp.name as sponsor_name,
            ROW_NUMBER() OVER (PARTITION BY idc.identifier_code ORDER BY k.created_at ASC) AS rn
        FROM
            identity_card idc
            INNER JOIN subject sub ON idc.identifier_code = sub.identifier_code
            INNER JOIN sample sam ON sub.id = sam.subject_id
            LEFT JOIN sponsors sp ON sp.id = sam.sponsor_id

            INNER JOIN (
                SELECT DISTINCT ON (samplecode) *
                FROM kit
                ORDER BY samplecode, created_at ASC
            ) AS k ON sam.samplecode = k.samplecode
            left join tracking_collection tc ON sam.samplecode = tc."maThuNhan"
            INNER JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
    ) AS ranked_data
"""

COUNT_ID_CARD_QUERY_v3 = """
    SELECT
        count(identifier_code)
    FROM (
        SELECT
            idc.identifier_code,
            idc.full_name,
            idc.dob,
            idc.gender,
            idc.nationality,
            idc.origin,
            idc.residence,
            idc.ethnic,
            idc.email,
            idc.phone_number,
            idc.created_at,
            idc.deleted_at,
            k.samplecode,
            k.barcode,
            tu."tenDonVi" as "tenDonViThuNhanMau",
            tu."id" as idDonViThuNhanMau,
            idc.customer_support_id,
            idc.customer_support_name,
            sam.sample_collector_name,
            ROW_NUMBER() OVER (PARTITION BY idc.identifier_code ORDER BY k.created_at ASC) AS rn
        FROM
            identity_card idc
            INNER JOIN subject sub ON idc.identifier_code = sub.identifier_code
            INNER JOIN sample sam ON sub.id = sam.subject_id
            INNER JOIN (
                SELECT DISTINCT ON (samplecode) *
                FROM kit
                ORDER BY samplecode, created_at ASC
            ) AS k ON sam.samplecode = k.samplecode
            left join tracking_collection tc ON sam.samplecode = tc."maThuNhan"
            INNER JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
    ) AS ranked_data
"""


# get all id cards
async def get_identity_cards(
    offset: Optional[int],
    size: Optional[int],
    order_by: Optional[str],
    identifier_code: str,
    samplecode: str,
    full_name: str,
    dob: str,
    gender: str,
    customer_support_id: Optional[str],
    phone_number: Optional[str] = None,
    tenDonViThuNhanMau: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    is_scanned: Optional[bool] = None,
    sample_collector_name: Optional[str] = None,
    customer_support_ids: Optional[List[str]] = [],
    idDonViThuNhanMau: Optional[List[str]] = [],
    bearer_token_credential: Optional[str] = None
):
    query_filter = "rn = 1 and deleted_at is null "
    if identifier_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"identifier_code like '%{identifier_code}%' "
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"samplecode like '%{samplecode}%' "
    if tenDonViThuNhanMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f""" lower("tenDonViThuNhanMau") = '{tenDonViThuNhanMau.lower()}' """
        )
    if idDonViThuNhanMau:
        query_filter += "and " if query_filter != "" else " "
        ids_str = ",".join(f"'{id}'" for id in idDonViThuNhanMau)
        query_filter += f"idDonViThuNhanMau in ({ids_str})"
    if full_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(full_name) like '%{full_name.lower()}%' "
    if dob:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"dob::date = date('{dob}') "
    if gender:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"gender = '{gender}' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"phone_number like '%{phone_number}%' "
    if customer_support_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"customer_support_id = '{customer_support_id}' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_at::date >= '{start_date}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_at::date <= '{end_date}' "
    if is_scanned is not None:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"barcode {'is not' if is_scanned else 'is'} null "
    if sample_collector_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sample_collector_name) like '%{sample_collector_name.lower()}%' "

    if customer_support_ids:
        query_filter += "and " if query_filter != "" else " "
        ids_str = ",".join(f"'{id}'" for id in customer_support_ids)
        query_filter += f"customer_support_id in ({ids_str})"

    raw_count_query = (
        COUNT_ID_CARD_QUERY_v3 + "where " + query_filter
        if query_filter != ""
        else COUNT_ID_CARD_QUERY_v3
    )
    # print("raw_count_query: ", raw_count_query)
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if (order_by == "created_time") or not order_by:
        order_by = "created_at"
    query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = ID_CARD_QUERY_V3 + query_filter
    else:
        raw_query = (
            ID_CARD_QUERY_V3 + "where " + query_filter
            if query_filter != ""
            else ID_CARD_QUERY_V3
        )
    # print("raw_query: ", raw_query)
    modified_results = []

    results = await db.all(db.text(raw_query))

    account_mapping = {}

    for row in results:
        updated_row = dict(row)
        updated_row["martyr_name"] = parse_field(row["martyr_name"])
        updated_row["martyr_relationships"] = parse_field(format_relationships(row["martyr_relationships"]))
        modified_results.append(updated_row)
        if updated_row["customer_support_id"] not in account_mapping:
            account = await get_account_by_cs_id(
                id=updated_row["customer_support_id"], credential=bearer_token_credential
            )
            account_mapping[updated_row["customer_support_id"]] = account
        else:
            account = account_mapping[updated_row["customer_support_id"]]

        updated_row["customer_support_account"] = account

    return modified_results, total

def parse_field(martyr_raw):
    if martyr_raw and str(martyr_raw).startswith("[") and str(martyr_raw).endswith("]") and "," in str(martyr_raw):
        items = str(martyr_raw)[1:-1].split(",")
        items = [item.strip().strip('"').strip("'") for item in items]
        return ", ".join(f"{item} ({i + 1})" for i, item in enumerate(items))
    elif martyr_raw and str(martyr_raw).startswith("[") and str(martyr_raw).endswith("]") and "," not in str(martyr_raw):
        return str(martyr_raw)[2:-2]
    elif martyr_raw:
        return martyr_raw
    else:
        return ""

def format_relationships(martyr_relationships):
    standard_mapping = {
        "Anh trai Liệt sĩ": "UT3",
        "Em trai Liệt sĩ": "UT3",
        "Anh trai của mẹ đẻ Liệt sĩ": "UT4",
        "Em trai của mẹ đẻ Liệt sĩ": "UT4",
        "Con trai của chị gái của mẹ đẻ Liệt sĩ": "UT5",
        "Con trai của em gái của mẹ đẻ Liệt sĩ": "UT5",
        "Con trai của chị gái Liệt sĩ": "UT6",
        "Con trai của em gái Liệt sĩ": "UT6",
        "Mẹ đẻ Liệt sĩ": "UT1",
        "Bà ngoại Liệt sĩ": "UT2",
        "Chị gái Liệt sĩ": "UT3",
        "Em gái Liệt sĩ": "UT3",
        "Chị gái của mẹ đẻ Liệt sĩ": "UT4",
        "Em gái của mẹ đẻ Liệt sĩ": "UT4",
        "Con gái của chị gái của mẹ đẻ Liệt sĩ": "UT5",
        "Con gái của em gái của mẹ đẻ Liệt sĩ": "UT5",
        "Con gái của chị gái Liệt sĩ": "UT6",
        "Con gái của em gái Liệt sĩ": "UT6",
        "Chị/em gái Liệt sĩ": "UT3",
        "Anh/em trai Liệt sĩ": "UT3",
        "Chị/em gái của mẹ đẻ Liệt sĩ": "UT4",
        "Anh/em trai của mẹ đẻ Liệt sĩ": "UT4",
        "Con gái của chị/em gái của mẹ đẻ Liệt sĩ": "UT5",
        "Con trai của chị/em gái của mẹ đẻ Liệt sĩ": "UT5",
        "Con gái của chị/em gái Liệt sĩ": "UT6",
        "Con trai của chị/em gái Liệt sĩ": "UT6",
    }

    def process_item(item: str) -> str:
        item = item.strip()
        if re.search(r"\(UT\d+\)", item):
            return item
        base = item.strip()
        ut = standard_mapping.get(base)
        return f"{base} ({ut})" if ut else base

    if isinstance(martyr_relationships, list):
        return [process_item(item) for item in martyr_relationships]
    elif isinstance(martyr_relationships, str):
        items = re.split(r",\s*|\n", martyr_relationships.strip())
        return "\n".join(process_item(item) for item in items if item)
    return ""


# get card detail by cccd number
async def get_identity_card_by_id(
    identifier_code: str,
):
    id_card = await IdentityCard.query.where(
        and_(
            IdentityCard.identifier_code == identifier_code,
            IdentityCard.deleted_at == None
        )
    ).gino.first()
    return id_card


async def get_identity_card_by_id_w_orm(
    identifier_code: str,
):
    id_card = await db.select([
        IdentityCard,
        Subject
    ]).select_from(
        IdentityCard.join(Subject, IdentityCard.identifier_code == Subject.identifier_code)
    ).where(
        and_(
            IdentityCard.identifier_code == identifier_code,
            IdentityCard.deleted_at == None
        )
    ).gino.load(IdentityCard.load(subject=Subject)).first()


    if id_card:
        # Convert the customer_support_id to a string if it exists
        id_card_dict = id_card.to_dict()
        subject = id_card.subject
        if id_card_dict.get("customer_support_id"):
            id_card_dict["customer_support_id"] = str(
                id_card_dict["customer_support_id"]
            )
        response = id_card_dict
        if subject:
            extra_fields = [
                "martyr_name", "martyr_relationships",
                "guardian_name", "guardian_gender",
                "guardian_phone_number", "guardian_identifier_code"
            ]
            for key in extra_fields:
                value = getattr(subject, key)
                if key in ["martyr_name", "martyr_relationships"]:
                    value = str(value) if value is not None else ""
                    response[key] = parse_field(value)
                else:
                    response[key] = value

        # Return the serialized response using the Pydantic model
        return IdentityCardResponse(**response)

        # Return the serialized response using the Pydantic model
        # return IdentityCardResponse.from_orm(id_card)
    return None

def parse_field(martyr_raw):
    if martyr_raw and str(martyr_raw).startswith("[") and str(martyr_raw).endswith("]") and "," in str(martyr_raw):
        items = str(martyr_raw)[1:-1].split(",")
        items = [item.strip().strip('"').strip("'") for item in items]
        return ", ".join(f"{item} ({i + 1})" for i, item in enumerate(items))
    elif martyr_raw and str(martyr_raw).startswith("[") and str(martyr_raw).endswith("]") and "," not in str(martyr_raw):
        return str(martyr_raw)[2:-2]
    elif martyr_raw:
        return martyr_raw
    else:
        return ""

async def existed_identity_card(identifier_code: str) -> bool:
    id_card = await get_identity_card_by_id(identifier_code)
    if id_card:
        return True
    return False


# create id card
async def create_identity_card(identity_card: IdentityCard):
    # subject.id = uuid.uuid4
    identity_card.created_at = get_current_date_time_utc_7()
    identity_card.updated_at = get_current_date_time_utc_7()
    results = await identity_card.create()
    return results.to_dict()


# update contact infor on id card
async def update_identity_card(identity_card: IdentityCard):
    identity_card.updated_at = get_current_date_time_utc_7()
    results = await identity_card.update(**identity_card.to_dict()).apply()
    return results


KITS_ID_CARD_QUERY_V3 = """
    select
        k.id as kit_uuid,
        k.barcode,
        k.nickname,
        k.samplecode,
        idc.identifier_code,
        idc.full_name,
        idc.dob::date,
        idc.gender,
        idc.nationality,
        idc.origin,
        idc.residence,
        idc.ethnic,
        sub.yob,
        sub.user_id,
        sub.phone_number,
        sub.email,
        sub.address,
        k.product_name,
        k.product_code,
        k.product_type,
        k.current_status,
        k.workflow,
        a.id as account_id,
        a.name as account_name,
        s.id as source_id,
        st.name as nominator,
        st.id as nominator_id,
        stf.name as freelancer,
        stf.id as freelancer_id,
        st2.name as sale_pic,
        sam.subject_id,
        sam.sample_collection_date::timestamp,
        sam.sample_collection_time,
        sam.sample_receipt_date::timestamp,
        sam.lab_receipt_date::timestamp,
        sam.sample_collector_name,
        sam.sample_receiver_name,
        k.expected_report_release_date::date,
        k.customer_support_id,
        k.customer_support_name,
        k.free_of_charge,
        k.is_priority,
        sam.scan_status,
        k.note,
        k.promotion as promotion_id,
        p.code as promotion_code,
        b.number as batch_number,
        sub.diagnosis,
        sub.validate_account,
        sub.martyr_name,
        sub.martyr_relationships,
        sub.guardian_name,
        sub.guardian_gender,
        sub.guardian_phone_number,
        sub.guardian_identifier_code,
        k.actual_report_release_time::date as actual_report_release_date,
        k.actual_report_release_time,
        k.default_pdf_link,
        sam.sample_type,
        ls.technology,
        case 
            when k.default_pdf_link is null then 'NOT_AVAILABLE'
            when k.pdf_generation_date > current_date - interval '6 days' then 'AVAILABLE'
            else 'EXPIRED'
        end
        as pdf_status,
        dna_records.lid as dna_extraction_id,
        k.created_at as created_at,
        k.created_at as upgraded_at,
        k.updated_at,
        k.deleted_at
    from
        identity_card idc
        INNER JOIN subject sub on idc.identifier_code = sub.identifier_code
        INNER JOIN sample sam ON sub.id = sam.subject_id
        INNER JOIN kit k ON sam.samplecode = k.samplecode
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join promotion p on k.promotion = p.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id

"""


KITS_ID_CARD_COUNT_QUERY_V3 = """
    select
        count(k.barcode)
    from
        identity_card idc
        INNER JOIN subject sub on idc.identifier_code = sub.identifier_code
        INNER JOIN sample sam ON sub.id = sam.subject_id
        INNER JOIN kit k ON sam.samplecode = k.samplecode
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join lab_sample ls on k.barcode = ls.barcode
        left join (
            select de.*
                from dna_extractions de
                inner join (
                    select 
                        dna.lid,
                        dna.id,
                        ROW_NUMBER() OVER (
                            PARTITION BY dna.lid 
                            ORDER BY 
                                CASE 
                                    WHEN pl.name IS NULL THEN '0' 
                                    ELSE pl.name 
                                END 
                            DESC
                        ) AS rank
                    from dna_extractions dna
                    left join sample_mapping smp on smp.dna_extraction_id = dna.id
                    left join plate pl on smp.plate_id = pl.id
                    where dna.dna_qc_status = 'PASS'
                ) de2 on de.id = de2.id and de.lid = de2.lid
                where de2.rank = 1
            ) dna_records on dna_records.lid = ls.lid
        left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
        left join chip c on c.chip_id = smp.chip_id
        left join plate pl on smp.plate_id = pl.id
        left join batch_mapping bm on pl.id = bm.plate_id
        left join batch b on b.id = bm.batch_id
"""

# get all kits cccd via id code


async def get_all_kits_identity_card_via_id_code_v3(
    identifier_code: Optional[str] = None,
    collect_date_start: Optional[str] = None,
    collect_date_end: Optional[str] = None,
    size: Optional[int] = None,
    offset: Optional[int] = None,
    order_by: Optional[str] = None,
):
    query_filter = "idc.deleted_at is null "
    if identifier_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.identifier_code = '{identifier_code}' "
    if collect_date_start:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collection_date::date >= '{collect_date_start}' "
    if collect_date_end:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collection_date::date <= '{collect_date_end}' "
        pass

    raw_count_query = (
        KITS_ID_CARD_COUNT_QUERY_V3 + "where " + query_filter
        if query_filter != ""
        else KITS_ID_CARD_COUNT_QUERY_V3
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if order_by:
        if order_by == "created_time":
            order_by = "created_at"
        query_filter += f"order by {order_by} asc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = KITS_ID_CARD_QUERY_V3 + query_filter
    else:
        raw_query = (
            KITS_ID_CARD_QUERY_V3 + "where " + query_filter
            if query_filter != ""
            else KITS_ID_CARD_QUERY_V3
        )

    results = await db.all(db.text(raw_query))

    return results, total


ID_CARD_REQUIRE_REGISTRATION = """
    SELECT
        idc.identifier_code,
        idc.full_name,
        idc.dob::date,
        idc.gender,
        idc.nationality,
        idc.origin,
        idc.residence,
        idc.ethnic,
        idc.email,
        idc.phone_number,
        idc.created_at,
        idc.deleted_at,
        idc.customer_support_id,
        idc.customer_support_name,
        sub.user_id,
        sub.require_registration,
        sub.martyr_name,
        sub.martyr_relationships
    FROM
        identity_card idc
        INNER JOIN subject sub ON idc.identifier_code = sub.identifier_code
"""

COUNT_ID_CARD_REQUIRE_REGISTRATION = """
    SELECT
        COUNT(idc.identifier_code)
    FROM
        identity_card idc
        INNER JOIN subject sub ON idc.identifier_code = sub.identifier_code
"""


async def get_identity_cards_require_registration(
    offset: Optional[int],
    size: Optional[int],
    order_by: Optional[str],
    identifier_code: str,
    full_name: str,
    dob: str,
    gender: str,
    customer_support_id: Optional[str],
    start_date: Optional[str],
    end_date: Optional[str],
):
    query_filter = "sub.require_registration='true' "
    if identifier_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.identifier_code like '%{identifier_code}%' "
    if full_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(idc.full_name) like '%{full_name.lower()}%' "
    if dob:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.dob::date = date('{dob}') "
    if gender:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.gender like '%{gender}%' "
    if customer_support_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.customer_support_id = '{customer_support_id}' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.created_at::date >= '{start_date}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.created_at::date <= '{end_date}' "

    raw_count_query = (
        COUNT_ID_CARD_REQUIRE_REGISTRATION + "where " + query_filter
        if query_filter != ""
        else COUNT_ID_CARD_REQUIRE_REGISTRATION
    )
    # print("raw_count_query: ", raw_count_query)
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if (order_by == "created_time") or not order_by:
        order_by = "created_at"
    query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    # if query_filter.startswith("order"):
    #     raw_query = ID_CARD_REQUIRE_REGISTRATION + query_filter
    # else:
    raw_query = (
        ID_CARD_REQUIRE_REGISTRATION + "where " + query_filter
        if query_filter != ""
        else ID_CARD_REQUIRE_REGISTRATION
    )
    # print("raw_query: ", raw_query)
    results = await db.all(db.text(raw_query))
    return results, total

async def delete_identity_card(
    identifier_code: str,
):
    id_card = await IdentityCard.query.where(
        and_(
            IdentityCard.identifier_code == identifier_code,
            IdentityCard.deleted_at == None
        )
    ).gino.first()

    if not id_card:
        return None

    id_card.deleted_at = get_current_date_time_utc_7()
    await id_card.update(deleted_at=id_card.deleted_at).apply()

    return id_card.to_dict()
