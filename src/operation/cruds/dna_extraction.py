import uuid
from typing import Optional
from ..models.models import db, DnaExtraction, LabSample, SampleMapping
from ..utils.utils import (
    get_current_date_time_utc_7,
    format_date,
)

from ..utils.utils import (
    DEFAULT_DATE_STR
)
from .. import logger
from gino.loader import <PERSON><PERSON><PERSON><PERSON>oa<PERSON>
from sqlalchemy import Date, cast
from datetime import datetime, timedelta
from ..schemas.dna_extraction import *

async def count_dna_extractions():
    total = db.select([db.func.count(DnaExtraction.id)])
    return await total.gino.scalar()

async def get_dna_extraction(id: int) -> DnaExtraction:
    dna_extraction = await DnaExtraction.get(id)
    return dna_extraction


async def count_matched_dna_extractions(
    lid: Optional[str] = None,
    start_dna_extraction_date: Optional[str] = None,
    end_dna_extraction_date: Optional[str] = None,
    agarose_gel: Optional[str] = None,
    dna_qc_status: Optional[str] = None,
    note: Optional[str] = None
):
    results = db.select([db.func.count()])
    results = results.where(DnaExtraction.deleted_at == None)
    if start_dna_extraction_date and end_dna_extraction_date: 
        results = results.where(DnaExtraction.dna_extraction_date.between(start_dna_extraction_date,end_dna_extraction_date))
    if lid:
        results = results.where(DnaExtraction.lid == lid)
    if agarose_gel:
        results = results.where(DnaExtraction.agarose_gel == agarose_gel)
    if dna_qc_status:
        results = results.where(DnaExtraction.dna_qc_status == dna_qc_status)
    if note:
        results = results.where(db.func.lower(DnaExtraction.note).contains(f"%{note}"))

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0

async def count_matched_dna_extractions_join_lab_sample(
    lid: Optional[str] = None,
    chip_id: Optional[str] = None,
    barcode: Optional[str] = None,
    start_dna_extraction_date: Optional[str] = None,
    end_dna_extraction_date: Optional[str] = None,
    agarose_gel: Optional[str] = None,
    dna_qc_status: Optional[str] = None,
    technology: Optional[str] = None,
    note: Optional[str] = None
):
    
    sample_mapping = db.select([
        SampleMapping.lid.label('lid'),
        SampleMapping.chip_id.label('chip_id'),
        ]).select_from(SampleMapping)
    if chip_id:
        sample_mapping = sample_mapping.where(SampleMapping.chip_id == chip_id)
    sample_mapping = sample_mapping.alias()
    
    lab_sample = db.select([
        LabSample.lid.label('lid'),
        LabSample.barcode.label('barcode'),
        LabSample.technology.label('technology'),
        sample_mapping.c.chip_id.label('chip_id')
        ]).select_from(
        LabSample.join(sample_mapping, sample_mapping.c.lid == LabSample.lid)
        )
    if barcode:
        lab_sample = lab_sample.where(LabSample.barcode == barcode)
    if technology:
        lab_sample = lab_sample.where(LabSample.technology == technology)

    lab_sample = lab_sample.alias()
    

    # results = db.select([db.func.count()])
    results = db.select([
        db.func.count()]).select_from(
        DnaExtraction.join(lab_sample, lab_sample.c.lid == DnaExtraction.lid)
        )
    results = results.where(DnaExtraction.deleted_at == None)
    results = results.where(DnaExtraction.dna_extraction_date.between(start_dna_extraction_date,end_dna_extraction_date))
    if lid:
        results = results.where(DnaExtraction.lid == lid)
    if agarose_gel:
        results = results.where(DnaExtraction.agarose_gel == agarose_gel)
    if dna_qc_status:
        results = results.where(DnaExtraction.dna_qc_status == dna_qc_status)
    if note:
        results = results.where(db.func.lower(DnaExtraction.note).contains(f"%{note}"))

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0

async def get_all_dna_extractions_join_lab_sample(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    lid: Optional[str] = None,
    chip_id: Optional[str] = None,
    barcode: Optional[str] = None,
    start_dna_extraction_date: Optional[str] = None,
    end_dna_extraction_date: Optional[str] = None,
    agarose_gel: Optional[str] = None, # using VAR in ConfigMap
    dna_qc_status: Optional[str] = None, # using VAR in ConfigMap
    technology: Optional[str] = None,
    note: Optional[str] = None,
    order_by: Optional[str] = None,
):
    
    sample_mapping = db.select([
        SampleMapping.lid.label('lid'),
        SampleMapping.chip_id.label('chip_id'),
        ]).select_from(SampleMapping)
    if chip_id:
        sample_mapping = sample_mapping.where(SampleMapping.chip_id == chip_id)
    sample_mapping = sample_mapping.alias()

    lab_sample = db.select([
        LabSample.lid.label('lid'),
        LabSample.barcode.label('barcode'),
        LabSample.technology.label('technology'),
        sample_mapping.c.chip_id.label('chip_id')
        ]).select_from(
        LabSample.join(sample_mapping, sample_mapping.c.lid == LabSample.lid)
        )
    if barcode:
        lab_sample = lab_sample.where(LabSample.barcode == barcode)
    if technology:
        lab_sample = lab_sample.where(LabSample.technology == technology)
    lab_sample = lab_sample.alias()
    # accounts = Account.alias('sacc')
    # results = DnaExtraction.query
    results = db.select([
        DnaExtraction,
        lab_sample.c.barcode.label('barcode'),
        lab_sample.c.technology.label('technology'),
        lab_sample.c.chip_id.label('chip_id'),
        ]).select_from(
        DnaExtraction.join(lab_sample, lab_sample.c.lid == DnaExtraction.lid)
        )
    results = results.where(DnaExtraction.deleted_at == None)
    results = results.where(DnaExtraction.dna_extraction_date.between(start_dna_extraction_date,end_dna_extraction_date))
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if lid:
        results = results.where(DnaExtraction.lid == lid)
    if agarose_gel:
        results = results.where(DnaExtraction.agarose_gel == agarose_gel)
    if dna_qc_status:
        results = results.where(DnaExtraction.dna_qc_status == dna_qc_status)
    if note:
        results = results.where(db.func.lower(DnaExtraction.note).contains(f"%{note}"))
    if order_by:
        results = results.order_by(getattr(DnaExtraction, order_by).desc())
    total = await count_matched_dna_extractions_join_lab_sample(
        lid=lid,
        chip_id=chip_id,
        barcode=barcode,
        start_dna_extraction_date=start_dna_extraction_date,
        end_dna_extraction_date=end_dna_extraction_date,
        agarose_gel=agarose_gel,
        dna_qc_status=dna_qc_status,
        technology=technology,
        note=note
    )
    return [r for r in await results.gino.all()], total

    pass

async def get_all_dna_extractions(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    lid: Optional[str] = None,
    start_dna_extraction_date: Optional[str] = None,
    end_dna_extraction_date: Optional[str] = None,
    agarose_gel: Optional[str] = None, # using VAR in ConfigMap
    dna_qc_status: Optional[str] = None, # using VAR in ConfigMap
    note: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = DnaExtraction.query
    results = results.where(DnaExtraction.deleted_at == None)
    if start_dna_extraction_date and end_dna_extraction_date: 
        results = results.where(DnaExtraction.dna_extraction_date.between(start_dna_extraction_date,end_dna_extraction_date))
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if lid:
        results = results.where(DnaExtraction.lid == lid)
    if agarose_gel:
        results = results.where(DnaExtraction.agarose_gel == agarose_gel)
    if dna_qc_status:
        results = results.where(DnaExtraction.dna_qc_status == dna_qc_status)
    if note:
        results = results.where(db.func.lower(DnaExtraction.note).contains(f"%{note}"))
    if order_by:
        results = results.order_by(getattr(DnaExtraction, order_by).desc())
    total = await count_matched_dna_extractions(
        lid=lid,
        start_dna_extraction_date=start_dna_extraction_date,
        end_dna_extraction_date=end_dna_extraction_date,
        agarose_gel=agarose_gel,
        dna_qc_status=dna_qc_status,
        note=note
    )
    return [r for r in await results.gino.all()], total

async def existed_lid(lid: str) -> bool:
    total = await db.select([db.func.count()]).where(LabSample.lid == lid).where(LabSample.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def existed_dna_extractions_w_lid(lid: str) -> bool:
    total = await db.select([db.func.count()]).where(DnaExtraction.lid == lid).where(DnaExtraction.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def replicate_positive_control_dna_extraction_w_id(dna_extraction_id: int):
    dna_extraction = await DnaExtraction.get(dna_extraction_id)
    data = dna_extraction.to_dict()
    result = await create_dna_extraction(data=data)
    return result

async def create_dna_extraction(data: dict):
    # if await existed_lid(data['lid']):
    #     err = f"Error: DnaExtraction with code {data['lid']} is already existed"
    #     return data, err
    current_time = get_current_date_time_utc_7()
    data = {
        'lid': data['lid'],
        'dna_extraction_date': data['dna_extraction_date'],
        'qubit': data['qubit'],
        'nano_drop': data['nano_drop'], # if data.get('discount') else None
        'a260_a280': data['a260_a280'], # if data.get('discount') else None
        'agarose_gel': data['agarose_gel'],
        'dna_qc_status': data['dna_qc_status'],
        'note': data['note'] if data['note'] else None,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await DnaExtraction.create(**data)
    return result.to_dict(), None

async def create_many_dna_extractions(data_arrs):
    results = []
    async with db.transaction() as tx:
      for data in data_arrs:
        logger.info(f"Create a dna_extraction code with date formated data {data}")
        data, err = await create_dna_extraction(data)
        if err:
            return None, err
        results.append(data)
    return results, None

        


async def update_dna_extraction(dna_extraction: DnaExtraction, data: dict):
    if dna_extraction.deleted_at:
        err = f"dna_extraction with if: {dna_extraction.id} already deleted!"
        return err
    await dna_extraction.update(**data).apply()
    logger.info(f"dna_extraction with lid: {dna_extraction.lid} updated")
    return None
    
async def delete_dna_extraction(dna_extraction):

    if not dna_extraction:
        err = f"dna_extraction with id: {dna_extraction.id} cannot be found"
        return None, err

    res = await DnaExtraction.delete.where(DnaExtraction.id == dna_extraction.id).gino.status()
    logger.info(f"dna_extraction with lid: {dna_extraction.lid} deleted")
    return res, None
