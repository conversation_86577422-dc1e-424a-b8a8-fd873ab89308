from typing import Optional

from fastapi import HTTPException
from starlette.status import HTTP_400_BAD_REQUEST

from .. import logger
from ..config import config
from ..models.models import Card, CardProduct, db
from ..schemas.card import (
    CardProductUpdateReq,
    CardUpdateReq,
    CreateCardProductReq,
    CreateCardReq,
)
from ..utils.utils import (
    convert_rowproxy_to_dict,
    failure_response,
    generate_card_id,
    get_current_date_time,
    get_language_based_on_barcode,
    get_version_report_px,
)

CARD_COUNT_QUERY = """
    select count(c.id)
    from
        card c
        inner join card_product cp on c.card_product_id = cp.id

"""

CARD_QUERY = """
    select
        c.id,
        c.card_product_id,
        c.barcode,
        c.phone_number,
        c.report_ver,
        c.db_ver,
        c.card_status,
        c.full_name,
        c.qr_url,
        c.s3_object_key,
        c.lang,
        c.presigned_s3_font_url,
        c.presigned_s3_back_url,
        c.created_at,
        cp.card_product_name,
        cp.type,
        CONCAT(c.s3_object_key, '_front.svg') AS card_front_s3_object_uri,
        CONCAT(c.s3_object_key, '_back.svg') AS card_back_s3_object_uri
    from
        card c
        inner join card_product cp on c.card_product_id = cp.id

"""


async def get_filtered_card_list(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    card_id: Optional[str] = None,
    barcode: Optional[str] = None,
    full_name: Optional[str] = None,
    phone_number: Optional[str] = None,
    report_ver: Optional[str] = None,
    db_ver: Optional[str] = None,
    card_product_id: Optional[str] = None,
    card_status: Optional[str] = None,
    order_by: Optional[str] = None,
    order_option: Optional[str] = "desc",
    is_unlocked_only: Optional[bool] = False,
):
    query_filter = ""
    if card_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.id = '{card_id}' "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.barcode = '{barcode}' "
    if full_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.full_name like '%{full_name}%' "
    if card_product_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.card_product_id = '{card_product_id}' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.phone_number = '{phone_number}' "
    if report_ver:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.report_ver like '%{report_ver}%' "
    if db_ver:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.db_ver like '%{db_ver}%' "
    if card_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"c.card_status = '{card_status}' "
    # if plate_status_filter:
    #     query_filter += "and " if query_filter != "" else " "
    #     query_filter += f"(p.status in {tuple(plate_status_filter)} or p.status is null) "
    if is_unlocked_only:
        query_filter += "and " if query_filter != "" else " "
        query_filter += "c.card_status != 'LOCKED' "

    raw_count_query = (
        CARD_COUNT_QUERY + "where " + query_filter
        if query_filter != ""
        else CARD_COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    raw_query = (
        CARD_QUERY + "where " + query_filter if query_filter != "" else CARD_QUERY
    )

    if order_by:
        raw_query += f"order by {order_by} {order_option} "
    if size:
        raw_query += f"limit {size} "
        raw_query += f"offset {offset} "

    results = await db.all(db.text(raw_query))
    # REMEMBER to_dict()
    return results, total


async def create_card_ctl(card_req: CreateCardReq):
    card = Card(
        id=card_req.id,
        card_product_id=card_req.card_product_id,
        barcode=card_req.barcode,
        phone_number=card_req.phone_number,
        report_ver=card_req.report_ver,
        db_ver=card_req.db_ver,
        lang=card_req.lang,
        card_status=card_req.card_status,
        full_name=card_req.full_name,
        qr_url=card_req.qr_url,
        s3_object_key=card_req.s3_object_key,
        created_at=get_current_date_time(),
    )

    async with db.transaction() as tx:
        await card.create()
        total = (
            await db.select([db.func.count()])
            .where(Card.id == card_req.id)
            .gino.scalar()
        )
        if total > 1:
            err = f"Error: CARD {card_req.id} is already existed. The card ID is duplicated!"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
    return card.to_dict()


async def check_version_n_get_pre_issued_cards(
    issue_cards_req, card_product_name, card_product_id
):
    results = []
    card_requests = issue_cards_req.card_requests
    for req in card_requests:
        # report_ver, db_ver = "AA97E72", "09BA32E"
        entry = dict()
        entry["barcode"] = req.barcode
        entry["phone_number"] = req.phone_number
        entry["product_name"] = req.product_name
        entry["full_name"] = req.full_name
        entry["user_id"] = req.user_id
        entry["card_product_name"] = card_product_name
        entry["card_product_id"] = card_product_id
        is_new_barcode = await is_new_card_w_barcode(req.barcode)
        is_new_phone_number = await is_new_card_w_phone_n_card_product_id(
            req.phone_number, card_product_id, req.full_name
        )
        entry["lang"] = get_language_based_on_barcode(req.barcode)
        entry["is_new"] = is_new_barcode and is_new_phone_number

        # SKIP getting version of report due to PRE-CHECK kit status
        if req.is_valid:
            report_ver, db_ver = await get_version_report_px(req.barcode)
            if report_ver and db_ver:
                entry["report_ver"] = report_ver
                entry["db_ver"] = db_ver
                pass
            else:
                # raise ValueError(f"CARD with barcode {req.barcode} didn't have report_ver, db_ver. Please generate report first!! ")
                req.note = f"CARD with barcode {req.barcode} didn't have report_ver, db_ver. Please generate report first!! "
                req.is_valid = False
                entry["report_ver"] = None
                entry["db_ver"] = None
        if not entry["lang"]:
            req.is_valid = False
        entry["note"] = req.note
        entry["is_valid"] = req.is_valid
        results.append(entry)

    return results


async def issue_cards_ctl(issue_cards_req):
    results = []
    card_requests = issue_cards_req.card_requests
    generate_card_id_attempts = config["CARD_MANAGEMENT"]["GENERATE_CARD_ID_ATTEMPTS"]

    async with db.transaction() as tx:
        for req in card_requests:
            if not req.is_valid:
                raise ValueError(
                    f"Request to created CARD with barcode {req.barcode} is not valid "
                )
            # report_ver, db_ver = "test","test"
            report_ver, db_ver = await get_version_report_px(req.barcode)
            # report_ver, db_ver = "AA97E72", "09BA32E"
            if report_ver and db_ver:
                if not req.is_new:
                    _ = await disable_card_w_barcode_before_re_issue(req.barcode)
                    _ = await disable_card_w_phone_number_before_re_issue(
                        req.phone_number
                    )
                # card_id = None [CARD_ID generator]
                # card_id = "8888822011755154"
                idx = 0
                while idx < generate_card_id_attempts:
                    if idx == (generate_card_id_attempts // 2):
                        report_ver = report_ver.upper()
                        db_ver = db_ver.upper()
                    card_id = generate_card_id(
                        report_ver, db_ver, req.full_name, req.barcode
                    )
                    idx += 1

                    card = Card(
                        id=card_id,
                        card_product_id=issue_cards_req.card_product_id,
                        barcode=req.barcode,
                        phone_number=req.phone_number,
                        user_id=req.user_id,
                        report_ver=report_ver,
                        db_ver=db_ver,
                        lang=req.lang.lower(),
                        card_status="CREATED",
                        full_name=req.full_name,
                        qr_url=req.qr_url
                        if issue_cards_req.included_qr
                        else None,  # [QR_CODE generator]
                        s3_object_key=None,
                        created_at=get_current_date_time(),
                    )
                    try:
                        await card.create()
                    except Exception as err:
                        logger.error(err)
                        err = f"Error: CARD {card.id} is already existed. The card ID is duplicated!"
                        logger.info(err)
                    else:
                        logger.info(f"CARD {card.id} generated!")
                        data = dict()
                        data["barcode"] = card.barcode
                        data["lang"] = card.lang
                        data["phone_number"] = card.phone_number
                        data["user_id"] = card.user_id
                        data["id"] = card.id
                        results.append(data)
                        break
                else:
                    err = f"Error: Couldn't generate card ID after {generate_card_id_attempts} attempts!!"
                    http_code = HTTP_400_BAD_REQUEST
                    errs = failure_response(err)
                    raise HTTPException(status_code=http_code, detail=errs)

                    # print("card: ",card)
            else:
                raise ValueError(
                    f"CARD with barcode {req.barcode} didn't have report_ver, db_ver. Please generate report first!! "
                )

    return results


async def is_new_card_w_barcode(barcode: str):
    """_summary_

    Args:
        barcode (str): _description_
    """
    _, total = await get_filtered_card_list(barcode=barcode, is_unlocked_only=True)
    if total == 0:
        logger.info(f"There's no GeneCard to lock for this barcode {barcode} ")
        return True

    else:
        logger.info(f"There's {total} GeneCard to lock for this barcode {barcode} ")

        return False


async def is_new_card_w_phone_n_card_product_id(
    phone_number: str, card_product_id: str, full_name: str
):
    """_summary_

    Args:
        phone_number (str):
    """
    _, total = await get_filtered_card_list(
        phone_number=phone_number,
        card_product_id=card_product_id,
        full_name=full_name,
        is_unlocked_only=True,
    )
    if total == 0:
        logger.info(
            f"There's no GeneCard to lock for this phone_number {phone_number} "
        )
        return True

    else:
        logger.info(
            f"There's {total} GeneCard to lock for this phone_number {phone_number} "
        )

        return False


async def disable_card_w_barcode_before_re_issue(barcode: str):
    """_summary_
    Lock all active GeneCard for this barcode

    Args:
        barcode (str): _description_
    """
    result = []
    data, total = await get_filtered_card_list(barcode=barcode, is_unlocked_only=True)
    if total == 0:
        logger.info(f"There's no GeneCard to lock for this barcode {barcode} ")
        return result

    else:
        logger.info(f"There's {total} GeneCard to lock for this barcode {barcode} ")
        for row in data:
            card = convert_rowproxy_to_dict(row)
            card_id = card.get("id")
            lock_card_req = CardUpdateReq(card_status="LOCKED")
            res = await update_card_ctl(id=card_id, update_data=lock_card_req)
            logger.info(
                f"LOCKING GeneCard {card_id} of barcode {barcode} in order to re-issue new GeneCard!"
            )
            result.append(res)

        return result


async def disable_card_w_phone_number_before_re_issue(phone_number: str):
    """_summary_
    Lock all active GeneCard for this phone_number

    Args:
        phone_number (str): _description_
    """
    result = []
    data, total = await get_filtered_card_list(
        phone_number=phone_number, is_unlocked_only=True
    )
    if total == 0:
        logger.info(
            f"There's no GeneCard to lock for this phone_number {phone_number} "
        )
        return result

    else:
        logger.info(
            f"There's {total} GeneCard to lock for this phone_number {phone_number} "
        )
        for row in data:
            card = convert_rowproxy_to_dict(row)
            card_id = card.get("id")
            lock_card_req = CardUpdateReq(card_status="LOCKED")
            res = await update_card_ctl(id=card_id, update_data=lock_card_req)
            logger.info(
                f"LOCKING GeneCard {card_id} of phone_number {phone_number} in order to re-issue new GeneCard!"
            )
            result.append(res)

        return result


async def update_card_ctl(id: str, update_data: CardUpdateReq):
    card = await Card.query.where(Card.id == id).gino.first()
    data = dict()
    if update_data.card_status:
        data["card_status"] = update_data.card_status
    if update_data.qr_url:
        data["qr_url"] = update_data.qr_url
    if update_data.s3_object_key:
        data["s3_object_key"] = update_data.s3_object_key
    if update_data.presigned_s3_font_url:
        data["presigned_s3_font_url"] = update_data.presigned_s3_font_url
    if update_data.presigned_s3_back_url:
        data["presigned_s3_back_url"] = update_data.presigned_s3_back_url
    await card.update(**data).apply()
    return card.to_dict()


async def delete_card_ctl(id: str):
    async with db.transaction() as tx:
        card = await Card.query.where(Card.id == id).gino.first()
        if card is None:
            raise ValueError(f"CARD with ID {id} is not exsisted")

        # delete batch
        await Card.delete.where(Card.id == card.id).gino.status()


# -------------------------------------------------

CARD_PRODUCT_COUNT_QUERY = """
    select count(cp.id)
    from
        card_product cp

"""

CARD_PRODUCT_QUERY = """
    select
        cp.id,
        cp.type,
        cp.card_product_name,
        cp.policy,
        cp.created_at
    from
        card_product cp

"""


async def get_filtered_card_product_list(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    card_type: Optional[str] = None,
    card_product_id: Optional[str] = None,
    card_product_name: Optional[str] = None,
    policy: Optional[str] = None,
    order_by: Optional[str] = None,
    order_option: Optional[str] = "desc",
    is_active_only: Optional[bool] = False,
):
    query_filter = ""
    if card_type:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"cp.type = '{card_type}' "
    if card_product_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"cp.id = '{card_product_id}' "
    if card_product_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"cp.card_product_name like '%{card_product_name}%' "
    if policy:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"cp.policy = '{policy}' "
    # if plate_status_filter:
    #     query_filter += "and " if query_filter != "" else " "
    #     query_filter += f"(p.status in {tuple(plate_status_filter)} or p.status is null) "
    if is_active_only:
        query_filter += "and " if query_filter != "" else " "
        query_filter += "cp.policy != 'DISABLE' "

    raw_count_query = (
        CARD_PRODUCT_COUNT_QUERY + "where " + query_filter
        if query_filter != ""
        else CARD_PRODUCT_COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    raw_query = (
        CARD_PRODUCT_QUERY + "where " + query_filter
        if query_filter != ""
        else CARD_PRODUCT_QUERY
    )

    if order_by:
        raw_query += f"order by {order_by} {order_option} "
    if size:
        raw_query += f"limit {size} "
        raw_query += f"offset {offset} "
    results = await db.all(db.text(raw_query))
    # REMEMBER to_dict()
    return results, total


async def create_card_product_ctl(card_product_req: CreateCardProductReq):
    card_product = CardProduct(
        # id=card_product_req.id,
        type=card_product_req.type,
        card_product_name=card_product_req.card_product_name,
        policy=card_product_req.policy,
        created_at=get_current_date_time(),
    )

    async with db.transaction() as tx:
        await card_product.create()
        total = (
            await db.select([db.func.count()])
            .where(CardProduct.card_product_name == card_product_req.card_product_name)
            .gino.scalar()
        )
        if total > 1:
            err = f"Error: CARD PRODUCT name {card_product_req.card_product_name} is already existed. The card name is duplicated!"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
    return card_product.to_dict()


async def update_card_product_ctl(id: str, update_data: CardProductUpdateReq):
    card_product = await CardProduct.query.where(CardProduct.id == id).gino.first()
    data = dict()
    if update_data.type:
        data["type"] = update_data.type
    if update_data.policy:
        data["policy"] = update_data.policy
    await card_product.update(**data).apply()
    return card_product.to_dict()
