from asyncio.log import logger
from http.client import HTTPException

# from .. import logger
# dot-env
from os.path import dirname, join

import boto3
import requests
from dotenv import load_dotenv
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from src.operation.utils.utils import failure_response

from ..config import config

dotenv_path = join(dirname(dirname(__file__)), ".env")
load_dotenv(dotenv_path)

BUCKET = config["RAW_DATA_BUCKET"]["MICROARRAY"]
KEY_ID = config["AWS"]["AWS_ACCESS_KEY_ID"]
SECRET_KEY = config["AWS"]["AWS_SECRET_ACCESS_KEY"]


def is_input_file_px_existed(batch_number):
    prefix = f"{batch_number}/"
    files = get_files_from_s3_w_bucket_name(
        prefix, config.get("RAW_DATA_BUCKET").get("PCR")
    )

    for file in files:
        if f"PCR{batch_number}" in file:
            return True
    return False


def validate_files_s3(chip_id, physical_position):
    prefix = f"{chip_id}/"
    files = get_files_from_s3(prefix)
    missing_files = []
    if f"{chip_id}_{physical_position}_Grn.idat" not in files:
        missing_files.append(f"{chip_id}_{physical_position}_Grn.idat")
    if f"{chip_id}_{physical_position}_Red.idat" not in files:
        missing_files.append(f"{chip_id}_{physical_position}_Red.idat")
    return missing_files


def validate_files_s3_from_list(chip_id, physical_position, list_files):
    missing_files = []
    if f"{chip_id}_{physical_position}_Grn.idat" not in list_files:
        missing_files.append(f"{chip_id}_{physical_position}_Grn.idat")
    if f"{chip_id}_{physical_position}_Red.idat" not in list_files:
        missing_files.append(f"{chip_id}_{physical_position}_Red.idat")
    return missing_files


def get_files_from_s3_w_bucket_name(prefix, bucket_name):
    res = []
    s3_client = boto3.client(
        "s3", aws_access_key_id=KEY_ID, aws_secret_access_key=SECRET_KEY
    )

    file_objects = s3_client.list_objects(
        Bucket=bucket_name, Prefix=prefix, Delimiter="/"
    )
    if file_objects.get("Contents"):
        for file_object in file_objects.get("Contents"):
            key = file_object.get("Key")
            res.append(key.split("/")[-1])
    return res


def get_files_from_s3(prefix):
    res = []
    s3_client = boto3.client(
        "s3", aws_access_key_id=KEY_ID, aws_secret_access_key=SECRET_KEY
    )

    file_objects = s3_client.list_objects(Bucket=BUCKET, Prefix=prefix, Delimiter="/")
    if file_objects.get("Contents"):
        for file_object in file_objects.get("Contents"):
            key = file_object.get("Key")
            res.append(key.split("/")[-1])
    return res


def get_files_from_s3_w_bucket(bucket, prefix):
    res = []
    s3_client = boto3.client(
        "s3", aws_access_key_id=KEY_ID, aws_secret_access_key=SECRET_KEY
    )

    file_objects = s3_client.list_objects(Bucket=bucket, Prefix=prefix, Delimiter="/")
    if file_objects.get("Contents"):
        for file_object in file_objects.get("Contents"):
            key = file_object.get("Key")
            res.append(key.split("/")[-1])
    return res


def run_pipeline(step_function_input):
    try:
        pipeline_url = config["PIPELINE_URL"]
        logger.info(f"RUN MICROARRAY & PGS with config: {pipeline_url}")
        if pipeline_url.get("MICROARRAY"):
            microarray_pipeline_url = pipeline_url.get("MICROARRAY")
            request_headers = {
                "Content-type": "application/json",
                "Accept": "text/plain",
            }
            logger.debug(
                f"Trigger pipeline with url {microarray_pipeline_url} and data {step_function_input}"
            )
            response = requests.post(
                microarray_pipeline_url,
                json=step_function_input,
                headers=request_headers,
                timeout=3,
            )

            response.raise_for_status()
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response("MISSING CONFIG URL for MICROARRAY pipeline")
            raise HTTPException(status_code=http_code, detail=errs)

        if pipeline_url.get("PGS"):
            pgs_pipeline_url = pipeline_url.get("PGS")
            request_headers = {
                "Content-type": "application/json",
                "Accept": "text/plain",
            }
            logger.debug(
                f"Trigger pipeline with url {pgs_pipeline_url} and data {step_function_input}"
            )
            response = requests.post(
                pgs_pipeline_url,
                json=step_function_input,
                headers=request_headers,
                timeout=3,
            )

            response.raise_for_status()
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response("MISSING CONFIG URL for PGS pipeline")
            raise HTTPException(status_code=http_code, detail=errs)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def run_pipeline_extended(step_function_input, pipeline_name):
    try:
        extended_pipeline_url = config["EXTENDED_PIPELINE_URL"]
        if pipeline_name.lower() == "px" and extended_pipeline_url.get("PX"):
            microarray_pipeline_url = extended_pipeline_url.get("PX")
            request_headers = {
                "Content-type": "application/json",
                "Accept": "text/plain",
            }
            logger.debug(
                f"Trigger pipeline with url {microarray_pipeline_url} and data {step_function_input}"
            )
            response = requests.post(
                microarray_pipeline_url,
                json=step_function_input,
                headers=request_headers,
                timeout=3,
            )

            response.raise_for_status()
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response("MISSING CONFIG URL for PX pipeline")
            raise HTTPException(status_code=http_code, detail=errs)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


def run_pipelines(inputs, types):
    for type in types:
        run_pipeline_w_type(inputs[type], type)
    pass


def run_pipeline_w_type(step_function_input, type):
    try:
        pipeline_url = config["PIPELINE_URL"]
        logger.info(f"RUN MICROARRAY & PGS with config: {pipeline_url}")
        if type.upper() == "MICROARRAY" and pipeline_url.get("MICROARRAY"):
            microarray_pipeline_url = pipeline_url.get("MICROARRAY")
            request_headers = {
                "Content-type": "application/json",
                "Accept": "text/plain",
            }
            logger.debug(
                f"Trigger pipeline with url {microarray_pipeline_url} and data {step_function_input}"
            )
            response = requests.post(
                microarray_pipeline_url,
                json=step_function_input,
                headers=request_headers,
                timeout=3,
            )

            response.raise_for_status()
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response("MISSING CONFIG URL for MICROARRAY pipeline")
            raise HTTPException(status_code=http_code, detail=errs)

        if type.upper() == "PGS" and pipeline_url.get("PGS"):
            pgs_pipeline_url = pipeline_url.get("PGS")
            request_headers = {
                "Content-type": "application/json",
                "Accept": "text/plain",
            }
            logger.debug(
                f"Trigger pipeline with url {pgs_pipeline_url} and data {step_function_input}"
            )
            response = requests.post(
                pgs_pipeline_url,
                json=step_function_input,
                headers=request_headers,
                timeout=3,
            )

            response.raise_for_status()
        else:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response("MISSING CONFIG URL for PGS pipeline")
            raise HTTPException(status_code=http_code, detail=errs)

        if type.upper() == "PX":
            run_pipeline_extended(step_function_input, type)

    except requests.exceptions.ConnectionError as err:
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
