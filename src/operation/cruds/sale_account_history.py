import uuid
from typing import Optional
from ..models.models import db, SaleAccountHistory
from ..utils.utils import get_current_date_time_utc_7
from .. import logger

async def count_sale_account_histories():
    total = db.select([db.func.count(SaleAccountHistory.id)])
    return await total.gino.scalar()

async def get_sale_account_history(id: uuid.uuid4) -> SaleAccountHistory:
    sale_account_history = await SaleAccountHistory.get(id)
    return sale_account_history

async def get_all_sale_account_history():
    return None

async def get_current_sale_account_history_by_account_id(id):
    results = SaleAccountHistory.query
    results = results.where(SaleAccountHistory.account_id==id)
    results = results.order_by(getattr(SaleAccountHistory, "created_at").desc())
    results = results.limit(1)
    res = [r.to_dict() for r in await results.gino.all()]
    logger.info(f"Response from Sale Account History: {res[0]}!")
    return res[0]

async def delete_sale_account_history(history: SaleAccountHistory, data: dict):
    current_time = get_current_date_time_utc_7()
    if not history:
        err = f"Please pass Sale_Account_History with id"
        return None, err
    elif history.deleted_at:
        err = f"Sale_Account_History with id: {history.id} already deleted!"
        return None, err

    await history.update(**data).apply()
    logger.info(f"Sale_Account_History with name: {history.id} deleted")
    return history, None

async def create_sale_account_history(data: dict):
    current_time = get_current_date_time_utc_7()

    data = {
        'id': uuid.uuid4(),
        'account_id': data['account_id'],
        'pic_id': data['pic_id'],
        'created_at': current_time
    }
    result = await SaleAccountHistory.create(**data)
    return result.to_dict(), None