import uuid
from typing import Optional
from ..models.models import TrackingTemplate, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from .tracking_template_step import * 
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# TrackingTemplate - model 
# TEMPLATE - QUERY gs_template_name
# _tracking_template - function gs_template_name
# tracking_template - single
# tracking_templates - plural
# gs_template_name -- search key

TEMPLATE_LIST_QUERY_V3 = """
    select
        tt.id,
        tt."congNghe",
        tt."tenKit",
        tt.gs_template_name,
        tt.created_at,
        tt.updated_at,
        tt.deleted_at
    from
        tracking_template tt
"""

TEMPLATE_LIST_COUNT_QUERY_V3 = """
    select
        count(tt.id)
    from
        tracking_template tt
"""

async def get_tracking_template_by_id(id: uuid.uuid4):
    tracking_template = await TrackingTemplate.get(id)
    return tracking_template


async def get_all_tracking_templates(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="tt.created_at",
    congNghe: Optional[str] = None,
    gs_template_name: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if congNghe:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tt.'congNghe' = '{str(congNghe)}' """
    if gs_template_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(tt.gs_template_name) like '%{gs_template_name.lower()}%' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tt.deleted_at is null """
    
    raw_count_query = TEMPLATE_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else TEMPLATE_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = TEMPLATE_LIST_QUERY_V3 + query_filter
    else:
        raw_query = TEMPLATE_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else TEMPLATE_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_template_existed(gs_template_name: str, congNghe: str):
    total = await db.select([db.func.count()]).where(TrackingTemplate.gs_template_name == gs_template_name).where(TrackingTemplate.congNghe == congNghe).where(TrackingTemplate.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_tracking_template(data: dict):
    """
    congNghe: Optional[Literal[tuple(config['ALLOWED_TECHNOLOGY'])]]
    tenKit: Optional[str]
    gs_template_name: Optional[str]
    cacBuocThucHien: List[BuocThucHien]
    
    step_id: str
    tenBuoc: str
    gs_step_number: int
    """
    if await is_tracking_template_existed(data['gs_template_name'], data['congNghe']):
        err = f"Error: Integration TrackingTemplate with gs_template_name {data['gs_template_name']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    async with db.transaction() as tx:
        templateData = {
            'congNghe': data.get('congNghe'),
            'tenKit': data.get('tenKit'),
            'gs_template_name': data.get('gs_template_name'),
            'created_at': current_time,
            'updated_at': current_time
        }
        template = await TrackingTemplate.create(**templateData)
        template = template.to_dict()
        template_id = template.get('id')
        result = {
            **template
        }
        result['cacBuocThucHien'] = []
        # print("cacBuocThucHien: ", data.get('cacBuocThucHien'))
        
        for buocThucHien in data.get('cacBuocThucHien'):
            templateStepData = {
                'gs_step_number': buocThucHien.get('gs_step_number'),
                'step_id': buocThucHien.get('step_id'),
                'template_id': template_id,
                'tenBuoc': buocThucHien.get('tenBuoc'),
                'gs_template_name': data.get('gs_template_name')
            }
            
            _ = await create_tracking_template_step(data=templateStepData)
            result['cacBuocThucHien'].append(templateStepData)
            
    return result

async def update_tracking_template(tracking_template: TrackingTemplate, data: dict):
    if tracking_template.deleted_at:
        err = f"TrackingTemplate with gs_template_name: {tracking_template.gs_template_name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_template.update(**data).apply()
    logger.info(f"TrackingTemplate with gs_template_name: {tracking_template.gs_template_name} updated")
    return None


async def delete_tracking_template(tracking_template: TrackingTemplate):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_template:
        err = f"TrackingTemplate with gs_template_name: {tracking_template.gs_template_name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_template.deleted_at:
        err = f"TrackingTemplate with: {tracking_template.gs_template_name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_template.update(**data).apply()
    logger.info(f"TrackingTemplate with gs_template_name: {tracking_template.gs_template_name} deleted")
