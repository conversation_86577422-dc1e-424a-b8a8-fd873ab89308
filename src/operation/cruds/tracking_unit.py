import uuid
from typing import Op<PERSON>

from fastapi import HTT<PERSON>Exception
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_409_CONFLICT
from sqlalchemy import or_
from .. import logger
from ..models.models import TrackingUnit, db
from ..utils.message_service import add_group_cccd_new_user, send_mess_queue
from ..utils.utils import (
    admin_create_phone_user,
    check_user_existence,
    failure_response,
    get_current_date_time_utc_7,
    parse_user_information,
    build_phone_variants
)

# TrackingUnit - model
# UNIT - QUERY tenDonVi
# _tracking_unit - function tenDonVi
# tracking_unit - single
# tracking_units - plural
# tenDonVi -- search key

UNIT_LIST_QUERY_V3 = """
    select
        tu.id,
        tu."tenDonVi",
        tu.gs_area,
        tu.gs_area_code,
        tu.gs_phone_number,
        tu.created_at,
        tu.updated_at,
        tu.deleted_at
    from
        tracking_unit tu
"""

UNIT_LIST_COUNT_QUERY_V3 = """
    select
        count(tu.id)
    from
        tracking_unit tu
"""


async def get_tracking_unit_by_id(id: uuid.uuid4):
    tracking_unit = await TrackingUnit.get(id)
    return tracking_unit


async def get_tracking_unit_by_phone_number(phone_number: str):
    phone_variants = build_phone_variants(phone_number)
    tracking_unit = await TrackingUnit.query.where(
        or_(*[TrackingUnit.gs_phone_number.ilike(f"%{v}%") for v in phone_variants])
    ).gino.first()
    return tracking_unit


async def is_linked_phone_number_existed(phone_number: str):
    phone_variants = build_phone_variants(phone_number)

    total = (
        await db.select([db.func.count()])
        .where(
            or_(*[TrackingUnit.gs_phone_number.ilike(f"%{v}%") for v in phone_variants])
        )
        .where(TrackingUnit.deleted_at == None)
        .gino.scalar()
    )
    return True if total > 0 else False


async def link_unit_with_phone_number(
    id: uuid.uuid4, phone_number: str, password: str, credential: str
):

    tracking_unit = await get_tracking_unit_by_id(id)
    if not tracking_unit:
        err = f"Không tìm thấy TrackingUnit với id: {id}"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    current_time = get_current_date_time_utc_7()
    # CHECK EXISTED PHONE NUMBER
    is_phone_existed = await check_user_existence(phone=phone_number)

    if is_phone_existed:
        phone_numbers = tracking_unit.gs_phone_number.split(",") if tracking_unit.gs_phone_number else []
        phone_numbers.append(phone_number)
        phone_numbers = list(set(phone_numbers))
        gs_phone_number = ",".join(phone_numbers)
        data = {"gs_phone_number": gs_phone_number, "updated_at": current_time}
        await tracking_unit.update(**data).apply()
        return await get_tracking_unit_by_id(id)

    if await is_linked_phone_number_existed(phone_number):
        err = f"Lỗi: Số điện thoại {phone_number} đã được liên kết với đơn vị theo dõi khác"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    async with db.transaction() as tx:
        # CREATE A NEW USER
        await admin_create_phone_user(
            full_name=tracking_unit.tenDonVi,
            phone_number=phone_number,
            password=password,
            credential=credential,
        )
        # VALIDATE PHONE NUMBER
        user_info = await parse_user_information(phone=phone_number)
        if user_info["data"]["result"]:
            logger.info(f"Linked user with {phone_number}: created!")

            # GRANT CCCD ROLE TO USER
            user_id = user_info["data"]["uuid"]
            mess_adding_group = add_group_cccd_new_user(user_id)
            response = send_mess_queue(mess_adding_group)
            logger.info(f"Granting {phone_number} cccd role: {response}!")
            phone_numbers = tracking_unit.gs_phone_number.split(",") if tracking_unit.gs_phone_number else []
            phone_numbers.append(phone_number)
            phone_numbers = list(set(phone_numbers))
            gs_phone_number = ",".join(phone_numbers)
            data = {"gs_phone_number": gs_phone_number, "updated_at": current_time}
            await tracking_unit.update(**data).apply()

        else:
            err = f"Lỗi: Không thể tạo người dùng với số điện thoại {phone_number}"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

    return await get_tracking_unit_by_id(id)

async def unlink_unit_with_phone_number(
    id: uuid.uuid4, phone_number: str
):
    tracking_unit = await get_tracking_unit_by_id(id)
    if not tracking_unit:
        err = f"Không tìm thấy TrackingUnit với id: {id}"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    async with db.transaction() as tx:
        phone_numbers = tracking_unit.gs_phone_number.split(",") if tracking_unit.gs_phone_number else []
        new_phone_numbers = filter(lambda pn: pn != phone_number, phone_numbers)
        gs_phone_number = ",".join(new_phone_numbers)
        current_time = get_current_date_time_utc_7()
        data = {"gs_phone_number": gs_phone_number, "updated_at": current_time}
        await tracking_unit.update(**data).apply()

    return await get_tracking_unit_by_id(id)


async def get_all_tracking_units(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tu.created_at",
    tenDonVi: Optional[str] = None,
    gs_area: Optional[str] = None,
    gs_area_code: Optional[int] = None,
    include_deleted: Optional[bool] = False,
):
    query_filter = ""
    if tenDonVi:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(tu.'tenDonVi') like '%{tenDonVi.lower()}%' """
    if gs_area:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tu.gs_area = '{str(gs_area)}' """
    if gs_area_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tu.gs_area_code = '{gs_area_code}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += """tu.deleted_at is null """

    raw_count_query = (
        UNIT_LIST_COUNT_QUERY_V3 + "where" + query_filter
        if query_filter != ""
        else UNIT_LIST_COUNT_QUERY_V3
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = UNIT_LIST_QUERY_V3 + query_filter
    else:
        raw_query = (
            UNIT_LIST_QUERY_V3 + "where " + query_filter
            if query_filter != ""
            else UNIT_LIST_QUERY_V3
        )
    results = await db.all(db.text(raw_query))
    return results, total

    pass


async def is_tracking_unit_existed(tenDonVi: str):
    total = (
        await db.select([db.func.count()])
        .where(TrackingUnit.tenDonVi == tenDonVi)
        .where(TrackingUnit.deleted_at == None)
        .gino.scalar()
    )
    return True if total > 0 else False


async def create_tracking_unit(data: dict):
    if await is_tracking_unit_existed(data["tenDonVi"]):
        err = f"Error: Integration TrackingUnit with tenDonVi {data['tenDonVi']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    current_time = get_current_date_time_utc_7()
    data = {
        "tenDonVi": data["tenDonVi"],
        "gs_area": data["gs_area"],
        "gs_area_code": data["gs_area_code"],
        "created_at": current_time,
        "updated_at": current_time,
    }
    result = await TrackingUnit.create(**data)
    return result.to_dict()


async def update_tracking_unit(tracking_unit: TrackingUnit, data: dict):
    if tracking_unit.deleted_at:
        err = f"TrackingUnit with tenDonVi: {tracking_unit.tenDonVi} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_unit.update(**data).apply()
    logger.info(f"TrackingUnit with tenDonVi: {tracking_unit.tenDonVi} updated")
    return None


async def delete_tracking_unit(tracking_unit: TrackingUnit):
    current_time = get_current_date_time_utc_7()
    data = {
        "updated_at": current_time,
        "deleted_at": current_time,
    }

    if not tracking_unit:
        err = f"TrackingUnit with tenDonVi: {tracking_unit.tenDonVi} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_unit.deleted_at:
        err = f"TrackingUnit with: {tracking_unit.tenDonVi} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_unit.update(**data).apply()
    logger.info(f"TrackingUnit with tenDonVi: {tracking_unit.tenDonVi} deleted")
