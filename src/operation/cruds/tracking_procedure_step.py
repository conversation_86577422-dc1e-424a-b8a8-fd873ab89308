import uuid
from typing import Optional
from ..models.models import TrackingProcedureStep, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# TrackingProcedureStep - model 
# PROCEDURE_STEP - QUERY NAME
# _tracking_procedure_step - function name
# tracking_procedure_step - single
# procedure_steps - plural
# name -- search key

PROCEDURE_STEP_LIST_QUERY_V3 = """
    select
        tps.id,
        tps.procedure_id,
        tps.employee_id,
        tps.template_step_id,
        tts.step_id,
        ts."tenBuoc",
        te."hoTen<PERSON><PERSON><PERSON><PERSON>",
        te."hocVi",
        te."soDinhDanh",
        te."chucVu",
        te.unit_id,
        tu."tenDonVi",
        tu.gs_area,
        tu.gs_area_code,
        tps.created_at,
        tps.updated_at,
        tps.deleted_at
    from
        tracking_procedure_step tps
        INNER JOIN tracking_template_step tts ON tps.template_step_id = tts.id
        INNER JOIN tracking_step ts ON tts.step_id = ts.id
        INNER JOIN tracking_employee te ON tps.employee_id = te.id
        INNER JOIN tracking_unit tu ON te.unit_id = tu.id
"""

PROCEDURE_STEP_LIST_COUNT_QUERY_V3 = """
    select
        count(tps.id)
    from
        tracking_procedure_step tps
"""

async def get_tracking_procedure_step_by_id(id: uuid.uuid4):
    tracking_procedure_step = await TrackingProcedureStep.get(id)
    return tracking_procedure_step


async def get_all_procedure_steps(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="tts.gs_step_number",
    hoTenNhanVien: Optional[str] = None,
    soDinhDanh: Optional[str] = None,
    tenDonVi: Optional[str] = None,
    gs_area: Optional[str] = None,
    procedure_id: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if hoTenNhanVien:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""te."hoTenNhanVien" = '{str(hoTenNhanVien)}' """
    if soDinhDanh:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""te."soDinhDanh" = '{str(soDinhDanh)}' """
    if tenDonVi:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tu."tenDonVi" = '{str(tenDonVi)}' """
    if gs_area:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tu."gs_area" = '{str(gs_area)}' """
    if procedure_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tps."procedure_id" = '{str(procedure_id)}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tps.deleted_at is null """
    
    raw_count_query = PROCEDURE_STEP_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else PROCEDURE_STEP_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} asc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = PROCEDURE_STEP_LIST_QUERY_V3 + query_filter
    else:
        raw_query = PROCEDURE_STEP_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else PROCEDURE_STEP_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_procedure_step_existed(procedure_id: str, template_step_id: str):
    total = await db.select([db.func.count()]).where(TrackingProcedureStep.procedure_id == procedure_id).where(TrackingProcedureStep.template_step_id == template_step_id).where(TrackingProcedureStep.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_tracking_procedure_step(data: dict):
    if await is_tracking_procedure_step_existed(data['procedure_id'], data['template_step_id']):
        err = f"Error: TrackingProcedureStep with name {data['name']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await TrackingProcedureStep.create(**data)
    return result.to_dict()

async def update_tracking_procedure_step(tracking_procedure_step: TrackingProcedureStep, data: dict):
    if tracking_procedure_step.deleted_at:
        err = f"TrackingProcedureStep with name: {tracking_procedure_step.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_procedure_step.update(**data).apply()
    logger.info(f"TrackingProcedureStep with name: {tracking_procedure_step.name} updated")
    return None


async def delete_tracking_procedure_step(tracking_procedure_step: TrackingProcedureStep):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_procedure_step:
        err = f"TrackingProcedureStep with name: {tracking_procedure_step.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_procedure_step.deleted_at:
        err = f"TrackingProcedureStep with: {tracking_procedure_step.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_procedure_step.update(**data).apply()
    logger.info(f"TrackingProcedureStep with name: {tracking_procedure_step.name} deleted")
