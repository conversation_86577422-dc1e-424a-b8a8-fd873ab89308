from datetime import datetime
from functools import wraps
from typing import List, Optional

from sqlalchemy import Date, cast

from .. import logger
from ..config import config
from ..models.models import Samplecode, db
from ..utils.utils import generate_samplecode_string, get_current_date_time_utc_7

SAMPLECODE_LIST_QUERY = """
    select
        samplecode,
        printed,
        state,
        note,
        created_at,
        updated_at
    from samplecode
"""

SAMPLECODE_LIST_COUNT_QUERY = """
    select count(samplecode)
    from samplecode
"""


async def get_samplecode(samplecode: str) -> dict:
    samplecode_obj = await Samplecode.get(samplecode)
    return samplecode_obj


async def update_samplecode(
    samplecode: str, state: str, note: str = None, updated_at: str = None
):
    samplecode_obj = await get_samplecode(samplecode)
    current_time = get_current_date_time_utc_7()
    update_body = {
        "state": state,
        "updated_at": current_time if updated_at is None else updated_at,
    }
    if note:
        update_body.update(
            {
                "note": note,
            }
        )
    await samplecode_obj.update(**update_body).apply()


async def update_printed(data_arr: list):
    res = []
    current_time = get_current_date_time_utc_7()
    for data in data_arr:
        samplecode_obj = await get_samplecode(data["samplecode"])
        if samplecode_obj is None:
            err = "Cannot find samplecode_obj samplecode in database"
            res.append({"samplecode": data["samplecode"], "err_msg": err})
            continue
        updated_body = {"printed": data["is_printed"], "updated_at": current_time}
        await samplecode_obj.update(**updated_body).apply()
        res.append(samplecode_obj.to_dict())
    return res, None


async def count_samplecodes():
    total = db.select([db.func.count(Samplecode.samplecode)])
    return await total.gino.scalar()


async def delete_samplecode(samplecode: str):
    return await update_samplecode(
        samplecode=samplecode,
        state=config["DEACTIVATED_SAMPLECODE_STATE"],
        updated_at=datetime.strptime("2000-01-01", "%Y-%m-%d"),
    )


async def delete_samplecodes(samplecode_list: List[str]):
    try:
        for samplecode in samplecode_list:
            await delete_samplecode(samplecode=samplecode)
        return samplecode_list
    except Exception as e:
        raise ValueError(f"delete samplecode {samplecode} with error {e}")


async def count_matched_samplecodes(
    samplecode: Optional[str] = None,
    state: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
):
    results = db.select([db.func.count(Samplecode.samplecode)])
    if samplecode:
        results = results.where(Samplecode.samplecode.like(f"%{samplecode}%"))
    if state:
        results = results.where(Samplecode.state == state)
    if start_date:
        results = results.where(cast(Samplecode.created_at, Date) >= start_date)
    if end_date:
        results = results.where(cast(Samplecode.created_at, Date) <= end_date)
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.exception(e)
        return 0


async def get_all_samplecodes(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    samplecode: Optional[str] = None,
    state: Optional[List[str]] = None,
    note: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = None,
):
    query_filter = ""
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"samplecode = '{samplecode}' "
    if state:
        state_filter = ""
        s_str = [f"'{s}'" for s in state]
        state_filter = ",".join(s_str)
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"state in ({state_filter}) "
    if note:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(note) like '%{note.lower()}%' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_at::date >= '{start_date}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"created_at::date <= '{end_date}' "
    raw_count_query = (
        SAMPLECODE_LIST_COUNT_QUERY + "where " + query_filter
        if query_filter != ""
        else SAMPLECODE_LIST_COUNT_QUERY
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "
    raw_query = ""
    if (
        query_filter.startswith("order")
        or query_filter.startswith("limit")
        or query_filter.startswith("offset")
    ):
        raw_query = SAMPLECODE_LIST_QUERY + query_filter
    else:
        raw_query = (
            SAMPLECODE_LIST_QUERY + "where " + query_filter
            if query_filter != ""
            else SAMPLECODE_LIST_QUERY
        )
    results = await db.all(db.text(raw_query))
    return results, total


async def duplicated_samplecode(samplecode: str) -> bool:
    samplecode_obj = await get_samplecode(samplecode)
    if samplecode_obj and samplecode_obj.state == config["USED_SAMPLECODE_STATE"]:
        return True
    return False


async def available_samplecode(samplecode: str) -> bool:
    samplecode_obj = await get_samplecode(samplecode)
    if samplecode_obj and samplecode_obj.state in (
        config["DEFAULT_SAMPLECODE_STATE"],
        config["SHIPPED_SAMPLECODE_STATE"],
    ):
        return True
    return False


async def deactivated_samplecode(samplecode: str) -> bool:
    samplecode_obj = await get_samplecode(samplecode)
    if (
        samplecode_obj
        and samplecode_obj.state == config["DEACTIVATED_SAMPLECODE_STATE"]
    ):
        return True
    return False


async def existed_samplecode(samplecode: str) -> bool:
    samplecode_obj = await get_samplecode(samplecode)
    if samplecode_obj:
        return True
    return False


async def save_samplecode(data: dict):
    await Samplecode.create(**data)


def generate_samplecode(samplecode=None):
    if not samplecode:
        samplecode = generate_samplecode_string()
    current_time = get_current_date_time_utc_7()
    data = {
        "samplecode": samplecode,
        "printed": False,
        "state": config["DEFAULT_SAMPLECODE_STATE"],
        "created_at": current_time,
        "updated_at": current_time,
    }
    return data


def validate_samplecode(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract `register_kit_req` from either kwargs or args
        req_body = kwargs.get("register_kit_req") or kwargs.get("req_body")
        # Perform the validations
        if not await existed_samplecode(req_body.samplecode):
            raise ValueError(
                f"Samplecode {req_body.samplecode} đã bị xóa hoặc không tồn tại."
            )

        if not await available_samplecode(req_body.samplecode):
            if await deactivated_samplecode(req_body.samplecode):
                raise ValueError(f"Samplecode {req_body.samplecode} đã bị vô hiệu hóa.")
            elif await duplicated_samplecode(req_body.samplecode):
                raise ValueError(
                    f"Samplecode {req_body.samplecode} đã được đăng ký trước đó."
                )

        # If all checks pass, proceed with the original function
        return await func(*args, **kwargs)

    return wrapper
