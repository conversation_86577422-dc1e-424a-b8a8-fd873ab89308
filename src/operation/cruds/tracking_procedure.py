import uuid
from typing import Optional
from ..models.models import TrackingProcedure, db, TrackingProcedureStep
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

from .tracking_procedure_step import *

# TrackingProcedure - model 
# PROCEDURE - QUERY maXetNghiem
# _tracking_procedure - function maXetNghiem
# tracking_procedure - single
# procedures - plural
# maXetNghiem -- search key

PROCEDURE_LIST_QUERY_V3 = """
    select
        tp.id,
        tp.tracking_id,
        tp.template_id,
        tp."maXetNghiem",
        tp."nhietDoLuuTru",
        tp."gs_barcode",
        tp."gs_lid",
        tp."gs_totalStep",
        tp."gs_currStep",
        tp."gs_totalStep",
        tp."gs_isComplete",
        tt."congNghe",
        tt."tenKit",
        tt.gs_template_name,
        tp.created_at,
        tp.updated_at,
        tp.deleted_at
    from
        tracking_procedure tp
        INNER JOIN tracking_template tt ON tp.template_id = tt.id
"""

PROCEDURE_LIST_COUNT_QUERY_V3 = """
    select
        count(tp.id)
    from
        tracking_procedure tp
        INNER JOIN tracking_template tt ON tp.template_id = tt.id
"""

async def get_tracking_procedure_by_id(id: uuid.uuid4):
    tracking_procedure = await TrackingProcedure.get(id)
    return tracking_procedure

async def get_tracking_procedure_by_barcode(barcode: str):
    tracking_procedure = await TrackingProcedure.query.where(TrackingProcedure.gs_barcode == barcode).gino.first()
    return tracking_procedure
    

async def get_all_procedures(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="tp.created_at",
    maXetNghiem: Optional[str] = None,
    nhietDoLuuTru: Optional[str] = None,
    gs_barcode: Optional[str] = None,
    gs_lid: Optional[str] = None,
    gs_isComplete: Optional[bool] = None,
    congNghe: Optional[str] = None,
    gs_template_name: Optional[str] = None,
    tracking_id: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if maXetNghiem:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(tp."maXetNghiem") like '%{maXetNghiem.lower()}%' """
    if nhietDoLuuTru:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tp."nhietDoLuuTru" = '{str(nhietDoLuuTru)}' """
    if gs_isComplete:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tp."gs_isComplete" is '{gs_isComplete}' """
    if gs_barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tp."gs_barcode" = '{str(gs_barcode)}' """
    if gs_lid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tp."gs_lid" = '{str(gs_lid)}' """
    if congNghe:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tt."congNghe" = '{str(congNghe)}' """
    if gs_template_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tt."gs_template_name" = '{str(gs_template_name)}' """
    if tracking_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tp."tracking_id" = '{str(tracking_id)}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tp.deleted_at is null """
    
    raw_count_query = PROCEDURE_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else PROCEDURE_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = PROCEDURE_LIST_QUERY_V3 + query_filter
    else:
        raw_query = PROCEDURE_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else PROCEDURE_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_procedure_existed(maXetNghiem: str):
    total = await db.select([db.func.count()]).where(TrackingProcedure.maXetNghiem == maXetNghiem).where(TrackingProcedure.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_tracking_procedure(data: dict):

    """
    tracking_id: str
    template_id: str
    maXetNghiem: str
    nhietDoLuuTru: str
    gs_lid: str
    gs_barcode: str
    gs_currStep: Optional[int]
    gs_totalStep: Optional[int]
    gs_isComplete: Optional[bool]
    
    cacBuocThucHien: List[BuocThucHienExtended]
    
    template_step_id: str
    employee: EmployeeBaseDetail = Field(exclude=True)
    employee_id: Optional[str]
    """

    if await is_tracking_procedure_existed(data['maXetNghiem']):
        err = f"Error: Integration TrackingProcedure with maXetNghiem {data['maXetNghiem']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    async with db.transaction() as tx:
        procedureData = {
            'tracking_id': data['tracking_id'],
            'template_id': data['template_id'],
            'maXetNghiem': data['maXetNghiem'],
            'gs_lid': data['gs_lid'],
            'gs_barcode': data['gs_barcode'],
            'nhietDoLuuTru': data['nhietDoLuuTru'],
            'created_at': current_time,
            'updated_at': current_time
        }
        procedure = await TrackingProcedure.create(**procedureData)
        procedure = procedure.to_dict()
        procedure_id = procedure.get('id')
        result = {
            **procedure
        }
        result['cacBuocXetNghiem'] = {
            "cacBuocThucHien": []
        }
        
        for buocThucHienExt in data.get('cacBuocXetNghiem').get('cacBuocThucHien'):
            procedureStep = {
                "procedure_id": procedure_id,
                "employee_id": buocThucHienExt.get('employee_id'),
                "template_step_id": buocThucHienExt.get('template_step_id')
            }
            buocThucHienExt['procedure_id']=procedure_id
            _ = await create_tracking_procedure_step(procedureStep)
            result['cacBuocXetNghiem']['cacBuocThucHien'].append(buocThucHienExt)
            
    return result

async def update_tracking_procedure(tracking_procedure: TrackingProcedure, data: dict):
    if tracking_procedure.deleted_at:
        err = f"TrackingProcedure with maXetNghiem: {tracking_procedure.maXetNghiem} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_procedure.update(**data).apply()
    logger.info(f"TrackingProcedure with maXetNghiem: {tracking_procedure.maXetNghiem} updated")
    return None


async def delete_tracking_procedure(tracking_procedure: TrackingProcedure):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_procedure:
        err = f"TrackingProcedure with maXetNghiem: {tracking_procedure.maXetNghiem} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_procedure.deleted_at:
        err = f"TrackingProcedure with: {tracking_procedure.maXetNghiem} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_procedure.update(**data).apply()
    logger.info(f"TrackingProcedure with maXetNghiem: {tracking_procedure.maXetNghiem} deleted")
