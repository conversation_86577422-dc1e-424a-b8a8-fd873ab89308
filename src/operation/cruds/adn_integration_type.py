import uuid
from typing import Optional
from ..models.models import AdnIntegrationType, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)
# AdnIntegrationType - model 
# ADN_INTEGRATION_TYPE - QUERY NAME
# _adn_integration_type - function name
# adn_integration_type ait - single
# ait. - SQL alias
# adnIntegrationType - single
# adn_integration_types - plural
# _adn_integration_types
# name -- search key

ADN_INTEGRATION_TYPE_LIST_QUERY_V3 = """
    select
        ait.id,
        ait.name,
        ait.adn_type,
        ait.method,
        ait.created_at,
        ait.updated_at,
        ait.deleted_at
    from
        adn_integration_type ait
"""

ADN_INTEGRATION_TYPE_LIST_COUNT_QUERY_V3 = """
    select
        count(ait.id)
    from
        adn_integration_type ait
"""

async def get_adn_integration_type_by_id(id: uuid.uuid4):
    adnIntegrationType = await AdnIntegrationType.get(id)
    return adnIntegrationType

async def get_adn_integration_type_by_mapping(adn_type: str, method: str):
    adn_integration_type = await AdnIntegrationType.query.where(AdnIntegrationType.adn_type == adn_type).where(AdnIntegrationType.method == method).gino.first()
    return adn_integration_type


async def get_all_adn_integration_types(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="ait.created_at",
    name: Optional[str] = None,
    adn_type: Optional[str] = None,
    method: Optional[str] = None,
    include_deleted: Optional[bool]=False
):
    query_filter = ""
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(ait.name) like '%{name.lower()}%' "
    if adn_type:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ait.adn_type = '{str(adn_type)}' "
    if method:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ait.method = '{str(method)}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ait.deleted_at is null "
    
    raw_count_query = ADN_INTEGRATION_TYPE_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else ADN_INTEGRATION_TYPE_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = ADN_INTEGRATION_TYPE_LIST_QUERY_V3 + query_filter
    else:
        raw_query = ADN_INTEGRATION_TYPE_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else ADN_INTEGRATION_TYPE_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_adn_integration_type_existed_w_name(name: str):
    total = await db.select([db.func.count()]).where(AdnIntegrationType.name == name).where(AdnIntegrationType.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def is_adn_integration_type_existed(adn_type: str, method: str):
    total = await db.select([db.func.count()]).where(AdnIntegrationType.adn_type == adn_type).where(AdnIntegrationType.method == method).where(AdnIntegrationType.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_adn_integration_type(data: dict):
    if await is_adn_integration_type_existed_w_name(data['name']):
        err = f"Error: Integration AdnIntegrationType with name {data['name']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    if await is_adn_integration_type_existed(adn_type=data['adn_type'], method=data['method']):
        err = f"Error: Integration AdnIntegrationType with adn_type {data['adn_type']} and method {data['method']}  is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        'name': data['name'],
        'adn_type': data['adn_type'],
        'method': data['method'], 
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await AdnIntegrationType.create(**data)
    return result.to_dict()
    pass

async def update_adn_integration_type(adnIntegrationType: AdnIntegrationType, data: dict):
    if adnIntegrationType.deleted_at:
        err = f"Integration request with name: {adnIntegrationType.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await adnIntegrationType.update(**data).apply()
    logger.info(f"Integration request with name: {adnIntegrationType.name} updated")
    return None


async def delete_adn_integration_type(adnIntegrationType: AdnIntegrationType):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not adnIntegrationType:
        err = f"Integration request with name: {adnIntegrationType.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    elif adnIntegrationType.deleted_at:
        err = f"Integration request with: {adnIntegrationType.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await adnIntegrationType.update(**data).apply()
    logger.info(f"Integration request with name: {adnIntegrationType.name} deleted")
