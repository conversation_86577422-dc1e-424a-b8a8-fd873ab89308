from cmath import log
from typing import List, Optional

from ..utils.utils import (
    get_current_date_time_utc_7, 
    get_time_range_days_ago, 
    get_current_date_time_utc_7, 
    get_current_date_time_utc_7_in_desired_format
)
from ..models.models import Subject, KitV3, SampleV3, db, Kit, Status, Sample
from ..config import config
from .. import logger
from .samplecodes import update_samplecode

async def get_kit(barcode: str) -> Kit:
    kit = await Kit.get(barcode)
    return kit

# async def get_kit_v2(barcode: str) -> dict:
async def get_kits_with_status(barcode: str, status: str) -> Kit:
    kit = await Kit.query.where(Kit.barcode==barcode).where(Kit.current_status==status).gino.first()
    return kit
    

async def get_all_kits(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    barcode: Optional[str] = None,
    userid: Optional[str] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    myself: Optional[bool] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    order_by: Optional[str] = None,
    exclude_status: Optional[str] = None
):
    results = Kit.query
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if userid:
        results = results.where(Kit.userid == userid)
    if barcode:
        results = results.where(Kit.barcode.like(f"%{barcode}%"))
    if email:
        results = results.where(Kit.email.like(f"%{email}%"))
    if phone_number:
        results = results.where(Kit.email.like(f"%{phone_number}%"))
    if myself:
        results = results.where(Kit.myself == myself)
    if name:
        results = results.where(Kit.name.like(f"%{name}%"))
    if gender:
        results = results.where(Kit.gender == gender)
    if nickname:
        results = results.where(Kit.nickname.like(f"%{nickname}%"))
    if current_status:
        results = results.where(Kit.current_status.like(f"%{current_status}%"))
    if exclude_status:
        results = results.where(Kit.current_status.notlike(f"%{exclude_status}"))
    if order_by:
        results = results.order_by(getattr(Kit, order_by).desc())
    total = await count_matched_kits(
        barcode=barcode,
        userid=userid,
        email=email,
        phone_number=phone_number,
        myself=myself,
        name=name,
        gender=gender,
        nickname=nickname,
        current_status=current_status,
        exclude_status=exclude_status,
    )
    return [r for r in await results.gino.all()], total


async def count_matched_kits(
        barcode: Optional[str] = None,
        userid: Optional[str] = None,
        email: Optional[str] = None,
        phone_number: Optional[str] = None,
        myself: Optional[bool] = None,
        name: Optional[str] = None,
        gender: Optional[str] = None,
        nickname: Optional[str] = None,
        current_status: Optional[str] = None,
        exclude_status: Optional[str] = None
):
    results = db.select([db.func.count()])
    if userid:
        results = results.where(Kit.userid == userid)
    if barcode:
        results = results.where(Kit.barcode.like(f"%{barcode}%"))
    if email:
        results = results.where(Kit.email.like(f"%{email}%"))
    if phone_number:
        results = results.where(Kit.email.like(f"%{phone_number}%"))
    if myself:
        results = results.where(Kit.myself == myself)
    if name:
        results = results.where(Kit.name.like(f"%{name}%"))
    if nickname:
        results = results.where(Kit.nickname.like(f"%{nickname}%"))
    if current_status:
        results = results.where(Kit.current_status.like(f"%{current_status}%"))
    if exclude_status:
        results = results.where(Kit.current_status.notlike(f"%{exclude_status}%"))
    if gender:
        results = results.where(Kit.gender == gender)
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


# async def update_kit(kit: Kit, data: dict):
#     await kit.update(**data).apply()

async def update_kit_with_default_link(kit: Kit, data: dict):
    await kit.update(**data).apply()
    return None

async def update_kit(kit: Kit):
    kit.updated_time = get_current_date_time_utc_7()
    await kit.update(**kit.to_dict()).apply()
    # return results make infinite recursion error
    # return results
    


async def existed_kit(barcode: str) -> bool:
    kit = await get_kit(barcode)
    if kit:
        return True
    return False


async def count_kits():
    total = db.select([db.func.count(Kit.barcode)])
    return await total.gino.scalar()


# async def delete_kit(barcode: str):
#     kit = await get_kit(barcode)
#     if not kit:
#         err = f"Kit with id {barcode} can not be found"
#         return None, err
#     await kit.delete()
#     return kit, None

async def delete_kit(barcode: str):
    kit = await get_kit(barcode=barcode)
    if not kit:
        err = f"Kit with id {barcode} can not be found"
        raise ValueError(err)
    if kit.deleted_at is not None:
        err = f"Kit with id {barcode} is already deleted"
        raise ValueError(err)
    kit.deleted_at=get_current_date_time_utc_7()
    kit.current_status = 'DELETED'
    await kit.update(**kit.to_dict()).apply()
    return kit.to_dict()


async def delete_kit_status(barcode: str):
    await Status.delete.where(Status.barcode == barcode).gino.status()


async def get_status(barcode: str):
    status = [
        r
        for r in (
            await Status.query.where(Status.barcode == barcode).gino.all()
        )
    ]
    if status:
        return status, None
    else:
        err = f"Status of the Kit with id {barcode} is not found"
        return [], err


async def get_status_from_name(barcode: str, status: str):
    status_ids = [
        r.to_dict()
        for r in (
            await Status.query.where(Status.barcode == barcode).where(Status.status == status).gino.all()
        )
    ]
    if status_ids:
        return status_ids, None
    else:
        err = f"Status {status} of the Kit with id {barcode} is not found"
        return [], err


async def delete_status_by_name(barcode: str, status: str):
    status, err = await get_status_from_name(barcode, status)
    if err:
        return err
    for s in status:
        if isinstance(s, Status):
            await Status.delete.where(Status.barcode == s.id).gino.status()
    return None


async def existed_status(data: dict):
    item = await Status.query.where(
        Status.barcode == data['barcode']
    ).where(
        Status.status == data['status']
    ).gino.first()

    return item


def init_status(barcode: str) -> dict:
    current_time = get_current_date_time_utc_7()
    initial_status = {
        'barcode': barcode,
        'status': config['REGISTERED_KIT_STATUS'],
        'note': '',
        'created_time': current_time,
        'updated_time': current_time
    }

    return initial_status

# async def check_and_update_booking_status(barcode: str):
#     partner_booking = await get_partner_booking_id_by_barcode(barcode=barcode)
    
        

async def save_status(body: dict):
    try:
        current_time = get_current_date_time_utc_7()
        data = {
            'barcode': body['barcode'],
            'status': body['status'],
            'note': '' if not body.get('note') else body['note']
        }
        item = await existed_status(data)
        if not item:
            data = {
                **data,
                'created_time': current_time,
                'updated_time': current_time
            }
            result = await Status.create(**data)
            return result, None
        else:
            data = {
                **data,
                'updated_time': current_time
            }
            await item.update(**data).apply()
            # booking
            return item, None
    except Exception as err:
        return None, str(err)
    
async def create_status(body: dict):
    current_time = get_current_date_time_utc_7()
    data = {
        'barcode': body['barcode'],
        'status': body['status'],
        'note': '' if not body.get('note') else body['note']
    }
    item = await existed_status(data)
    if not item:
        data = {
            **data,
            'created_time': current_time,
            'updated_time': current_time
        }
        result = await Status.create(**data)
        return result
    else:
        data = {
            **data,
            'updated_time': current_time
        }
        await item.update(**data).apply()
        return item

async def get_sample(barcode: str) -> dict:
    sample = await Sample.query.where(Sample.barcode == barcode).gino.all()
    return sample
    
      
async def update_sample_gender(data: dict, id: str):
    sample = await get_sample(id)
    if sample is not None and len(sample) > 0:
        for s in sample:
            updated_sample = {**s.to_dict(), 'gender': data["gender"]}
            await s.update(**updated_sample).apply()
    else:
      logger.info(f"Could not find sample with barcode {id} in Sample table")
    


async def update_status(data: dict):
    current_time = get_current_date_time_utc_7()
    kit = await get_kit(data['barcode'])
    if data['status'] not in config['ALLOWED_KIT_STATUS']:
        err = 'Error: requested status is not in allowed status list'
        return err
    new_status, err = await save_status(data)
    if err: return err
    updated_status_body = {
        'barcode': data['barcode'],
        'current_status': new_status.status,
        'current_status_id': new_status.id,
        'updated_time': current_time
    }
    await kit.update(**updated_status_body).apply()


async def create_kit(data: Kit):
    if await existed_kit(data.barcode):
        err = f"Error: Kit with barcode {data.barcode} is already registered"
        return data, err
    status = init_status(data.barcode)
    result = await Status.create(**status)
    data.current_status = result.status
    data.current_status_id = result.id
    created_time = get_current_date_time_utc_7()
    data.created_time = created_time
    data.updated_time = created_time
    result = await data.create()
    return result.to_dict(), None

async def register(data: dict):
    if await existed_kit(data['barcode']):
        err = f"Error: Kit with barcode {data['barcode']} is already registered"
        return data, err
    current_time = get_current_date_time_utc_7()
    status = init_status(data['barcode'])
    result = await Status.create(**status)
    status_id = result.id
    status_name = result.status
    data = {
        'barcode': data['barcode'],
        'nickname': data['nickname'],
        'name': data['name'],
        'userid': data['userid'],
        'email': data.get('email'),
        'phone_number': data.get('phone_number'),
        'gender': data['gender'],
        'version': str(data['version']),
        'myself': data['myself'],
        'current_status': status_name,
        'current_status_id': status_id,
        'info': {},
        'dob': data['dob'],
        'promotion': data['promotion'],
        'created_time': current_time,
        'updated_time': current_time
    }
    result = await Kit.create(**data)
    return result.to_dict(), None


async def get_status_with_days_ago(
        status: str,
        days: str,
        offset: Optional[int] = None,
        size: Optional[int] = None,
        order_by: Optional[str] = None,
):
    start_date_time, end_date_time = get_time_range_days_ago(days=days)
    results = Status.query
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    results = results.where(Status.updated_time.between(start_date_time, end_date_time))
    results = results.where(Status.status == status)
    if order_by:
        results = results.order_by(getattr(Status, order_by).desc())

    total = await count_matched_status(
        status,
        start_date_time,
        end_date_time,
        order_by=order_by
    )

    return [r.to_dict() for r in await results.gino.all()], total


async def count_matched_status(
        status: str,
        start_date_time: str,
        end_date_time: str,
        order_by: Optional[str] = None,
):
    results = db.select([db.func.count()])
    results = results.where(Status.updated_time.between(start_date_time, end_date_time))
    results = results.where(Status.status == status)
    if order_by:
        results = results.order_by(getattr(Status, order_by).desc())
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.exception(e)
        return 0

async def get_sample_by_samplecode_v3(
    samplecode: str,
    ):
    result = await SampleV3.query.where(SampleV3.samplecode == samplecode).gino.first()
    return result

async def get_sample_v3_via(
    subject_id: str,
    ):
    results = SampleV3.query
    if subject_id:
        results = results.where(SampleV3.subject_id == subject_id)
    return [r for r in await results.gino.all()]
    

def init_status_v3() -> dict:
    current_time = get_current_date_time_utc_7()
    initial_status = {
        # 'barcode': barcode,
        'status': config['REGISTERED_KIT_STATUS'],
        'note': '',
        'created_time': current_time,
        'updated_time': current_time
    }

    return initial_status


async def create_sample_v3(data: SampleV3):
    data.updated_at = get_current_date_time_utc_7()
    result = await data.create()
    
    await update_samplecode(
        samplecode = data.samplecode, 
        state = config['USED_SAMPLECODE_STATE']
        )
    
    return result.to_dict(), None

async def create_kit_v3(data: KitV3):
    
    # current_status only appear after barcode is generated
    
    # if await existed_kit(data.barcode):
    #     err = f"Error: Kit with barcode {data.barcode} is already registered"
    #     return data, err
    # status = init_status(data.barcode)
    # result = await Status.create(**status)
    # data.current_status = result.status
    # data.current_status_id = result.id

    result = await data.create()
    kit_data = result.to_dict()
    kit_data['kit_uuid']=kit_data['id']
    kit_data.pop('id')
    return kit_data, None

async def update_sample_scan_status_v3(sample: SampleV3, data: SampleV3):
    data.updated_at = get_current_date_time_utc_7()
    if (data.scan_status == 2 and not sample.sample_receipt_date):
        curr_time_w_std_format = get_current_date_time_utc_7_in_desired_format()
        logger.info("Update sample_receipt_date with current time %s", curr_time_w_std_format)
        data.sample_receipt_date = curr_time_w_std_format
        
    await sample.update(**data.to_dict()).apply()
    
async def update_kit_barcode_v3(kit: KitV3, data: KitV3):
    data.updated_at = get_current_date_time_utc_7()
    await kit.update(**data.to_dict()).apply()

async def get_kit_v3(barcode: str) -> KitV3:
    kit = await KitV3.query.where(KitV3.barcode == barcode).gino.first()
    return kit

async def existed_status_v3(data: dict):
    item = await Status.query.where(
        Status.barcode == data['barcode']
    ).where(
        Status.status == data['status']
    ).gino.first()

    return item

async def save_status_v3(body: dict):
    try:
        current_time = get_current_date_time_utc_7()
        data = {
            'barcode': body.get('barcode'),
            'status': body.get('status'),
            'note': '' if not body.get('note') else body['note']
        }
        item = await existed_status_v3(data)
        if not item:
            data = {
                **data,
                'created_time': current_time,
                'updated_time': current_time
            }
            result = await Status.create(**data)
            return result, None
        else:
            data = {
                **data,
                'updated_time': current_time
            }
            await item.update(**data).apply()
            # booking
            return item, None
    except Exception as err:
        return None, str(err)
    
async def create_status_v3(body: dict):
    current_time = get_current_date_time_utc_7()
    data = {
        'barcode': body.get('barcode'),
        'status': body.get('status'),
        'note': '' if not body.get('note') else body['note']
    }
    item = await existed_status_v3(data)
    if not item:
        data = {
            **data,
            'created_time': current_time,
            'updated_time': current_time
        }
        result = await Status.create(**data)
        return result
    else:
        data = {
            **data,
            'updated_time': current_time
        }
        await item.update(**data).apply()
        return item

async def update_sample_v3(sample: SampleV3):
    sample.updated_time = get_current_date_time_utc_7()
    await sample.update(**sample.to_dict()).apply()
    # return results make infinite recursion error
    # return results

async def get_status_v3(barcode: str):
    status = [
        r
        for r in (
            await Status.query.where(Status.barcode == barcode).gino.all()
        )
    ]
    if status:
        return status, None
    else:
        err = f"Status of the Sample with Barcode {barcode} is not found"
        return [], err

async def get_sample_with_status_v3(samplecode: str) -> SampleV3:
    sample = await SampleV3.query.where(SampleV3.samplecode==samplecode).gino.first()
    return sample

async def get_sample_v3(samplecode: str) -> SampleV3:
    sample = await SampleV3.query.where(SampleV3.samplecode==samplecode).gino.first()
    return sample

async def get_kit_v3_w_samplecode(samplecode: str) -> KitV3:
    kit = await KitV3.query.where(KitV3.samplecode==samplecode).gino.first()
    return kit

# get_kit_v3_w_kit_uuid
async def get_kit_v3_w_kit_uuid(kit_uuid: str) -> KitV3:
    kit = await KitV3.query.where(KitV3.id==kit_uuid).gino.first()
    return kit
async def get_kit_v3_w_barcode(barcode: str) -> KitV3:
    kit = await KitV3.query.where(KitV3.barcode==barcode).gino.first()
    return kit

async def update_kit_v3(kit: KitV3):
    kit.updated_at = get_current_date_time_utc_7()
    await kit.update(**kit.to_dict()).apply()
    
async def update_sample_v3(sample: SampleV3):
    sample.updated_at = get_current_date_time_utc_7()
    await sample.update(**sample.to_dict()).apply()
    
async def update_subject_v3(subject: Subject):
    subject.updated_at = get_current_date_time_utc_7()
    await subject.update(**subject.to_dict()).apply()

async def update_kit_with_default_link_v3(kit: KitV3, data: dict):
    await kit.update(**data).apply()
    return None

async def delete_kit_v3(barcode: str):
    kit = await get_kit_v3(barcode=barcode)
    if not kit:
        err = f"Kit with id {barcode} can not be found"
        raise ValueError(err)
    if kit.deleted_at is not None:
        err = f"Kit with id {barcode} is already deleted"
        raise ValueError(err)
    kit.deleted_at=get_current_date_time_utc_7()
    kit.current_status = 'DELETED'
    await kit.update(**kit.to_dict()).apply()
    return kit.to_dict()


async def delete_kit_status(barcode: str):
    await Status.delete.where(Status.barcode == barcode).gino.status()


async def update_kit_with_new_samplecode(barcode: str, new_samplecode: str):
    UPDATE_LAB_SAMPLE = f"""
    UPDATE kit
    SET samplecode='{new_samplecode}'
    WHERE barcode='{barcode}'
    """
    result = await db.all(db.text(UPDATE_LAB_SAMPLE))
    return result