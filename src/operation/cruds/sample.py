from typing import List, Optional

from ..models.models import db

SAMPLE_QUERY_V3 = """
    select
        sam.samplecode,
        sub.user_id,
        sub.identifier_code,
        sub.full_name,
        sub.dob::date,
        sub.yob,
        sub.legal_guardian,
        sub.gender,
        sub.phone_number,
        sub.email,
        sub.address,
        sub.martyr_name,
        sub.martyr_relationships,
        a.id as account_id,
        a.name as account_name,
        s.id as source_id,
        st.name as nominator,
        st.id as nominator_id,
        stf.name as freelancer,
        stf.id as freelancer_id,
        st2.name as sale_pic,
        sam.subject_id,
        sam.sample_collection_date::timestamp,
        sam.sample_collection_time,
        sam.sample_receipt_date::timestamp,
        sam.lab_receipt_date::timestamp,
        sam.sample_collector_name,
        sam.sample_receiver_name,
        sam.scan_status,
        sub.diagnosis,
        sub.validate_account,
        sam.sample_type,
        sp.name as sponsor_name
    from
        sample sam
        INNER JOIN subject sub ON sam.subject_id = sub.id
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
        left join sponsors sp on sam.sponsor_id = sp.id
"""

SAMPLE_COUNT_QUERY_V3 = """
    select
        count(sam.samplecode)
    from
        sample sam
        INNER JOIN subject sub ON sam.subject_id = sub.id
        inner join source s on sam.source_id = s.id
        inner join sale_account_history sah on s.account_history_id = sah.id
        inner join account a on sah.account_id = a.id
        left join staff st on s.nominator_id = st.id
        left join staff stf on s.freelancer_id = stf.id
        inner join staff st2 on sah.pic_id = st2.id
"""


def get_kit_sample_list_query_v3(barcode: str, product_code: str):
    skip_to_kit_instead = ""

    if barcode:
        skip_to_kit_instead = f"barcode='{barcode}' "
    else:
        skip_to_kit_instead = ""

    # print("skip_to_kit_instead: ", skip_to_kit_instead)

    if product_code:
        if len(product_code) == 1:
            skip_to_kit_instead += "and " if skip_to_kit_instead != "" else " "
            skip_to_kit_instead += f"product_code = '{product_code[0]}' "
        else:
            skip_to_kit_instead += "and " if skip_to_kit_instead != "" else " "
            skip_to_kit_instead += f"product_code in {tuple(product_code)} "

    if skip_to_kit_instead:
        skip_to_kit_instead = "WHERE " + skip_to_kit_instead

    # print("skip_to_kit_instead: ", skip_to_kit_instead)
    KIT_SAMPLE_LIST_QUERY_V3 = f"""
        select
            ls.lid,
            sam.samplecode,
            latest_kit.id as kit_uuid,
            latest_kit.barcode,
            latest_kit.nickname,
            sub.user_id,
            sub.identifier_code,
            sub.full_name,
            sub.dob::date,
            sub.yob,
            sub.legal_guardian,
            sub.gender,
            sub.phone_number,
            sub.email,
            sub.address,
            idc.manual_input,
            latest_kit.product_name,
            latest_kit.product_code,
            latest_kit.product_type,
            latest_kit.current_status,
            latest_kit.workflow,
            a.id as account_id,
            a.name as account_name,
            s.id as source_id,
            st.name as nominator,
            st.id as nominator_id,
            stf.name as freelancer,
            stf.id as freelancer_id,
            st2.name as sale_pic,
            sam.subject_id,
            sam.sample_collection_date::timestamp,
            sam.sample_collection_time,
            sam.sample_receipt_date::timestamp,
            sam.lab_receipt_date::timestamp,
            sam.sample_collector_name,
            sam.sample_receiver_name,
            latest_kit.expected_report_release_date::date,
            latest_kit.customer_support_id,
            latest_kit.customer_support_name,
            latest_kit.free_of_charge,
            latest_kit.is_priority,
            sam.scan_status,
            latest_kit.note,
            latest_kit.promotion as promotion_id,
            p.code as promotion_code,
            b.number as batch_number,
            bm.wetlab_date,
            sub.diagnosis,
            sub.validate_account,
            latest_kit.actual_report_release_time::date as actual_report_release_date,
            latest_kit.actual_report_release_time,
            latest_kit.default_pdf_link,
            sam.sample_type,
            case 
                when latest_kit.default_pdf_link is null then 'NOT_AVAILABLE'
                when latest_kit.pdf_generation_date > current_date - interval '6 days' then 'AVAILABLE'
                else 'EXPIRED'
            end as pdf_status,
            dna_records.lid as dna_extraction_id,
            tu."tenDonVi" as sample_collector_unit_name,
            sam.created_at,
            sam.created_at as upgraded_at,
            sam.updated_at,
            sam.deleted_at,
            idc.deleted_at as identity_card_deleted_at
        from
            sample sam
            LEFT JOIN tracking t ON sam.samplecode = t.samplecode
            LEFT JOIN tracking_collection tc ON t.id = tc.tracking_id
            LEFT JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
            INNER JOIN (
                SELECT DISTINCT ON (samplecode) *
                FROM kit {skip_to_kit_instead}
                ORDER BY samplecode, created_at DESC
            ) AS latest_kit ON sam.samplecode = latest_kit.samplecode
            INNER JOIN subject sub ON sam.subject_id = sub.id
            inner join source s on sam.source_id = s.id
            inner join sale_account_history sah on s.account_history_id = sah.id
            inner join account a on sah.account_id = a.id
            left join identity_card idc on idc.identifier_code = sub.identifier_code
            left join promotion p on latest_kit.promotion = p.id
            left join staff st on s.nominator_id = st.id
            left join staff stf on s.freelancer_id = stf.id
            inner join staff st2 on sah.pic_id = st2.id
            left join lab_sample ls on latest_kit.barcode = ls.barcode
            left join (
                SELECT dna.lid, dna.id
                FROM dna_extractions dna
                LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
                LEFT JOIN plate pl ON smp.plate_id = pl.id
                WHERE dna.dna_qc_status = 'PASS'
                AND dna.id = (
                    SELECT dna_inner.id
                    FROM dna_extractions dna_inner
                    LEFT JOIN sample_mapping smp_inner ON smp_inner.dna_extraction_id = dna_inner.id
                    LEFT JOIN plate pl_inner ON smp_inner.plate_id = pl_inner.id
                    WHERE dna_inner.lid = dna.lid
                    ORDER BY COALESCE(pl_inner.name, '0') DESC
                    LIMIT 1
                )
            ) dna_records on dna_records.lid = ls.lid
            left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
            left join chip c on c.chip_id = smp.chip_id
            left join plate pl on smp.plate_id = pl.id
            left join batch_mapping bm on pl.id = bm.plate_id
            left join batch b on b.id = bm.batch_id
    """

    return KIT_SAMPLE_LIST_QUERY_V3

def get_kit_sample_list_query_v4(barcode: str, product_code):
    # Tạo danh sách điều kiện WHERE
    conditions = []

    if barcode:
        conditions.append(f"barcode = '{barcode}'")

    if product_code:
        if isinstance(product_code, str):
            conditions.append(f"product_code = '{product_code}'")
        elif isinstance(product_code, (list, tuple, set)):
            if len(product_code) == 1:
                conditions.append(f"product_code = '{list(product_code)[0]}'")
            elif len(product_code) > 1:
                product_code_list = tuple(product_code)
                conditions.append(f"product_code IN {product_code_list}")

    # Gộp các điều kiện thành WHERE
    where_clause = f"WHERE {' AND '.join(conditions)}" if conditions else ""

    KIT_SAMPLE_LIST_QUERY_V4 = f"""
        SELECT
            ls.lid,
            sam.samplecode,
            latest_kit.id AS kit_uuid,
            latest_kit.barcode,
            latest_kit.nickname,
            sub.user_id,
            sub.identifier_code,
            sub.full_name,
            sub.dob::date,
            sub.yob,
            sub.legal_guardian,
            sub.gender,
            sub.phone_number,
            sub.email,
            sub.address,
            idc.manual_input,
            latest_kit.product_name,
            latest_kit.product_code,
            latest_kit.product_type,
            latest_kit.current_status,
            latest_kit.workflow,
            a.id AS account_id,
            a.name AS account_name,
            s.id AS source_id,
            st.name AS nominator,
            st.id AS nominator_id,
            stf.name AS freelancer,
            stf.id AS freelancer_id,
            st2.name AS sale_pic,
            sam.subject_id,
            sam.sample_collection_date::timestamp,
            sam.sample_collection_time,
            sam.sample_receipt_date::timestamp,
            sam.lab_receipt_date::timestamp,
            sam.sample_collector_name,
            sam.sample_receiver_name,
            latest_kit.expected_report_release_date::date,
            latest_kit.customer_support_id,
            latest_kit.customer_support_name,
            latest_kit.free_of_charge,
            latest_kit.is_priority,
            sam.scan_status,
            latest_kit.note,
            latest_kit.promotion AS promotion_id,
            p.code AS promotion_code,
            b.number AS batch_number,
            bm.wetlab_date,
            sub.diagnosis,
            sub.validate_account,
            latest_kit.actual_report_release_time::date AS actual_report_release_date,
            latest_kit.actual_report_release_time,
            latest_kit.default_pdf_link,
            sam.sample_type,
            CASE 
                WHEN latest_kit.default_pdf_link IS NULL THEN 'NOT_AVAILABLE'
                WHEN latest_kit.pdf_generation_date > current_date - INTERVAL '6 days' THEN 'AVAILABLE'
                ELSE 'EXPIRED'
            END AS pdf_status,
            dna_records.lid AS dna_extraction_id,
            tu."tenDonVi" AS sample_collector_unit_name,
            sam.created_at,
            sam.created_at AS upgraded_at,
            sam.updated_at,
            sam.deleted_at,
            idc.deleted_at AS identity_card_deleted_at
        FROM
            sample sam
            LEFT JOIN tracking t ON sam.samplecode = t.samplecode
            LEFT JOIN tracking_collection tc ON t.id = tc.tracking_id
            LEFT JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
            INNER JOIN (
                SELECT DISTINCT ON (samplecode) *
                FROM kit
                {where_clause}
                ORDER BY samplecode, created_at DESC
            ) AS latest_kit ON sam.samplecode = latest_kit.samplecode
            INNER JOIN subject sub ON sam.subject_id = sub.id
            INNER JOIN source s ON sam.source_id = s.id
            INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
            INNER JOIN account a ON sah.account_id = a.id
            LEFT JOIN identity_card idc ON idc.identifier_code = sub.identifier_code
            LEFT JOIN promotion p ON latest_kit.promotion = p.id
            LEFT JOIN staff st ON s.nominator_id = st.id
            LEFT JOIN staff stf ON s.freelancer_id = stf.id
            INNER JOIN staff st2 ON sah.pic_id = st2.id
            LEFT JOIN lab_sample ls ON latest_kit.barcode = ls.barcode
            LEFT JOIN (
                SELECT dna.lid, dna.id
                FROM dna_extractions dna
                LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
                LEFT JOIN plate pl ON smp.plate_id = pl.id
                WHERE dna.dna_qc_status = 'PASS'
                AND dna.id = (
                    SELECT dna_inner.id
                    FROM dna_extractions dna_inner
                    LEFT JOIN sample_mapping smp_inner ON smp_inner.dna_extraction_id = dna_inner.id
                    LEFT JOIN plate pl_inner ON smp_inner.plate_id = pl_inner.id
                    WHERE dna_inner.lid = dna.lid
                    ORDER BY COALESCE(pl_inner.name, '0') DESC
                    LIMIT 1
                )
            ) dna_records ON dna_records.lid = ls.lid
            LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna_records.id
            LEFT JOIN chip c ON c.chip_id = smp.chip_id
            LEFT JOIN plate pl ON smp.plate_id = pl.id
            LEFT JOIN batch_mapping bm ON pl.id = bm.plate_id
            LEFT JOIN batch b ON b.id = bm.batch_id
    """

    return KIT_SAMPLE_LIST_QUERY_V4

def get_count_kit_sample_list_query_v3(barcode: str, product_code: str):
    skip_to_kit_instead = ""

    if barcode:
        skip_to_kit_instead = f"barcode='{barcode}' "
    else:
        skip_to_kit_instead = ""

    # print("skip_to_kit_instead: ", skip_to_kit_instead)

    if product_code:
        if len(product_code) == 1:
            skip_to_kit_instead += "and " if skip_to_kit_instead != "" else " "
            skip_to_kit_instead += f"product_code = '{product_code[0]}' "
        else:
            skip_to_kit_instead += "and " if skip_to_kit_instead != "" else " "
            skip_to_kit_instead += f"product_code in {tuple(product_code)} "

    if skip_to_kit_instead:
        skip_to_kit_instead = "WHERE " + skip_to_kit_instead

    # print("skip_to_kit_instead: ", skip_to_kit_instead)

    KIT_SAMPLE_LIST_COUNT_QUERY_V3 = f"""
        select
            count(sam.samplecode)
        from
            sample sam
            LEFT JOIN tracking t ON sam.samplecode = t.samplecode
            LEFT JOIN tracking_collection tc ON t.id = tc.tracking_id
            LEFT JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
            INNER JOIN (
                SELECT DISTINCT ON (samplecode) *
                FROM kit {skip_to_kit_instead}
                ORDER BY samplecode, created_at DESC
            ) AS latest_kit ON sam.samplecode = latest_kit.samplecode
            INNER JOIN subject sub ON sam.subject_id = sub.id
            inner join source s on sam.source_id = s.id
            inner join sale_account_history sah on s.account_history_id = sah.id
            inner join account a on sah.account_id = a.id
            left join identity_card idc on idc.identifier_code = sub.identifier_code
            left join promotion p on latest_kit.promotion = p.id
            left join staff st on s.nominator_id = st.id
            left join staff stf on s.freelancer_id = stf.id
            inner join staff st2 on sah.pic_id = st2.id
            left join lab_sample ls on latest_kit.barcode = ls.barcode

    """
    # left join (
    #     select de.*
    #         from dna_extractions de
    #         inner join (
    #             select
    #                 dna.lid,
    #                 dna.id,
    #                 ROW_NUMBER() OVER (
    #                     PARTITION BY dna.lid
    #                     ORDER BY
    #                         CASE
    #                             WHEN pl.name IS NULL THEN '0'
    #                             ELSE pl.name
    #                         END
    #                     DESC
    #                 ) AS rank
    #             from dna_extractions dna
    #             left join sample_mapping smp on smp.dna_extraction_id = dna.id
    #             left join plate pl on smp.plate_id = pl.id
    #             where dna.dna_qc_status = 'PASS'
    #         ) de2 on de.id = de2.id and de.lid = de2.lid
    #         where de2.rank = 1
    #     ) dna_records on dna_records.lid = ls.lid
    # left join sample_mapping smp on smp.dna_extraction_id = dna_records.id
    # left join chip c on c.chip_id = smp.chip_id
    # left join plate pl on smp.plate_id = pl.id
    # left join batch_mapping bm on pl.id = bm.plate_id
    # left join batch b on b.id = bm.batch_id

    return KIT_SAMPLE_LIST_COUNT_QUERY_V3


def raw_querry_w_batch_number_v3(batch):
    raw_querry = f"""
    SELECT
        sam.samplecode,
        b.number AS batch_number,
        latest_kit.barcode,
        latest_kit.nickname,
        latest_kit.product_name,
        latest_kit.product_code,
        latest_kit.product_type,
        latest_kit.expected_report_release_date::date,
        latest_kit.customer_support_id,
        latest_kit.customer_support_name,
        latest_kit.free_of_charge,
        latest_kit.is_priority,
        latest_kit.actual_report_release_time::date AS actual_report_release_date,
        latest_kit.actual_report_release_time,
        latest_kit.default_pdf_link,
        latest_kit.promotion AS promotion_id,
        sub.user_id,
        sub.identifier_code,
        sub.full_name,
        sub.dob::date,
        sub.yob,
        sub.legal_guardian,
        sub.gender,
        sub.phone_number,
        sub.email,
        sub.address,
        sub.diagnosis,
        sub.validate_account,
        idc.manual_input,
        a.id AS account_id,
        a.name AS account_name,
        s.id AS source_id,
        st.name AS nominator,
        st.id AS nominator_id,
        stf.name AS freelancer,
        stf.id AS freelancer_id,
        st2.name AS sale_pic,
        latest_kit.current_status,
        sam.sample_collection_date::timestamp,
        sam.sample_collection_time,
        sam.sample_receipt_date::timestamp,
        sam.lab_receipt_date::timestamp,
        sam.sample_collector_name,
        sam.sample_receiver_name,
        latest_kit.note,
        sam.scan_status,
        sam.sample_type,
        p.code AS promotion_code,
        CASE
            WHEN latest_kit.default_pdf_link IS NULL THEN 'NOT_AVAILABLE'
            WHEN latest_kit.pdf_generation_date > current_date - interval '6 days' THEN 'AVAILABLE'
            ELSE 'EXPIRED'
        END AS pdf_status,
        dna_records.lid AS dna_extraction_id,
        latest_kit.created_at,
        latest_kit.updated_at,
        latest_kit.deleted_at,
        idc.deleted_at as identity_card_deleted_at

    FROM
        batch b
        INNER JOIN (
            SELECT bh.*
            FROM batch bh
            WHERE bh.number={batch}
        ) b2 on b.id = b2.id
        INNER JOIN batch_mapping bm ON b.id = bm.batch_id
        INNER JOIN plate pl ON bm.plate_id = pl.id
        INNER JOIN sample_mapping smp ON pl.id = smp.plate_id
        INNER JOIN dna_extractions de ON smp.dna_extraction_id = de.id
        INNER JOIN lab_sample ls ON de.lid = ls.lid
        left join (
            SELECT dna.lid, dna.id
            FROM dna_extractions dna
            LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
            LEFT JOIN plate pl ON smp.plate_id = pl.id
            WHERE dna.dna_qc_status = 'PASS'
            AND dna.id = (
                SELECT dna_inner.id
                FROM dna_extractions dna_inner
                LEFT JOIN sample_mapping smp_inner ON smp_inner.dna_extraction_id = dna_inner.id
                LEFT JOIN plate pl_inner ON smp_inner.plate_id = pl_inner.id
                WHERE dna_inner.lid = dna.lid
                ORDER BY COALESCE(pl_inner.name, '0') DESC
                LIMIT 1
            )
        ) dna_records on dna_records.lid = ls.lid
        INNER JOIN sample sam on ls.samplecode = sam.samplecode
        INNER JOIN kit latest_kit on latest_kit.barcode = ls.barcode
        INNER JOIN subject sub ON sam.subject_id = sub.id
        INNER JOIN source s ON sam.source_id = s.id
        INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
        INNER JOIN account a ON sah.account_id = a.id
        LEFT JOIN identity_card idc ON sub.identifier_code = idc.identifier_code
        LEFT JOIN promotion p ON latest_kit.promotion = p.id
        LEFT JOIN staff st ON s.nominator_id = st.id
        LEFT JOIN staff stf ON s.freelancer_id = stf.id
        INNER JOIN staff st2 ON sah.pic_id = st2.id
    """
    return raw_querry

def raw_querry_w_batch_number_v4():
    raw_querry = f"""
    SELECT
        sam.samplecode,
        b.number AS batch_number,
        latest_kit.barcode,
        latest_kit.nickname,
        latest_kit.product_name,
        latest_kit.product_code,
        latest_kit.product_type,
        latest_kit.expected_report_release_date::date,
        latest_kit.customer_support_id,
        latest_kit.customer_support_name,
        latest_kit.free_of_charge,
        latest_kit.is_priority,
        latest_kit.actual_report_release_time::date AS actual_report_release_date,
        latest_kit.actual_report_release_time,
        latest_kit.default_pdf_link,
        latest_kit.promotion AS promotion_id,
        sub.user_id,
        sub.identifier_code,
        sub.full_name,
        sub.dob::date,
        sub.yob,
        sub.legal_guardian,
        sub.gender,
        sub.phone_number,
        sub.email,
        sub.address,
        sub.diagnosis,
        sub.validate_account,
        idc.manual_input,
        a.id AS account_id,
        a.name AS account_name,
        s.id AS source_id,
        st.name AS nominator,
        st.id AS nominator_id,
        stf.name AS freelancer,
        stf.id AS freelancer_id,
        st2.name AS sale_pic,
        latest_kit.current_status,
        sam.sample_collection_date::timestamp,
        sam.sample_collection_time,
        sam.sample_receipt_date::timestamp,
        sam.lab_receipt_date::timestamp,
        sam.sample_collector_name,
        sam.sample_receiver_name,
        latest_kit.note,
        sam.scan_status,
        sam.sample_type,
        p.code AS promotion_code,
        CASE
            WHEN latest_kit.default_pdf_link IS NULL THEN 'NOT_AVAILABLE'
            WHEN latest_kit.pdf_generation_date > current_date - interval '6 days' THEN 'AVAILABLE'
            ELSE 'EXPIRED'
        END AS pdf_status,
        dna_records.lid AS dna_extraction_id,
        latest_kit.created_at,
        latest_kit.updated_at,
        latest_kit.deleted_at,
        idc.deleted_at AS identity_card_deleted_at

    FROM
        batch b
        INNER JOIN batch_mapping bm ON b.id = bm.batch_id
        INNER JOIN plate pl ON bm.plate_id = pl.id
        INNER JOIN sample_mapping smp ON pl.id = smp.plate_id
        INNER JOIN dna_extractions de ON smp.dna_extraction_id = de.id
        INNER JOIN lab_sample ls ON de.lid = ls.lid
        LEFT JOIN (
            SELECT dna.lid, dna.id
            FROM dna_extractions dna
            LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
            LEFT JOIN plate pl ON smp.plate_id = pl.id
            WHERE dna.dna_qc_status = 'PASS'
              AND dna.id = (
                  SELECT dna_inner.id
                  FROM dna_extractions dna_inner
                  LEFT JOIN sample_mapping smp_inner ON smp_inner.dna_extraction_id = dna_inner.id
                  LEFT JOIN plate pl_inner ON smp_inner.plate_id = pl_inner.id
                  WHERE dna_inner.lid = dna.lid
                  ORDER BY COALESCE(pl_inner.name, '0') DESC
                  LIMIT 1
              )
        ) dna_records ON dna_records.lid = ls.lid
        INNER JOIN sample sam ON ls.samplecode = sam.samplecode
        INNER JOIN kit latest_kit ON latest_kit.barcode = ls.barcode
        INNER JOIN subject sub ON sam.subject_id = sub.id
        INNER JOIN source s ON sam.source_id = s.id
        INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
        INNER JOIN account a ON sah.account_id = a.id
        LEFT JOIN identity_card idc ON sub.identifier_code = idc.identifier_code
        LEFT JOIN promotion p ON latest_kit.promotion = p.id
        LEFT JOIN staff st ON s.nominator_id = st.id
        LEFT JOIN staff stf ON s.freelancer_id = stf.id
        INNER JOIN staff st2 ON sah.pic_id = st2.id
    """
    return raw_querry


def count_raw_querry_w_batch_number_v3(batch):
    raw_querry = f"""
    SELECT
       count(sam.samplecode)
    FROM
        batch b
        INNER JOIN (
            SELECT bh.*
            FROM batch bh
            WHERE bh.number={batch}
        ) b2 on b.id = b2.id
        INNER JOIN batch_mapping bm ON b.id = bm.batch_id
        INNER JOIN plate pl ON bm.plate_id = pl.id
        INNER JOIN sample_mapping smp ON pl.id = smp.plate_id
        INNER JOIN dna_extractions de ON smp.dna_extraction_id = de.id
        INNER JOIN lab_sample ls ON de.lid = ls.lid
        left join (
            SELECT dna.lid, dna.id
            FROM dna_extractions dna
            LEFT JOIN sample_mapping smp ON smp.dna_extraction_id = dna.id
            LEFT JOIN plate pl ON smp.plate_id = pl.id
            WHERE dna.dna_qc_status = 'PASS'
            AND dna.id = (
                SELECT dna_inner.id
                FROM dna_extractions dna_inner
                LEFT JOIN sample_mapping smp_inner ON smp_inner.dna_extraction_id = dna_inner.id
                LEFT JOIN plate pl_inner ON smp_inner.plate_id = pl_inner.id
                WHERE dna_inner.lid = dna.lid
                ORDER BY COALESCE(pl_inner.name, '0') DESC
                LIMIT 1
            )
        ) dna_records on dna_records.lid = ls.lid
        INNER JOIN sample sam on ls.samplecode = sam.samplecode
        INNER JOIN kit latest_kit on latest_kit.barcode = ls.barcode
        INNER JOIN subject sub ON sam.subject_id = sub.id
        INNER JOIN source s ON sam.source_id = s.id
        INNER JOIN sale_account_history sah ON s.account_history_id = sah.id
        INNER JOIN account a ON sah.account_id = a.id
        LEFT JOIN identity_card idc ON sub.identifier_code = idc.identifier_code
        LEFT JOIN promotion p ON latest_kit.promotion = p.id
        LEFT JOIN staff st ON s.nominator_id = st.id
        LEFT JOIN staff stf ON s.freelancer_id = stf.id
        INNER JOIN staff st2 ON sah.pic_id = st2.id
    """
    return raw_querry


async def get_sample_detail_via_samplecode_v3(
    samplecode: str,
):
    query_filter = f"where sam.samplecode = '{samplecode}'"
    raw_query = SAMPLE_QUERY_V3 + query_filter
    raw_query += "LIMIT 1"
    results = await db.all(db.text(raw_query))
    if len(results) < 1:
        raise ValueError(f"kit with samplecode {samplecode} not found")
    return results[0]


async def get_sample_list_detail_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = None,
    barcode: Optional[int] = None,
    samplecode: Optional[int] = None,
    userid: Optional[str] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    workflow: Optional[str] = None,
    exclude_status: Optional[str] = None,
    filter_status_list: Optional[list] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    sale_pic_id: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    sample_collector_unit_name: Optional[str] = None,
    include_deleted: Optional[bool] = False,
    pic_phone_number: Optional[str] = None,
):
    query_filter = "idc.deleted_at is null "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.barcode like '%{barcode}%' "
    if userid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.user_id = '{str(userid)}' "
    if email:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.email like '%{email}%' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.phone_number like '%{phone_number}%' "
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sub.full_name) like '%{name.lower()}%' "
    if gender:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.gender = '{gender}' "
    if nickname:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.nickname like '%{nickname}%' "
    if sample_collector_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collector_name like '%{sample_collector_name}%' "
    if sample_receiver_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receiver_name like '%{sample_receiver_name}%' "
    if sample_collector_unit_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"""lower(tu."tenDonVi") = '{sample_collector_unit_name.lower()}' """
        )
    if filter_status_list:
        query_filter += "and " if query_filter != "" else " "
        if len(filter_status_list) == 1:
            query_filter += f"latest_kit.current_status = '{filter_status_list[0]}' "
        else:
            query_filter += f"latest_kit.current_status in {tuple(filter_status_list)} "
    if current_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.current_status = '{current_status}' "
    if workflow:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.workflow = '{workflow}' "
    if exclude_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.current_status not like '%{exclude_status}%' "
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.samplecode like '%{samplecode}%' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receipt_date::date >= '{start_date}' "
    # if product_code:
    #     if len(product_code) == 1:
    #         query_filter += "and " if query_filter != "" else " "
    #         query_filter += f"latest_kit.product_code = '{product_code[0]}' "
    #     else:
    #         query_filter += "and " if query_filter != "" else " "
    #         query_filter += f"latest_kit.product_code in {tuple(product_code)} "
    if sale_pic:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.name like '%{sale_pic}%' "
    if sale_pic_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.id = '{sale_pic_id}' "
    if pic_phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.phone_number = '{pic_phone_number}' "
    if account_name:
        if len(account_name) == 1:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name = '{account_name[0]}'"
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name in {tuple(account_name)} "
    if nominator:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st.name like '%{nominator}%' "

    if batch:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.number = {batch} "
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology = '{technology}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receipt_date::date <= '{end_date}' "

    if start_date or end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.scan_status = 2 "

    if release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"latest_kit.expected_report_release_date::date >= '{release_start_date}' "
        )
    if release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"latest_kit.expected_report_release_date::date <= '{release_end_date}' "
        )
    if collection_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"sam.sample_collection_date::date >= '{collection_start_date}' "
        )
    if collection_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collection_date::date <= '{collection_end_date}' "
    if actual_report_release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.actual_report_release_time::date >= '{actual_report_release_start_date}' "
    if actual_report_release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.actual_report_release_time::date <= '{actual_report_release_end_date}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += "latest_kit.deleted_at is null "

    if batch:
        COUNT_QUERY_v3 = count_raw_querry_w_batch_number_v3(batch)

        if product_code:
            if len(product_code) == 1:
                query_filter += "and " if query_filter != "" else " "
                query_filter += f"latest_kit.product_code = '{product_code[0]}' "
            else:
                query_filter += "and " if query_filter != "" else " "
                query_filter += f"latest_kit.product_code in {tuple(product_code)} "
    else:
        COUNT_QUERY_v3 = get_count_kit_sample_list_query_v3(
            barcode=barcode, product_code=product_code
        )

    raw_count_query = (
        COUNT_QUERY_v3 + "where " + query_filter
        if query_filter != ""
        else COUNT_QUERY_v3
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if (order_by == "updated_at") or not order_by:
        order_by = "sam.updated_at"
    query_filter += f"order by {order_by} desc "

    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if batch:
        LIST_QUERY_V3 = raw_querry_w_batch_number_v3(batch)
    else:
        LIST_QUERY_V3 = get_kit_sample_list_query_v3(
            barcode=barcode, product_code=product_code
        )
    if query_filter.startswith("order"):
        raw_query = LIST_QUERY_V3 + query_filter
    else:
        raw_query = (
            LIST_QUERY_V3 + "where " + query_filter
            if query_filter != ""
            else LIST_QUERY_V3
        )
    results = await db.all(db.text(raw_query))
    return results, total

async def get_sample_list_detail_v4(
    order_by: Optional[str] = None,
    barcode: Optional[int] = None,
    samplecode: Optional[int] = None,
    userid: Optional[str] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    workflow: Optional[str] = None,
    exclude_status: Optional[str] = None,
    filter_status_list: Optional[list] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    sale_pic_id: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    sample_collector_unit_name: Optional[str] = None,
    include_deleted: Optional[bool] = False,
    pic_phone_number: Optional[str] = None,
):
    query_filter = "idc.deleted_at is null "
    if barcode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.barcode like '%{barcode}%' "
    if userid:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.user_id = '{str(userid)}' "
    if email:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.email like '%{email}%' "
    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.phone_number like '%{phone_number}%' "
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sub.full_name) like '%{name.lower()}%' "
    if gender:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.gender = '{gender}' "
    if nickname:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.nickname like '%{nickname}%' "
    if sample_collector_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collector_name like '%{sample_collector_name}%' "
    if sample_receiver_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receiver_name like '%{sample_receiver_name}%' "
    if sample_collector_unit_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"""lower(tu."tenDonVi") = '{sample_collector_unit_name.lower()}' """
        )
    if filter_status_list:
        query_filter += "and " if query_filter != "" else " "
        if len(filter_status_list) == 1:
            query_filter += f"latest_kit.current_status = '{filter_status_list[0]}' "
        else:
            query_filter += f"latest_kit.current_status in {tuple(filter_status_list)} "
    if current_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.current_status = '{current_status}' "
    if workflow:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.workflow = '{workflow}' "
    if exclude_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.current_status not like '%{exclude_status}%' "
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.samplecode like '%{samplecode}%' "
    if start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receipt_date::date >= '{start_date}' "
    # if product_code:
    #     if len(product_code) == 1:
    #         query_filter += "and " if query_filter != "" else " "
    #         query_filter += f"latest_kit.product_code = '{product_code[0]}' "
    #     else:
    #         query_filter += "and " if query_filter != "" else " "
    #         query_filter += f"latest_kit.product_code in {tuple(product_code)} "
    if sale_pic:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.name like '%{sale_pic}%' "
    if sale_pic_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.id = '{sale_pic_id}' "
    if pic_phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st2.phone_number = '{pic_phone_number}' "
    if account_name:
        if len(account_name) == 1:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name = '{account_name[0]}'"
        else:
            query_filter += "and " if query_filter != "" else " "
            query_filter += f"a.name in {tuple(account_name)} "
    if nominator:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"st.name like '%{nominator}%' "

    if batch:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"b.number = {batch} "
    if technology:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ls.technology = '{technology}' "
    if end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_receipt_date::date <= '{end_date}' "
    if release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"latest_kit.expected_report_release_date::date >= '{release_start_date}' "
        )
    if release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"latest_kit.expected_report_release_date::date <= '{release_end_date}' "
        )
    if collection_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"sam.sample_collection_date::date >= '{collection_start_date}' "
        )
    if collection_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sam.sample_collection_date::date <= '{collection_end_date}' "
    if actual_report_release_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.actual_report_release_time::date >= '{actual_report_release_start_date}' "
    if actual_report_release_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"latest_kit.actual_report_release_time::date <= '{actual_report_release_end_date}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += "latest_kit.deleted_at is null "

    query_filter += "and " if query_filter != "" else " "
    query_filter += f"latest_kit.current_status = 'REGISTERED' "

    if (order_by == "updated_at") or not order_by:
        order_by = "sam.updated_at"
    query_filter += f"order by {order_by} desc "

    if batch:
        LIST_QUERY_V4 = raw_querry_w_batch_number_v4()
    else:
        LIST_QUERY_V4 = get_kit_sample_list_query_v4(
            barcode=barcode, product_code=product_code
        )
    if query_filter.startswith("order"):
        raw_query = LIST_QUERY_V4 + query_filter
    else:
        raw_query = (
            LIST_QUERY_V4 + "where " + query_filter
            if query_filter != ""
            else LIST_QUERY_V4
        )
    results = await db.all(db.text(raw_query))
    return [row["barcode"] for row in results if row["barcode"] is not None]

async def get_sample_list_detail_labcheck(
        barcode: Optional[int],
        offset: Optional[int] = None,
        size: Optional[int] = None,
        order_by: Optional[str] = "updated_at",
        current_status: Optional[str] = None
    ):
    if barcode:
        raw_count_query = f"""
            select
                count(sam.samplecode)
            from
                sample sam
                INNER JOIN (
                    SELECT DISTINCT ON (samplecode) 
                        samplecode,
                        current_status
                    FROM kit 
                    WHERE barcode='{barcode}'
                    ORDER BY samplecode, created_at DESC
                ) AS latest_kit ON sam.samplecode = latest_kit.samplecode
                INNER JOIN subject sub ON sam.subject_id = sub.id
                inner join source s on sam.source_id = s.id
                inner join sale_account_history sah on s.account_history_id = sah.id
                inner join account a on sah.account_id = a.id
                inner join staff st2 on sah.pic_id = st2.id
        """

        raw_query = f"""
            select
                latest_kit.barcode,
                sub.gender,
                latest_kit.product_name,
                latest_kit.product_code,
                ls.technology,
                sam.sample_type,
                sam.lab_receipt_date::timestamp,
                latest_kit.is_priority,
                a.name as account_name,
                latest_kit.current_status,
                latest_kit.note
            from
                sample sam
                INNER JOIN (
                    SELECT DISTINCT ON (samplecode) 
                        samplecode,
                        barcode,
                        product_name,
                        product_code,
                        is_priority,
                        current_status,
                        note
                    FROM kit 
                    WHERE barcode='{barcode}'
                    ORDER BY samplecode, created_at DESC
                ) AS latest_kit ON sam.samplecode = latest_kit.samplecode
                INNER JOIN subject sub ON sam.subject_id = sub.id
                inner join source s on sam.source_id = s.id
                inner join sale_account_history sah on s.account_history_id = sah.id
                inner join account a on sah.account_id = a.id
                left join lab_sample ls on latest_kit.barcode = ls.barcode
            """

        if current_status:
            raw_count_query += f" where latest_kit.current_status = '{current_status}'"
            raw_query += f" where latest_kit.current_status = '{current_status}'"

        if (order_by == "updated_at") or not order_by:
            order_by = "sam.updated_at"
        raw_query += f" order by {order_by} desc"

        if size:
            raw_query += f" limit {size}"
            raw_query += f" offset {offset}"

        count = await db.all(db.text(raw_count_query))
        total = count[0][0]
        results = await db.all(db.text(raw_query))
    else:
        results = []
        total = 0
    
    return results, total