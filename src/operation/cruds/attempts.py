from typing import Optional

from ..utils.utils import get_current_date_time_utc_7
from ..models.models import db, Attempts
from ..config import config
from .. import logger


async def create_attempts(data: dict):
    attempt_obj = await Attempts.create(**data)
    return attempt_obj


async def get_attempts(userid: str) -> dict:
    attempt = await Attempts.get(userid)
    return attempt


async def update_attempts(userid: str,
                          attempt: int,
                          block: bool):
    obj = await get_attempts(userid)
    current_time = get_current_date_time_utc_7()
    data = {
        'userid': userid,
        'attempt': attempt,
        'block': block,
        'updated_time': current_time
    }
    if obj:
        await obj.update(**data).apply()
    else:
        data['created_time'] = current_time
        await Attempts.create(**data)


async def delete_attempts(userid: str):
    obj = await get_attempts(userid)
    if obj:
        await obj.delete()
        return obj, None
    else:
        error = f"Attempt object with id {userid} can not be found"
        return obj, error


async def get_all_attempts(
        offset: Optional[int] = None,
        size: Optional[int] = None,
        userid: Optional[str] = None,
        block: Optional[bool] = None,
        order_by: Optional[str] = None,
        attempt: Optional[int] = None,
) -> list:
    results = Attempts.query
    print(f"resutls: {results}")
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if userid:
        results = results.where(Attempts.userid.like(f"%{userid}%"))
    if block is not None:
        results = results.where(Attempts.block == block)
    if order_by:
        results = results.order_by(getattr(Attempts, order_by).desc())
    if attempt:
        results = results.where(Attempts.attempt == attempt)
    total = await count_matched_attempts(
        userid=userid,
        block=block,
    )
    return [r for r in await results.gino.all()], total


async def count_matched_attempts(
        userid: Optional[str] = None,
        block: Optional[bool] = None,
) -> int:
    results = db.select([db.func.count(Attempts.userid)])
    if userid:
        results = results.where(Attempts.userid.like(f"%{userid}%"))
    if block is not None:
        results = results.where(Attempts.block == block)
    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.exception(e)
        return 0


async def count_attempts():
    total = db.select([db.func.count(Attempts.userid)])
    return await total.gino.scalar()


async def is_block(userid: str) -> bool:
    count_match = await count_matched_attempts(userid=userid,
                                               block=True)
    if count_match > 0:
        return True
    return False


async def get_user_attempts(userid: str) -> bool:
    obj = await get_attempts(userid)
    if obj:
        return int(obj.attempt)
    return False


async def block_kit_registration(userid: str):
    obj = await get_attempts(userid)
    current_time = get_current_date_time_utc_7()
    await obj.update(
        userid=userid,
        block=True,
        updated_time=current_time
    ).apply()


async def unblock_kit_registration(userid: str):
    obj = await get_attempts(userid)
    if obj:
        current_time = get_current_date_time_utc_7()
        await obj.update(
            userid=userid,
            block=False,
            attempt=0,
            updated_time=current_time
        ).apply()


async def reset_attempts(userid: str):
    try:
        await update_attempts(userid, 0, False)
        return None
    except Exception as e:
        logger.exception(e)
        return e


async def increase_attempts(userid: str):
    obj = await get_attempts(userid)
    current_time = get_current_date_time_utc_7()
    if not obj:
        init_data = {
            'userid': userid,
            'attempt': 1,
            'block': False
        }
        fobj = await create_attempts(init_data)
        return int(fobj.attempt), None
    else:
        attempt = int(obj.attempt) + 1
        if attempt >= int(config['MAX_ATTEMPTS']):
            err = "You have entered the wrong code more times than allowed. \
            Your account is temporarily blocked from registering for the kit, \
            please contact the website administrator to resolve this issue."
            await obj.update(
                userid=userid,
                attempt=attempt,
                block=True,
                updated_time=current_time
            ).apply()
            return attempt, err
        else:
            await obj.update(
                userid=userid,
                attempt=attempt,
                updated_time=current_time
            ).apply()
            return attempt, None
