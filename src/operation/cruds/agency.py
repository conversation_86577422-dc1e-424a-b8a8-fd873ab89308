import uuid
from typing import Optional
from ..models.models import Agency, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# Agency - model 
# AGENCY - QUERY NAME
# _agency - function name
# agency - single
# agencies - plural
# name -- search key

AGENCY_LIST_QUERY_V3 = """
    select
        ag.id,
        ag.name,
        ag.type,
        ag.created_at,
        ag.updated_at,
        ag.deleted_at
    from
        agency ag
"""

AGENCY_LIST_COUNT_QUERY_V3 = """
    select
        count(ag.id)
    from
        agency ag
"""

async def get_agency_by_id(id: uuid.uuid4):
    agency = await Agency.get(id)
    return agency


async def get_all_agencies(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="ag.created_at",
    name: Optional[str] = None,
    type: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(ag.name) like '%{name.lower()}%' "
    if type:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ag.type = '{str(type)}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"ag.deleted_at is null "
    
    raw_count_query = AGENCY_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else AGENCY_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = AGENCY_LIST_QUERY_V3 + query_filter
    else:
        raw_query = AGENCY_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else AGENCY_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_agency_existed(name: str):
    total = await db.select([db.func.count()]).where(Agency.name == name).where(Agency.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_agency(data: dict):
    if await is_agency_existed(data['name']):
        err = f"Error: Integration Agency with name {data['name']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        'name': data['name'],
        'type': data['type'],
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await Agency.create(**data)
    return result.to_dict()

async def update_agency(agency: Agency, data: dict):
    if agency.deleted_at:
        err = f"Agency with name: {agency.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await agency.update(**data).apply()
    logger.info(f"Agency with name: {agency.name} updated")
    return None


async def delete_agency(agency: Agency):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not agency:
        err = f"Agency with name: {agency.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif agency.deleted_at:
        err = f"Agency with: {agency.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await agency.update(**data).apply()
    logger.info(f"Agency with name: {agency.name} deleted")
