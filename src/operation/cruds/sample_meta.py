import uuid
from typing import Optional
from ..models.models import db, SampleMeta
from ..utils.utils import get_current_date_time_utc_7
from .. import logger

async def get_sample_meta(
    full_name: str,
    dob: str,
    gender: str,
    phone_number: Optional[str] = None,
):
    results = SampleMeta.query
    results = results.where(SampleMeta.full_name.is_(full_name))
    results = results.where(SampleMeta.dob.is_(dob))
    results = results.where(SampleMeta.gender.is_(gender))
    if phone_number:
        results = results.where(SampleMeta.phone_number.is_(phone_number))
    return [r for r in await results.gino.all()]

async def get_sample_meta_by_id(
    sample_meta_id: str,
):
    sample_meta = await SampleMeta.get(sample_meta_id)
    return sample_meta

async def create_sample_meta(
    sample_meta: SampleMeta
):
    # sample_meta.id = uuid.uuid4
    sample_meta.created_at = get_current_date_time_utc_7()
    sample_meta.updated_at = get_current_date_time_utc_7()
    results = await sample_meta.create()
    return results.to_dict()

async def update_sample_meta(
    sample_meta: SampleMeta
):
    sample_meta.updated_at = get_current_date_time_utc_7()
    results = await sample_meta.update(**sample_meta.to_dict()).apply()
    return results
