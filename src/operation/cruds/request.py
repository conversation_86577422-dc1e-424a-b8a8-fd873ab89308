import uuid
from typing import Optional
from ..models.models import Request, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTT<PERSON>Exception
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)



REQUEST_LIST_QUERY_V3 = """
    select
        ag.name,
        req.id,
        req.request_code,
        req.agency_id,
        idc.customer_support_id,
        idc.customer_support_name,
        req.customer_name,
        req.customer_phone,
        req.dob::date,
        req.gender,
        req.payment_amount,
        req.payment_method,
        req.payment_status,
        req.status,
        req.status_code,
        req.error_message,
        req.collect_date,
        req.request_date,
        req.response_date,
        req.identifier_code,
        req.created_at,
        req.updated_at,
        req.deleted_at
    from
        request req
        INNER JOIN agency ag ON req.agency_id = ag.id
        INNER JOIN identity_card idc ON req.identifier_code = idc.identifier_code
"""

REQUEST_LIST_COUNT_QUERY_V3 = """
    select
        count(req.request_code)
    from
        request req
        INNER JOIN agency ag ON req.agency_id = ag.id
        INNER JOIN identity_card idc ON req.identifier_code = idc.identifier_code
"""

async def get_request_by_id(id: uuid.uuid4):
    integrationReq = await Request.get(id)
    return integrationReq
    pass
async def get_request_dict_by_id(id: uuid.uuid4):
    integrationReq = await Request.get(id)
    return integrationReq.to_dict() if integrationReq else None

async def get_all_requests_by_identifier_code(identifier_code: str):
    # integrationReq: Request
    requests = await Request.query.where(Request.identifier_code == identifier_code).where(Request.deleted_at == None).gino.all()
    return requests

async def get_all_requests(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="req.updated_at",
    id: Optional[str] = None,
    agency_name: Optional[str] = None,
    request_code: Optional[str] = None,
    identifier_code: Optional[str] = None,
    customer_support_id: Optional[str] = None,
    customer_name: Optional[str] = None,
    customer_phone: Optional[str] = None,
    payment_status: Optional[str] = None,
    status: Optional[str] = None,
    start_collect_date: Optional[str] = None,
    end_collect_date: Optional[str] = None,
    start_request_date: Optional[str] = None,
    end_request_date: Optional[str] = None,
    start_response_date: Optional[str] = None,
    end_response_date: Optional[str] = None,
    include_deleted: Optional[bool]=False
):
    query_filter = ""
    if id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.id = '{str(id)}' "
    if agency_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(ag.name) like '%{agency_name.lower()}%' "
    if request_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.request_code = '{str(request_code)}' "
    if identifier_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.identifier_code like '%{identifier_code}%' "
    if customer_support_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"idc.customer_support_id = '{customer_support_id}' "
    if customer_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(req.customer_name) like '%{customer_name.lower()}%' "
    if customer_phone:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.customer_phone like '%{customer_phone}%' "
    if payment_status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.payment_status = '{str(payment_status)}' "
    if status:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.status = '{str(status)}' "
    if start_collect_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.collect_date::date >= '{start_collect_date}' "
    if end_collect_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.collect_date::date <= '{end_collect_date}' "
    if start_request_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.request_date::date >= '{start_request_date}' "
    if end_request_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.request_date::date <= '{end_request_date}' "
    if start_response_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.response_date::date >= '{start_response_date}' "
    if end_response_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.response_date::date <= '{end_response_date}' "
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"req.deleted_at is null "
    
    raw_count_query = REQUEST_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else REQUEST_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    if query_filter.startswith("order"):
        raw_query = REQUEST_LIST_QUERY_V3 + query_filter
    else:
        raw_query = REQUEST_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else REQUEST_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_request_existed(request_code: str):
    total = await db.select([db.func.count()]).where(Request.request_code == request_code).where(Request.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_request(data: dict):
    if await is_request_existed(data['request_code']):
        err = f"Error: Integration Request with name {data['request_code']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await Request.create(**data)
    return result.to_dict()

async def update_request(integrationReq: Request, data: dict):
    if integrationReq.deleted_at:
        err = f"Integration request with request_code: {integrationReq.request_code} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await integrationReq.update(**data).apply()
    logger.info(f"Integration request with request_code: {integrationReq.request_code} updated")
    return None


async def delete_request(integrationReq: Request):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not integrationReq:
        err = f"Integration request with request_code: {integrationReq.request_code} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    elif integrationReq.deleted_at:
        err = f"Integration request with: {integrationReq.request_code} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await integrationReq.update(**data).apply()
    logger.info(f"Integration request with: {integrationReq.request_code} deleted!")
