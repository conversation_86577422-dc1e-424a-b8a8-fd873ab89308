import uuid
from typing import Optional
from ..models.models import TrackingCollection, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# TrackingCollection - model 
# COLLECTION - QUERY maThuNhan
# _tracking_collection - function maThuNhan
# tracking_collection - single
# collections - plural
# maThuNhan -- search key

COLLECTION_LIST_QUERY_V3 = """
    select
        tc.id,
        tc.tracking_id,
        tc."maThu<PERSON>han",
        tc."donViThuNhanMau_id",
        tu."tenDonVi" as "tenDonViThuNhan<PERSON>au",
        tc."noiThuThapMau",
        tc."ngayGioThuThapMau",
        tc."nhanVienLayMau_id",
        nvlm."hoTenNhanVien" as "hoTenNhanVienLayMau",
        nvlm."hocVi" as "hocViNhanVienLayMau",
        nvlm."soDinhDanh" as "soDinhDanhNhanVienLayMau",
        nvlm."chucVu" as "chucVuNhanVienLayMau",
        tc."nhanVienGhiHoSo_id",
        nvghs."hoTenNhanVien" as "hoTenNhanVienGhiHoSo",
        nvghs."hocVi" as "hocViNhanVienGhiHoSo",
        nvghs."soDinhDanh" as "soDinhDanhNhanVienGhiHoSo",
        nvghs."chucVu" as "chucVuNhanVienGhiHoSo",
        tc."nhanVienLuuMau_id",
        nvlum."hoTenNhanVien" as "hoTenNhanVienLuuMau",
        nvlum."hocVi" as "hocViNhanVienLuuMau",
        nvlum."soDinhDanh" as "soDinhDanhNhanVienLuuMau",
        nvlum."chucVu" as "chucVuNhanVienLuuMau",
        tc.created_at,
        tc.updated_at,
        tc.deleted_at
    from
        tracking_collection tc
        INNER JOIN tracking_unit tu ON tc."donViThuNhanMau_id" = tu.id
        INNER JOIN tracking_employee nvlm ON tc."nhanVienLayMau_id" = nvlm.id
        INNER JOIN tracking_employee nvghs ON tc."nhanVienGhiHoSo_id" = nvghs.id
        LEFT JOIN tracking_employee nvlum ON tc."nhanVienLuuMau_id" = nvlum.id
"""

COLLECTION_LIST_COUNT_QUERY_V3 = """
    select
        count(tc.id)
    from
        tracking_collection tc
"""

async def get_tracking_collection_by_id(id: uuid.uuid4):
    tracking_collection = await TrackingCollection.get(id)
    return tracking_collection

async def get_tracking_collection_by_samplecode(samplecode: str):
    tracking_collection = await TrackingCollection.query.where(TrackingCollection.maThuNhan == samplecode).gino.first()
    return tracking_collection
    

async def get_tracking_collection_by_tracking_id(tracking_id: str):
    tracking_collection = await TrackingCollection.query.where(TrackingCollection.tracking_id == tracking_id).gino.first()
    return tracking_collection

async def get_all_collections(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="tc.created_at",
    maThuNhan: Optional[str] = None,
    tenDonViThuNhanMau: Optional[str] = None,
    noiThuThapMau: Optional[int] = None,
    ngayGioThuThapMau_start_date: Optional[str] = None,
    ngayGioThuThapMau_end_date: Optional[str] = None,
    hoTenNhanVienLayMau: Optional[str] = None,
    soDinhDanhNhanVienLayMau: Optional[str] = None,
    hoTenNhanVienGhiHoSo: Optional[str] = None,
    soDinhDanhNhanVienGhiHoSo: Optional[str] = None,
    hoTenNhanVienLuuMau: Optional[str] = None,
    soDinhDanhNhanVienLuuMau: Optional[str] = None,
    tracking_collection_id: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if maThuNhan:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tc.'maThuNhan' = '{str(maThuNhan)}' """
    if tenDonViThuNhanMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tu.'tenDonVi' = '{str(tenDonViThuNhanMau)}' """
    if noiThuThapMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tc.'noiThuThapMau' = '{noiThuThapMau}' """
    if ngayGioThuThapMau_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tc.'ngayGioThuThapMau'::date >= '{ngayGioThuThapMau_start_date}' """
    if ngayGioThuThapMau_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tc.'ngayGioThuThapMau'::date <= '{ngayGioThuThapMau_end_date}' """
    if hoTenNhanVienLayMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvlm.'hoTenNhanVien' = '{str(hoTenNhanVienLayMau)}' """
    if soDinhDanhNhanVienLayMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvlm.'soDinhDanh' = '{str(soDinhDanhNhanVienLayMau)}' """
    if hoTenNhanVienGhiHoSo:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvghs.'hoTenNhanVien' = '{str(hoTenNhanVienGhiHoSo)}' """
    if soDinhDanhNhanVienGhiHoSo:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvghs.'soDinhDanh' = '{str(soDinhDanhNhanVienGhiHoSo)}' """
    if hoTenNhanVienLuuMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvlum.'hoTenNhanVien' = '{str(hoTenNhanVienLuuMau)}' """
    if soDinhDanhNhanVienLuuMau:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""nvlum.'soDinhDanh' = '{str(soDinhDanhNhanVienLuuMau)}' """
    if tracking_collection_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tc.id = '{tracking_collection_id}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tc.deleted_at is null """
    
    raw_count_query = COLLECTION_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else COLLECTION_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = COLLECTION_LIST_QUERY_V3 + query_filter
    else:
        raw_query = COLLECTION_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else COLLECTION_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_collection_existed(maThuNhan: str):
    total = await db.select([db.func.count()]).where(TrackingCollection.maThuNhan == maThuNhan).where(TrackingCollection.deleted_at == None).gino.scalar()
    return True if total > 0 else False


async def is_tracking_collection_existed_w_tracking_id(tracking_id: str):
    total = await db.select([db.func.count()]).where(TrackingCollection.tracking_id == tracking_id).where(TrackingCollection.deleted_at == None).gino.scalar()
    return True if total > 0 else False


async def create_tracking_collection(data: dict):
    """
    tracking_id: str
    maThuNhan: str
    donViThuNhanMau_id: int
    noiThuThapMau: int
    ngayGioThuThapMau: str
    nhanVienLayMau_id: str
    nhanVienGhiHoSo_id: str
    nhanVienLuuMau_id: Optional[str]
    """
    if await is_tracking_collection_existed(data['maThuNhan']):
        err = f"Error: TrackingCollection with maThuNhan {data['maThuNhan']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    if await is_tracking_collection_existed_w_tracking_id(data['tracking_id']):
        err = f"Error: TrackingCollection with tracking_id {data['tracking_id']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await TrackingCollection.create(**data)
    return result.to_dict()

async def update_tracking_collection(tracking_collection: TrackingCollection, data: dict):
    if tracking_collection.deleted_at:
        err = f"TrackingCollection with maThuNhan: {tracking_collection.maThuNhan} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_collection.update(**data).apply()
    logger.info(f"TrackingCollection with maThuNhan: {tracking_collection.maThuNhan} updated")
    return None


async def delete_tracking_collection(tracking_collection: TrackingCollection):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_collection:
        err = f"TrackingCollection with maThuNhan: {tracking_collection.maThuNhan} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_collection.deleted_at:
        err = f"TrackingCollection with: {tracking_collection.maThuNhan} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_collection.update(**data).apply()
    logger.info(f"TrackingCollection with maThuNhan: {tracking_collection.maThuNhan} deleted")
