import uuid
from typing import Optional
from ..models.models import RequestAdnIntegration, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTT<PERSON>Exception
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# RequestAdnIntegration - model 
# request_adn_integration - QUERY id
# _request_adn_integration - function id
# request_adn_integration ai - single
# ai. - SQL alias
# RequestAdnIntegration - single
# request_adn_integrations - plural
# _request_adn_integrations
# id -- search key

async def get_request_adn_integration_by_id(id: uuid.uuid4):
    RequestAdnIntegration = await RequestAdnIntegration.get(id)
    return RequestAdnIntegration




async def is_request_adn_integration_existed(request_id: str, adn_integration_id: str):
    total = await db.select([db.func.count()]).where(RequestAdnIntegration.request_id == request_id).where(RequestAdnIntegration.adn_integration_id == adn_integration_id).where(RequestAdnIntegration.deleted_at == None).gino.scalar()
    return True if total > 0 else False



async def create_request_adn_integration(data: dict):
    
    """
    id: str
    request_id: str
    adn_integration_id: str
    
    """
    
    if await is_request_adn_integration_existed(request_id=data['request_id'], adn_integration_id=data['adn_integration_id']):
        err = f"RequestAdnIntegration with request_id {data['request_id']} and adn_integration_id {data['adn_integration_id']} is already created"
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        **data,
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await RequestAdnIntegration.create(**data)
    return result.to_dict()
    pass

async def update_request_adn_integration(RequestAdnIntegration: RequestAdnIntegration, data: dict):
    if RequestAdnIntegration.deleted_at:
        err = f"Request ADN Integration with id: {RequestAdnIntegration.id} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await RequestAdnIntegration.update(**data).apply()
    logger.info(f"Request ADN Integration with id: {RequestAdnIntegration.id} updated")
    return None


async def delete_request_adn_integration(RequestAdnIntegration: RequestAdnIntegration):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not RequestAdnIntegration:
        err = f"Request ADN Integration with id: {RequestAdnIntegration.id} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    elif RequestAdnIntegration.deleted_at:
        err = f"Request ADN Integration with: {RequestAdnIntegration.id} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await RequestAdnIntegration.update(**data).apply()
    logger.info(f"Request ADN Integration with id: {RequestAdnIntegration.id} deleted")
