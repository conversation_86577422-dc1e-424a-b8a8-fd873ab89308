import uuid
from typing import Optional
from ..models.models import db, DnaBoxMappings, LabSample, SampleMapping
from ..utils.utils import get_current_date_time_utc_7, get_alphabet_position_vertical
from .. import logger
from ..config import config
from gino.loader import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import Date, cast
from datetime import datetime, timedelta
from ..schemas.dna_box_mappings import *
from .dna_box import *

async def count_dna_box_mappings():
    total = db.select([db.func.count(DnaBoxMappings.lid)])
    return await total.gino.scalar()

async def get_dna_box_mapping(lid: str) -> DnaBoxMappings:
    dna_box_mapping = await DnaBoxMappings.get(lid)
    return dna_box_mapping


async def count_matched_dna_box_mappings(
    position: Optional[str] = None,
    dna_box_id: Optional[str] = None,
    lid: Optional[str] = None,
):
    results = db.select([db.func.count()])
    if position:
        results = results.where(DnaBoxMappings.position == position)
    if dna_box_id:
        results = results.where(DnaBoxMappings.dna_box_id == dna_box_id)
    if lid:
        results = results.where(DnaBoxMappings.lid == lid)

    try:
        return await results.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0

async def get_all_dna_box_mappings(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    position: Optional[str] = None,
    dna_box_id: Optional[str] = None,
    lid: Optional[str] = None,
    order_by: Optional[str] = None,
):
    # accounts = Account.alias('sacc')
    results = DnaBoxMappings.query
    if size:
        results = results.limit(size)
    if offset:
        results = results.offset(offset)
    if position:
        results = results.where(DnaBoxMappings.position == position)
    if dna_box_id:
        results = results.where(DnaBoxMappings.dna_box_id == dna_box_id)
    if lid:
        results = results.where(DnaBoxMappings.lid == lid)
    if order_by:
        results = results.order_by(getattr(DnaBoxMappings, order_by).desc())

    total = await count_matched_dna_box_mappings(
        position=position,
        dna_box_id=dna_box_id,
        lid=lid
    )
    return [r for r in await results.gino.all()], total

async def existed_id(lid: str) -> bool:
    total = await db.select([db.func.count()]).where(DnaBoxMappings.lid == lid).gino.scalar()
    return True if total > 0 else False

async def create_dna_box_mapping(data: dict):
    if await existed_id(data.get('lid')):
        err = f"Error: DnaBoxMappings of LID {data.get('lid')} is already existed"
        return data, err
    current_time = get_current_date_time_utc_7()

    data = {
        'position': data.get('position'),
        'dna_box_id': data.get('dna_box_id'),
        'lid': data.get('lid'),
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await DnaBoxMappings.create(**data)
    return result.to_dict(), None

async def put_samples_into_dna_box_w_curr_capacity(technology, dna_box_id ,current_capacity, lab_sample_ids):
    results = []
    MAX_ROWS= config['DNA_BOX']['SCALE'][technology.upper()]['MAX_ROWS']
    async with db.transaction() as tx:
        for idx, lid in enumerate(lab_sample_ids, start=current_capacity+1):
            data = {}
            data['dna_box_id']=dna_box_id
            data['position']=get_alphabet_position_vertical(idx,MAX_ROWS)
            data['lid']=lid
            ret, err = await create_dna_box_mapping(data)
            if err:
                raise ValueError(err)
            results.append(ret)

    return results
        


async def update_dna_box_mapping(dna_box_mapping: DnaBoxMappings, data: dict):
    await dna_box_mapping.update(**data).apply()
    logger.info(f"dna_box_mapping with lid: {dna_box_mapping.lid} updated")
    return None

async def delete_dna_box_mapping(dna_box_mapping):
    if not dna_box_mapping:
        err = f"dna_box_mapping with lid: {dna_box_mapping.lid} cannot be found"
        return None, err

    await DnaBoxMappings.delete.where(
        DnaBoxMappings.lid==dna_box_mapping.lid,
    ).gino.status()
    logger.info(f"dna_box_mapping with id: {dna_box_mapping.lid} deleted")

    return dna_box_mapping, None

async def delete_dna_box_mappings_w_id(dna_box_id):

    await DnaBoxMappings.delete.where(
        DnaBoxMappings.dna_box_id==dna_box_id,
    ).gino.status()

    logger.info(f"dna_box_mapping with id: {dna_box_id} deleted")