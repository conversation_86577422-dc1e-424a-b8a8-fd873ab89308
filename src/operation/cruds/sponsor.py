import uuid
from typing import List, <PERSON>tional, <PERSON><PERSON>

from sqlalchemy import Date, cast

from src.operation import logger
from src.operation.models.models import KitV3, SampleV3, db
from src.operation.models.sponsor import (
    GsArea,
    Sponsor,
    SponsorCampaign,
    SponsorContract,
)
from src.operation.schemas.sponsor import (
    GsAreaCreate,
    GsAreaUpdate,
    SponsorCampaignCreate,
    SponsorCampaignUpdate,
    SponsorContractCreate,
    SponsorContractUpdate,
    SponsorCreate,
    SponsorUpdate,
)
from src.operation.utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE,
    format_date,
    get_current_date_time_utc_7,
)

# --- Sponsor CRUD ---


async def get_sponsor(sponsor_id: uuid.UUID) -> Optional[Sponsor]:
    """Get a sponsor by ID"""
    sponsor = await Sponsor.get(sponsor_id)
    return sponsor


async def count_sponsors() -> int:
    """Count all non-deleted sponsors"""
    total = (
        await db.select([db.func.count(Sponsor.id)])
        .where(Sponsor.deleted_at.is_(None))
        .gino.scalar()
    )
    return total


async def count_matched_sponsors(
    name: Optional[str] = None,
    code: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> int:
    """Count sponsors matching the filter criteria"""
    query = db.select([db.func.count()]).where(Sponsor.deleted_at.is_(None))

    if start_date and end_date:
        query = query.where(Sponsor.created_at >= start_date)
        query = query.where(Sponsor.created_at <= end_date)
    if name:
        query = query.where(Sponsor.name.contains(f"%{name}%"))
    if code:
        query = query.where(Sponsor.code.contains(f"%{code}%"))

    try:
        return await query.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


async def get_all_sponsors(
    page_number: Optional[int] = None,
    page_size: Optional[int] = None,
    name: Optional[str] = None,
    code: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = "created_at",
    order_option: Optional[str] = "desc",
) -> Tuple[List[Sponsor], int]:
    """Get all sponsors matching the filter criteria"""
    query = Sponsor.query.where(Sponsor.deleted_at.is_(None))

    if start_date and end_date:
        start_date = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE)
        end_date = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE)
        query = query.where(cast(Sponsor.created_at, Date) >= start_date)
        query = query.where(cast(Sponsor.created_at, Date) <= end_date)
    if name:
        query = query.where(Sponsor.name.contains(f"%{name}%"))
    if code:
        query = query.where(Sponsor.code.contains(f"%{code}%"))

    if order_by:
        if order_option == "desc":
            query = query.order_by(getattr(Sponsor, order_by).desc())
        else:
            query = query.order_by(getattr(Sponsor, order_by))

    if page_size:
        query = query.limit(page_size)
    if page_number:
        query = query.offset((page_number - 1) * page_size)

    total = await count_matched_sponsors(
        name=name,
        code=code,
        start_date=start_date,
        end_date=end_date,
    )

    sponsors = await query.gino.all()
    return sponsors, total


async def create_sponsor(data: SponsorCreate) -> Tuple[dict, Optional[str]]:
    """Create a new sponsor"""
    current_time = get_current_date_time_utc_7()

    sponsor_data = {
        "id": uuid.uuid4(),
        "name": data.name,
        "code": data.code,
        "description": data.description,
        "type": data.type,
        "created_at": current_time,
        "updated_at": current_time,
    }

    try:
        result = await Sponsor.create(**sponsor_data)
        return result.to_dict(), None
    except Exception as e:
        logger.error(f"Error creating sponsor: {e}")
        return None, str(e)


async def update_sponsor(sponsor: Sponsor, data: SponsorUpdate) -> Optional[str]:
    """Update an existing sponsor"""
    if sponsor.deleted_at:
        return f"Sponsor with id: {sponsor.id} already deleted!"

    update_data = {k: v for k, v in data.dict().items() if v is not None}
    if not update_data:
        return None

    update_data["updated_at"] = get_current_date_time_utc_7()

    try:
        await sponsor.update(**update_data).apply()
        logger.info(f"Sponsor with name: {sponsor.name} updated")
        return None
    except Exception as e:
        logger.error(f"Error updating sponsor: {e}")
        return str(e)


async def delete_sponsor(sponsor: Sponsor) -> Tuple[Optional[Sponsor], Optional[str]]:
    """Delete a sponsor by marking it as deleted"""
    if not sponsor:
        return None, "Sponsor cannot be found"
    elif sponsor.deleted_at:
        return None, f"Sponsor with id: {sponsor.id} already deleted!"

    current_time = get_current_date_time_utc_7()
    update_data = {"deleted_at": current_time}

    try:
        await sponsor.update(**update_data).apply()
        logger.info(f"Sponsor with name: {sponsor.name} deleted")
        return sponsor, None
    except Exception as e:
        logger.error(f"Error deleting sponsor: {e}")
        return None, str(e)


# --- GsArea CRUD ---


async def get_gs_area(area_id: int) -> Optional[GsArea]:
    """Get a GS area by ID"""
    area = await GsArea.get(area_id)
    return area


async def count_gs_areas() -> int:
    """Count all GS areas"""
    total = await db.select([db.func.count(GsArea.id)]).gino.scalar()
    return total


async def get_all_gs_areas(
    page_number: Optional[int] = None,
    page_size: Optional[int] = None,
    area: Optional[str] = None,
    order_by: Optional[str] = "area",
    order_option: Optional[str] = "asc",
) -> Tuple[List[GsArea], int]:
    """Get all GS areas matching the filter criteria"""
    # Create the base query
    query = GsArea.query

    # Build the count query with the same filters
    count_query = db.select([db.func.count()]).select_from(GsArea)

    # Apply filters to both queries
    if area:
        query = query.where(GsArea.area.contains(f"%{area}%"))
        count_query = count_query.where(GsArea.area.contains(f"%{area}%"))

    # Apply sorting
    if order_by:
        if order_option == "desc":
            query = query.order_by(getattr(GsArea, order_by).desc())
        else:
            query = query.order_by(getattr(GsArea, order_by))

    # Get the filtered count
    total = await count_query.gino.scalar()

    # Apply pagination
    if page_size:
        query = query.limit(page_size)
    if page_number:
        query = query.offset((page_number - 1) * page_size)

    # Get the filtered and paginated results
    areas = await query.gino.all()
    return areas, total


async def create_gs_area(data: GsAreaCreate) -> Tuple[dict, Optional[str]]:
    """Create a new GS area"""
    area_data = {
        "id": data.id if data.id else None,
        "area": data.area,
    }

    try:
        result = await GsArea.create(**area_data)
        return result.to_dict(), None
    except Exception as e:
        logger.error(f"Error creating GS area: {e}")
        return None, str(e)


async def update_gs_area(area: GsArea, data: GsAreaUpdate) -> Optional[str]:
    """Update an existing GS area"""
    update_data = {k: v for k, v in data.dict().items() if v is not None}
    if not update_data:
        return None

    try:
        await area.update(**update_data).apply()
        logger.info(f"GS area: {area.area} updated")
        return None
    except Exception as e:
        logger.error(f"Error updating GS area: {e}")
        return str(e)


async def delete_gs_area(area: GsArea) -> Tuple[Optional[GsArea], Optional[str]]:
    """Delete a GS area"""
    if not area:
        return None, "GS area cannot be found"

    try:
        # Check if area is used in any campaign
        campaign_count = (
            await db.select([db.func.count()])
            .where(SponsorCampaign.gs_area_code == area.id)
            .where(SponsorCampaign.deleted_at.is_(None))
            .gino.scalar()
        )

        if campaign_count > 0:
            return (
                None,
                f"Cannot delete GS area: {area.area} as it is used in {campaign_count} campaigns",
            )

        await area.delete()
        logger.info(f"GS area: {area.area} deleted")
        return area, None
    except Exception as e:
        logger.error(f"Error deleting GS area: {e}")
        return None, str(e)


# --- SponsorContract CRUD ---


async def get_sponsor_contract(contract_id: uuid.UUID) -> Optional[SponsorContract]:
    """Get a sponsor contract by ID"""
    contract = await SponsorContract.get(contract_id)
    return contract


async def count_sponsor_contracts() -> int:
    """Count all non-deleted sponsor contracts"""
    total = (
        await db.select([db.func.count(SponsorContract.id)])
        .where(SponsorContract.deleted_at.is_(None))
        .gino.scalar()
    )
    return total


async def count_matched_sponsor_contracts(
    name: Optional[str] = None,
    code: Optional[str] = None,
    sponsor_id: Optional[uuid.UUID] = None,
    gs_area_code: Optional[uuid.UUID] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> int:
    """Count sponsor contracts matching the filter criteria"""
    query = (
        db.select([db.func.count(db.distinct(SponsorContract.id))])
        .select_from(
            SponsorContract.outerjoin(
                SponsorCampaign, SponsorContract.id == SponsorCampaign.contract_id
            )
        )
        .where(SponsorContract.deleted_at.is_(None))
    )

    if start_date and end_date:
        start_date = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE)
        end_date = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE)
        query = query.where(cast(SponsorContract.created_at, Date) >= start_date)
        query = query.where(cast(SponsorContract.created_at, Date) <= end_date)
    if name:
        query = query.where(SponsorContract.name.contains(f"%{name}%"))
    if code:
        query = query.where(SponsorContract.code.contains(f"%{code}%"))
    if sponsor_id:
        query = query.where(SponsorContract.sponsor_id == sponsor_id)
    if gs_area_code:
        # Filter contracts that have at least one campaign with this gs_area_code
        query = query.where(SponsorCampaign.gs_area_code == gs_area_code)
        # Make sure we only get non-deleted campaigns
        query = query.where(SponsorCampaign.deleted_at.is_(None))

    try:
        return await query.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


async def get_all_sponsor_contracts(
    page_number: Optional[int] = None,
    page_size: Optional[int] = None,
    name: Optional[str] = None,
    code: Optional[str] = None,
    sponsor_id: Optional[uuid.UUID] = None,
    gs_area_code: Optional[int] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = "created_at",
    order_option: Optional[str] = "desc",
) -> Tuple[List, int]:
    """Get all sponsor contracts matching the filter criteria"""
    # First, build a subquery to get distinct contract IDs that match our criteria
    contract_ids_query = (
        db.select([SponsorContract.id])
        .select_from(
            SponsorContract.outerjoin(
                SponsorCampaign, SponsorContract.id == SponsorCampaign.contract_id
            )
        )
        .where(SponsorContract.deleted_at.is_(None))
    )

    if start_date and end_date:
        start_date = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE)
        end_date = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE)
        contract_ids_query = contract_ids_query.where(
            cast(SponsorContract.created_at, Date) >= start_date
        )
        contract_ids_query = contract_ids_query.where(
            cast(SponsorContract.created_at, Date) <= end_date
        )
    if name:
        contract_ids_query = contract_ids_query.where(
            SponsorContract.name.contains(f"%{name}%")
        )
    if code:
        contract_ids_query = contract_ids_query.where(
            SponsorContract.code.contains(f"%{code}%")
        )
    if sponsor_id:
        contract_ids_query = contract_ids_query.where(
            SponsorContract.sponsor_id == sponsor_id
        )
    if gs_area_code:
        # Filter contracts that have at least one campaign with this gs_area_code
        contract_ids_query = contract_ids_query.where(
            SponsorCampaign.gs_area_code == gs_area_code
        )
        # Make sure we only get non-deleted campaigns
        contract_ids_query = contract_ids_query.where(
            SponsorCampaign.deleted_at.is_(None)
        )

    contract_ids_query = contract_ids_query.group_by(SponsorContract.id)

    # Now create the main query to get contract data with sponsor names
    query = (
        db.select([SponsorContract, Sponsor.name.label("sponsor_name")])
        .select_from(
            SponsorContract.outerjoin(Sponsor, SponsorContract.sponsor_id == Sponsor.id)
        )
        .where(SponsorContract.id.in_(contract_ids_query))
    )

    # Apply ordering
    if order_by:
        if order_option == "desc":
            query = query.order_by(getattr(SponsorContract, order_by).desc())
        else:
            query = query.order_by(getattr(SponsorContract, order_by))

    # Update the count function to match the new filtering criteria
    total = await count_matched_sponsor_contracts(
        name=name,
        code=code,
        sponsor_id=sponsor_id,
        gs_area_code=gs_area_code,
        start_date=start_date,
        end_date=end_date,
    )

    if page_size:
        query = query.limit(page_size)
    if page_number:
        query = query.offset((page_number - 1) * page_size)

    contracts = await query.gino.all()
    return contracts, total


async def get_sponsor_contract_details(contract_id: uuid.UUID):
    """
    Get sponsor contract details including associated samples and kits.

    Args:
        contract_id: UUID of the sponsor contract

    Returns:
        Dictionary containing contract details with associated samples and kits
    """
    contract = await SponsorContract.get(contract_id)
    if not contract:
        return None

    # Get sponsor information
    sponsor = await Sponsor.get(contract.sponsor_id) if contract.sponsor_id else None

    # Get associated campaigns
    campaigns = (
        await SponsorCampaign.query.where(SponsorCampaign.contract_id == contract_id)
        .where(SponsorCampaign.deleted_at.is_(None))
        .gino.all()
    )

    campaign_details = []
    for campaign in campaigns:
        # Get GS area name
        gs_area = (
            await GsArea.get(campaign.gs_area_code) if campaign.gs_area_code else None
        )

        campaign_details.append(
            {
                "id": str(campaign.id),
                "name": campaign.name,
                "start_date": campaign.start_date.isoformat()
                if campaign.start_date
                else None,
                "end_date": campaign.end_date.isoformat()
                if campaign.end_date
                else None,
                "quantity": campaign.quantity,
                "gs_area_code": str(campaign.gs_area_code)
                if campaign.gs_area_code
                else None,
                "gs_area_name": gs_area.area if gs_area else None,
                "description": campaign.description,
                "created_at": campaign.created_at.isoformat()
                if campaign.created_at
                else None,
                "updated_at": campaign.updated_at.isoformat()
                if campaign.updated_at
                else None,
            }
        )
    # Query samples with the given sponsor_contract_id
    samples = await SampleV3.query.where(
        SampleV3.sponsor_id == contract.sponsor_id
    ).gino.all()

    # Prepare result structure
    result = {
        "id": str(contract.id),
        "name": contract.name,
        "code": contract.code,
        "signing_date": contract.signing_date.isoformat()
        if contract.signing_date
        else None,
        "sponsor_id": str(contract.sponsor_id) if contract.sponsor_id else None,
        "sponsor_name": sponsor.name if sponsor else None,
        "sponsored_party": str(contract.sponsored_party)
        if contract.sponsored_party
        else None,
        "s3_key": contract.s3_key,
        "s3_bucket": contract.s3_bucket,
        "total_quantity": contract.total_quantity,
        "created_at": contract.created_at.isoformat() if contract.created_at else None,
        "updated_at": contract.updated_at.isoformat() if contract.updated_at else None,
        "deleted_at": contract.deleted_at.isoformat() if contract.deleted_at else None,
        "campaigns": campaign_details,
        "samples": [],
    }

    # For each sample, fetch its associated kit information
    for sample in samples:
        # Get kit associated with this sample by samplecode
        kit = await KitV3.query.where(
            KitV3.samplecode == sample.samplecode
        ).gino.first()

        # Prepare sample data with kit information if available
        sample_data = {
            "id": str(sample.id),
            "samplecode": sample.samplecode,
            "subject_id": str(sample.subject_id),
            "sample_collection_date": sample.sample_collection_date.isoformat()
            if sample.sample_collection_date
            else None,
            "sample_receipt_date": sample.sample_receipt_date.isoformat()
            if sample.sample_receipt_date
            else None,
            "lab_receipt_date": sample.lab_receipt_date.isoformat()
            if sample.lab_receipt_date
            else None,
            "sample_type": sample.sample_type,
            "sample_recollection": sample.sample_recollection,
            "sample_collector_name": sample.sample_collector_name,
            "sample_receiver_name": sample.sample_receiver_name,
            "created_at": sample.created_at.isoformat() if sample.created_at else None,
            "kit": None,
        }

        # Add kit information if available
        if kit:
            sample_data["kit"] = {
                "id": str(kit.id),
                "barcode": kit.barcode,
                "product_code": kit.product_code,
                "product_name": kit.product_name,
                "product_type": kit.product_type,
                "current_status": kit.current_status,
                "current_status_id": kit.current_status_id,
                "expected_report_release_date": kit.expected_report_release_date.isoformat()
                if kit.expected_report_release_date
                else None,
                "actual_report_release_time": kit.actual_report_release_time.isoformat()
                if kit.actual_report_release_time
                else None,
                "is_priority": kit.is_priority,
                "is_card_issued": kit.is_card_issued,
                "free_of_charge": kit.free_of_charge,
                "workflow": kit.workflow,
            }

        # Add sample data to result
        result["samples"].append(sample_data)

    return result


async def create_sponsor_contract(data: SponsorContractCreate) -> dict:
    """
    Create a new sponsor contract

    Raises:
        ValueError: For validation errors
        Exception: For database errors

    Returns:
        Dictionary with contract data
    """
    current_time = get_current_date_time_utc_7()

    # Check if sponsor exists
    sponsor = await Sponsor.get(data.sponsor_id)
    if not sponsor:
        raise ValueError(f"Sponsor with id: {data.sponsor_id} not found")

    if sponsor.deleted_at:
        raise ValueError(f"Sponsor with id: {data.sponsor_id} is deleted")

    # Validate that at least one campaign is provided
    # if not data.campaigns or len(data.campaigns) == 0:
    #     raise ValueError(
    #         "At least one campaign with start date, end date, and GS area code must be specified"
    #     )

    # Validate each campaign has required fields
    total_quantity = 0
    for campaign in data.campaigns:
        if (
            not campaign.start_date
            or not campaign.end_date
            or not campaign.gs_area_code
        ):
            raise ValueError(
                "Each campaign must have start date, end date, and GS area code"
            )

        # Validate gs_area_code exists
        gs_area = await GsArea.get(campaign.gs_area_code)
        if not gs_area:
            raise ValueError(f"GS area with id: {campaign.gs_area_code} not found")

        total_quantity += campaign.quantity

    if total_quantity > data.total_quantity:
        raise ValueError(
            f"Total quantity of campaigns ({total_quantity}) is greater than the total quantity of the contract ({data.total_quantity})"
        )

    contract_data = {
        "id": uuid.uuid4(),
        "name": data.name,
        "code": data.code,
        "signing_date": data.signing_date,
        "sponsor_id": data.sponsor_id,
        "sponsored_party": data.sponsored_party,
        "s3_key": data.s3_key,
        "s3_bucket": data.s3_bucket,
        "total_quantity": data.total_quantity,
        "created_at": current_time,
        "updated_at": current_time,
    }

    try:
        async with db.transaction():
            # Create the contract
            contract = await SponsorContract.create(**contract_data)

            # Create the campaigns
            for campaign_data in data.campaigns:
                campaign = {
                    "id": uuid.uuid4(),
                    "start_date": campaign_data.start_date,
                    "end_date": campaign_data.end_date,
                    "quantity": campaign_data.quantity,
                    "contract_id": contract.id,
                    "gs_area_code": campaign_data.gs_area_code,
                    "name": campaign_data.name,
                    "description": campaign_data.description,
                    "created_at": current_time,
                    "updated_at": current_time,
                }
                await SponsorCampaign.create(**campaign)

            return contract.to_dict()
    except Exception as e:
        logger.error(f"Error creating sponsor contract: {e}")
        raise Exception(f"Error creating sponsor contract: {str(e)}")


async def update_sponsor_contract(
    contract: SponsorContract, data: SponsorContractUpdate
) -> Optional[str]:
    """Update an existing sponsor contract"""
    if contract.deleted_at:
        return f"Contract with id: {contract.id} already deleted!"

    update_data = {k: v for k, v in data.dict().items() if v is not None}
    if not update_data:
        return None

    # If updating sponsor_id, check if the new sponsor exists and is not deleted
    if "sponsor_id" in update_data:
        sponsor = await Sponsor.get(update_data["sponsor_id"])
        if not sponsor:
            return f"Sponsor with id: {update_data['sponsor_id']} not found"
        if sponsor.deleted_at:
            return f"Sponsor with id: {update_data['sponsor_id']} is deleted"

    update_data["updated_at"] = get_current_date_time_utc_7()

    try:
        await contract.update(**update_data).apply()
        logger.info(f"Contract with name: {contract.name} updated")
        return None
    except Exception as e:
        logger.error(f"Error updating sponsor contract: {e}")
        return str(e)


async def delete_sponsor_contract(
    contract: SponsorContract,
) -> Tuple[Optional[SponsorContract], Optional[str]]:
    """Delete a sponsor contract by marking it as deleted"""
    if not contract:
        return None, "Contract cannot be found"
    elif contract.deleted_at:
        return None, f"Contract with id: {contract.id} already deleted!"

    current_time = get_current_date_time_utc_7()
    update_data = {"deleted_at": current_time}

    try:
        # Check if contract has any non-deleted campaigns
        campaign_count = (
            await db.select([db.func.count()])
            .where(SponsorCampaign.contract_id == contract.id)
            .where(SponsorCampaign.deleted_at.is_(None))
            .gino.scalar()
        )

        if campaign_count > 0:
            # Mark campaigns as deleted first
            await (
                SponsorCampaign.update.values(deleted_at=current_time)
                .where(SponsorCampaign.contract_id == contract.id)
                .where(SponsorCampaign.deleted_at.is_(None))
                .gino.status()
            )

            logger.info(
                f"Deleted {campaign_count} campaigns for contract: {contract.name}"
            )

        await contract.update(**update_data).apply()
        logger.info(f"Contract with name: {contract.name} deleted")
        return contract, None
    except Exception as e:
        logger.error(f"Error deleting sponsor contract: {e}")
        return None, str(e)


# --- SponsorCampaign CRUD ---


async def get_sponsor_campaign(campaign_id: uuid.UUID) -> Optional[SponsorCampaign]:
    """Get a sponsor campaign by ID"""
    campaign = await SponsorCampaign.get(campaign_id)
    return campaign


async def count_sponsor_campaigns() -> int:
    """Count all non-deleted sponsor campaigns"""
    total = (
        await db.select([db.func.count(SponsorCampaign.id)])
        .where(SponsorCampaign.deleted_at.is_(None))
        .gino.scalar()
    )
    return total


async def count_matched_sponsor_campaigns(
    contract_id: Optional[uuid.UUID] = None,
    gs_area_code: Optional[uuid.UUID] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> int:
    """Count sponsor campaigns matching the filter criteria"""
    query = db.select([db.func.count()]).where(SponsorCampaign.deleted_at.is_(None))

    if start_date and end_date:
        start_date = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE)
        end_date = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE)
        query = query.where(cast(SponsorCampaign.created_at, Date) >= start_date)
        query = query.where(cast(SponsorCampaign.created_at, Date) <= end_date)
    if contract_id:
        query = query.where(SponsorCampaign.contract_id == contract_id)
    if gs_area_code:
        query = query.where(SponsorCampaign.gs_area_code == gs_area_code)

    try:
        return await query.gino.scalar()
    except Exception as e:
        logger.warning(e)
        return 0


async def get_all_sponsor_campaigns(
    page_number: Optional[int] = None,
    page_size: Optional[int] = None,
    contract_id: Optional[uuid.UUID] = None,
    gs_area_code: Optional[uuid.UUID] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = "created_at",
    order_option: Optional[str] = "desc",
) -> Tuple[List, int]:
    """Get all sponsor campaigns matching the filter criteria"""
    # Join with GsArea to get area name and SponsorContract for contract details
    query = (
        db.select(
            [
                SponsorCampaign,
                SponsorContract.name.label("contract_name"),
                GsArea.area.label("area_name"),
            ]
        )
        .select_from(
            SponsorCampaign.outerjoin(
                SponsorContract, SponsorCampaign.contract_id == SponsorContract.id
            ).outerjoin(GsArea, SponsorCampaign.gs_area_code == GsArea.id)
        )
        .where(SponsorCampaign.deleted_at.is_(None))
    )

    if start_date and end_date:
        start_date = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE)
        end_date = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE)
        query = query.where(cast(SponsorCampaign.created_at, Date) >= start_date)
        query = query.where(cast(SponsorCampaign.created_at, Date) <= end_date)
    if contract_id:
        query = query.where(SponsorCampaign.contract_id == contract_id)
    if gs_area_code:
        query = query.where(SponsorCampaign.gs_area_code == gs_area_code)

    if order_by:
        if order_option == "desc":
            query = query.order_by(getattr(SponsorCampaign, order_by).desc())
        else:
            query = query.order_by(getattr(SponsorCampaign, order_by))

    total = await count_matched_sponsor_campaigns(
        contract_id=contract_id,
        gs_area_code=gs_area_code,
        start_date=start_date,
        end_date=end_date,
    )

    if page_size:
        query = query.limit(page_size)
    if page_number:
        query = query.offset((page_number - 1) * page_size)

    campaigns = await query.gino.all()
    return campaigns, total


async def create_sponsor_campaign(
    data: SponsorCampaignCreate,
) -> Tuple[dict, Optional[str]]:
    """Create a new sponsor campaign"""
    current_time = get_current_date_time_utc_7()

    # Check if contract exists
    contract = await SponsorContract.get(data.contract_id)
    if not contract:
        return None, f"Contract with id: {data.contract_id} not found"

    if contract.deleted_at:
        return None, f"Contract with id: {data.contract_id} is deleted"

    # Check if gs_area exists if provided
    if data.gs_area_code:
        gs_area = await GsArea.get(data.gs_area_code)
        if not gs_area:
            return None, f"GS area with id: {data.gs_area_code} not found"

    campaign_data = {
        "id": uuid.uuid4(),
        "start_date": data.start_date,
        "end_date": data.end_date,
        "quantity": data.quantity,
        "contract_id": data.contract_id,
        "gs_area_code": data.gs_area_code,
        "name": data.name,
        "description": data.description,
        "created_at": current_time,
        "updated_at": current_time,
    }

    try:
        result = await SponsorCampaign.create(**campaign_data)
        return result.to_dict(), None
    except Exception as e:
        logger.error(f"Error creating sponsor campaign: {e}")
        return None, str(e)


async def update_sponsor_campaign(
    campaign: SponsorCampaign, data: SponsorCampaignUpdate
) -> Optional[str]:
    """Update an existing sponsor campaign"""
    if campaign.deleted_at:
        return f"Campaign with id: {campaign.id} already deleted!"

    update_data = {k: v for k, v in data.dict().items() if v is not None}
    if not update_data:
        return None

    # If updating contract_id, check if the new contract exists and is not deleted
    if "contract_id" in update_data:
        contract = await SponsorContract.get(update_data["contract_id"])
        if not contract:
            return f"Contract with id: {update_data['contract_id']} not found"
        if contract.deleted_at:
            return f"Contract with id: {update_data['contract_id']} is deleted"

    # If updating gs_area_code, check if the new gs_area exists
    if "gs_area_code" in update_data and update_data["gs_area_code"]:
        gs_area = await GsArea.get(update_data["gs_area_code"])
        if not gs_area:
            return f"GS area with id: {update_data['gs_area_code']} not found"

    update_data["updated_at"] = get_current_date_time_utc_7()

    try:
        await campaign.update(**update_data).apply()
        logger.info(f"Campaign with id: {campaign.id} updated")
        return None
    except Exception as e:
        logger.error(f"Error updating sponsor campaign: {e}")
        return str(e)


async def delete_sponsor_campaign(
    campaign: SponsorCampaign,
) -> Tuple[Optional[SponsorCampaign], Optional[str]]:
    """Delete a sponsor campaign by marking it as deleted"""
    if not campaign:
        return None, "Campaign cannot be found"
    elif campaign.deleted_at:
        return None, f"Campaign with id: {campaign.id} already deleted!"

    current_time = get_current_date_time_utc_7()
    update_data = {"deleted_at": current_time}

    try:
        await campaign.update(**update_data).apply()
        logger.info(f"Campaign with id: {campaign.id} deleted")
        return campaign, None
    except Exception as e:
        logger.error(f"Error deleting sponsor campaign: {e}")
        return None, str(e)


async def get_sponsor_details(sponsor_id: uuid.UUID):
    """
    Get sponsor details including associated contracts and samples.

    Args:
        sponsor_id: UUID of the sponsor

    Returns:
        Dictionary containing sponsor details with associated contracts and samples
    """
    # Get sponsor information
    sponsor = await Sponsor.get(sponsor_id)
    if not sponsor:
        return None

    # Get all non-deleted contracts for this sponsor
    contracts = (
        await SponsorContract.query.where(SponsorContract.sponsor_id == sponsor_id)
        .where(SponsorContract.deleted_at.is_(None))
        .gino.all()
    )

    contract_details = []
    for contract in contracts:
        # Get associated campaigns for this contract
        campaigns = (
            await SponsorCampaign.query.where(SponsorCampaign.contract_id == contract.id)
            .where(SponsorCampaign.deleted_at.is_(None))
            .gino.all()
        )

        campaign_details = []
        for campaign in campaigns:
            # Get GS area name
            gs_area = (
                await GsArea.get(campaign.gs_area_code) if campaign.gs_area_code else None
            )

            campaign_details.append(
                {
                    "id": str(campaign.id),
                    "name": campaign.name,
                    "start_date": campaign.start_date.isoformat()
                    if campaign.start_date
                    else None,
                    "end_date": campaign.end_date.isoformat()
                    if campaign.end_date
                    else None,
                    "quantity": campaign.quantity,
                    "gs_area_code": str(campaign.gs_area_code)
                    if campaign.gs_area_code
                    else None,
                    "gs_area_name": gs_area.area if gs_area else None,
                    "description": campaign.description,
                    "created_at": campaign.created_at.isoformat()
                    if campaign.created_at
                    else None,
                    "updated_at": campaign.updated_at.isoformat()
                    if campaign.updated_at
                    else None,
                }
            )

        contract_details.append(
            {
                "id": str(contract.id),
                "name": contract.name,
                "code": contract.code,
                "signing_date": contract.signing_date.isoformat()
                if contract.signing_date
                else None,
                "sponsored_party": str(contract.sponsored_party)
                if contract.sponsored_party
                else None,
                "s3_key": contract.s3_key,
                "s3_bucket": contract.s3_bucket,
                "total_quantity": contract.total_quantity,
                "created_at": contract.created_at.isoformat()
                if contract.created_at
                else None,
                "updated_at": contract.updated_at.isoformat()
                if contract.updated_at
                else None,
                "campaigns": campaign_details,
            }
        )

    # Get all samples associated with this sponsor's contracts
    samples = (
        await db.select([SampleV3])
        .select_from(
            SampleV3.join(
                Sponsor, SampleV3.sponsor_id == sponsor_id
            )
        )
        .where(SampleV3.deleted_at.is_(None))
        .gino.all()
    )

    sample_details = []
    for sample in samples:
        # Get kit associated with this sample by samplecode
        kit = await KitV3.query.where(
            KitV3.samplecode == sample.samplecode
        ).gino.first()

        # Prepare sample data with kit information if available
        sample_data = {
            "id": str(sample.id),
            "samplecode": sample.samplecode,
            "subject_id": str(sample.subject_id),
            "sample_collection_date": sample.sample_collection_date.isoformat()
            if sample.sample_collection_date
            else None,
            "sample_receipt_date": sample.sample_receipt_date.isoformat()
            if sample.sample_receipt_date
            else None,
            "lab_receipt_date": sample.lab_receipt_date.isoformat()
            if sample.lab_receipt_date
            else None,
            "sample_type": sample.sample_type,
            "sample_recollection": sample.sample_recollection,
            "sample_collector_name": sample.sample_collector_name,
            "sample_receiver_name": sample.sample_receiver_name,
            "created_at": sample.created_at.isoformat() if sample.created_at else None,
            "kit": None,
        }

        # Add kit information if available
        if kit:
            sample_data["kit"] = {
                "id": str(kit.id),
                "barcode": kit.barcode,
                "product_code": kit.product_code,
                "product_name": kit.product_name,
                "product_type": kit.product_type,
                "current_status": kit.current_status,
                "current_status_id": kit.current_status_id,
                "expected_report_release_date": kit.expected_report_release_date.isoformat()
                if kit.expected_report_release_date
                else None,
                "actual_report_release_time": kit.actual_report_release_time.isoformat()
                if kit.actual_report_release_time
                else None,
                "is_priority": kit.is_priority,
                "is_card_issued": kit.is_card_issued,
                "free_of_charge": kit.free_of_charge,
                "workflow": kit.workflow,
            }

        sample_details.append(sample_data)

    # Prepare result structure
    result = {
        "id": str(sponsor.id),
        "name": sponsor.name,
        "code": sponsor.code,
        "description": sponsor.description,
        "type": sponsor.type,
        "created_at": sponsor.created_at.isoformat() if sponsor.created_at else None,
        "updated_at": sponsor.updated_at.isoformat() if sponsor.updated_at else None,
        "deleted_at": sponsor.deleted_at.isoformat() if sponsor.deleted_at else None,
        "contracts": contract_details,
        "samples": sample_details,
    }

    return result
