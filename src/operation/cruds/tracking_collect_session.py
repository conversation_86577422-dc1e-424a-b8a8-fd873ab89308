import uuid
from datetime import datetime
from typing import Optional

from fastapi import H<PERSON><PERSON><PERSON>xception
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_404_NOT_FOUND, HTTP_409_CONFLICT

from src.operation.config import config

from .. import logger
from ..models.models import (
    KitV3,
    SampleV3,
    Status,
    TrackingCollectSession,
    TrackingSessionSample,
    db,
)
from ..schemas.tracking_collect_session import *
from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE,
    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
    failure_response,
    format_date,
    get_current_date_time_utc_7,
    get_product_by_product_code,
)
from .account import get_account_by_name
from .register_kit import (
    create_kit_v3,
    create_sample_v3,
    get_sample_v3_via,
    init_status_v3,
)
from .sale_account_history import get_current_sale_account_history_by_account_id
from .samplecodes import validate_samplecode
from .source import (
    create_source_by_staff_and_account,
    create_source_by_staffs_n_account,
)
from .staff import get_staff
from .subject import get_subject_by_id_card, update_subject
from .tracking import create_tracking
from .tracking_collection import create_tracking_collection
from .tracking_employee import *
from .tracking_unit import get_tracking_unit_by_id
from .tracking_unit_location import *

COLLECT_SESSION_LIST_QUERY_V3 = """
    select
        tcs.id,
        tu.id as unit_id,
        tu."tenDonVi",
        tcs.location_id,
        tul.name as location_name,
        tcs.user_id,
        tcs.phone_number,
        tcs.employee_id,
        tcs.employee_name,
        tcs.collect_date,
        tcs.created_at,
        tcs.updated_at,
        tcs.deleted_at
    from
        tracking_collect_session tcs
        INNER JOIN tracking_unit_location tul ON tcs.location_id=tul.id
        INNER JOIN tracking_unit tu ON tul.unit_id=tu.id
"""

COLLECT_SESSION_LIST_COUNT_QUERY_V3 = """
    select
        count(tcs.id)
    from
        tracking_collect_session tcs
        INNER JOIN tracking_unit_location tul ON tcs.location_id=tul.id
        INNER JOIN tracking_unit tu ON tul.unit_id=tu.id
"""


async def get_tracking_collect_session_by_id(id: uuid.uuid4):
    tracking_collect_session = await TrackingCollectSession.get(id)
    return tracking_collect_session


async def get_all_tracking_collect_sessions(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tcs.created_at",
    unit_id: Optional[int] = None,
    tenDonVi: Optional[str] = None,
    location_name: Optional[str] = None,
    phone_number: Optional[str] = None,
    employee_name: Optional[str] = None,
    customer_support_id: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    query_filter = ""
    if unit_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tu.id = '{unit_id}' """

    if tenDonVi:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(tu."tenDonVi") like '%{tenDonVi.lower()}%' """

    if location_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tul.name = '{location_name}' """

    if phone_number:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tcs.phone_number = '{phone_number}' """

    if employee_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += (
            f"""lower(tcs.employee_name) like '%{employee_name.lower()}%' """
        )

    if customer_support_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tcs.user_id = '{customer_support_id}' """

    if collection_start_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tcs.collect_date::date >= '{collection_start_date}' """

    if collection_end_date:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tcs.collect_date::date <= '{collection_end_date}' """

    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += """tcs.deleted_at is null """

    raw_count_query = (
        COLLECT_SESSION_LIST_COUNT_QUERY_V3 + "where" + query_filter
        if query_filter != ""
        else COLLECT_SESSION_LIST_COUNT_QUERY_V3
    )
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = COLLECT_SESSION_LIST_QUERY_V3 + query_filter
    else:
        raw_query = (
            COLLECT_SESSION_LIST_QUERY_V3 + "where " + query_filter
            if query_filter != ""
            else COLLECT_SESSION_LIST_QUERY_V3
        )
    results = await db.all(db.text(raw_query))
    return results, total

    pass


async def is_tracking_session_sample_w_subject_id_existed(
    session_id: str, subject_id: str
):
    total = (
        await db.select([db.func.count()])
        .where(TrackingSessionSample.session_id == session_id)
        .where(TrackingSessionSample.subject_id == subject_id)
        .where(TrackingSessionSample.deleted_at == None)
        .gino.scalar()
    )
    return True if total > 0 else False


async def is_tracking_collect_session_existed(id: str):
    total = (
        await db.select([db.func.count()])
        .where(TrackingCollectSession.id == id)
        .where(TrackingCollectSession.deleted_at == None)
        .gino.scalar()
    )
    return True if total > 0 else False


async def create_tracking_collect_session(data: dict):
    """
    location_id
    user_id
    phone_number
    employee_name
    """
    async with db.transaction() as tx:
        unit_location = await get_tracking_unit_location_by_id(id=data["location_id"])
        if not unit_location:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(
                f"Không tìm thấy đơn vị với location_id {data['location_id']}"
            )
            raise HTTPException(status_code=http_code, detail=errs)

        unit_id = unit_location.unit_id
        logger.info(f"unit_location with unit_id: {unit_id}")
        unit = await get_tracking_unit_by_id(id=unit_id)
        tracking_employee = await get_tracking_employee_by_name_n_unit_id(
            data["employee_name"], unit_id
        )

        current_time = get_current_date_time_utc_7()

        if not tracking_employee:
            employee_data = {
                "hoTenNhanVien": data["employee_name"],
                "soDinhDanh": "REQUIRE_TO_UPDATE" + str(current_time),
                "chucVu": "Chuyên Viên",
                "unit_id": unit_id,
            }
            _ = await create_tracking_employee(employee_data)
            logger.info(
                f"employee with name: {data['employee_name']} and unit_id: {unit_id} created!"
            )
            tracking_employee = await get_tracking_employee_by_name_n_unit_id(
                data["employee_name"], unit_id
            )
            # tracking_employee = await get_tracking_employee_by_id(str(res['id']))
            # print("tracking_employee id: ", tracking_employee.id)

        if unit.gs_phone_number and data["phone_number"] in unit.gs_phone_number:
            collect_date_str = current_time.strftime(
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR
            )  # Format to string
            collect_date = datetime.strptime(
                collect_date_str, "%Y-%m-%dT%H:%M:%S"
            )  # Convert back to datetime
            data = {
                "location_id": data["location_id"],
                "user_id": data["user_id"],
                "phone_number": data["phone_number"],
                "employee_id": tracking_employee.id,
                "employee_name": data["employee_name"],
                "collect_date": collect_date,
                "created_at": current_time,
                "updated_at": current_time,
            }
            result = await TrackingCollectSession.create(**data)
            logger.info(
                f"TrackingCollectSession with unit_id: {unit_id} and employee_name: {data['employee_name']} created"
            )
            return result.to_dict()
        else:
            err = f"Lỗi: TrackingCollectSession với số điện thoại {data['phone_number']} không khớp với số điện thoại hiện tại của đơn vị!"
            http_code = HTTP_409_CONFLICT
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)


async def update_tracking_collect_session(
    tracking_collect_session: TrackingCollectSession, data: dict
):
    """
    location_id: str
    employee_id: str
    collect_date: str

    employee_name: str
    """
    if tracking_collect_session.deleted_at:
        err = f"TrackingCollectSession with id: {tracking_collect_session.id} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    curr_unit_location = await get_tracking_unit_location_by_id(
        id=tracking_collect_session.location_id
    )

    if data.get("location_id"):
        unit_location = await get_tracking_unit_location_by_id(id=data["location_id"])
        if not unit_location:
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(
                f"Unit with location_id {data['location_id']} not found"
            )
            raise HTTPException(status_code=http_code, detail=errs)

        if unit_location.unit_id != curr_unit_location.unit_id:
            err = f"UnitLocation with id: {data.get('location_id')} is not belong to this unit"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

    if data.get("collect_date"):
        collect_date = format_date(
            data.get("collect_date"),
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
        data.update({"collect_date": collect_date})

    if data.get("employee_id"):
        tracking_employee = await get_tracking_employee_by_id(data.get("employee_id"))
        if not tracking_employee:
            err = f"TrackingEmployee with id: {data.get('employee_id')} cannot be found"
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        if tracking_employee.unit_id != curr_unit_location.unit_id:
            err = f"TrackingEmployee with id: {data.get('employee_id')} is not belong to this unit"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        data.update({"employee_name": tracking_employee.hoTenNhanVien})

    await tracking_collect_session.update(**data).apply()
    logger.info(
        f"TrackingCollectSession with id: {tracking_collect_session.id} updated"
    )
    return await get_tracking_collect_session_by_id(tracking_collect_session.id)


async def delete_tracking_collect_session(
    tracking_collect_session: TrackingCollectSession,
):
    current_time = get_current_date_time_utc_7()
    data = {
        "updated_at": current_time,
        "deleted_at": current_time,
    }

    if not tracking_collect_session:
        err = f"TrackingCollectSession with name: {tracking_collect_session.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_collect_session.deleted_at:
        err = f"TrackingCollectSession with: {tracking_collect_session.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_collect_session.update(**data).apply()
    logger.info(
        f"TrackingCollectSession with name: {tracking_collect_session.name} deleted"
    )


@validate_samplecode
async def process_sample_data(
    req_body: AddSamplesToCollectSession,
    subject_data: dict,
    current_time: datetime,
):
    if req_body.sample_recollection:
        err = str(
            f"Using recollection flow for CCCD/Identity Card  {req_body.identifier_code} instead!"
        )
        raise ValueError(err)

    if req_body.validate_account:
        err = str(
            f"Using validate account flow for CCCD/Identity Card  {req_body.identifier_code} instead!"
        )
        raise ValueError(err)

    if req_body.sample_receipt_date:
        sample_receipt_date = format_date(
            req_body.sample_receipt_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
    else:
        sample_receipt_date = None

    # no subject update

    # account & source
    if req_body.account_id:
        current_sale_account_his = await get_current_sale_account_history_by_account_id(
            req_body.account_id
        )
    else:
        account = await get_account_by_name(req_body.account_name)
        if not account:
            err = f"Account with name {req_body.account_name} not found"
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        current_sale_account_his = await get_current_sale_account_history_by_account_id(
            account.id
        )

    if req_body.freelancer_id:
        freelancer = await get_staff(req_body.freelancer_id)
        if freelancer.role != "FREELANCER":
            err = "Staff role is not FREELANCER!"
            raise ValueError(err)
        source = await create_source_by_staffs_n_account(
            req_body.nominator_id,
            current_sale_account_his["id"],
            req_body.freelancer_id,
        )
    else:
        source = await create_source_by_staff_and_account(
            req_body.nominator_id, current_sale_account_his["id"]
        )

    # LAB DATE
    expected_report_release_date = None
    lab_receipt_date = None

    if sample_receipt_date is not None:
        if req_body.expected_report_release_date is None:
            expected_report_release_date = sample_receipt_date.date() + timedelta(
                days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
            )
        else:
            expected_report_release_date = format_date(
                req_body.expected_report_release_date,
                DEFAULT_DATE_NO_TIME_ZONE,
                validate_current_time=False,
            )
        if expected_report_release_date < sample_receipt_date.date():
            raise ValueError(
                "expected report release date cannot be before sample receipt date"
            )

        if req_body.lab_receipt_date:
            lab_receipt_date = format_date(
                req_body.lab_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            if lab_receipt_date < sample_receipt_date:
                raise ValueError(
                    "lab receipt date cannot be before sample receipt date"
                )
        else:
            lab_receipt_date = None

    # RECOLLECTION
    subject_id = subject_data.get("id")
    sample_res = await get_sample_v3_via(subject_id=subject_id)
    total_sample = len(sample_res)

    if total_sample:
        if req_body.sample_recollection is True:
            raise ValueError(
                "Đối tượng đã được thu mẫu. Vui lòng thực hiện luồng thu lại mẫu."
            )

        sample_collection_time = total_sample + 1
    else:
        if req_body.sample_recollection is not False:
            raise ValueError("Đối tượng cần được thu mẫu mới")

        sample_collection_time = 1

    # SAMPLE
    # CURRENT_STATUS
    status_data = init_status_v3()
    status = await Status.create(**status_data)
    sample_req = SampleV3(
        id=uuid.uuid4(),
        samplecode=req_body.samplecode,
        subject_id=subject_id,
        sample_collection_date=req_body.sample_collection_date,
        sample_recollection=req_body.sample_recollection,
        sample_collection_time=sample_collection_time,
        sample_receipt_date=sample_receipt_date,
        sample_collector_name=req_body.std_sample_collector_name,
        sample_receiver_name=req_body.std_sample_receiver_name,
        source_id=source["id"],
        # scan_status
        # lab_check_date <-- lab_sample update this
        lab_receipt_date=lab_receipt_date,
        sample_type=req_body.sample_type,
    )
    sample_data, err = await create_sample_v3(sample_req)
    current_time = get_current_date_time_utc_7()
    product_detail = get_product_by_product_code(req_body.product_code)

    if product_detail is not None:
        if product_detail["name"] != req_body.product_name:
            raise ValueError(
                f"Product's name {req_body.product_name} doesnt match with the name from database {product_detail['name']}!"
            )
        product_code = product_detail["code"]
        product_name = product_detail["name"]
    else:
        raise ValueError("Product Name and Product Code not found!")

    # KIT
    kit_req = KitV3(
        id=uuid.uuid4(),
        # barcode=register_kit_req.barcode, <--- generated when scan_status==2
        samplecode=req_body.samplecode,
        nickname=req_body.nickname,
        version=req_body.version,
        expected_report_release_date=expected_report_release_date,
        # actual_report_release_time <-- update when releasing
        customer_support_id=req_body.customer_support_id,
        customer_support_name=req_body.customer_support_name,
        free_of_charge=req_body.free_of_charge,
        promotion=req_body.promotion_id,
        is_priority=req_body.is_priority,
        # pdf_generation_date <- Nullable
        # is_card_issued <-- False by default
        product_code=product_code,  # before from barcode.detail
        product_name=product_name,  # before from barcode.detail
        product_type=req_body.product_type,
        note=req_body.note,
        current_status=status.status,
        current_status_id=status.id,
        workflow=config["WORKFLOW_STATUS"]["REGISTER_KIT_W_CCCD"],
        created_at=current_time,
        updated_at=current_time,
    )
    kit_data, err = await create_kit_v3(kit_req)
    if err:
        raise ValueError(err)
    # print("Successfully Created Kit!")

    data = {**subject_data, **sample_data, **kit_data}
    return data


async def process_tracking_collection(req_body, current_time, data):
    tracking_dict = {
        "samplecode": req_body.samplecode,
        "status": "Thu nhận mẫu",
        "identifier_code": req_body.identifier_code,
    }

    tracking = await create_tracking(tracking_dict)

    # Add tracking_collection
    tracking_unit = await get_tracking_unit_by_id(req_body.tracking_unit_id)
    std_tracking_employee_name = req_body.std_sample_collector_name
    tracking_employee_dict = await get_tracking_employee_dict_by_name_n_unit_id(
        std_tracking_employee_name, req_body.tracking_unit_id
    )
    if not tracking_employee_dict:
        employee_data = {
            "hoTenNhanVien": std_tracking_employee_name,
            "soDinhDanh": "REQUIRE_TO_UPDATE" + str(current_time),
            "chucVu": "Chuyên Viên",
            "unit_id": req_body.tracking_unit_id,
        }
        tracking_employee_dict = await create_tracking_employee(employee_data)

    tracking_collection_dict = {
        "tracking_id": tracking.get("id"),
        "maThuNhan": req_body.samplecode,
        "donViThuNhanMau_id": req_body.tracking_unit_id,
        "noiThuThapMau": tracking_unit.gs_area_code,
        "ngayGioThuThapMau": req_body.sample_collection_date,
        "nhanVienLayMau_id": tracking_employee_dict.get("id"),
        "nhanVienGhiHoSo_id": tracking_employee_dict.get("id"),
    }
    tracking_collection = await create_tracking_collection(tracking_collection_dict)

    data = {
        **data,
        "tracking": tracking,
        "tracking_collection": tracking_collection,
    }

    return data


async def upsert_tracking_collect_session_sample(
    req_body: AddSamplesToCollectSession, session_id: str
):
    async with db.transaction() as tx:
        # no identity_card update

        current_time = get_current_date_time_utc_7()
        subject = await get_subject_by_id_card(identifier_code=req_body.identifier_code)
        if not subject:
            err = (
                f"Không tìm thấy đối tượng với mã định danh {req_body.identifier_code}"
            )
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        if req_body.samplecode:
            subject.require_registration = False
            _ = await update_subject(subject=subject)
            subject_data = subject.to_dict()
            subject_id = subject.id

            logger.info(
                f"Onboard CCCD {subject_id} with samplecode {req_body.samplecode}!"
            )
            data = await process_sample_data(
                req_body=req_body,
                subject_data=subject_data,
                current_time=current_time,
            )
            # TRACKING
            data = await process_tracking_collection(
                req_body=req_body,
                current_time=current_time,
                data=data,
            )

            updated_data_collect_session_sample = {
                "samplecode": req_body.samplecode,
                "updated_at": current_time,
            }

            collect_session_sample = (
                await TrackingSessionSample.query.where(
                    TrackingSessionSample.session_id == session_id
                )
                .where(TrackingSessionSample.subject_id == subject_id)
                .gino.first()
            )

            if collect_session_sample:
                await collect_session_sample.update(
                    **updated_data_collect_session_sample
                ).apply()
            else:
                # raise ValueError(
                #     f"TrackingSessionSample with session_id: {session_id} and subject_id: {subject_id} not found!"
                # )
                data = {}
                data_collect_session_sample = {
                    "session_id": session_id,
                    "subject_id": subject_id,
                    "samplecode": req_body.samplecode,
                    "created_at": current_time,
                    "updated_at": current_time,
                }

                collect_session_sample = await TrackingSessionSample.create(
                    **data_collect_session_sample
                )

            data.update({"collect_session_sample": collect_session_sample.to_dict()})

            # -> Will Update Barcode right after it being generated
            # -> Send SQS mess to grant permission
            # await update_code(register_kit_req.barcode, config['USED_CODE_STATE'])
            # if user_info:
            #     send_resouce_message_to_queue(user_info['data']["uuid"], register_kit_req.barcode)
            return data

        else:
            if await is_tracking_session_sample_w_subject_id_existed(
                session_id=session_id, subject_id=subject.id
            ):
                err = "CCCD đã tồn tại! Vui lòng kiểm tra lại!"
                raise ValueError(err)

            subject.require_registration = True
            _ = await update_subject(subject=subject)
            subject_data = subject.to_dict()
            subject_id = subject.id
            logger.info(f"Onboard CCCD {subject_id} w/o samplecode!")
            data = {}
            data_collect_session_sample = {
                "session_id": session_id,
                "subject_id": subject_id,
                "created_at": current_time,
                "updated_at": current_time,
            }

            collect_session_sample = await TrackingSessionSample.create(
                **data_collect_session_sample
            )

            data.update({"collect_session_sample": collect_session_sample.to_dict()})
            return data


# async def upsert_tracking_collect_session_sample_w_only_cccd(
#     req_body: AddSamplesToCollectSession, session_id: str
# ):
#     pass


SESSION_SAMPLES = """
    SELECT
        sub.identifier_code,
        sub.full_name,
        sub.dob::date,
        sub.gender,
        tss.samplecode,
        sam.sample_collection_date,
        sam.sample_collector_name,
        tul.name as location_name,
        tu."tenDonVi" as unit_name
    FROM
        tracking_collect_session tcs
        INNER JOIN tracking_unit_location tul ON tcs.location_id = tul.id
        INNER JOIN tracking_unit tu ON tul.unit_id = tu.id
        INNER JOIN tracking_session_sample tss ON tcs.id = tss.session_id
        LEFT JOIN sample sam ON tss.samplecode = sam.samplecode
        INNER JOIN subject sub ON tss.subject_id = sub.id
"""

COUNT_SESSION_SAMPLES = """
    SELECT
        COUNT(sub.identifier_code)
    FROM
        tracking_collect_session tcs
        INNER JOIN tracking_unit_location tul ON tcs.location_id = tul.id
        INNER JOIN tracking_unit tu ON tul.unit_id = tu.id
        INNER JOIN tracking_session_sample tss ON tcs.id = tss.session_id
        LEFT JOIN sample sam ON tss.samplecode = sam.samplecode
        INNER JOIN subject sub ON tss.subject_id = sub.id
"""


async def get_all_collect_session_samples(
    offset: Optional[int],
    size: Optional[int],
    order_by: Optional[str],
    session_id: Optional[str],
    identifier_code: str,
    full_name: str,
    dob: str,
    gender: str,
    samplecode: Optional[str],
):
    query_filter = ""
    if session_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"tcs.id = '{session_id}' "
    if identifier_code:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.identifier_code like '%{identifier_code}%' "
    if full_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"lower(sub.full_name) like '%{full_name.lower()}%' "
    if dob:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.dob::date = date('{dob}') "
    if gender:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"sub.gender like '%{gender}%' "
    if samplecode:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"tss.samplecode = '{samplecode}' "

    raw_count_query = (
        COUNT_SESSION_SAMPLES + "where " + query_filter
        if query_filter != ""
        else COUNT_SESSION_SAMPLES
    )
    # print("raw_count_query: ", raw_count_query)
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]

    if (order_by == "created_time") or not order_by:
        order_by = "created_at"
    query_filter += f"order by {order_by} desc "
    if size:
        query_filter += f"limit {size} "
        query_filter += f"offset {offset} "

    # if query_filter.startswith("order"):
    #     raw_query = ID_CARD_REQUIRE_REGISTRATION + query_filter
    # else:
    raw_query = (
        SESSION_SAMPLES + "where " + query_filter
        if query_filter != ""
        else SESSION_SAMPLES
    )
    # print("raw_query: ", raw_query)
    results = await db.all(db.text(raw_query))
    return results, total
    pass
