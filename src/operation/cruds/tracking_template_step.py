import uuid
from typing import Optional
from ..models.models import TrackingTemplateStep, db
from sqlalchemy import DateTime, and_
from sqlalchemy.dialects.postgresql import ARRAY
from ..utils.utils import get_current_date_time_utc_7, failure_response
from .. import logger
from fastapi import HTTPException
from starlette.status import (
    HTTP_200_OK,
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_404_NOT_FOUND,
    HTTP_408_REQUEST_TIMEOUT,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR
)

# TrackingTemplateStep - model 
# TEMPLATE_STEP - QUERY NAME
# _tracking_template_step - function name
# tracking_template_step - single
# tracking_template_steps - plural
# name -- search key

TEMPLATE_STEP_LIST_QUERY_V3 = """
    select
        tts.id as template_step_id,
        tts.step_id,
        ts.id,
        ts."tenBuoc",
        tts.template_id,
        tt."congNghe",
        tt.gs_template_name,
        tts.gs_step_number,
        tts.created_at,
        tts.updated_at,
        tts.deleted_at
    from
        tracking_template_step tts
        INNER JOIN tracking_step ts ON tts.step_id = ts.id
        INNER JOIN tracking_template tt ON tts.template_id = tt.id
"""

TEMPLATE_STEP_LIST_COUNT_QUERY_V3 = """
    select
        count(tts.id)
    from
        tracking_template_step tts
        INNER JOIN tracking_step ts ON tts.step_id = ts.id
        INNER JOIN tracking_template tt ON tts.template_id = tt.id
"""

async def get_tracking_template_step_by_id(id: uuid.uuid4):
    tracking_template_step = await TrackingTemplateStep.get(id)
    return tracking_template_step


async def get_all_tracking_template_steps(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]="tts.created_at",
    tenBuoc: Optional[str] = None,
    congNghe: Optional[str] = None,
    gs_template_name: Optional[str] = None,
    template_id: Optional[str] = None,
    include_deleted: Optional[bool]=False,
    
):
    query_filter = ""
    if tenBuoc:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(ts.'tenBuoc') like '%{tenBuoc.lower()}%' """
    if congNghe:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tt.'congNghe' = '{str(congNghe)}' """
    if gs_template_name:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""lower(tt.'gs_template_name') like '%{gs_template_name.lower()}%' """
    if template_id:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tts.template_id = '{str(template_id)}' """
    if not include_deleted:
        query_filter += "and " if query_filter != "" else " "
        query_filter += f"""tts.deleted_at is null """
    
    raw_count_query = TEMPLATE_STEP_LIST_COUNT_QUERY_V3 + "where"+ query_filter if query_filter != "" else TEMPLATE_STEP_LIST_COUNT_QUERY_V3
    count = await db.all(db.text(raw_count_query))
    total = count[0][0]
    if order_by:
        query_filter += f"""order by {order_by} desc """
    if size:
        query_filter += f"""limit {size} """
        query_filter += f"""offset {offset} """

    if query_filter.startswith("order"):
        raw_query = TEMPLATE_STEP_LIST_QUERY_V3 + query_filter
    else:
        raw_query = TEMPLATE_STEP_LIST_QUERY_V3 + "where " + query_filter if query_filter != "" else TEMPLATE_STEP_LIST_QUERY_V3
    results = await db.all(db.text(raw_query))
    return results, total
        
    pass

async def is_tracking_template_step_existed(step_id: str, template_id:str):
    total = await db.select([db.func.count()]).where(TrackingTemplateStep.step_id == step_id).where(TrackingTemplateStep.template_id == template_id).where(TrackingTemplateStep.deleted_at == None).gino.scalar()
    return True if total > 0 else False

async def create_tracking_template_step(data: dict):
    if await is_tracking_template_step_existed(data['step_id'], data['template_id']):
        err = f"Error: TemplateStep with step {data['tenBuoc']} and template {data['gs_template_name']} is already created"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    current_time = get_current_date_time_utc_7()
    data = {
        'gs_step_number': data.get('gs_step_number'),
        'step_id': data.get('step_id'),
        'template_id': data.get('template_id'),
        'created_at': current_time,
        'updated_at': current_time
    }
    result = await TrackingTemplateStep.create(**data)
    return result.to_dict()

async def update_tracking_template_step(tracking_template_step: TrackingTemplateStep, data: dict):
    if tracking_template_step.deleted_at:
        err = f"TrackingTemplateStep with name: {tracking_template_step.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    await tracking_template_step.update(**data).apply()
    logger.info(f"TrackingTemplateStep with name: {tracking_template_step.name} updated")
    return None


async def delete_tracking_template_step(tracking_template_step: TrackingTemplateStep):

    current_time = get_current_date_time_utc_7()
    data = {
        'updated_at': current_time,
        'deleted_at': current_time,
    }

    if not tracking_template_step:
        err = f"TrackingTemplateStep with name: {tracking_template_step.name} cannot be found"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    elif tracking_template_step.deleted_at:
        err = f"TrackingTemplateStep with: {tracking_template_step.name} already deleted!"
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await tracking_template_step.update(**data).apply()
    logger.info(f"TrackingTemplateStep with name: {tracking_template_step.name} deleted")
