from src.operation.models.models import SampleBoxPosition


def set_sample_position(
        samplecode: str,
        x_position: str,
        y_position: str,
        sample_box_id: uuid
):
    position = SampleBoxPosition(
        samplecode=samplecode,
        x_position=x_position,
        y_position=y_position,
        sample_box_id=sample_box_id
    )

    return SampleBoxPosition.create(position)

def get_samples_in_box(
    sample_box_id
    samplecode: Optional[str] = None,
    x_position: Optional[str] = None,
    y_position: Optional[str] = None,
    order_by: Optional[str] = "crea",
    order_option: Optional[str] = "asc",
)
    query = SampleBoxPosition
