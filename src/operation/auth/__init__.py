from fastapi import Security
from fastapi.security import <PERSON><PERSON><PERSON>uthorizationCredentials, HTT<PERSON><PERSON>earer
from starlette.status import HTTP_401_UNAUTHORIZED

from .. import logger
from ..config import config
from ..utils.utils import get_signing_keys


# auto_error=False prevents FastAPI from raising a 403 when the request
# is missing an Authorization header. Instead, we want to return a 401
# to signify that we did not recieve valid credentials
bearer = HTTPBearer(auto_error=False)


class Auth:
    def __init__(
        self,
        bearer_token: HTTPAuthorizationCredentials = Security(bearer),
    ):
        self.bearer_token = bearer_token

    async def get_token_claims(self):
        if not self.bearer_token:
            err_msg = "Must provide an access token."
            logger.error(err_msg)
            err_code = HTTP_401_UNAUTHORIZED
            err = {
                'err_msg': err_msg,
                'err_code': err_code
            }
            return None, err
        try:
            url = config['AUTH']['ISSUER'][config['ENV']]
            token_claims = get_signing_keys(url, self.bearer_token)
        except Exception as e:
            logger.error(f"Could not get token claims:\n{e}", exc_info=True)
            err_msg = f"Could not verify, parse, and/or validate scope from provided access token: {str(e)}"
            err_code = HTTP_401_UNAUTHORIZED
            err = {
                'err_msg': err_msg,
                'err_code': err_code
            }
            return None, err

        return token_claims, None
