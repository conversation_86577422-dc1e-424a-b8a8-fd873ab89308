import json

import boto3
from botocore.exceptions import Client<PERSON>rror
from cdislogging import get_logger

from src.operation.config import config

debug = config["DEBUG"]
logger = get_logger("operation", log_level="debug" if debug else "info")


if config["ENV"] == "LOCAL":
    sqs = boto3.resource(
        "sqs",
        aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
        endpoint_url=config["AWS"]["ENDPOINT_URL"],
        region_name=config["AWS"]["NOTIFICATION_REGION_NAME"],
    )
else:
    sqs = boto3.resource(
        "sqs",
        aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
        region_name=config["AWS"]["NOTIFICATION_REGION_NAME"],
    )


def ensure_cfgs():
    """Ensure that these environment variables are provided at runtime"""
    required_cfgs = ["QUEUE_NAME"]

    if not config.get("NOTIFICATION"):
        message = "Required configurations for NOTIFICATION are missing"
        raise AssertionError(message)
    missing_cfgs = []
    for required_cfg in required_cfgs:
        if not config["NOTIFICATION"].get(required_cfg):
            missing_cfgs.append(required_cfg)

    if missing_cfgs:
        message = "Required configuration variables are missing: " + repr(missing_cfgs)
        raise AssertionError(message)


def send_message(queue, message_body, message_attributes=None):
    """
    Send a message to an Amazon SQS queue.

    :param queue: The queue that receives the message.
    :param message_body: The body text of the message.
    :param message_attributes: Custom attributes of the message. These are key-value
                               pairs that can be whatever you want.
    :return: The response from SQS that contains the assigned message ID.
    """
    if not message_attributes:
        message_attributes = {}

    try:
        response = queue.send_message(
            MessageBody=json.dumps(message_body), MessageAttributes=message_attributes
        )
    except ClientError as error:
        logger.exception("Send message failed: %s", message_body)
        raise error
    else:
        return response


def send_messages(queue, messages):
    """
    Send a batch of messages in a single request to an SQS queue.
    This request may return overall success even when some messages were not sent.
    The caller must inspect the Successful and Failed lists in the response and
    resend any failed messages.

    :param queue: The queue to receive the messages.
    :param messages: The messages to send to the queue. These are simplified to
                     contain only the message body and attributes.
    :return: The response from SQS that contains the list of successful and failed
             messages.
    """
    try:
        entries = [
            {
                "Id": str(ind),
                "MessageBody": msg["body"],
                "MessageAttributes": msg["attributes"],
            }
            for ind, msg in enumerate(messages)
        ]
        response = queue.send_messages(Entries=entries)
        if "Successful" in response:
            for msg_meta in response["Successful"]:
                logger.info(
                    "Message sent: %s: %s",
                    msg_meta["MessageId"],
                    messages[int(msg_meta["Id"])]["body"],
                )
        if "Failed" in response:
            for msg_meta in response["Failed"]:
                logger.warning(
                    "Failed to send: %s: %s",
                    msg_meta["MessageId"],
                    messages[int(msg_meta["Id"])]["body"],
                )
    except ClientError as error:
        logger.exception("Send messages failed to queue: %s", queue)
        raise error
    else:
        return response


def send_msg_to_queue(queue_name, message_body):
    try:
        ensure_cfgs()
    except AssertionError as e:
        logger.error(str(e))
        raise
    logger.info(f"Sending messages to queue {queue_name}")
    queue = sqs.get_queue_by_name(QueueName=queue_name)

    response = send_message(queue, message_body)
    logger.info(f"Successfully sent message to {queue_name}, response: {response}")
    return response
