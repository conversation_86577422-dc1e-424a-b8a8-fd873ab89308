import asyncio
import logging
import os
import sys

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../")))
import boto3
from botocore.exceptions import Client<PERSON>rror
from cdislogging import get_logger

from src.operation.config import DEFAULT_CFG_PATH, config

logging.basicConfig(level=os.environ.get("LOG_LEVEL", "INFO"))
logger = logging.getLogger(__name__)


# Load the configuration *before* importing models
try:
    if os.environ.get("OPERATION_CONFIG_PATH"):
        config.load(config_path=os.environ["OPERATION_CONFIG_PATH"])
    else:
        CONFIG_SEARCH_FOLDERS = [
            "/src",
            "{}/.gt/operation".format(os.path.expanduser("~")),
        ]
        config.load(search_folders=CONFIG_SEARCH_FOLDERS)
except Exception:
    logger.warning("Unable to load config, using default config...", exc_info=False)
    config.load(config_path=DEFAULT_CFG_PATH)

debug = config["DEBUG"]
logger = get_logger("operation", log_level="debug" if debug else "info")

from src.operation.messages.sqs.message_processing import process_message
from src.operation.models.models import db

if config["ENV"] == "LOCAL":
    sqs = boto3.resource(
        "sqs",
        aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
        endpoint_url=config["AWS"]["ENDPOINT_URL"],
        region_name=config["AWS"]["CONSUMER_REGION_NAME"],
    )
else:
    sqs = boto3.resource(
        "sqs",
        aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
        region_name=config["AWS"]["CONSUMER_REGION_NAME"],
    )


def ensure_cfgs():
    """Ensure that these environment variables are provided at runtime"""
    required_cfgs = [
        "SQSCONSUMER_QUEUENAME",
        "MAX_NUMBER_OF_MESSAGES",
        "WAIT_TIME_SECONDS",
    ]

    if not config.get("SQS_CFG"):
        message = "Required configurations for SQS are missing"
        raise AssertionError(message)
    missing_cfgs = []
    for required_cfg in required_cfgs:
        if not config["SQS_CFG"].get(required_cfg):
            missing_cfgs.append(required_cfg)

    if missing_cfgs:
        message = "Required configuration variables are missing: " + repr(missing_cfgs)
        raise AssertionError(message)


async def consumer(queue, max_number, wait_time):
    logger.info("SQS Consumer starting ...")
    await db.set_bind(config["DB_URL"])
    while True:
        logger.info("Polling messages")
        received_messages = await receive_messages(queue, max_number, wait_time)
        if received_messages:
            delete_messages(queue, received_messages)


async def receive_messages(queue, max_number, wait_time):
    try:
        messages = queue.receive_messages(
            MessageAttributeNames=["All"],
            MaxNumberOfMessages=max_number,
            WaitTimeSeconds=wait_time,
        )
        logger.info(f"Received {len(messages)} messages")
        for msg in messages:
            logger.info("Received message: %s: %s", msg.message_id, msg.body)
            await process_message(msg.body)
    except ClientError as error:
        logger.exception("Couldn't receive messages from queue: %s", queue)
        raise error
    else:
        return messages


def delete_message(message):
    """
    Delete a message from a queue. Clients must delete messages after they
    are received and processed to remove them from the queue.

    :param message: The message to delete. The message's queue URL is contained in
                    the message's metadata.
    :return: None
    """
    try:
        message.delete()
        logger.info("Deleted message: %s", message.message_id)
    except ClientError as error:
        logger.exception("Couldn't delete message: %s", message.message_id)
        raise error


def delete_messages(queue, messages):
    """
    Delete a batch of messages from a queue in a single request.

    :param queue: The queue from which to delete the messages.
    :param messages: The list of messages to delete.
    :return: The response from SQS that contains the list of successful and failed
             message deletions.
    """
    try:
        entries = [
            {"Id": str(ind), "ReceiptHandle": msg.receipt_handle}
            for ind, msg in enumerate(messages)
        ]
        response = queue.delete_messages(Entries=entries)
        if "Successful" in response:
            for msg_meta in response["Successful"]:
                logger.info("Deleted %s", messages[int(msg_meta["Id"])].receipt_handle)
        if "Failed" in response:
            for msg_meta in response["Failed"]:
                logger.warning(
                    "Could not delete %s", messages[int(msg_meta["Id"])].receipt_handle
                )
    except ClientError:
        logger.exception("Couldn't delete messages from queue %s", queue)
    else:
        return response


if __name__ == "__main__":
    try:
        ensure_cfgs()
    except AssertionError as e:
        logger.error(str(e))
        raise
    queue_name = config["SQS_CFG"]["SQSCONSUMER_QUEUENAME"]
    max_number = config["SQS_CFG"]["MAX_NUMBER_OF_MESSAGES"]
    wait_time = config["SQS_CFG"]["WAIT_TIME_SECONDS"]
    logger.info(f"Subscribing to queue {queue_name}")
    queue = sqs.get_queue_by_name(QueueName=queue_name)
    loop = asyncio.get_event_loop()
    loop.run_until_complete(consumer(queue, max_number, wait_time))
    loop.close()
