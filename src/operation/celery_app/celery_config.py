import time

import boto3
from celery import Celery
from kombu.utils.url import safequote

from src.operation.config import config


def get_celery_config():
    return {
        "broker_url": config["CELERY"]["BROKER_URL"],
        "result_backend": config["CELERY"]["BACKEND_URL"],
    }


def get_sqs_url():
    start_time = time.time()
    while time.time() - start_time < float(config["SQS_CFG"]["SQS_CHECK_DURATION"]):
        sqs = boto3.client(
            "sqs",
            aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
            aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
            region_name=config["AWS"]["CELERY_REGION_NAME"],
        )
        try:
            response = sqs.get_queue_url(QueueName=config["CELERY"]["QUEUE_NAME"])
            return response["QueueUrl"]
        except sqs.exceptions.QueueDoesNotExist:
            time.sleep(5)

    raise Exception("SQS Queue is not healthy")


def get_celery_app():
    broker_type = config["CELERY"]["BROKER_TYPE"]

    sqs_config = get_sqs_config()
    config_class_list = {"sqs": sqs_config}

    app = Celery(__name__)
    config_class = config_class_list.get(broker_type, get_celery_config())
    app.conf.update(config_class)

    return app


def get_sqs_config():
    sqs_url = get_sqs_url()
    aws_access_key = safequote(config["AWS"]["AWS_ACCESS_KEY_ID"])
    aws_secret_key = safequote(config["AWS"]["AWS_SECRET_ACCESS_KEY"])
    broker_url = "sqs://{aws_access_key}:{aws_secret_key}@".format(
        aws_access_key=aws_access_key,
        aws_secret_key=aws_secret_key,
    )
    conf = {
        "broker_url": broker_url,
        "timezone": config["CELERY"]["TIMEZONE"],
        "broker_transport_options": {
            "region": config["AWS"]["CELERY_REGION_NAME"],
            "predefined_queues": {"celery": {"url": sqs_url}},
        },
    }
    return conf
