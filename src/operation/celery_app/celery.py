import asyncio
import json
import logging
import os
from datetime import datetime

import requests
from cdislogging import get_logger
from fastapi import HTT<PERSON>Ex<PERSON>
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from src.operation.config import DEFAULT_CFG_PATH, config

logging.basicConfig(level=os.environ.get("LOG_LEVEL", "INFO"))
logger = logging.getLogger(__name__)

# Load the configuration *before* importing models
try:
    if os.environ.get("OPERATION_CONFIG_PATH"):
        config.load(config_path=os.environ["OPERATION_CONFIG_PATH"])
    else:
        CONFIG_SEARCH_FOLDERS = [
            "/src",
            "{}/.gt/operation".format(os.path.expanduser("~")),
        ]
        config.load(search_folders=CONFIG_SEARCH_FOLDERS)
except Exception:
    logger.warning("Unable to load config, using default config...", exc_info=False)
    config.load(config_path=DEFAULT_CFG_PATH)

debug = config["DEBUG"]
logger = get_logger("operation", log_level="debug" if debug else "info")

from celery.schedules import crontab

from src.operation.models.models import db
from src.operation.producer import send_msg_to_queue
from src.operation.services.notification import get_no_data_kits
from src.operation.utils.utils import (
    failure_response,
    parse_delete_request_res,
    send_noti_to_teams,
)

from .celery_config import get_celery_app

celery = get_celery_app()


@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    # For testing purpose
    # sender.add_periodic_task(15.0, query_no_completed_data.s(), name="query")
    sender.add_periodic_task(
        # 10.0,
        # handle_pending_delete_requests_due_to_delete.s('2022-08-27T15:14:42.911062'),
        crontab(
            hour=config["CELERY"]["SCHEDULE"]["HOUR"],
            minute=config["CELERY"]["SCHEDULE"]["MINUTE"],
        ),
        handle_pending_delete_requests_due_to_delete.s(),  # create another replica of gt_authen_v2
        name="Handle due delete requests",
    )

    # Executes every morning at 11:30 a.m.
    sender.add_periodic_task(
        crontab(
            hour=config["CELERY"]["SCHEDULE"]["HOUR"],
            minute=config["CELERY"]["SCHEDULE"]["MINUTE"],
        ),
        query_no_completed_data.s(),
    )


async def get_no_data_kit_status():
    await db.set_bind(config["DB_URL"])
    kit_status, batches = await get_no_data_kits()

    return kit_status, batches


@celery.task
def handle_pending_delete_requests_due_to_delete(date=""):
    fifteen_day_after = datetime.utcnow()
    datetime_str = date if date else str(fifteen_day_after).replace(" ", "T")
    env = config["ENV"]
    cron_str = "Everyday at 11:30 AM"  # "Every 10 seconds"    #
    ms_team_webhook = config["CELERY"]["MS_TEAM_WEBHOOK"]

    try:
        request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
        response = requests.put(
            config["AUTHEN_URL"][config["ENV"]] + "/internal/requests/date",
            headers=request_headers,
            json={"status": "DELETED", "datetime_str": datetime_str},
            timeout=5,
        )

        response.raise_for_status()

        data = response.json()["data"]
        exec_result = True
        body = parse_delete_request_res(
            data=data, env=env, cron_str=cron_str, exec_result=exec_result, err=""
        )

        _, msg = send_noti_to_teams(ms_team_webhook, body)

    except requests.exceptions.ConnectionError as err:
        data = None
        exec_result = False
        body = parse_delete_request_res(
            data=data, env=env, cron_str=cron_str, exec_result=exec_result, err=str(err)
        )
        _ = send_noti_to_teams(ms_team_webhook, body)

        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)


@celery.task
def query_no_completed_data():
    loop = asyncio.get_event_loop()
    kit_status, batches = loop.run_until_complete(get_no_data_kit_status())
    if kit_status:
        send_email_notifications(kit_status, batches)


def send_email_notifications(kit_status, batches):
    if len(batches) > 3:
        batches = [str(r) for r in sorted([int(x) for x in batches])][0:3]
        batches = ", ".join(batches) + "..."
    else:
        batches = ", ".join(batches)

    variables = [
        {"name": "batchId", "value": batches},
        {
            "name": "batchItems",
            "value": kit_status if len(kit_status) <= 100 else kit_status[0:100],
        },
    ]
    message_dict = {
        "from_id": config["MAIL"]["USERS"]["SENDER_ID"],
        "template_name_as_subject": config["MAIL"]["TEMPLATE_NAME_AS_SUBJECT"],
        "to": config["NOTIFICATION"]["NO_DATA_MAIL_LIST"],
        "template_id": config["MAIL"]["TEMPLATES"]["NO_DATA_KITS"],
        "variables": variables,
    }

    message_body = json.dumps(message_dict)

    try:
        _ = send_msg_to_queue(config["NOTIFICATION"]["QUEUE_NAME"], message_body)
    except Exception as errs:
        raise errs
