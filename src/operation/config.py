import os

from gen3config import Config
from sqlalchemy.engine.url import URL, make_url

DEFAULT_CFG_PATH = os.path.join(
    os.path.dirname(os.path.abspath(__file__)), "config-default.yaml"
)


class OperationConfig(Config):
    def __init__(self, *args, **kwargs):
        super(OperationConfig, self).__init__(*args, **kwargs)

    def post_process(self) -> None:
        # generate DB_URL from DB configs
        self["DB_URL"] = make_url(
            URL(
                drivername=self["DB_DRIVER"],
                host=self["DB_HOST"],
                port=self["DB_PORT"],
                username=self["DB_USER"],
                password=self["DB_PASSWORD"],
                database=self["DB_DATABASE"],
            ),
        )


config = OperationConfig(DEFAULT_CFG_PATH)
config.load(config_path=DEFAULT_CFG_PATH)
# print(config["DB_URL"])
