from fastapi import Query
from typing import Optional


from ..config import config

class PaginationParams:
    def __init__(
        self,
        page_size: int = Query(10, ge=0, le=config["MAXIMUM_REQUEST_LENGTH"]),
        page_number: int = Query(1, ge=1),
        full_data: Optional[bool] = Query(False, alias="full_data")
    ):
        if full_data:
            self.page_number = None
            self.page_size = None
            self.offset = None
            self.size = None
        else:
            self.page_number = page_number
            self.page_size = page_size
            self.offset = (page_number - 1) * page_size if page_number and page_size else None
            self.size = page_size
