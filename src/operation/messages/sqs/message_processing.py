import json

from src.operation.schemas.partner_booking import PartnerBookingCreationReq
from src.operation.services.cards import (
    generate_qr_code_for_card_w_lang,
    update_card_s3_object_key_w_presigned_url,
)
from src.operation.services.kit import (
    check_and_update_booking_status,
    check_and_update_booking_status_v3,
    create_new_kit_status,
    create_new_kit_status_via_barcode_v3,
    update_kit_actual_release_date,
    update_kit_actual_release_date_v3,
    update_kit_default_link,
    update_kit_default_link_v3,
)
from src.operation.services.partner_booking import (
    create_partner_booking_srv,
    create_partner_booking_srv_v3,
)
from src.operation.services.plate import update_plate_name_w_status
from src.operation.services.sample_mapping import update_sample_mappings_qc_status

from ... import logger


async def process_message(message_body):
    message_body = json.loads(message_body)
    logger.info(f"Processing message: {message_body}")

    if message_body.get("status"):
        sample_status = {"status": message_body["status"], "note": ""}
        await create_new_kit_status_via_barcode_v3(
            message_body["kit_id"], sample_status
        )
        if message_body["status"] == "COMPLETED":
            await update_kit_actual_release_date_v3(message_body=message_body)
        await check_and_update_booking_status_v3(barcode=message_body["kit_id"])

    if message_body.get("cccd") and message_body.get("default_pdf_link"):
        logger.info(
            f"[CCCD] Update default_pdf_link of barcode: {message_body['barcode']}"
        )
        await update_kit_default_link_v3(message_body)

    if message_body.get("default_pdf_link"):
        logger.info(f"Update default_pdf_link of barcode: {message_body['barcode']}")
        await update_kit_default_link_v3(message_body)

    # if message_body.get('pipeline_qc_report'):
    #     logger.info(f"Update pipeline_qc_report of chip: {message_body['chip_id']}")
    #     await update_chip_id_w_pipeline_qc_report(message_body)

    if message_body.get("plate_status"):
        logger.info(
            f"Update status of plate: {message_body['plate_name']} using {message_body['technology']}"
        )
        await update_plate_name_w_status(message_body)

    if message_body.get("pipeline_qc_status"):
        logger.info(
            f"Update QC_STATUS of samples in BATCH_BARCODE {message_body['batch_barcode']} - TECHNOLOGY {message_body['technology']}"
        )
        await update_sample_mappings_qc_status(message_body)

    if message_body.get("is_card_generated") == "true":
        logger.info("Update S3_OBJECT_KEY of cards!")
        await update_card_s3_object_key_w_presigned_url(message_body)

    if message_body.get("is_card_qr_code_generator") == "true":
        logger.info("Generate QR_CODE for card!")
        await generate_qr_code_for_card_w_lang(message_body)

    if message_body.get("action") == "UPDATE_BOOKING_STATUS":
        logger.info("Update booking status")
        barcode = message_body.get("barcode")
        await check_and_update_booking_status_v3(barcode=barcode)

    if message_body.get("action") == "CREATE_PARTNER_BOOKING":
        logger.info("Create partner booking")
        payload = json.loads(message_body.get("body"))
        try:
            req = PartnerBookingCreationReq(
                partner_name=payload.get("partner_name"),
                customer_name=payload.get("customer_name"),
                customer_phone=payload.get("customer_phone"),
                referral_code=payload.get("referral_code"),
                note=payload.get("note"),
                products=payload.get("products"),
                consulted=False,
            )
            await create_partner_booking_srv_v3(req=req)
        except Exception as e:
            logger.error(f"Exception: {str(e)}")
            logger.error(f"Failed to process message: {message_body}")

    if message_body.get("version") == "v1.0":
        if message_body.get("status"):
            kit_status = {"status": message_body["status"], "note": ""}
            await create_new_kit_status(message_body["kit_id"], kit_status)
            if message_body["status"] == "COMPLETED":
                await update_kit_actual_release_date(message_body=message_body)
            await check_and_update_booking_status(barcode=message_body["kit_id"])

        if message_body.get("default_pdf_link"):
            logger.info(
                f"Update default_pdf_link of barcode: {message_body['barcode']}"
            )
            await update_kit_default_link(message_body)

        if message_body.get("action") == "UPDATE_BOOKING_STATUS":
            logger.info("Update booking status")
            barcode = message_body.get("barcode")
            await check_and_update_booking_status(barcode=barcode)

        if message_body.get("action") == "CREATE_PARTNER_BOOKING":
            logger.info("Create partner booking")
            payload = json.loads(message_body.get("body"))
            try:
                req = PartnerBookingCreationReq(
                    partner_name=payload.get("partner_name"),
                    customer_name=payload.get("customer_name"),
                    customer_phone=payload.get("customer_phone"),
                    referral_code=payload.get("referral_code"),
                    note=payload.get("note"),
                    products=payload.get("products"),
                    consulted=False,
                )
                await create_partner_booking_srv(req=req)
            except Exception as e:
                logger.error(f"Exception: {str(e)}")
                logger.error(f"Failed to process message: {message_body}")
