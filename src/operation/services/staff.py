from ..cruds.staff import *


async def check_default_sale_pic_exist():
    data = {
        "name": "DEFAULT"
    }

    data, err = await create_staff(data=data)
    if err:
        data = await get_existed_default_sale_pic()
        return data[0], None
    return data, err

def parse_monthly_sale_pic_stats(data):
    """
    input:
        account_name
        account_id
        sale_pic_name
        sale_pic_id
        total_in_month
        month
        year
        created_at
    
    output:
        account_name
        sale_pic_name
        total_in_months
        year
        created_at
    """

    results = dict()
    for row in data:
        curr_id = (row['account_id'], row['sale_pic_id'])
        if  curr_id not in results.keys():
            join_dates = set()
            join_dates.add(row['created_at'])
            results[curr_id] = {
                    "id": row['account_id'],
                    "account_name": row['account_name'],
                    "account_id": row['account_id'],
                    "sale_pic_name": row['sale_pic_name'],
                    "sale_pic_id": row['sale_pic_id'],
                    "total_in_months": calculate_total_kits_registered_monthy_via_joint_sale_account_history(total=row['total_in_month'], month=row['month'], is_init=True),
                    "year": int(row['year']),
                    "join_dates": join_dates
                }
        else:
            results[curr_id]['total_in_months'] = calculate_total_kits_registered_monthy_via_joint_sale_account_history(total=row['total_in_month'], month=row['month'], is_init=False, curr_totals=results[curr_id]['total_in_months'])
            results[curr_id]['join_dates'].add(row['created_at'])
    
    data = []
    for k,v in results.items():
        data.append(v)

    return data, len(data)


def calculate_total_kits_registered_monthy_via_joint_sale_account_history(total, month, is_init=False, curr_totals= []):
    if is_init:
        result = [0 for idx in range(12)]
    else:
        result = curr_totals

    result[int(month)-1] += total
    return result

