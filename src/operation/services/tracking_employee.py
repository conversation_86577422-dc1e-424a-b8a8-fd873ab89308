from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking_employee import *
from ..schemas.tracking_employee import *
from ..utils.utils import failure_response


async def get_all_employees_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "te.created_at",
    hoTenNhanVien: Optional[str] = None,
    tenDonVi: Optional[str] = None,
    soDinhDanh: Optional[str] = None,
    chucVu: Optional[str] = None,
    gs_area: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_employees(
        offset=offset,
        size=size,
        order_by=order_by,
        hoTenNhanVien=hoTenNhanVien,
        tenDonVi=tenDonVi,
        soDinhDanh=soDinhDanh,
        chucVu=chucVu,
        gs_area=gs_area,
        include_deleted=include_deleted,
    )


async def get_employee_by_id_service(id: str):
    data = await get_tracking_employee_by_id(id)
    return data.to_dict()


async def create_employee_service(data: dict):
    """
    hoTenNhanVien: str
    hocVi: str
    soDinhDanh: str
    chucVu: str
    unit_id: int
    """
    return await create_tracking_employee(data)


async def update_employee_service(id: str, data: dict):
    """
    type: str
    """
    employee = await get_tracking_employee_by_id(id)
    if not employee:
        err = f"Employee with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_employee(employee, data)
    data = await get_tracking_employee_by_id(id)
    return data.to_dict()


async def delete_employee_by_id_service(id: str):
    """
    id: str
    """
    employee = await get_tracking_employee_by_id(id)

    await delete_tracking_employee(employee=employee)

    data = await get_tracking_employee_by_id(id)
    return data.to_dict()
