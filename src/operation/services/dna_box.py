from datetime import datetime, <PERSON><PERSON><PERSON>
from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_400_BAD_REQUEST,
    HTTP_423_LOCKED,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from ..utils.utils import *

from ..cruds.dna_box import *

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, DEFAULT_DATE_STR, DEFAULT_YEAR
)

def get_dna_box_capacity(technology):
    return config["DNA_BOX"]["CAPACITY"][technology.upper()]

def get_dna_box_tech_prefix(technology):
    return config["DNA_BOX"]["DNA_BOX_PREFIX"][technology.upper()]

def get_dna_box_supported_tech():
    return config["DNA_BOX"]["DNA_BOX_PREFIX"].keys()
     

def get_next_dna_box_id(prefix,total):
    if prefix and total >= 0:
        return prefix+str(total+1), None
    else:
        err = "Missing prefix or curr_total dna boxes"
        return None, err


async def fill_dna_box_w_lids(lids: list):
    
    pass