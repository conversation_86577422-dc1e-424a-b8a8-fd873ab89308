from datetime import datetime, <PERSON><PERSON><PERSON>
from email import message
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_400_BAD_REQUEST,
    HTTP_423_LOCKED,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from ..utils.utils import (
    failure_response,
    get_current_date_time,
    convert_str_to_iso_datetime,
    convert_datetime_to_iso,
    failure_response_kit_registration,
    success_response,
    format_date,
    get_product_by_product_code,
    get_step_function_client,
    list_execution_function,
    renew_obj_key,
    get_pdf_report_qr_dynamo_id
)

from ..cruds.cards import *

from ..cruds.kit import (
    get_kit_list_detail
)

from ..cruds.sample_management import (
    get_filtered_sample_mappings,
    get_filtered_sample_mappings_v3
)

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, 
    DEFAULT_DATE_STR, 
    DEFAULT_YEAR,
    convert_rowproxy_to_dict, 
    convert_vnese_full_name_to_print_text,
    send_message_to_sqs_queue,
    get_report_pdf_link,
    parse_pdf_report_link,
    renew_obj_key_w_region,
    get_obj_key_from_s3_uri
)
from ..config import config
from ..producer import send_msg_to_queue
from .. import logger

import json

def get_card_printing_link(front_s3_object_key, back_s3_object_key):
    CARD_OUTPUT_BUCKET = config['CARD_MANAGEMENT']['CARD_OUTPUT_BUCKET']
    REGION_NAME = config['CARD_MANAGEMENT']['CARD_BUCKET_REGION']
    card_font_presigned_link = renew_obj_key_w_region(CARD_OUTPUT_BUCKET, front_s3_object_key, REGION_NAME)
    card_back_presigned_link = renew_obj_key_w_region(CARD_OUTPUT_BUCKET, back_s3_object_key, REGION_NAME)
    return card_font_presigned_link, card_back_presigned_link

async def update_card_s3_object_key(message_body):

    cards = message_body.get('samples')
    results = []

    for card in cards:
        id = card.get('card_id')
        s3_object_key=card.get('s3_object_key')
        if not id:
            raise ValueError(f"CARD with ID {id} is not exsisted")
        if not s3_object_key:
            raise ValueError(f"CARD with ID {id} does not have s3_object_key {s3_object_key}")

        update_data = CardUpdateReq(s3_object_key=s3_object_key,card_status='READY')
        res = await update_card_ctl(id=id,update_data=update_data)
        results.append(res)

    return results

async def update_card_s3_object_key_w_presigned_url(message_body):

    cards = message_body.get('samples')

    for card in cards:
        id = card.get('card_id')
        s3_object_key=card.get('s3_object_key')
        if not id:
            raise ValueError(f"CARD with ID {id} is not exsisted")
        if not s3_object_key:
            raise ValueError(f"CARD with ID {id} does not have s3_object_key {s3_object_key}")

        # update_data = CardUpdateReq(s3_object_key=s3_object_key,card_status='READY')
        # res = await update_card_ctl(id=id,update_data=update_data)

        card_front_s3_object_uri = s3_object_key+'_front.svg'
        card_back_s3_object_uri = s3_object_key+'_back.svg'
        _ = await get_new_card_presigned_links(id, s3_object_key, card_front_s3_object_uri, card_back_s3_object_uri)

async def get_new_card_presigned_links(card_id, s3_object_key,card_front_s3_object_uri, card_back_s3_object_uri):
        front_s3_object_key = get_obj_key_from_s3_uri(card_front_s3_object_uri)
        back_s3_object_key = get_obj_key_from_s3_uri(card_back_s3_object_uri)
        card_font_presigned_link, card_back_presigned_link = get_card_printing_link(
            front_s3_object_key = front_s3_object_key,
            back_s3_object_key = back_s3_object_key
        )
        # RE-ISSUE
        entry = {
            "card_id": card_id,
            "card_font_presigned_link": card_font_presigned_link,
            "card_back_presigned_link": card_back_presigned_link
        }
        update_data = CardUpdateReq(
                s3_object_key=s3_object_key, 
                card_status='READY', 
                presigned_s3_font_url=card_font_presigned_link,
                presigned_s3_back_url=card_back_presigned_link
            )
        _ = await update_card_ctl(id=card_id,update_data=update_data)
        return entry

async def renew_card_presigned_links(card_id,card_front_s3_object_uri, card_back_s3_object_uri):
        front_s3_object_key = get_obj_key_from_s3_uri(card_front_s3_object_uri)
        back_s3_object_key = get_obj_key_from_s3_uri(card_back_s3_object_uri)
        card_font_presigned_link, card_back_presigned_link = get_card_printing_link(
            front_s3_object_key = front_s3_object_key,
            back_s3_object_key = back_s3_object_key
        )
        # RE-ISSUE
        entry = {
            "card_id": card_id,
            "card_font_presigned_link": card_font_presigned_link,
            "card_back_presigned_link": card_back_presigned_link
        }
        update_data = CardUpdateReq(presigned_s3_font_url=card_font_presigned_link,presigned_s3_back_url=card_back_presigned_link, card_status='READY')
        _ = await update_card_ctl(id=card_id,update_data=update_data)
        return entry
        

def grant_permission_qr_code_to_phone_number(user_id, report_id):
    message_body = {
        "action": config['AUTHZ']['CREATE_POLICY_ACTION'],
        "body": {
            "user_id": user_id,
            "service_id": config['CARD_MANAGEMENT']['AUTHZ_RESOURCE'],
            "action_list": [
                {
                    "method": "GET",
                    "path": f"report/id/{report_id}"
                },
            ]
        }
    }
    send_msg_to_queue(config['AUTHZ']['QUEUE_NAME'], message_body)
    
    # SEND MESSAGE TO REPORT GENERATOR


async def generate_qr_code_for_card(message_body):

    card = message_body.get('card')

    # GENERATE PDF PGX REPORT LINK
    pdf_link = await get_report_pdf_link(
        barcode=card.get('barcode'),
        technology='pgx'
        )
    bucket, file_name = parse_pdf_report_link(pdf_link)
    # QR CODE
    report_id = await get_pdf_report_qr_dynamo_id(bucket, file_name, message_body.get('bearer_token'))
    phone_number = card.get('phone_number')
    user_id = card.get('user_id')
    # GRANT PERMISSION
    grant_permission_qr_code_to_phone_number(user_id, report_id)
    
    qr_url = config['CARD_MANAGEMENT']['DOMAIN']+report_id+f"?p={phone_number}&r=%2B84"
    update_data = CardUpdateReq(
        card_status="PENDING",
        qr_url=qr_url)
    _ = await update_card_ctl(id=card.get('id'),update_data=update_data)
    logger.info(f"card_id: {card.get('id')} - bucket {bucket} file_name {file_name} report_id {report_id} user_id {user_id}")


async def generate_qr_code_for_card_w_lang(message_body):

    card = message_body.get('card')

    # GENERATE PDF PGX REPORT LINK
    pdf_link = await get_report_pdf_link(
        barcode=card.get('barcode'),
        technology='pgx'
        )
    bucket, file_name = parse_pdf_report_link(pdf_link)
    # QR CODE
    report_id = await get_pdf_report_qr_dynamo_id(bucket, file_name, message_body.get('bearer_token'))
    phone_number = card.get('phone_number')
    user_id = card.get('user_id')
    # GRANT PERMISSION
    grant_permission_qr_code_to_phone_number(user_id, report_id)
    
    qr_url = config['CARD_MANAGEMENT']['DOMAIN']
    if card.get('lang').upper() != 'VN':
        qr_url +=  card.get('lang') + '/' + report_id+f"?p={phone_number}&r=%2B84"
        pass
    else:
        qr_url += report_id+f"?p={phone_number}&r=%2B84"
        pass


    update_data = CardUpdateReq(
        card_status="PENDING",
        qr_url=qr_url)
    _ = await update_card_ctl(id=card.get('id'),update_data=update_data)
    logger.info(f"card_id: {card.get('id')} - bucket {bucket} file_name {file_name} report_id {report_id} user_id {user_id}")


async def generate_cards_ctl(generating_reqs: list, card_product_id:str, card_product_name: str):
    message_body = dict()
    message_body['is_card_generator']="true"
    message_body['samples']=generating_reqs
    message_body['card_product_id']=card_product_id
    message_body['card_product_name']=card_product_name
    queue_name = config['CARD_MANAGEMENT']['CARD_GENERATOR_SQS_QUEUE']
    # response = send_message_to_sqs_queue(message_body,queue_name)
    # message_body = json.dumps(message_body)
    response = send_msg_to_queue(queue_name, message_body)
    return response

async def generate_qr_code_for_card_ctl(card, bearer_token_credential):

    message_body = dict()
    message_body['is_card_qr_code_generator']="true"
    message_body['card']=card
    message_body['bearer_token']=bearer_token_credential
    queue_name = config['CARD_MANAGEMENT']['CARD_QR_CODE_GENERATOR_SQS_QUEUE']
    # message_body = json.dumps(message_body)
    response = send_msg_to_queue(queue_name, message_body)
    return response


async def check_card_metadata_before_generating(card_id: str):
    valid_res = dict()
    generating_req= dict()
    data, total = await get_filtered_card_list(card_id=card_id)
    if total == 0:
        valid_res['result'] = False
        valid_res['msg'] = f"card_id: {card_id} - not EXISTED !"
        return valid_res, None
    elif total == 1:
        res = convert_rowproxy_to_dict(data[0])
        if res['card_status'] == 'LOCKED':
            valid_res['result'] = False
            valid_res['msg'] = f"card_id: {card_id} - is LOCKED !"
            return valid_res, None
        
        generating_req['card_id']=res['id']
        generating_req['lang']=res['lang']
        generating_req['barcode']=res['barcode']
        generating_req['link']=res['qr_url']
        generating_req["name"] = res['full_name']

        kit_data, kit_total = await get_filtered_sample_mappings_v3(
            barcode=generating_req['barcode'],
            plate_status_filter=config['AVAILABLE_CHIP_STATUS']+config['EXPORT_PLATE_STATUS'],
            is_added_to_batch=True
            )

        # print("kit_data: ",kit_data[0])
        if kit_total == 0:
            valid_res['result'] = False
            valid_res['msg'] = f"kit {generating_req['barcode']} - not existed !"
            return valid_res, None
            
        elif kit_total == 1:
            kit_res = kit_data[0] # convert_rowproxy_to_dict(kit_data[0])
            generating_req["sample"] = convert_vnese_full_name_to_print_text(generating_req["name"])
            generating_req["gender"] = 'm' if kit_res['gender'] == 'male' else 'f'
            generating_req["info"] = '1'
            generating_req["batch"] = kit_res['batch_barcode']
            generating_req["chip_id"] = kit_res['chip_id']
            valid_res['result'] = True
            return valid_res, generating_req
            
        else:
            valid_res['result'] = False
            valid_res['msg'] = f"kit {generating_req['barcode']} - duplicated !"
            return valid_res, None
            

    else:
        valid_res['result'] = False
        valid_res['msg'] = f"card_id: {card_id} - duplicated !"
        return valid_res, None
    

async def get_card_product_infor(card_product_id: str):
    data, total = await get_filtered_card_product_list(card_product_id=card_product_id,is_active_only=True)
    if total == 0:
        card_product = dict()
        card_product['id']=card_product_id
        return card_product
    else:
        card_product = convert_rowproxy_to_dict(data[0])
        return card_product

async def check_card_metadata(barcode: str, product_code_o_kit: str, card_product: dict):
    """_summary_

    Args:
        barcode (str): 
        product_code (str): 
        card_product_id (str): 

    Returns:
        _type_: 
    """
    valid_res = dict()

    if product_code_o_kit == card_product.get('product_code'):
        valid_res['result'] = True

    else:
        valid_res['result'] = False
        valid_res['msg'] = f" product_code {card_product.get('product_code')} of Card Product does not match product_code {product_code_o_kit} on kit {barcode}!"


    return valid_res