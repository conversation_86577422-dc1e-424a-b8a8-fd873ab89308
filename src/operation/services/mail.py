import requests

from src.operation.config import config

from .. import logger


def create_email(
    receivers,
    template_id,
    from_id=1,
    template_name_as_subject=True,
    variables=[],
    subject=None,
):
    data = {
        "to": receivers,
        "from_id": from_id,
        "template_name_as_subject": template_name_as_subject,
        "template_id": template_id,
    }
    headers = {"Content-type": "application/json"}
    if subject:
        data["subject"] = subject
    if variables:
        data["variables"] = variables
    url = config["MAIL"]["CONNECTION"]["INTERNAL_ADDRESS"] + "/mail"
    try:
        res = requests.post(url, json=data, headers=headers)
    except Exception as err:
        logger.exception(err)
        return None, err
    if res.status_code != 200:
        if res.json().get("error") and res.json()["error"]["message"]:
            err = (
                f"Create email failed, error message: {res.json()['error']['message']}"
            )
            logger.error(err)
            return None, err

    return res.json()["mail_id"], None


def send_email_with_mail_id(mail_id):
    data = {"MailID": mail_id}
    headers = {"Content-type": "application/json"}
    url = config["MAIL"]["CONNECTION"]["INTERNAL_ADDRESS"] + "/mail/send"
    try:
        res = requests.post(url, json=data, headers=headers)
    except Exception as err:
        logger.exception(err)
        return err
    if res.status_code != 200:
        if res.json().get("error") and res.json()["error"]["message"]:
            err = f"Send mail with ID {mail_id} failed! Error messages: {res.json()['error']['message']}"
            logger.error(err)
            return err
    return None


def send_email(
    receivers,
    template_id,
    from_id=1,
    template_name_as_subject=True,
    variables=[],
    subject=None,
):
    logger.info(f"Sending email to {receivers}")
    mail_id, err = create_email(
        receivers,
        template_id,
        from_id=from_id,
        template_name_as_subject=template_name_as_subject,
        variables=variables,
        subject=subject,
    )
    if err:
        return err
    err = send_email_with_mail_id(mail_id)
    if err:
        return err
