from datetime import datetime, <PERSON><PERSON><PERSON>
from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_400_BAD_REQUEST,
    HTTP_423_LOCKED,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from ..utils.utils import (
    failure_response,
    get_current_date_time,
    convert_str_to_iso_datetime,
    convert_datetime_to_iso,
    failure_response_kit_registration,
    success_response,
    format_date,
    get_product_by_product_code,
)

from ..cruds.plate import *

from ..cruds.batch import *

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, DEFAULT_DATE_STR, DEFAULT_YEAR
)
from ..config import config


async def get_next_plate_name_w_type(type: str):
    logger.info(f"Get next PLATE NAME w/ technology {type}")
    max_plate_name = await get_max_plate_name(type.upper())
    logger.info(f"NEXT PLATE NAME: {max_plate_name} w/ technology {type}")
    if max_plate_name:
        next_name = int(max_plate_name)
        return str(next_name+1), None
    else:
        return "1", None
    # return None, err

async def update_plate_name_w_status(message_body):
    updated_plate_info = {
        "status": message_body['plate_status']
    }
    try:
        plate = await get_plate_by_name(message_body['technology'],message_body['plate_name'])
        err = await update_plate(plate,updated_plate_info)
        if err:
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
    except AttributeError as error:
        msg = f"PLATE WITH name: {message_body['plate_name']} using {message_body['technology']} NOT FOUND"
        logger.info(msg)
        raise error

async def check_plate_existed_to_create_batch(type: str, plate_name: str):
    is_plate_existed = await existed_plate_name(type, plate_name)
    return is_plate_existed

async def check_plate_passed_to_run(type: str, plate_name: str):
    curr_plate = await get_plate(type, plate_name)
    if curr_plate.status in config['AVAILABLE_CHIP_STATUS']:
        return False, curr_plate.status 
    return True, curr_plate.status 
