import boto3
import os
from typing import Optional
from ..config import config
from .. import logger
from ..cruds.sample_management import (
    get_filtered_sample_mappings,
    get_filtered_sample_mappings_v3,
    get_sample_on_chip,
    bulk_add_chip_to_batch,
    get_filtered_batch_stats
)

from ..cruds.chip import get_chip_by_chip_id

from ..schemas.sample_management import AddChipToBatchReq

from ..utils.utils import (
    format_date,
    DEFAULT_DATE_NO_TIME_ZONE,
    get_product_by_product_code,
    convert_rowproxy_to_dict
)

from fastapi import HTTPException, status

async def get_available_sample_mappings(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    order_option: Optional[str]=None,
    barcode: Optional[int]=None,
    lid: Optional[int]=None,
    dna_extraction_id: Optional[int]=None,
    chip_id: Optional[str]=None,
    plate_name: Optional[str]=None,
    plate_name_null_only: Optional[bool]=False,
    dna_box: Optional[str]=None,
    technology: Optional[str]=None,
    note: Optional[str]=None,
    dna_extraction_date_start: Optional[str]=None,
    dna_extraction_date_end: Optional[str]=None,
    agarose_gel: Optional[str]=None,
    dna_qc_status: Optional[str]=None,
    pipeline_qc_status: Optional[str]=None,
    wetlab_date_start: Optional[str]=None,
    wetlab_date_end: Optional[str]=None,
    drylab_date_start: Optional[str]=None,
    drylab_date_end: Optional[str]=None,
    plate_added_date_start: Optional[str]=None,
    plate_added_date_end: Optional[str]=None,
    is_added_to_batch: Optional[bool]=False,
    export: Optional[bool]=False
):
    if dna_extraction_date_start:
        temp_dna_extraction_date_start_std = format_date(dna_extraction_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if dna_extraction_date_end:
        temp_dna_extraction_date_end_std = format_date(dna_extraction_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if temp_dna_extraction_date_end_std < temp_dna_extraction_date_start_std:
            raise ValueError("end date cannot be after start date")
    if wetlab_date_start:
        temp_start_date_std = format_date(wetlab_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if wetlab_date_end:
        temp_end_date_std = format_date(wetlab_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if temp_end_date_std < temp_start_date_std:
            raise ValueError("end date cannot be after start date")
    if drylab_date_start:
        start_date_std = format_date(drylab_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if drylab_date_end:
        end_date_std = format_date(drylab_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")
    if plate_added_date_start:
        start_plate_added_date_std = format_date(plate_added_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if plate_added_date_end:
        end_plate_added_date_std = format_date(plate_added_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_plate_added_date_std < start_plate_added_date_std:
            raise ValueError("end plate added date cannot be after start date")
    results, total = await get_filtered_sample_mappings(
        offset=offset,
        size=size,
        order_by=order_by,
        order_option=order_option,
        barcode=barcode,
        lid=lid,
        dna_extraction_id=dna_extraction_id,
        chip_id=chip_id,
        plate_name=plate_name,
        plate_name_null_only=plate_name_null_only,
        dna_box=dna_box,
        technology=technology,
        note=note,
        plate_status_filter=config['AVAILABLE_CHIP_STATUS']+config['EXPORT_PLATE_STATUS'] if export else config['AVAILABLE_CHIP_STATUS'],
        dna_extraction_date_start=temp_dna_extraction_date_start_std if dna_extraction_date_start else None,
        dna_extraction_date_end=temp_dna_extraction_date_end_std if dna_extraction_date_end else None,
        agarose_gel=agarose_gel,
        dna_qc_status=dna_qc_status,
        pipeline_qc_status=pipeline_qc_status,
        wetlab_date_start=temp_start_date_std if wetlab_date_start else None,
        wetlab_date_end=temp_end_date_std if wetlab_date_end else None,
        drylab_date_start=start_date_std if drylab_date_start else None,
        drylab_date_end=end_date_std if drylab_date_end else None,
        plate_added_date_start=start_plate_added_date_std if plate_added_date_start else None,
        plate_added_date_end=end_plate_added_date_std if plate_added_date_end else None,
        is_added_to_batch=is_added_to_batch
    )
    return results, total

async def get_sample_mapping(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    barcode: Optional[int]=None,
    dna_extraction_id: Optional[int]=None,
    chip_id: Optional[str]=None,
    plate_name: Optional[str]=None,
    technology: Optional[str]=None,
    batch_barcode: Optional[str]=None,
):
    results, total = await get_filtered_sample_mappings(
        offset=offset,
        size=size,
        order_by=order_by,
        dna_extraction_id=dna_extraction_id,
        barcode=barcode,
        chip_id=chip_id,
        plate_name=plate_name,
        technology=technology,
        batch_barcode=batch_barcode,
    )
    return results, total

async def check_raw_data_microarray_chip(chip_id: str):
    #list all files in
    session = boto3.Session(
        aws_access_key_id=config['AWS']['AWS_ACCESS_KEY_ID'],
        aws_secret_access_key=config['AWS']['AWS_SECRET_ACCESS_KEY']
    )
    s3_rersource = session.resource("s3")
    raw_data_bucket = s3_rersource.Bucket(config['RAW_DATA_BUCKET']['MICROARRAY'])
    raw_file_set = set()
    for object in raw_data_bucket.objects.filter(Prefix=f"{chip_id}/"):
        if object.key.endswith(config['RAW_DATA_FORMAT']['MICROARRAY']):
            raw_file_set.add(os.path.basename(object.key))
            
    samples_on_chip = await get_sample_on_chip(chip_id=chip_id)
    
    missing_files = []
    
    for sample in samples_on_chip:
        position = sample['position']
        grn_idat_name = f"{chip_id}_{position}_Grn.idat"
        red_idat_name = f"{chip_id}_{position}_Red.idat"
        if grn_idat_name not in samples_on_chip:
            missing_files.append(grn_idat_name)
        if red_idat_name not in samples_on_chip:
            missing_files.append(red_idat_name)
    
    return missing_files

async def add_chips_to_batch(add_chip_to_batch_req: AddChipToBatchReq):
    for chip_id in add_chip_to_batch_req.chip_id_list:
        chip = await get_chip_by_chip_id(chip_id)
        if chip is None:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=f"This chip_id {chip_id} was already processed")
        if chip.status in ['CREATED']:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=f"Samples not added to this chip_id {chip_id}")
        if chip.status in ['COMPLETED', 'RUNNING']:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=f"This chip_id {chip_id} was already processed")
    res = await bulk_add_chip_to_batch(add_chip_to_batch_req)
    return res

async def get_batch_stats(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    order_option: Optional[str]=None,
    batch_number: Optional[str]=None,
    product_name: Optional[str]=None,
    product_code: Optional[str]=None,
    wetlab_date_start: Optional[str]=None,
    wetlab_date_end: Optional[str]=None,
):
    
    if wetlab_date_start:
        temp_start_date_std = format_date(wetlab_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if wetlab_date_end:
        temp_end_date_std = format_date(wetlab_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
    if wetlab_date_start and wetlab_date_end:
        if temp_end_date_std < temp_start_date_std:
            raise ValueError("end date cannot be after start date")
    results, total = await get_filtered_batch_stats(offset,size,order_by, order_option, batch_number, product_code, wetlab_date_start, wetlab_date_end)
    results_to_return=[]
    for result in results:
        result_as_dict=convert_rowproxy_to_dict(result)
        result_as_dict["product_name"]=get_product_by_product_code(result_as_dict["product_code"])["name"]
        results_to_return.append(result_as_dict)
    if product_name:
        results_to_return=[result for result in results_to_return if result["product_name"]==product_name]
    return results_to_return,total


async def get_available_sample_mappings_v3(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    order_option: Optional[str]=None,
    barcode: Optional[int]=None,
    lid: Optional[int]=None,
    dna_extraction_id: Optional[int]=None,
    chip_id: Optional[str]=None,
    plate_name: Optional[str]=None,
    plate_name_null_only: Optional[bool]=False,
    dna_box: Optional[str]=None,
    technology: Optional[str]=None,
    note: Optional[str]=None,
    dna_extraction_date_start: Optional[str]=None,
    dna_extraction_date_end: Optional[str]=None,
    agarose_gel: Optional[str]=None,
    dna_qc_status: Optional[str]=None,
    pipeline_qc_status: Optional[str]=None,
    wetlab_date_start: Optional[str]=None,
    wetlab_date_end: Optional[str]=None,
    drylab_date_start: Optional[str]=None,
    drylab_date_end: Optional[str]=None,
    plate_added_date_start: Optional[str]=None,
    plate_added_date_end: Optional[str]=None,
    is_added_to_batch: Optional[bool]=False,
    is_encoded: Optional[bool]=False,
    export: Optional[bool]=False
):
    if dna_extraction_date_start:
        temp_dna_extraction_date_start_std = format_date(dna_extraction_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if dna_extraction_date_end:
        temp_dna_extraction_date_end_std = format_date(dna_extraction_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if temp_dna_extraction_date_end_std < temp_dna_extraction_date_start_std:
            raise ValueError("end date cannot be after start date")
    if wetlab_date_start:
        temp_start_date_std = format_date(wetlab_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if wetlab_date_end:
        temp_end_date_std = format_date(wetlab_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if temp_end_date_std < temp_start_date_std:
            raise ValueError("end date cannot be after start date")
    if drylab_date_start:
        start_date_std = format_date(drylab_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if drylab_date_end:
        end_date_std = format_date(drylab_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")
    if plate_added_date_start:
        start_plate_added_date_std = format_date(plate_added_date_start, DEFAULT_DATE_NO_TIME_ZONE)
    if plate_added_date_end:
        end_plate_added_date_std = format_date(plate_added_date_end, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_plate_added_date_std < start_plate_added_date_std:
            raise ValueError("end plate added date cannot be after start date")
    results, total = await get_filtered_sample_mappings_v3(
        offset=offset,
        size=size,
        order_by=order_by,
        order_option=order_option,
        barcode=barcode,
        lid=lid,
        dna_extraction_id=dna_extraction_id,
        chip_id=chip_id,
        plate_name=plate_name,
        plate_name_null_only=plate_name_null_only,
        dna_box=dna_box,
        technology=technology,
        note=note,
        plate_status_filter=config['AVAILABLE_CHIP_STATUS']+config['EXPORT_PLATE_STATUS'] if export else config['AVAILABLE_CHIP_STATUS'],
        dna_extraction_date_start=temp_dna_extraction_date_start_std if dna_extraction_date_start else None,
        dna_extraction_date_end=temp_dna_extraction_date_end_std if dna_extraction_date_end else None,
        agarose_gel=agarose_gel,
        dna_qc_status=dna_qc_status,
        pipeline_qc_status=pipeline_qc_status,
        wetlab_date_start=temp_start_date_std if wetlab_date_start else None,
        wetlab_date_end=temp_end_date_std if wetlab_date_end else None,
        drylab_date_start=start_date_std if drylab_date_start else None,
        drylab_date_end=end_date_std if drylab_date_end else None,
        plate_added_date_start=start_plate_added_date_std if plate_added_date_start else None,
        plate_added_date_end=end_plate_added_date_std if plate_added_date_end else None,
        is_added_to_batch=is_added_to_batch,
        is_encoded=is_encoded
    )
    return results, total

