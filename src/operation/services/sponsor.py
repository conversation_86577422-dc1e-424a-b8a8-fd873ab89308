import os
import uuid
from typing import Optional, <PERSON><PERSON>

import boto3
from botocore.exceptions import ClientError
from fastapi import HTTPException, UploadFile, status

from src.operation import logger
from src.operation.config import config
from src.operation.cruds.sponsor import (
    create_gs_area,
    create_sponsor,
    create_sponsor_campaign,
    create_sponsor_contract,
    delete_gs_area,
    delete_sponsor,
    delete_sponsor_campaign,
    delete_sponsor_contract,
    get_all_gs_areas,
    get_all_sponsor_campaigns,
    get_all_sponsor_contracts,
    get_all_sponsors,
    get_gs_area,
    get_sponsor,
    get_sponsor_details,
    get_sponsor_campaign,
    get_sponsor_contract,
    get_sponsor_contract_details,
    update_gs_area,
    update_sponsor,
    update_sponsor_campaign,
    update_sponsor_contract,
)
from src.operation.schemas.sponsor import (
    GsAreaCreate,
    GsAreaUpdate,
    SponsorCampaignCreate,
    SponsorCampaignUpdate,
    SponsorContractCreate,
    SponsorContractUpdate,
    SponsorCreate,
    SponsorUpdate,
)
from src.operation.utils.file_utils import read_and_validate_excel_schema


# --- Sponsor Service ---


class SponsorService:
    @staticmethod
    async def get_sponsor(sponsor_id: uuid.UUID, details: bool = False):
        """Get a sponsor by ID"""
        sponsor = await get_sponsor(sponsor_id)
        if not sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor with id {sponsor_id} not found",
            )
        if details:
            return sponsor.to_dict()
        else:
            sponsor_details = await get_sponsor_details(sponsor_id)
            return sponsor_details

    @staticmethod
    async def get_all_sponsors(
        page_number: Optional[int] = None,
        page_size: Optional[int] = None,
        name: Optional[str] = None,
        code: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        order_by: Optional[str] = "created_at",
        order_option: Optional[str] = "desc",
    ):
        """Get all sponsors with optional filtering"""
        sponsors, total = await get_all_sponsors(
            page_number=page_number,
            page_size=page_size,
            name=name,
            code=code,
            start_date=start_date,
            end_date=end_date,
            order_by=order_by,
            order_option=order_option,
        )
        sponsors = [sponsor.to_dict() for sponsor in sponsors]
        return sponsors, total

    @staticmethod
    async def create_sponsor(data: SponsorCreate):
        """Create a new sponsor"""
        result, error = await create_sponsor(data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)
        return result

    @staticmethod
    async def update_sponsor(sponsor_id: uuid.UUID, data: SponsorUpdate):
        """Update an existing sponsor"""
        sponsor = await get_sponsor(sponsor_id)
        if not sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor with id {sponsor_id} not found",
            )

        error = await update_sponsor(sponsor, data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        # Get updated sponsor
        updated_sponsor = await get_sponsor(sponsor_id)
        return updated_sponsor.to_dict()

    @staticmethod
    async def delete_sponsor(sponsor_id: uuid.UUID):
        """Delete a sponsor"""
        sponsor = await get_sponsor(sponsor_id)
        if not sponsor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor with id {sponsor_id} not found",
            )

        result, error = await delete_sponsor(sponsor)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        return {"detail": f"Sponsor {sponsor.name} deleted successfully"}

    @staticmethod
    async def import_sponsor(file: UploadFile):
        excel_template = [
            {
                "display_name": "Tên nhà tài trợ",
                "field": "sponsor_name",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Mã",
                "field": "code",
                "required": True,
                "type": str,
                "unique": True
            },
            {
                "display_name": "Mô tả",
                "field": "description",
                "required": True,
                "type": str,
            }
        ]


        file_bytes = await file.read()

        list_data = read_and_validate_excel_schema(file_bytes=file_bytes, template_schema=excel_template)

        sponsors, _ = await SponsorService.get_all_sponsors()

        sponsor_code_mapping = {sponsor["code"]: sponsor for sponsor in sponsors}

        for row in list_data:
            if row["code"] in sponsor_code_mapping:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"Duplicate code {row['code']}")

        for index, row in enumerate(list_data):
            sponsor_data = {
                "name": row["sponsor_name"],
                "code": row["code"],
                "description": row["description"]
            }
            sponsor_create = SponsorCreate(**sponsor_data)
            result = await SponsorService.create_sponsor(sponsor_create)

        return {"detail": f"Sponsor import successfully"}


# --- GsArea Service ---


class GsAreaService:
    @staticmethod
    async def get_gs_area(area_id: int):
        """Get a GS area by ID"""
        area = await get_gs_area(area_id)
        if not area:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"GS Area with id {area_id} not found",
            )
        return area.to_dict()

    @staticmethod
    async def get_all_gs_areas(
        page_number: Optional[int] = None,
        page_size: Optional[int] = None,
        area: Optional[str] = None,
        order_by: Optional[str] = "area",
        order_option: Optional[str] = "asc",
    ):
        """Get all GS areas with optional filtering"""
        areas, total = await get_all_gs_areas(
            page_number=page_number,
            page_size=page_size,
            area=area,
            order_by=order_by,
            order_option=order_option,
        )
        areas = [area.to_dict() for area in areas]
        return areas, total

    @staticmethod
    async def create_gs_area(data: GsAreaCreate):
        """Create a new GS area"""
        result, error = await create_gs_area(data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)
        return result

    @staticmethod
    async def update_gs_area(area_id: int, data: GsAreaUpdate):
        """Update an existing GS area"""
        area = await get_gs_area(area_id)
        if not area:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"GS Area with id {area_id} not found",
            )

        error = await update_gs_area(area, data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        # Get updated area
        updated_area = await get_gs_area(area_id)
        return updated_area.to_dict()

    @staticmethod
    async def delete_gs_area(area_id: int):
        """Delete a GS area"""
        area = await get_gs_area(area_id)
        if not area:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"GS Area with id {area_id} not found",
            )

        result, error = await delete_gs_area(area)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        return {"detail": f"GS Area {area.area} deleted successfully"}


# --- SponsorContract Service ---


class SponsorContractService:
    @staticmethod
    async def upload_file_to_s3(
        file: UploadFile, contract_id: Optional[uuid.UUID] = None
    ) -> Tuple[str, str]:
        """
        Upload a file to S3 and return the key and bucket

        Args:
            file: The file to upload
            contract_id: Optional contract ID to include in the S3 path

        Returns:
            Tuple of (s3_key, s3_bucket)
        """
        s3_bucket = config["SPONSOR_CONTRACT"]["S3_BUCKET_NAME"]
        if not s3_bucket:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="S3 bucket not configured",
            )

        try:
            # Generate a unique filename using UUID to prevent overwriting
            file_extension = os.path.splitext(file.filename)[1] if file.filename else ""

            # If contract_id is provided, use it in the path
            if contract_id:
                s3_key = f"contracts/{contract_id}/{uuid.uuid4()}{file_extension}"
            else:
                s3_key = f"contracts/{uuid.uuid4()}{file_extension}"

            # Initialize S3 client
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
                aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
                region_name=config["AWS"]["AWS_REGION"],
            )

            # Upload file
            file_content = await file.read()
            s3_client.put_object(
                Bucket=s3_bucket,
                Key=s3_key,
                Body=file_content,
                ContentType=file.content_type,
            )

            # Reset file cursor for potential future reads
            await file.seek(0)

            return s3_key, s3_bucket

        except Exception as e:
            logger.error(f"Error uploading file to S3: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload file: {str(e)}",
            )

    @staticmethod
    async def create_sponsor_contract(data: SponsorContractCreate):
        """
        Create a new sponsor contract without file

        Args:
            data: Contract data as Pydantic model

        Returns:
            Created contract details
        """
        contract_data = SponsorContractCreate(
            name=data.name,
            code=data.code,
            signing_date=data.signing_date,
            sponsor_id=data.sponsor_id,
            sponsored_party=data.sponsored_party,
            s3_key=None,
            s3_bucket=None,
            total_quantity=data.total_quantity,
            campaigns=[
                SponsorCampaignCreate(
                    start_date=campaign.start_date,
                    end_date=campaign.end_date,
                    gs_area_code=campaign.gs_area_code,
                    quantity=campaign.quantity,
                    name=campaign.name,
                    description=campaign.description,
                )
                for campaign in data.campaigns
            ],
        )

        try:
            result = await create_sponsor_contract(contract_data)
            return result
        except ValueError as e:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
        except Exception as e:
            # Handle other errors
            logger.error(f"Unexpected error creating sponsor contract: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating sponsor contract: {str(e)}",
            )

    @staticmethod
    async def upload_contract_file(contract_id: uuid.UUID, contract_file: UploadFile):
        """
        Upload a file for an existing sponsor contract

        Args:
            contract_id: ID of the existing contract
            contract_file: Contract file to upload

        Returns:
            Updated contract details
        """
        # Verify the contract exists
        contract = await get_sponsor_contract(contract_id)
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Contract with id {contract_id} not found",
            )

        if contract.deleted_at:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Sponsor Contract with id {contract_id} is deleted",
            )

        # Upload file to S3
        s3_key, s3_bucket = await SponsorContractService.upload_file_to_s3(
            contract_file, contract_id
        )

        # Update contract with file information
        update_data = SponsorContractUpdate(
            s3_key=s3_key,
            s3_bucket=s3_bucket,
        )

        error = await update_sponsor_contract(contract, update_data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        # Get updated contract
        updated_contract = await get_sponsor_contract(contract_id)

        return {
            "detail": "File uploaded successfully",
            "contract_id": str(contract_id),
            "filename": contract_file.filename,
            "contract": updated_contract.to_dict()
            if hasattr(updated_contract, "to_dict")
            else updated_contract,
        }

    @staticmethod
    async def get_sponsor_contract(contract_id: uuid.UUID):
        """Get a sponsor contract by ID"""
        contract = await get_sponsor_contract(contract_id)
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Contract with id {contract_id} not found",
            )
        return contract.to_dict()

    @staticmethod
    async def get_all_sponsor_contracts(
        page_number: Optional[int] = None,
        page_size: Optional[int] = None,
        name: Optional[str] = None,
        code: Optional[str] = None,
        sponsor_id: Optional[uuid.UUID] = None,
        gs_area_code: Optional[int] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        order_by: Optional[str] = "created_at",
        order_option: Optional[str] = "desc",
    ):
        """Get all sponsor contracts with optional filtering"""
        contracts, total = await get_all_sponsor_contracts(
            page_number=page_number,
            page_size=page_size,
            name=name,
            code=code,
            sponsor_id=sponsor_id,
            gs_area_code=gs_area_code,
            start_date=start_date,
            end_date=end_date,
            order_by=order_by,
            order_option=order_option,
        )

        # Transform results to include proper column names
        result_data = []
        for contract_row in contracts:
            contract_dict = dict(contract_row)
            sponsor_name = contract_dict.pop("sponsor_name", None)

            # Add sponsor name to the contract dictionary
            if sponsor_name:
                contract_dict["sponsor_name"] = sponsor_name

            result_data.append(contract_dict)

        return result_data, total

    @staticmethod
    async def get_sponsor_contract_details(contract_id: uuid.UUID):
        """Get sponsor contract details including samples and kits"""
        # First verify the contract exists
        contract = await get_sponsor_contract(contract_id)
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Contract with id {contract_id} not found",
            )

        details = await get_sponsor_contract_details(contract_id)
        return details

    @staticmethod
    async def update_sponsor_contract(
        contract_id: uuid.UUID, data: SponsorContractUpdate
    ):
        """Update an existing sponsor contract"""
        contract = await get_sponsor_contract(contract_id)
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Contract with id {contract_id} not found",
            )

        error = await update_sponsor_contract(contract, data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        # Get updated contract
        updated_contract = await get_sponsor_contract(contract_id)
        return updated_contract.to_dict()

    @staticmethod
    async def delete_sponsor_contract(contract_id: uuid.UUID):
        """Delete a sponsor contract"""
        contract = await get_sponsor_contract(contract_id)
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Contract with id {contract_id} not found",
            )

        result, error = await delete_sponsor_contract(contract)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        return {"detail": f"Sponsor Contract {contract.name} deleted successfully"}

    @staticmethod
    async def get_contract_file_download_url(contract_id: uuid.UUID) -> str:
        """
        Generate a presigned URL for downloading the contract file.

        Args:
            contract_id: UUID of the contract

        Returns:
            Presigned URL for downloading the file

        Raises:
            HTTPException: If contract not found, no file exists, or S3 error occurs
        """
        # Get contract details
        contract = await get_sponsor_contract(contract_id)
        if not contract:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Contract with id {contract_id} not found",
            )

        # Check if contract has an associated file
        if not contract.s3_key or not contract.s3_bucket:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No file associated with contract {contract_id}",
            )

        try:
            # Initialize S3 client
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
                aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
                region_name=config["AWS"]["AWS_REGION"],
            )

            # Generate presigned URL
            # URL will be valid for 1 hour (3600 seconds)
            presigned_url = s3_client.generate_presigned_url(
                "get_object",
                Params={"Bucket": contract.s3_bucket, "Key": contract.s3_key},
                ExpiresIn=3600,  # URL expires in 1 hour
                HttpMethod="GET",
            )

            return {
                "download_url": presigned_url,
                "expires_in": 3600,
                "file_key": contract.s3_key,
            }

        except ClientError as e:
            logger.error(
                f"Error generating presigned URL for contract {contract_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error generating download URL: {str(e)}",
            )
        except Exception as e:
            logger.error(
                f"Unexpected error generating download URL for contract {contract_id}: {e}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Unexpected error generating download URL: {str(e)}",
            )

    @staticmethod
    async def import_sponsor_contract(file: UploadFile):
        excel_template = [
            {
                "display_name": "Tên hợp đồng tài trợ",
                "field": "sponsor_contract_name",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Mã hợp đồng tài trợ",
                "field": "code",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Nhà tài trợ",
                "field": "sponsor_name",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Số lượng",
                "field": "total_quantity",
                "required": True,
                "type": int,
            },
            {
                "display_name": "Ngày ký",
                "field": "signing_date",
                "required": True,
                "type": "date",
            },
        ]

        file_bytes = await file.read()

        list_data = read_and_validate_excel_schema(file_bytes=file_bytes, template_schema=excel_template)

        sponsors, _ = await SponsorService.get_all_sponsors()

        sponsor_contracts, _ = await SponsorContractService.get_all_sponsor_contracts()

        sponsor_names = {sponsor["name"]: sponsor for sponsor in sponsors}
        sponsor_contract_mapping = {contract["code"]: contract for contract in sponsor_contracts}

        for row in list_data:
            if row["code"].strip() in sponsor_contract_mapping:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Sponsor contract code {row['code']} already exist",
                )

            if row["sponsor_name"].strip() not in sponsor_names:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Sponsor {row['sponsor_name']} do not exist",
                )

            row["sponsor_id"] = sponsor_names[row["sponsor_name"].strip()]["id"]


        for row in list_data:
            sponsor_contrace_data = {
                "name": row["sponsor_contract_name"],
                "code": row["code"],
                "signing_date": row["signing_date"],
                "sponsor_id": row["sponsor_id"],
                "total_quantity": row["total_quantity"]
            }

            sponsor_contrace_data_create = SponsorContractCreate(**sponsor_contrace_data)

            await SponsorContractService.create_sponsor_contract(sponsor_contrace_data_create)
        return {"detail": f"Sponsor contract import successfully"}


# --- SponsorCampaign Service ---


class SponsorCampaignService:
    @staticmethod
    async def get_sponsor_campaign(campaign_id: uuid.UUID):
        """Get a sponsor campaign by ID"""
        campaign = await get_sponsor_campaign(campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Campaign with id {campaign_id} not found",
            )
        return campaign.to_dict()

    @staticmethod
    async def get_all_sponsor_campaigns(
        page_number: Optional[int] = None,
        page_size: Optional[int] = None,
        contract_id: Optional[uuid.UUID] = None,
        gs_area_code: Optional[uuid.UUID] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        order_by: Optional[str] = "created_at",
        order_option: Optional[str] = "desc",
    ):
        """Get all sponsor campaigns with optional filtering"""
        campaigns, total = await get_all_sponsor_campaigns(
            page_number=page_number,
            page_size=page_size,
            contract_id=contract_id,
            gs_area_code=gs_area_code,
            start_date=start_date,
            end_date=end_date,
            order_by=order_by,
            order_option=order_option,
        )

        # Transform results to include proper column names
        result_data = []
        for campaign_row in campaigns:
            campaign_dict = dict(campaign_row)
            contract_name = campaign_dict.pop("contract_name", None)
            area_name = campaign_dict.pop("area_name", None)

            # Add extracted names to the campaign dictionary
            if contract_name:
                campaign_dict["contract_name"] = contract_name
            if area_name:
                campaign_dict["area_name"] = area_name

            result_data.append(campaign_dict)

        return result_data, total

    @staticmethod
    async def create_sponsor_campaign(data: SponsorCampaignCreate):
        """Create a new sponsor campaign"""
        result, error = await create_sponsor_campaign(data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)
        return result

    @staticmethod
    async def update_sponsor_campaign(
        campaign_id: uuid.UUID, data: SponsorCampaignUpdate
    ):
        """Update an existing sponsor campaign"""
        campaign = await get_sponsor_campaign(campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Campaign with id {campaign_id} not found",
            )

        error = await update_sponsor_campaign(campaign, data)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        # Get updated campaign
        updated_campaign = await get_sponsor_campaign(campaign_id)
        return updated_campaign.to_dict()

    @staticmethod
    async def delete_sponsor_campaign(campaign_id: uuid.UUID):
        """Delete a sponsor campaign"""
        campaign = await get_sponsor_campaign(campaign_id)
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Sponsor Campaign with id {campaign_id} not found",
            )

        result, error = await delete_sponsor_campaign(campaign)
        if error:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=error)

        return {
            "detail": f"Sponsor Campaign with ID {campaign_id} deleted successfully"
        }

    @staticmethod
    async def import_sponsor_campaign(file: UploadFile):
        excel_template = [
            {
                "display_name": "Tên chương trình tài trợ",
                "field": "sponsor_campaign_name",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Số lượng",
                "field": "quantity",
                "required": True,
                "type": int,
            },
            {
                "display_name": "Mô tả",
                "field": "description",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Hợp đồng tài trợ",
                "field": "contract_name",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Tỉnh/Thành phố",
                "field": "gs_area",
                "required": True,
                "type": str,
            },
            {
                "display_name": "Ngày bắt đầu",
                "field": "start_date",
                "required": True,
                "type": "date",
            },
            {
                "display_name": "Ngày kết thúc",
                "field": "end_date",
                "required": True,
                "type": "date",
            },
        ]

        file_bytes = await file.read()

        list_data = read_and_validate_excel_schema(file_bytes=file_bytes, template_schema=excel_template)

        sponsor_contracts, _ = await SponsorContractService.get_all_sponsor_contracts()
        gs_areas, _ = await GsAreaService.get_all_gs_areas()

        sponsor_contract_mapping = {sponsor_contract["name"]: sponsor_contract for sponsor_contract in sponsor_contracts}
        gs_area_mapping = {gs_area["area"]: gs_area for gs_area in gs_areas}

        for row in list_data:
            if row["contract_name"].strip() not in sponsor_contract_mapping:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Sponsor contract {row['contract_name']} do not exist",
                )

            row["contract_id"] = sponsor_contract_mapping[row["contract_name"].strip()]["id"]

            if row["gs_area"].strip() not in gs_area_mapping:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"GS area {row['gs_area']} do not exist",
                )

            row["gs_area_code"] = gs_area_mapping[row["gs_area"].strip()]["id"]


        for row in list_data:
            sponsor_campaign_data = {
                "name": row["sponsor_campaign_name"],
                "quantity": row["quantity"],
                "description": row["description"],
                "start_date": row["start_date"],
                "end_date": row["end_date"],
                "contract_id": row["contract_id"],
                "gs_area_code": row['gs_area_code'],
            }

            sponsor_campaign_data_create = SponsorCampaignCreate(**sponsor_campaign_data)

            await SponsorCampaignService.create_sponsor_campaign(sponsor_campaign_data_create)
        return {"detail": f"Sponsor campaign import successfully"}


