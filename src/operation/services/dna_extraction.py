from datetime import datetime, timedelta
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_400_BAD_REQUEST,
    HTTP_423_LOCKED,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from ..utils.utils import (
    failure_response,
    get_current_date_time,
    convert_str_to_iso_datetime,
    convert_datetime_to_iso,
    failure_response_kit_registration,
    success_response,
    format_date,
    get_product_by_product_code,
)

from ..cruds.dna_extraction import *

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, DEFAULT_DATE_STR, DEFAULT_YEAR
)

async def convert_start_dna_extraction_date(start_date):
    start_date = format_date(start_date+'T00:00:00.000000Z', DEFAULT_DATE_STR, validate_current_time=False)
    return start_date

async def convert_end_dna_extraction_date(end_date):
    end_date = format_date(end_date+'T23:59:59.999999Z', DEFAULT_DATE_STR, validate_current_time=False)
    return end_date


async def standardize_dna_extraction_service(data):
    try:
        now = datetime.now()
        date_time=now.strftime("%m/%d/%Y,%H:%M:%S")
        current_time=date_time.split(',')[1]
        dna_extraction_date = data['dna_extraction_date']+'T'+current_time+'.000000Z'
        formated_dna_extraction_date=format_date(dna_extraction_date, DEFAULT_DATE_STR, validate_current_time=False)
        data['dna_extraction_date']=formated_dna_extraction_date
        return data
        
    except Exception as e:
        raise e
