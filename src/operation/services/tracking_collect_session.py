from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking_collect_session import *
from ..cruds.tracking_unit import *
from ..cruds.tracking_unit_location import *
from ..schemas.tracking_collect_session import *
from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE,
    failure_response,
    format_date,
    handle_exceptions,
)


@handle_exceptions
async def get_all_collect_sessions_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tul.created_at",
    unit_id: Optional[int] = None,
    tenDonVi: Optional[str] = None,
    location_name: Optional[str] = None,
    phone_number: Optional[str] = None,
    employee_name: Optional[str] = None,
    customer_support_id: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    if collection_start_date:
        start_date_std = format_date(collection_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if collection_end_date:
        end_date_std = format_date(
            collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")

    return await get_all_tracking_collect_sessions(
        offset=offset,
        size=size,
        order_by=order_by,
        unit_id=unit_id,
        tenDonVi=tenDonVi,
        location_name=location_name,
        phone_number=phone_number,
        employee_name=employee_name,
        customer_support_id=customer_support_id,
        collection_start_date=start_date_std if collection_start_date else None,
        collection_end_date=end_date_std if collection_end_date else None,
        include_deleted=include_deleted,
    )


@handle_exceptions
async def get_collect_session_by_id_service(id: str):
    data = await get_tracking_collect_session_by_id(id)
    return data.to_dict()


@handle_exceptions
async def create_collect_session_service(data: dict):
    """
    location_id
    user_id
    phone_number
    employee_name
    """

    return await create_tracking_collect_session(data)


@handle_exceptions
async def update_collect_session_service(id: str, data: dict):
    """
    location_id: str
    employee_id: str
    collect_date: str
    """
    collect_session = await get_tracking_collect_session_by_id(id)
    if not collect_session:
        err = f"Unit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_collect_session(collect_session, data)
    data = await get_tracking_collect_session_by_id(id)
    return data.to_dict()


@handle_exceptions
async def delete_collect_session_by_id_service(id: str):
    """
    id: str
    """
    collect_session = await get_tracking_collect_session_by_id(id)

    await delete_tracking_collect_session(collect_session=collect_session)

    data = await get_tracking_collect_session_by_id(id)
    return data.to_dict()


@handle_exceptions
async def upsert_collect_session_sample_service(
    id: str, req_body: AddSamplesToCollectSession
):
    """
    location_id
    user_id
    phone_number
    employee_name

    """

    # get sample_collector_name from id

    session = await get_collect_session_by_id_service(id=id)
    tracking_unit_location = await get_tracking_unit_location_by_id(
        session.get("location_id")
    )

    req_body.tracking_unit_id = tracking_unit_location.unit_id
    req_body.sample_collector_name = session.get("employee_name")
    req_body.customer_support_id = session.get("user_id")
    req_body.customer_support_name = session.get("employee_name")
    req_body.std_sample_collector_name = (
        standardize_full_name(req_body.sample_collector_name)
        if req_body.sample_collector_name
        else None
    )
    req_body.std_sample_receiver_name = (
        standardize_full_name(req_body.sample_receiver_name)
        if req_body.sample_receiver_name
        else None
    )

    result = await upsert_tracking_collect_session_sample(
        req_body=req_body, session_id=id
    )

    return result


# get_all_collect_session_samples_service
@handle_exceptions
async def get_all_collect_session_samples_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tcs.created_at",
    session_id: Optional[str] = None,
    identifier_code: Optional[str] = None,
    full_name: Optional[str] = None,
    gender: Optional[str] = None,
    dob: Optional[str] = None,
    samplecode: Optional[str] = None,
):
    return await get_all_collect_session_samples(
        offset=offset,
        size=size,
        order_by=order_by,
        session_id=session_id,
        identifier_code=identifier_code,
        full_name=full_name,
        gender=gender,
        dob=dob,
        samplecode=samplecode,
    )
