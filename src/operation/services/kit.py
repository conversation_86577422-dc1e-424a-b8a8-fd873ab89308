import json
import uuid
from datetime import <PERSON><PERSON><PERSON>
from typing import List

import boto3
import requests
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from io import Bytes<PERSON>
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_423_LOCKED,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from ..cruds.tracking_collection import *
from src.operation.config import config
from src.operation.producer import send_msg_to_queue
from .tracking_employee import update_employee_service
from .. import logger
from ..cruds.account import get_account_by_name
from ..cruds.attempts import (
    increase_attempts,
    reset_attempts,
)
from ..cruds.codes import (
    available_code,
    deactivated_code,
    duplicated_code,
    existed_code,
    generate_used_barcode_v2,
    generate_used_code,
    get_code,
    save_code,
    update_code,
)
from ..cruds.identity_card import (
    create_identity_card,
    existed_identity_card,
    get_identity_card_by_id,
    update_identity_card,
)
from ..cruds.kit import (
    get_all_kits_by_samplecode_v3,
    get_kit_by_barcode_v3,
    get_kit_detail_via_barcode_v3,
    get_kit_list_by_sample_meta,
    get_kit_list_by_sample_meta_v3,
    get_kit_list_detail,
    get_kit_list_detail_v3,
    get_unscanned_kit_by_samplecode_v3,
)
from ..cruds.lab_sample import update_lab_sample_with_new_samplecode
from ..cruds.partner_booking import (
    get_booking_by_id,
    get_booking_by_id_v3,
    get_partner_booking_id_by_barcode,
    update_partner_booking,
    update_partner_booking_kit,
    update_partner_booking_v3,
)
from ..cruds.register_kit import (
    create_kit,
    create_kit_v3,
    create_sample_v3,
    get_kit,
    get_kit_v3,
    get_kit_v3_w_barcode,
    get_kit_v3_w_kit_uuid,
    get_sample_by_samplecode_v3,
    get_sample_v3_via,
    get_status,
    get_status_v3,
    init_status_v3,
    register,
    save_status,
    save_status_v3,
    update_kit,
    update_kit_barcode_v3,
    update_kit_v3,
    update_kit_with_default_link,
    update_kit_with_default_link_v3,
    update_kit_with_new_samplecode,
    update_sample_scan_status_v3,
    update_sample_v3,
)
from ..cruds.sale_account_history import get_current_sale_account_history_by_account_id
from ..cruds.sample_meta import (
    create_sample_meta,
    get_sample_meta_by_id,
    update_sample_meta,
)
from ..cruds.samplecodes import (
    available_samplecode,
    deactivated_samplecode,
    duplicated_samplecode,
    existed_samplecode,
    validate_samplecode,
)
from ..cruds.source import (
    create_source_by_staff_and_account,
    create_source_by_staffs_n_account,
    get_source_detail_by_id,
)
from ..cruds.staff import get_staff
from ..cruds.subject import (
    create_subject,
    get_subject,
    get_subject_by_id,
    get_subject_by_id_card,
    update_subject,
)
from ..cruds.tracking import create_tracking, get_tracking_by_samplecode
from ..cruds.tracking_collection import create_tracking_collection, get_tracking_collection_by_tracking_id
from ..cruds.tracking_employee import (
    create_tracking_employee,
    get_tracking_employee_dict_by_name_n_unit_id, get_tracking_employee_by_id,
)
from ..cruds.tracking_unit import get_tracking_unit_by_id
from ..models.models import (
    IdentityCard,
    Kit,
    KitV3,
    SampleMeta,
    SampleV3,
    Status,
    Subject,
    db,
)
from ..models.partner_order import BarcodeStatus
from ..schemas.kit import *
from ..schemas.partner_booking import PartnerBookingUpdateReq, SyncBookingInfoReq
from ..schemas.tracking_collect_session import *
from ..services.partner_booking import (
    sync_booking_information_srv,
    sync_booking_information_srv_v3,
)
from ..utils.excel_utils import export_to_excel
from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE,
    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
    DEFAULT_YEAR,
    add_business_days,
    contains_no_digits,
    convert_datetime_to_iso,
    convert_rowproxy_to_dict,
    convert_str_to_iso_datetime,
    failure_response,
    failure_response_kit_registration,
    format_date,
    get_current_date_time_utc_7,
    get_current_time_gmt_plus_7,
    get_product_by_product_code,
    parse_object_from_dict,
    parse_object_key,
    renew_obj_key,
    standardize_full_name,
    success_response,
)


async def renew_kit_default_link(kit_link_body):
    try:
        barcode = kit_link_body.get("barcode")
        default_pdf_link = kit_link_body.get("default_pdf_link")
        pdf_bucket, obj_key = parse_object_key(default_pdf_link)
        res = renew_obj_key(pdf_bucket, obj_key)

        message_body = {"barcode": barcode, "default_pdf_link": res}
        logger.info(f"UPDATE PDF DEFAULT LINK SUCCESS FOR {barcode}")

        await update_kit_default_link_v3(message_body)
        return res

    except Exception as error:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(str(error))
        raise HTTPException(status_code=http_code, detail=errs)


async def update_kit_default_link(message_body):
    updated_kit_info = {
        "default_pdf_link": message_body["default_pdf_link"],
        "pdf_generation_date": datetime.utcnow().date(),
    }
    try:
        kit = await get_kit(message_body["barcode"])
        if kit.actual_report_release_time is None:
            updated_kit_info.update(
                {
                    "actual_report_release_time": get_current_time_gmt_plus_7()
                    .replace(microsecond=0)
                    .replace(tzinfo=None)
                }
            )
        logger.info(f"UPDATE KIT INFO: {updated_kit_info}")
        await update_kit_with_default_link(kit=kit, data=updated_kit_info)
        logger.info(
            f"UPDATE PDF DEFAULT LINK SUCCESS FOR: {str(message_body['barcode'])}"
        )
    except AttributeError as error:
        msg = f"KIT WITH ID: {message_body['barcode']} NOT FOUND"
        logger.info(msg)
        raise error


async def update_kit_default_link_v3(message_body):
    updated_kit_info = {
        "default_pdf_link": message_body["default_pdf_link"],
        "pdf_generation_date": datetime.utcnow().date(),
    }
    if message_body.get("cccd"):
        updated_status = {"barcode": message_body.get("barcode"), "status": "COMPLETED"}
        new_status, err = await save_status_v3(updated_status)
        updated_kit_info.update(
            {"current_status": new_status.status, "current_status_id": new_status.id}
        )
    try:
        kit_detail = await get_kit_detail_via_barcode_v3(message_body["barcode"])
        kit = parse_object_from_dict(KitV3, kit_detail)
        if kit.get("actual_report_release_time") is None:
            updated_kit_info.update(
                {
                    "actual_report_release_time": get_current_time_gmt_plus_7()
                    .replace(microsecond=0)
                    .replace(tzinfo=None)
                }
            )
        logger.info(f"UPDATE KIT INFO: {updated_kit_info}")
        kitv3 = await get_kit_v3(barcode=kit.get("barcode"))
        await update_kit_with_default_link_v3(kit=kitv3, data=updated_kit_info)
        logger.info(
            f"UPDATE PDF DEFAULT LINK SUCCESS FOR: {str(message_body['barcode'])}"
        )
    except AttributeError as error:
        msg = f"KIT WITH ID: {message_body['barcode']} NOT FOUND"
        logger.info(msg)
        raise error


async def update_kit_actual_release_date(message_body):
    updated_kit_info = {
        "pdf_generation_date": datetime.utcnow().date(),
    }
    try:
        kit = await get_kit(message_body["kit_id"])
        if kit.actual_report_release_time is None:
            updated_kit_info.update(
                {
                    "actual_report_release_time": get_current_time_gmt_plus_7()
                    .replace(microsecond=0)
                    .replace(tzinfo=None)
                }
            )
        else:
            logger.info(
                f"PDF GENERATION DATE ALREADY UPDATED: {str(message_body['kit_id'])}"
            )
        logger.info(f"UPDATE KIT INFO: {updated_kit_info}")
        await update_kit_with_default_link(kit=kit, data=updated_kit_info)

        logger.info(
            f"UPDATE PDF ACTUAL RELEASE DATE SUCCESS FOR: {str(message_body['kit_id'])}"
        )
    except AttributeError as error:
        msg = f"KIT WITH ID: {message_body['kit_id']} NOT FOUND"
        logger.info(msg)
        raise error


async def update_kit_actual_release_date_v3(message_body):
    updated_kit_info = {
        "pdf_generation_date": datetime.utcnow().date(),
    }
    try:
        kit_detail = await get_kit_detail_via_barcode_v3(message_body["kit_id"])
        kit = parse_object_from_dict(KitV3, kit_detail)
        if kit.get("actual_report_release_time") is None:
            updated_kit_info.update(
                {
                    "actual_report_release_time": get_current_time_gmt_plus_7()
                    .replace(microsecond=0)
                    .replace(tzinfo=None)
                }
            )
        else:
            logger.info(
                f"PDF GENERATION DATE ALREADY UPDATED: {str(message_body['kit_id'])}"
            )
        logger.info(f"UPDATE KIT INFO: {updated_kit_info}")
        kitv3 = await get_kit_v3(barcode=kit.get("barcode"))
        await update_kit_with_default_link_v3(kit=kitv3, data=updated_kit_info)

        logger.info(
            f"UPDATE PDF ACTUAL RELEASE DATE SUCCESS FOR: {str(message_body['kit_id'])}"
        )
    except AttributeError as error:
        msg = f"KIT WITH ID: {message_body['kit_id']} NOT FOUND"
        logger.info(msg)
        raise error
    pass


async def check_and_update_booking_status(barcode: str):
    partner_booking = await get_partner_booking_id_by_barcode(barcode=barcode)
    if partner_booking is None:
        logger.info(f"This barcode {barcode} not belong to any partner booking")
        return
    booking = await get_booking_by_id(id=partner_booking.booking_id)
    item_list = booking["item_list"]
    booking_completed = True
    for item in item_list:
        current_status = item["current_status"]
        if current_status != config["COMPLETED_KIT_STATUS"]:
            booking_completed = False
            partner_barcode_status = BarcodeStatus.IN_ANALYSIS.name
        else:
            partner_barcode_status = BarcodeStatus.COMPLETED.name
        await update_partner_booking_kit(
            barcode=barcode,
            booking_id=partner_booking.booking_id,
            update_data={
                "status": partner_barcode_status,
            },
        )
    if booking_completed:
        await update_partner_booking(
            partner_booking_id=partner_booking.booking_id,
            req=PartnerBookingUpdateReq(status=BarcodeStatus.COMPLETED.name),
        )

    await sync_booking_information_srv(
        req=SyncBookingInfoReq(booking_list=[str(partner_booking.booking_id)])
    )
    logger.info("Done checking and updating booking status")


async def check_and_update_booking_status_v3(barcode: str):
    partner_booking = await get_partner_booking_id_by_barcode(barcode=barcode)
    if partner_booking is None:
        logger.info(f"This barcode {barcode} not belong to any partner booking")
        return
    booking = await get_booking_by_id_v3(id=partner_booking.booking_id)
    item_list = booking["item_list"]
    booking_completed = True
    for item in item_list:
        current_status = item["current_status"]
        if current_status != config["COMPLETED_KIT_STATUS"]:
            booking_completed = False
            partner_barcode_status = BarcodeStatus.IN_ANALYSIS.name
        else:
            partner_barcode_status = BarcodeStatus.COMPLETED.name
        await update_partner_booking_kit(
            barcode=barcode,
            booking_id=partner_booking.booking_id,
            update_data={
                "status": partner_barcode_status,
            },
        )
    if booking_completed:
        await update_partner_booking_v3(
            partner_booking_id=partner_booking.booking_id,
            req=PartnerBookingUpdateReq(status=BarcodeStatus.COMPLETED.name),
        )

    await sync_booking_information_srv_v3(
        req=SyncBookingInfoReq(booking_list=[str(partner_booking.booking_id)])
    )
    logger.info("Done checking and updating booking status")


async def send_check_booking_message(barcode):
    try:
        message_req = {
            "action": "UPDATE_BOOKING_STATUS",
            "barcode": barcode,
        }

        sqs_client = boto3.client(
            "sqs",
            aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
            aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
            region_name=config["AWS"]["NOTIFICATION_REGION_NAME"],
        )
        queue = sqs_client.get_queue_url(
            QueueName=config["SQS_CFG"]["SQSCONSUMER_QUEUENAME"]
        )
        queue_url = queue["QueueUrl"]
        logger.info(f"Queue URL: {queue_url}")
        res = sqs_client.send_message(
            QueueUrl=queue_url, MessageBody=json.dumps(message_req)
        )
        if res["ResponseMetadata"]["HTTPStatusCode"] == 200:
            logger.info(f"Successfully send check booking message: {message_req}")
        else:
            logger.info(f"Cannot send check booking message: {message_req}")
    except Exception as e:
        logger.info(f"Send check booking message error: {e}")


async def create_new_kit_status(id, kit_status, lab_receipt_date=None):
    # [TODO] This should be in a transaction
    if not isinstance(kit_status, dict):
        data = kit_status.dict()
    else:
        data = kit_status
    allowed_statuses = config["ALLOWED_KIT_STATUS"]
    if data["status"] not in allowed_statuses:
        err = f"Status '{data['status']}' is not an allowed request status ({allowed_statuses})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        return None, (http_code, errs)
    kit = await get_kit_v3(id)
    if not kit:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        return None, (http_code, errs)
    update_data = {**data, "barcode": id}
    saved_status, err = await save_status(update_data)
    # send check booking message
    if err:
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        return None, (http_code, errs)
    # kit_info = kit.to_dict()
    current_time = get_current_date_time_utc_7()
    # new_kit_info = {
    #     **kit_info,
    #     'current_status': saved_status.status,
    #     'current_status_id': saved_status.id,
    #     'updated_time': current_time
    # }
    new_kit_info = kit
    new_kit_info.current_status = saved_status.status
    new_kit_info.current_status_id = saved_status.id
    new_kit_info.updated_time = current_time
    if new_kit_info.current_status == "LAB_CHECK":
        new_kit_info.lab_receipt_date = format_date(
            lab_receipt_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
    await update_kit(new_kit_info)
    new_kit_status, err = await get_status(id)
    if err:
        await update_kit(kit)
        err_msg = "Can not update kit status"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err_msg)
        return None, (http_code, errs)

    # SEND MESSAGE TO REPORT GENERATOR
    # send_index_resource_message_to_report_generator(kit_id=id)

    await send_check_booking_message(barcode=id)

    return new_kit_status, None


async def create_new_kit_status_via_barcode_v3(
    id, sample_status, lab_receipt_date=None
):
    # [TODO] This should be in a transaction
    if not isinstance(sample_status, dict):
        data = sample_status.dict()
    else:
        data = sample_status
    allowed_statuses = config["ALLOWED_KIT_STATUS"]
    if data["status"] not in allowed_statuses:
        err = f"Status '{data['status']}' is not an allowed request status ({allowed_statuses})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        return None, (http_code, errs)
    kit = await get_kit_v3(id)
    if not kit:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        return None, (http_code, errs)

    sample = await get_sample_by_samplecode_v3(samplecode=kit.samplecode)
    if not sample:
        err = f"Sample with samplecode {kit.samplecode} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        return None, (http_code, errs)

    update_data = {**data, "barcode": id}
    saved_status, err = await save_status_v3(update_data)
    # send check booking message
    if err:
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        return None, (http_code, errs)
    # kit_info = kit.to_dict()
    current_time = get_current_date_time_utc_7()
    # new_kit_info = {
    #     **kit_info,
    #     'current_status': saved_status.status,
    #     'current_status_id': saved_status.id,
    #     'updated_time': current_time
    # }

    new_kit_info = kit
    new_kit_info.current_status = saved_status.status
    new_kit_info.current_status_id = saved_status.id
    new_kit_info.updated_time = current_time
    await update_kit_v3(new_kit_info)

    new_sample_info = sample
    new_sample_info.updated_time = current_time

    # Only need to update "lab_receipt_date" the 1st time "Register Kit/Sample" === Send "physical" sample to LAB
    if (new_kit_info.current_status == "LAB_CHECK") and (not sample.lab_receipt_date):
        new_sample_info.lab_receipt_date = format_date(
            lab_receipt_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
        await update_sample_v3(new_sample_info)

    new_kit_status, err = await get_status_v3(barcode=id)
    if err:
        await update_kit_v3(kit)
        await update_sample_v3(sample)
        err_msg = "Can not update sample status"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err_msg)
        return None, (http_code, errs)

    # SEND MESSAGE TO REPORT GENERATOR
    # send_index_resource_message_to_report_generator(kit_id=id)

    await send_check_booking_message(barcode=id)

    return new_kit_status, None


def send_index_resource_message_to_report_generator(kit_id: str):
    message_body = {
        "action": config["REPORT_GENERATOR"]["BATCH_INDEX_RESOURCES"],
        "body": {"kit_id": kit_id},
    }
    send_msg_to_queue(config["REPORT_GENERATOR"]["QUEUE_NAME"], message_body)


async def update_kit_service(id, kit, new_kit_info, kit_info):
    try:
        await update_kit(kit, new_kit_info)
        message_body = {
            "action": config["REPORT_GENERATOR"]["BATCH_INDEX_RESOURCES"],
            "body": {"kit_id": id},
        }
        send_msg_to_queue(config["REPORT_GENERATOR"]["QUEUE_NAME"], message_body)
        updated_kit_info = await get_kit(id)
        return success_response(updated_kit_info.to_dict()), None, None
    except Exception as err:
        await update_kit(kit, kit_info)
        http_code = HTTP_500_INTERNAL_SERVER_ERROR
        errs = failure_response(str(err))
        return None, errs, http_code


def send_resouce_message_to_queue(user_id, barcode):
    message_body = {
        "action": config["AUTHZ"]["CREATE_POLICY_ACTION"],
        "body": {
            "user_id": user_id,
            "service_id": "operation",
            "action_list": [
                {"method": "GET", "path": f"/kits/registrations/{barcode}"},
                {"method": "GET", "path": f"/kits/registrations/{barcode}/status"},
            ],
        },
    }
    send_msg_to_queue(config["AUTHZ"]["QUEUE_NAME"], message_body)

    # SEND MESSAGE TO REPORT GENERATOR
    message_body = {
        "action": config["REPORT_GENERATOR"]["BATCH_INDEX_RESOURCES"],
        "body": {"kit_id": barcode},
    }
    send_msg_to_queue(config["REPORT_GENERATOR"]["QUEUE_NAME"], message_body)


async def get_user_information(phone=None, email=None):
    request_headers = {"Content-type": "application/json", "Accept": "text/plain"}
    response = requests.get(
        config["AUTHEN_URL"][config["ENV"]] + config["AUTHEN_URL"]["VALIDATE_USER"],
        headers=request_headers,
        params={
            "phone_number": phone,
            "email": email,
        },
        timeout=3,
    )
    response.raise_for_status()
    user_info = response.json()
    if user_info["error"]["code"] == 400:
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(user_info["error"]["message"])
        raise HTTPException(status_code=http_code, detail=errs)
    else:
        return user_info


def parse_dob(dob, yob):
    if yob is not None and dob is None:
        date_obj = datetime.strptime(yob, DEFAULT_YEAR).date()
        return yob, date_obj
    elif dob is not None:
        date_obj = datetime.strptime(dob, DEFAULT_DATE_NO_TIME_ZONE)
        return None, date_obj.date()
    else:
        raise ValueError("invalid dob or yob information")


async def internal_register_kit(register_kit_req: OperatorRegisterKit):
    try:
        if not await existed_code(register_kit_req.barcode):
            err = str(f"Barcode {register_kit_req.barcode} is deleted or not existed.")
            raise ValueError(err)
        if not await available_code(register_kit_req.barcode):
            if await deactivated_code(register_kit_req.barcode):
                err = str(f"Barcode {register_kit_req.barcode} was deactivated.")
                raise ValueError(err)
            elif await duplicated_code(register_kit_req.barcode):
                err = str(f"Barcode {register_kit_req.barcode} was already registered.")
                raise ValueError(err)
        else:
            code_detail = await get_code(barcode=register_kit_req.barcode)
            user_id = register_kit_req.userid
            if register_kit_req.userid is None:
                user_info = await get_user_information(
                    register_kit_req.phone_number, email=register_kit_req.email
                )
                user_id = user_info["data"]["uuid"]
            yob, dob = parse_dob(register_kit_req.dob, register_kit_req.yob)
            sample_collection_date = format_date(
                register_kit_req.sample_collection_date, DEFAULT_DATE_NO_TIME_ZONE
            )
            sample_receipt_date = format_date(
                register_kit_req.sample_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            sample_meta_id = None
            if register_kit_req.sample_meta_id is None:
                # create sample meta
                sample_meta = SampleMeta(
                    userid=user_id,
                    full_name=register_kit_req.name,
                    address=register_kit_req.address,
                    email=register_kit_req.email,
                    phone_number=register_kit_req.phone_number,
                    validate_account=register_kit_req.validate_account,
                    gender=register_kit_req.gender,
                    dob=dob,
                    yob=yob,
                    diagnosis=register_kit_req.diagnosis,
                    created_at=get_current_date_time_utc_7(),
                    updated_at=get_current_date_time_utc_7(),
                    deleted_at=get_current_date_time_utc_7(),
                )
                sample_meta = await create_sample_meta(sample_meta=sample_meta)
                sample_meta_id = sample_meta["id"]
            current_sale_account_his = (
                await get_current_sale_account_history_by_account_id(
                    register_kit_req.account_id
                )
            )
            source = await create_source_by_staff_and_account(
                register_kit_req.nominator_id, current_sale_account_his["id"]
            )
            # ---GS-6868---#
            expected_report_release_date = None
            if register_kit_req.expected_report_release_date is None:
                expected_report_release_date = sample_receipt_date.date() + timedelta(
                    days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                )
            else:
                expected_report_release_date = format_date(
                    register_kit_req.expected_report_release_date,
                    DEFAULT_DATE_NO_TIME_ZONE,
                    validate_current_time=False,
                )
            if expected_report_release_date < sample_receipt_date.date():
                raise ValueError(
                    "expected report release date cannot be before sample receipt date"
                )
            if register_kit_req.lab_receipt_date:
                lab_receipt_date = format_date(
                    register_kit_req.lab_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                if lab_receipt_date < sample_receipt_date:
                    raise ValueError(
                        "lab receipt date cannot be before sample receipt date"
                    )
            else:
                lab_receipt_date = None
            sample_collection_time = (
                1
                if not register_kit_req.sample_collection_time
                else register_kit_req.sample_collection_time
            )
            print("creating kit:")
            kit_req = Kit(
                barcode=register_kit_req.barcode,
                sample_meta_id=sample_meta_id
                if sample_meta_id is not None
                else register_kit_req.sample_meta_id,
                nickname=register_kit_req.nickname,
                version=register_kit_req.version,
                product_code=code_detail.product_code,
                product_name=code_detail.product_name,
                product_type=register_kit_req.product_type,
                sample_collection_date=sample_collection_date,
                sample_receipt_date=sample_receipt_date,
                lab_receipt_date=lab_receipt_date,
                sample_collection_time=sample_collection_time,
                sample_collector_name=register_kit_req.sample_collector_name,
                sample_receiver_name=register_kit_req.sample_receiver_name,
                expected_report_release_date=expected_report_release_date,
                source_id=source["id"],
                note=register_kit_req.note,
                free_of_charge=register_kit_req.free_of_charge,
                promotion=register_kit_req.promotion_id,
                customer_support_id=register_kit_req.customer_support_id,
                customer_support_name=register_kit_req.customer_support_name,
                is_priority=register_kit_req.is_priority,
                sample_type=register_kit_req.sample_type,
            )
            data, err = await create_kit(kit_req)
            if err:
                raise ValueError(err)
            else:
                await update_code(register_kit_req.barcode, config["USED_CODE_STATE"])
                if user_id is not None:
                    send_resouce_message_to_queue(user_id, register_kit_req.barcode)
                return data, err

    except Exception as e:
        raise e


async def register_kit_service_v2(register_kit_req: OperatorRegisterKit):
    # validate user information if specified
    try:
        if not await existed_code(register_kit_req.barcode):
            err = str(f"Barcode {register_kit_req.barcode} is deleted or not existed.")
            raise ValueError(err)
        if not await available_code(register_kit_req.barcode):
            if await deactivated_code(register_kit_req.barcode):
                err = str(f"Barcode {register_kit_req.barcode} was deactivated.")
                raise ValueError(err)
            elif await duplicated_code(register_kit_req.barcode):
                err = str(f"Barcode {register_kit_req.barcode} was already registered.")
                raise ValueError(err)
        else:
            code_detail = await get_code(barcode=register_kit_req.barcode)
            user_id = None
            user_info = None
            if register_kit_req.validate_account:
                user_info = await get_user_information(register_kit_req.phone_number)
                user_id = user_info["data"]["uuid"]
            yob, dob = parse_dob(register_kit_req.dob, register_kit_req.yob)
            sample_collection_date = format_date(
                register_kit_req.sample_collection_date, DEFAULT_DATE_NO_TIME_ZONE
            )
            sample_receipt_date = format_date(
                register_kit_req.sample_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            sample_meta_id = None
            if register_kit_req.sample_meta_id is None:
                # create sample meta
                sample_meta = SampleMeta(
                    userid=user_id,
                    full_name=register_kit_req.name,
                    address=register_kit_req.address,
                    email=register_kit_req.email,
                    phone_number=register_kit_req.phone_number,
                    validate_account=register_kit_req.validate_account,
                    gender=register_kit_req.gender,
                    dob=dob,
                    yob=yob,
                    diagnosis=register_kit_req.diagnosis,
                    created_at=get_current_date_time_utc_7(),
                    updated_at=get_current_date_time_utc_7(),
                    deleted_at=get_current_date_time_utc_7(),
                )
                sample_meta = await create_sample_meta(sample_meta=sample_meta)
                sample_meta_id = sample_meta["id"]
            current_sale_account_his = (
                await get_current_sale_account_history_by_account_id(
                    register_kit_req.account_id
                )
            )
            # [GS-6868] #
            if register_kit_req.freelancer_id:
                freelancer = await get_staff(register_kit_req.freelancer_id)
                if freelancer.role != "FREELANCER":
                    err = "Staff role is not FREELANCER!"
                    raise ValueError(err)
                source = await create_source_by_staffs_n_account(
                    register_kit_req.nominator_id,
                    current_sale_account_his["id"],
                    register_kit_req.freelancer_id,
                )
            else:
                source = await create_source_by_staff_and_account(
                    register_kit_req.nominator_id, current_sale_account_his["id"]
                )
            # ---GS-6868---#
            expected_report_release_date = None
            if register_kit_req.expected_report_release_date is None:
                expected_report_release_date = sample_receipt_date.date() + timedelta(
                    days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                )
            else:
                expected_report_release_date = format_date(
                    register_kit_req.expected_report_release_date,
                    DEFAULT_DATE_NO_TIME_ZONE,
                    validate_current_time=False,
                )
            if expected_report_release_date < sample_receipt_date.date():
                raise ValueError(
                    "expected report release date cannot be before sample receipt date"
                )
            if register_kit_req.lab_receipt_date:
                lab_receipt_date = format_date(
                    register_kit_req.lab_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                if lab_receipt_date < sample_receipt_date:
                    raise ValueError(
                        "lab receipt date cannot be before sample receipt date"
                    )
            else:
                lab_receipt_date = None
            sample_collection_time = (
                1
                if not register_kit_req.sample_collection_time
                else register_kit_req.sample_collection_time
            )
            kit_req = Kit(
                barcode=register_kit_req.barcode,
                sample_meta_id=sample_meta_id
                if sample_meta_id is not None
                else register_kit_req.sample_meta_id,
                nickname=register_kit_req.nickname,
                # email=register_kit_req.email,
                # phone_number=register_kit_req.phone_number,
                # userid=register_kit_req.userid,
                # name=register_kit_req.name,
                # gender=register_kit_req.gender,
                version=register_kit_req.version,
                # dob=dob,
                product_code=code_detail.product_code,
                product_name=code_detail.product_name,
                product_type=register_kit_req.product_type,
                sample_collection_date=sample_collection_date,
                sample_receipt_date=sample_receipt_date,
                lab_receipt_date=lab_receipt_date,
                sample_collection_time=sample_collection_time,
                sample_collector_name=register_kit_req.sample_collector_name,
                sample_receiver_name=register_kit_req.sample_receiver_name,
                expected_report_release_date=expected_report_release_date,
                source_id=source["id"],
                note=register_kit_req.note,
                free_of_charge=register_kit_req.free_of_charge,
                promotion=register_kit_req.promotion_id,
                customer_support_id=register_kit_req.customer_support_id,
                customer_support_name=register_kit_req.customer_support_name,
                is_priority=register_kit_req.is_priority,
                sample_type=register_kit_req.sample_type,
            )
            data, err = await create_kit(kit_req)
            if err:
                raise ValueError(err)
            else:
                await update_code(register_kit_req.barcode, config["USED_CODE_STATE"])
                if user_info:
                    send_resouce_message_to_queue(
                        user_info["data"]["uuid"], register_kit_req.barcode
                    )
                return data, err

    except Exception as e:
        raise e


async def register_kit_service(data, original_request_body, apply_user_id):
    data["userid"] = apply_user_id
    barcode = data["barcode"]
    dob = data["dob"]
    date_time_obj, err = convert_str_to_iso_datetime(dob)
    if err:
        err = str(
            f"{dob} is not accepted format, the correct format is %Y-%m-%dT%H:%M:%S.%fZ"
        )
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        return None, (http_code, errs)
    current_date_obj = datetime.now()
    current_date_iso, err = convert_datetime_to_iso(current_date_obj)
    if date_time_obj >= current_date_iso:
        err = str(f"{dob} can not greater than the current time")
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        return None, (http_code, errs)
    data["dob"] = date_time_obj
    if not await available_code(barcode):
        total_attempts, err = await increase_attempts(apply_user_id)
        remain_attemps = int(config["MAX_ATTEMPTS"]) - int(total_attempts)
        if err:
            http_code = HTTP_423_LOCKED
            errs = failure_response(err)
            return None, (http_code, errs)
        if await deactivated_code(barcode):
            err = str(
                f"id {barcode} was deactivated, can not be used anymore. You have {remain_attemps} input times left"
            )
            http_code = HTTP_406_NOT_ACCEPTABLE
            errs = failure_response_kit_registration(
                err, remain_attempts=remain_attemps
            )
            return None, (http_code, errs)
        elif await duplicated_code(barcode):
            err = str(
                f"id {barcode} was already registered, can not be used anymore. You have {remain_attemps} input times left"
            )
            http_code = HTTP_409_CONFLICT
            errs = failure_response_kit_registration(
                err, remain_attempts=remain_attemps
            )
            return None, (http_code, errs)
        else:
            err = str(
                f"Can not register kit with id {barcode}. You have {remain_attemps} input times left"
            )
            http_code = HTTP_422_UNPROCESSABLE_ENTITY
            errs = failure_response_kit_registration(
                err, remain_attempts=remain_attemps
            )
            return None, (http_code, errs)
    else:
        if config.get("DEPLOY_ENV") != "test":
            err = await reset_attempts(data["userid"])
            if err:
                http_code = HTTP_500_INTERNAL_SERVER_ERROR
                errs = failure_response(err)
                return None, (http_code, errs)

        data, err = await register(data)
        if err:
            http_code = HTTP_500_INTERNAL_SERVER_ERROR
            errs = failure_response(err)
            return None, (http_code, errs)
        else:
            await update_code(barcode, config["USED_CODE_STATE"])
            message_body = {
                "action": config["AUTHZ"]["CREATE_POLICY_ACTION"],
                "body": {
                    "user_id": apply_user_id,
                    "service_id": "operation",
                    "action_list": [
                        {"method": "GET", "path": f"/kits/registrations/{barcode}"},
                        {
                            "method": "GET",
                            "path": f"/kits/registrations/{barcode}/status",
                        },
                    ],
                },
            }
            send_msg_to_queue(config["AUTHZ"]["QUEUE_NAME"], message_body)

            # SEND MESSAGE TO REPORT GENERATOR
            message_body = {
                "action": config["REPORT_GENERATOR"]["BATCH_INDEX_RESOURCES"],
                "body": {"kit_id": barcode},
            }
            send_msg_to_queue(config["REPORT_GENERATOR"]["QUEUE_NAME"], message_body)

            return success_response(original_request_body), None


async def get_kit_list(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "created_time",
    user_id: Optional[str] = None,
    barcode: Optional[int] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    exclude_status: Optional[str] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    receipt_start_date: Optional[str] = None,
    receipt_end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    if receipt_start_date:
        start_date_std = format_date(receipt_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if receipt_end_date:
        end_date_std = format_date(receipt_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")
    if release_start_date:
        release_start_date_std = format_date(
            release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if release_end_date:
        release_end_date_std = format_date(
            release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if release_end_date < release_start_date:
            raise ValueError("end date cannot be after start date")
    if collection_start_date:
        collection_start_date_std = format_date(
            collection_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if collection_end_date:
        collection_end_date_std = format_date(
            collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if collection_end_date_std < collection_start_date_std:
            raise ValueError("end date cannot be after start date")
    if actual_report_release_start_date:
        actual_report_release_start_date_std = format_date(
            actual_report_release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if actual_report_release_end_date:
        actual_report_release_end_date_std = format_date(
            actual_report_release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if actual_report_release_end_date_std < actual_report_release_start_date_std:
            raise ValueError("end date cannot be after start date")
    return await get_kit_list_detail(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        userid=user_id,
        email=email,
        phone_number=phone_number,
        name=name,
        gender=gender,
        nickname=nickname,
        current_status=current_status,
        exclude_status=exclude_status,
        product_code=product_code,
        sale_pic=sale_pic,
        account_name=account_name,
        batch=batch,
        technology=technology,
        sample_collector_name=sample_collector_name,
        sample_receiver_name=sample_receiver_name,
        start_date=start_date_std if receipt_start_date else None,
        end_date=end_date_std if receipt_end_date else None,
        release_start_date=release_start_date_std if release_start_date else None,
        release_end_date=release_end_date_std if release_end_date else None,
        collection_start_date=collection_start_date_std
        if collection_start_date
        else None,
        collection_end_date=collection_end_date_std if collection_end_date else None,
        actual_report_release_start_date=actual_report_release_start_date_std
        if actual_report_release_end_date
        else None,
        actual_report_release_end_date=actual_report_release_end_date_std
        if actual_report_release_end_date
        else None,
        include_deleted=include_deleted,
    )


async def get_kit_list_by_sample_info(
    name: str,
    gender: str,
    dob: Optional[str] = None,
    yob: Optional[str] = None,
    account_id: Optional[str] = None,
    product_code: Optional[str] = None,
):
    if dob:
        dob_std = format_date(dob, date_format=DEFAULT_DATE_NO_TIME_ZONE)
    return await get_kit_list_by_sample_meta(
        name=name,
        gender=gender,
        dob=dob_std if dob else None,
        yob=yob,
        account_id=account_id,
        product_code=product_code,
    )


def is_validate_account(update_kit_req, current_sample_meta):
    not_validate_list = ["", None]
    if update_kit_req.validate_account:
        return True
    elif update_kit_req.phone_number != current_sample_meta.phone_number:
        return True
    elif (
        update_kit_req.email in not_validate_list
        and current_sample_meta.email in not_validate_list
    ):
        return False
    elif update_kit_req.email != current_sample_meta.email:
        return True
    else:
        return False


async def update_kit_info(
    update_kit_req: UpdateKit,
):
    try:
        current_kit_info = await get_kit(barcode=update_kit_req.barcode)
        if current_kit_info.current_status == "DELETED":
            raise ValueError("Cannot update deleted kit")
        product_detail = None
        logger.info(
            f"UpdateKitInfo - getting product detail: {update_kit_req.product_code}"
        )
        if update_kit_req.product_code != current_kit_info.product_code:
            product_detail = get_product_by_product_code(update_kit_req.product_code)

        yob, dob = parse_dob(update_kit_req.dob, update_kit_req.yob)
        sample_collection_date = format_date(
            update_kit_req.sample_collection_date, DEFAULT_DATE_NO_TIME_ZONE
        )
        sample_receipt_date = format_date(
            update_kit_req.sample_receipt_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
        # sample_meta_id = None
        # Update sample meta
        current_sample_meta = await get_sample_meta_by_id(update_kit_req.sample_meta_id)
        logger.info(f"UpdateKitInfo - getting sample meta: {current_sample_meta}")
        user_id = None
        user_info = None
        if update_kit_req.validate_account:
            user_info = await get_user_information(update_kit_req.phone_number)
            user_id = user_info["data"]["uuid"]
        if user_id is not None:
            current_sample_meta.userid = user_id
        current_sample_meta.full_name = update_kit_req.name
        current_sample_meta.email = update_kit_req.email
        current_sample_meta.phone_number = update_kit_req.phone_number
        current_sample_meta.gender = update_kit_req.gender
        current_sample_meta.dob = dob
        current_sample_meta.yob = yob
        current_sample_meta.address = update_kit_req.address
        current_sample_meta.validate_account = update_kit_req.validate_account
        current_sample_meta.diagnosis = update_kit_req.diagnosis
        update_res = await update_sample_meta(current_sample_meta)

        source_detail = await get_source_detail_by_id(update_kit_req.source_id)
        source = None
        # [GS-6868] #
        if update_kit_req.freelancer_id:
            if (
                source_detail["account_id"] != update_kit_req.account_id
                or source_detail["nominator_id"] != update_kit_req.nominator_id
                or source_detail["freelancer_id"] != update_kit_req.freelancer_id
            ):
                current_sale_account_his = (
                    await get_current_sale_account_history_by_account_id(
                        update_kit_req.account_id
                    )
                )
                source = await create_source_by_staffs_n_account(
                    update_kit_req.nominator_id,
                    current_sale_account_his["id"],
                    update_kit_req.freelancer_id,
                )
                logger.info(f"Source: {source}")
                pass
        else:
            if (
                source_detail["account_id"] != update_kit_req.account_id
                or source_detail["nominator_id"] != update_kit_req.nominator_id
            ):
                current_sale_account_his = (
                    await get_current_sale_account_history_by_account_id(
                        update_kit_req.account_id
                    )
                )
                source = await create_source_by_staff_and_account(
                    update_kit_req.nominator_id, current_sale_account_his["id"]
                )
        # --- 6868 --- #
        expected_report_release_date = None
        if update_kit_req.expected_report_release_date is None:
            expected_report_release_date = sample_receipt_date.date() + timedelta(
                days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
            )
        else:
            expected_report_release_date = format_date(
                update_kit_req.expected_report_release_date,
                DEFAULT_DATE_NO_TIME_ZONE,
                validate_current_time=False,
            )
        if expected_report_release_date < sample_receipt_date.date():
            raise ValueError(
                "expected report release date cannot be before sample receipt date"
            )
        if update_kit_req.lab_receipt_date:
            lab_receipt_date = format_date(
                update_kit_req.lab_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            if lab_receipt_date < sample_receipt_date:
                raise ValueError(
                    "lab receipt date cannot be before sample receipt date"
                )
        else:
            lab_receipt_date = None
        sample_collection_time = (
            1
            if not update_kit_req.sample_collection_time
            else update_kit_req.sample_collection_time
        )
        current_kit = await get_kit(update_kit_req.barcode)
        current_kit.sample_meta_id = update_kit_req.sample_meta_id
        if product_detail is not None:
            current_kit.product_code = product_detail["code"]
            current_kit.product_name = product_detail["name"]
        current_kit.product_type = update_kit_req.product_type
        current_kit.sample_collection_date = sample_collection_date
        current_kit.sample_collection_time = sample_collection_time
        current_kit.sample_receipt_date = sample_receipt_date
        current_kit.lab_receipt_date = lab_receipt_date
        current_kit.sample_collector_name = update_kit_req.sample_collector_name
        current_kit.sample_receiver_name = update_kit_req.sample_receiver_name
        current_kit.expected_report_release_date = expected_report_release_date
        current_kit.source_id = (
            source["id"] if source is not None else update_kit_req.source_id
        )
        current_kit.note = update_kit_req.note
        current_kit.free_of_charge = update_kit_req.free_of_charge
        current_kit.promotion = update_kit_req.promotion_id
        current_kit.is_priority = update_kit_req.is_priority
        current_kit.sample_type = update_kit_req.sample_type
        logger.info(f"UpdateKitInfo - update kit information: {current_kit}")
        res = await update_kit(current_kit)
        return res
    except Exception as e:
        raise e


async def batch_update_kit_status(kit_list: List[str], status: str):
    try:
        failed_update_kits = []
        updated_kits = []
        for kit_id in kit_list:
            kit = await get_kit(kit_id)
            if kit.current_status not in [
                config["REGISTERED_KIT_STATUS"],
                config["FAILED_LAB_CHECK_KIT_STATUS"],
                config["MISSING_SAMPLE_KIT_STATUS"],
            ]:
                failed_update_kits.append(kit_id)
            else:
                await create_new_kit_status(id=kit_id, kit_status=status)
                updated_kits.append(kit_id)
        return failed_update_kits, updated_kits
    except Exception as e:
        raise e


async def batch_update_kit_status_n_date(
    kit_list: List[str], lab_receipt_date: str, status: str
):
    try:
        failed_update_kits = []
        updated_kits = []
        for kit_id in kit_list:
            kit = await get_kit(kit_id)
            if kit.current_status not in [
                config["REGISTERED_KIT_STATUS"],
                config["FAILED_LAB_CHECK_KIT_STATUS"],
                config["MISSING_SAMPLE_KIT_STATUS"],
            ]:
                failed_update_kits.append(kit_id)
            else:
                await create_new_kit_status(
                    id=kit_id, kit_status=status, lab_receipt_date=lab_receipt_date
                )
                updated_kits.append(kit_id)
        return failed_update_kits, updated_kits
    except Exception as e:
        raise e


async def send_kit_info_to_lab(kit_list: List[str], lab_receipt_date: str):
    try:
        return await batch_update_kit_status_n_date(
            kit_list=kit_list,
            lab_receipt_date=lab_receipt_date,
            status=NewKitStatus(status=config["LAB_CHECK_KIT_STATUS"]),
        )
        # send notification to lab operators
    except Exception as e:
        raise e


async def migrate_kit_service_v3(migrate_kit_req: OperatorMigrateKitV3):
    try:
        async with db.transaction() as tx:
            barcode = migrate_kit_req.barcode
            target_samplecode = migrate_kit_req.target_samplecode

            curr_kit = await get_kit_by_barcode_v3(barcode=barcode)
            curr_sample = await get_sample_by_samplecode_v3(
                samplecode=curr_kit.samplecode
            )
            subject_infor = await get_subject_by_id(subject_id=curr_sample.subject_id)
            target_sample = await get_sample_by_samplecode_v3(
                samplecode=target_samplecode
            )
            target_subject_infor = await get_subject_by_id(
                subject_id=target_sample.subject_id
            )

            if migrate_kit_req.is_target_cccd:
                if not target_subject_infor.identifier_code:
                    err = f"Subject with samplecode {target_samplecode} CCCD not found!"
                    raise ValueError(err)

            print("Type yob: ", type(subject_infor.yob), "yob: ", subject_infor.yob)
            print(
                "Type yob: ",
                type(str(target_subject_infor.dob.year)),
                "yob: ",
                target_subject_infor.dob.year,
            )
            is_valid = True
            if (
                subject_infor.full_name.strip()
                != target_subject_infor.full_name.strip()
                or subject_infor.gender != target_subject_infor.gender
            ):
                is_valid = False

            if not migrate_kit_req.is_skipping_yob:
                if subject_infor.dob != target_subject_infor.dob:
                    is_valid = False
            else:
                if subject_infor.yob != str(target_subject_infor.dob.year):
                    is_valid = False

            if not is_valid:
                err = f"Subject information of barcode {barcode} and samplecode {target_samplecode} not match!"
                raise ValueError(err)

            # print("is_valid: ", is_valid)

            # update "lab_sample" if exist to new "target_samplecode"
            _ = await update_lab_sample_with_new_samplecode(
                barcode=barcode, new_samplecode=target_samplecode
            )

            # update "kit" with "barcode" to new "target_samplecode"
            _ = await update_kit_with_new_samplecode(
                barcode=barcode, new_samplecode=target_samplecode
            )

            return is_valid

    except Exception as e:
        raise e


async def register_kit_service_v3(register_kit_req: OperatorRegisterKitV3):
    # validate user information if specified
    """
    Register new kits:
    [+] SampleID: Available
    """
    try:
        async with db.transaction() as tx:
            # Check SampleID / Samplecode
            if not await existed_samplecode(register_kit_req.samplecode):
                err = str(
                    f"Samplecode {register_kit_req.samplecode} is deleted or not existed."
                )
                raise ValueError(err)
            if not await available_samplecode(register_kit_req.samplecode):
                if await deactivated_samplecode(register_kit_req.samplecode):
                    err = str(
                        f"Samplecode {register_kit_req.samplecode} was deactivated."
                    )
                    raise ValueError(err)
                elif await duplicated_samplecode(register_kit_req.samplecode):
                    err = str(
                        f"Samplecode {register_kit_req.samplecode} was already registered."
                    )
                    raise ValueError(err)

            else:
                # samplecode_detail = await get_samplecode(samplecode=register_kit_req.samplecode)
                print("Samplecode: ", register_kit_req.samplecode)
                user_id = None
                user_info = None
                if register_kit_req.validate_account:
                    user_info = await get_user_information(
                        register_kit_req.phone_number
                    )
                    user_id = user_info["data"]["uuid"]
                print("user_id: ", user_id)
                yob, dob = parse_dob(register_kit_req.dob, register_kit_req.yob)
                sample_collection_date = format_date(
                    register_kit_req.sample_collection_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                sample_receipt_date = format_date(
                    register_kit_req.sample_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                subject_id = None
                res = await get_subject(
                    user_id=user_id,
                    full_name=register_kit_req.name,
                    dob=dob,
                    yob=yob,
                    gender=register_kit_req.gender,
                )
                if len(res) > 0 and register_kit_req.sample_recollection:
                    subject_data = res[0].to_dict()
                    subject_id = subject_data["id"]
                    print(
                        "Subject existed: ",
                        subject_id,
                        " Name: ",
                        subject_data["full_name"],
                    )
                elif len(res) > 0 and not register_kit_req.sample_recollection:
                    err = "Should using Sample Recollection flow!"
                    raise ValueError(err)
                else:
                    martyr_name = None
                    if register_kit_req.martyr_name is not None:
                        martyr_name = str(register_kit_req.martyr_name)
                    # create sample meta
                    print("Create a new subject!")
                    subject = Subject(
                        id=uuid.uuid4(),
                        user_id=user_id,
                        full_name=register_kit_req.name,
                        email=register_kit_req.email,
                        phone_number=register_kit_req.phone_number,
                        diagnosis=register_kit_req.diagnosis,
                        address=register_kit_req.address,
                        dob=dob,
                        gender=register_kit_req.gender,
                        yob=yob,
                        legal_guardian=register_kit_req.legal_guardian,
                        martyr_name=martyr_name,
                        martyr_relationships=register_kit_req.martyr_relationships,
                        validate_account=register_kit_req.validate_account,
                        created_at=get_current_date_time_utc_7(),
                        updated_at=get_current_date_time_utc_7(),
                    )
                    subject_data = await create_subject(subject=subject)
                    subject_id = subject_data["id"]
                print("subject_id: ", subject_id)
                current_sale_account_his = (
                    await get_current_sale_account_history_by_account_id(
                        register_kit_req.account_id
                    )
                )
                print("curr_sale_history: ", current_sale_account_his["id"])
                # [GS-6868] #
                if register_kit_req.freelancer_id:
                    freelancer = await get_staff(register_kit_req.freelancer_id)
                    if freelancer.role != "FREELANCER":
                        err = "Staff role is not FREELANCER!"
                        raise ValueError(err)
                    source = await create_source_by_staffs_n_account(
                        register_kit_req.nominator_id,
                        current_sale_account_his["id"],
                        register_kit_req.freelancer_id,
                    )
                else:
                    source = await create_source_by_staff_and_account(
                        register_kit_req.nominator_id, current_sale_account_his["id"]
                    )
                # ---GS-6868---#
                expected_report_release_date = None
                if register_kit_req.expected_report_release_date is None:
                    expected_report_release_date = (
                        sample_receipt_date.date()
                        + timedelta(
                            days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                        )
                    )
                else:
                    expected_report_release_date = format_date(
                        register_kit_req.expected_report_release_date,
                        DEFAULT_DATE_NO_TIME_ZONE,
                        validate_current_time=False,
                    )
                if expected_report_release_date < sample_receipt_date.date():
                    raise ValueError(
                        "expected report release date cannot be before sample receipt date"
                    )
                if register_kit_req.lab_receipt_date:
                    lab_receipt_date = format_date(
                        register_kit_req.lab_receipt_date,
                        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                        validate_current_time=False,
                    )
                    if lab_receipt_date < sample_receipt_date:
                        raise ValueError(
                            "lab receipt date cannot be before sample receipt date"
                        )
                else:
                    lab_receipt_date = None

                # sample_collection_time = 1 if not register_kit_req.sample_collection_time else register_kit_req.sample_collection_time

                # Get existed sample with subject_id
                sample_res = await get_sample_v3_via(subject_id=subject_id)
                total_sample = len(sample_res)
                print("total_sample: ", total_sample)
                if total_sample:
                    if register_kit_req.sample_recollection != True:
                        raise ValueError(
                            "Đối tượng đã được thu mẫu. Vui lòng thực hiện luồng thu lại mẫu."
                        )

                    sample_collection_time = total_sample + 1
                else:
                    if register_kit_req.sample_recollection != False:
                        raise ValueError("Recollection should be set to False")

                    sample_collection_time = 1

                # CURRENT_STATUS
                status_data = init_status_v3()
                status = await Status.create(**status_data)

                sample_req = SampleV3(
                    id=uuid.uuid4(),
                    samplecode=register_kit_req.samplecode,
                    subject_id=subject_id,
                    sample_collection_date=sample_collection_date,
                    sample_recollection=register_kit_req.sample_recollection,
                    sample_collection_time=sample_collection_time,
                    sample_receipt_date=sample_receipt_date,
                    sample_collector_name=register_kit_req.sample_collector_name,
                    sample_receiver_name=register_kit_req.sample_receiver_name,
                    source_id=source["id"],
                    lab_receipt_date=lab_receipt_date,
                    sample_type=register_kit_req.sample_type,
                    sponsor_id=register_kit_req.sponsor_id,
                )
                sample_data, err = await create_sample_v3(sample_req)
                current_time = get_current_date_time_utc_7()
                print("Successfully Created Sample!")
                product_detail = get_product_by_product_code(
                    register_kit_req.product_code
                )

                if product_detail is not None:
                    if product_detail["name"] != register_kit_req.product_name:
                        raise ValueError(
                            f"Product's name {register_kit_req.product_name} doesnt match with the name from database {product_detail['name']}!"
                        )
                    product_code = product_detail["code"]
                    product_name = product_detail["name"]
                else:
                    raise ValueError("Product Name and Product Code not found!")

                kit_req = KitV3(
                    id=uuid.uuid4(),
                    # barcode=register_kit_req.barcode, <--- generated when scan_status==2
                    samplecode=register_kit_req.samplecode,
                    nickname=register_kit_req.nickname,
                    version=register_kit_req.version,
                    expected_report_release_date=expected_report_release_date,
                    # actual_report_release_time <-- update when releasing
                    customer_support_id=register_kit_req.customer_support_id,
                    customer_support_name=register_kit_req.customer_support_name,
                    free_of_charge=register_kit_req.free_of_charge,
                    promotion=register_kit_req.promotion_id,
                    is_priority=register_kit_req.is_priority,
                    # pdf_generation_date <- Nullable
                    # is_card_issued <-- False by default
                    product_code=product_code,  # before from barcode.detail
                    product_name=product_name,  # before from barcode.detail
                    product_type=register_kit_req.product_type,
                    note=register_kit_req.note,
                    current_status=status.status,
                    current_status_id=status.id,
                    workflow=config["WORKFLOW_STATUS"]["REGISTER_KIT"],
                    created_at=current_time,
                    updated_at=current_time,
                )
                kit_data, err = await create_kit_v3(kit_req)
                print("Successfully Created Kit!")

                data = {**subject_data, **sample_data, **kit_data}
                if err:
                    raise ValueError(err)
                else:
                    # -> Will Update Barcode right after it being generated
                    # -> Send SQS mess to grant permission
                    # await update_code(register_kit_req.barcode, config['USED_CODE_STATE'])
                    # if user_info:
                    #     send_resouce_message_to_queue(user_info['data']["uuid"], register_kit_req.barcode)
                    return data, err

    except Exception as e:
        raise e


async def get_kit_list_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "created_time",
    user_id: Optional[str] = None,
    barcode: Optional[str] = None,
    samplecode: Optional[str] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    workflow: Optional[str] = None,
    exclude_status: Optional[str] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    sale_pic_id: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    receipt_start_date: Optional[str] = None,
    receipt_end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    if receipt_start_date:
        start_date_std = format_date(receipt_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if receipt_end_date:
        end_date_std = format_date(receipt_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")
    if release_start_date:
        release_start_date_std = format_date(
            release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if release_end_date:
        release_end_date_std = format_date(
            release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if release_end_date < release_start_date:
            raise ValueError("end date cannot be after start date")
    if collection_start_date:
        collection_start_date_std = format_date(
            collection_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if collection_end_date:
        collection_end_date_std = format_date(
            collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if collection_end_date_std < collection_start_date_std:
            raise ValueError("end date cannot be after start date")
    if actual_report_release_start_date:
        actual_report_release_start_date_std = format_date(
            actual_report_release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if actual_report_release_end_date:
        actual_report_release_end_date_std = format_date(
            actual_report_release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if actual_report_release_end_date_std < actual_report_release_start_date_std:
            raise ValueError("end date cannot be after start date")
    return await get_kit_list_detail_v3(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        samplecode=samplecode,
        userid=user_id,
        email=email,
        phone_number=phone_number,
        name=name,
        gender=gender,
        nickname=nickname,
        current_status=current_status,
        workflow=workflow,
        exclude_status=exclude_status,
        product_code=product_code,
        sale_pic=sale_pic,
        sale_pic_id=sale_pic_id,
        account_name=account_name,
        batch=batch,
        technology=technology,
        sample_collector_name=sample_collector_name,
        sample_receiver_name=sample_receiver_name,
        start_date=start_date_std if receipt_start_date else None,
        end_date=end_date_std if receipt_end_date else None,
        release_start_date=release_start_date_std if release_start_date else None,
        release_end_date=release_end_date_std if release_end_date else None,
        collection_start_date=collection_start_date_std
        if collection_start_date
        else None,
        collection_end_date=collection_end_date_std if collection_end_date else None,
        actual_report_release_start_date=actual_report_release_start_date_std
        if actual_report_release_end_date
        else None,
        actual_report_release_end_date=actual_report_release_end_date_std
        if actual_report_release_end_date
        else None,
        include_deleted=include_deleted,
    )


async def get_kit_by_meta_data_v3(
    name: str,
    gender: str,
    dob: Optional[str] = None,
    yob: Optional[str] = None,
    account_id: Optional[str] = None,
    product_code: Optional[str] = None,
):
    if dob:
        dob_std = format_date(dob, date_format=DEFAULT_DATE_NO_TIME_ZONE)
    return await get_kit_list_by_sample_meta_v3(
        name=name,
        gender=gender,
        dob=dob_std if dob else None,
        yob=yob,
        account_id=account_id,
        product_code=product_code,
    )


async def generate_barcode_service_v3(product_code, product_name, version):
    """
    Generates a barcode for a product and saves it if it's not already used.

    Args:
        product_code (str): The code of the product.
        product_name (str): The name of the product.
        version (int): The version of the barcode generation process.

    Raises:
        HTTPException: If the barcode version is not supported.
    """

    size = 1
    i = 0
    logger.info("Generating barcode for Product Code %s", product_code)

    while i < size:
        if version == 2 or contains_no_digits(product_code):
            code = generate_used_barcode_v2(
                product_code=product_code,
                product_name=product_name,
            )
        elif version == 1:
            code = generate_used_code(
                product_code=product_code,
                product_name=product_name,
            )
        else:
            raise HTTPException(
                status_code=HTTP_423_LOCKED,
                detail="New Barcode Format developing!",
            )

        # Check if the barcode is already used
        if not await get_code(code["barcode"]):
            # Save the new barcode
            await save_code(code)
            i += 1

    return code


async def scan_samplecode_service_v3(
    samplecode: str,
    version: int,
    product_codes: Optional[List[str]] = [],
):
    sample = await get_sample_by_samplecode_v3(samplecode=samplecode)
    sample_data = sample.to_dict()
    updated_sample = SampleV3(**sample_data)
    if sample:
        async with db.transaction() as tx:
            if sample.scan_status == 0:
                # print("Increase scan attempt by 1")
                logger.info("Increase scan attempt by 1 %s", sample.samplecode)
                updated_sample.scan_status += 1
                await update_sample_scan_status_v3(sample, updated_sample)

            elif sample.scan_status == 1:
                # print("Increase scan attempt by 1")
                logger.info("Increase scan attempt by 1 %s", sample.samplecode)
                updated_sample.scan_status += 1
                await update_sample_scan_status_v3(sample, updated_sample)

                kit = await get_unscanned_kit_by_samplecode_v3(
                    samplecode=samplecode
                )  # 1:1
                kit_data = kit.to_dict()
                code = await generate_barcode_service_v3(
                    product_code=kit_data.get("product_code"),
                    product_name=kit_data.get("product_name"),
                    version=version,
                )

                kit_data["barcode"] = code["barcode"]
                kit_data["updated_at"] = get_current_date_time_utc_7()

                sample_receipt_date = updated_sample.sample_receipt_date
                product_detail = get_product_by_product_code(
                    product_code=kit_data.get("product_code")
                )

                if not kit_data.get("expected_report_release_date"):
                    # kit_data['expected_report_release_date'] = sample_receipt_date + timedelta(days=int(product_detail['duration']))
                    kit_data["expected_report_release_date"] = add_business_days(
                        start_date=sample_receipt_date,
                        duration=int(product_detail["duration"]),
                    )

                updated_kit = KitV3(**kit_data)
                await update_kit_barcode_v3(kit, updated_kit)
                subject = await get_subject_by_id(subject_id=sample.subject_id)
                subject_data = subject.to_dict()
                user_id = str(subject_data.get("user_id"))
                if user_id:
                    send_resouce_message_to_queue(user_id, kit_data["barcode"])
                # print("Barcode generated!")
                logger.info("Barcode generated! %s", kit_data["barcode"])

            elif sample.scan_status == 2:
                # print("Scan attempt detected!")
                logger.warn("Scan attempt detected! %s", sample.samplecode)
                if len(product_codes):
                    all_kits = await get_all_kits_by_samplecode_v3(
                        samplecode=samplecode
                    )
                    current_product_codes = [kit.product_code for kit in all_kits]
                    diff_product_codes = list(
                        set(product_codes).difference(set(current_product_codes))
                    )
                    if len(diff_product_codes):
                        earlliest_kit = min(all_kits, key=lambda x: x.created_at)

                        for adding_product_code in diff_product_codes:
                            adding_product_detail = get_product_by_product_code(
                                product_code=adding_product_code
                            )
                            if not adding_product_detail:
                                raise HTTPException(
                                    status_code=HTTP_404_NOT_FOUND,
                                    detail=f"Product code {adding_product_code} not found!",
                                )

                            code = await generate_barcode_service_v3(
                                product_code=adding_product_detail.get("code"),
                                product_name=adding_product_detail.get("name"),
                                version=version,
                            )

                            temp_expected_report_release_date = add_business_days(
                                start_date=updated_sample.sample_receipt_date,
                                duration=int(adding_product_detail["duration"]),
                            )
                            # CURRENT_STATUS
                            status_data = init_status_v3()
                            status = await Status.create(**status_data)

                            adding_kit = KitV3(
                                id=uuid.uuid4(),
                                samplecode=samplecode,
                                barcode=code["barcode"],
                                nickname=earlliest_kit.nickname,
                                version=earlliest_kit.version,
                                expected_report_release_date=temp_expected_report_release_date,
                                customer_support_id=earlliest_kit.customer_support_id,
                                customer_support_name=earlliest_kit.customer_support_name,
                                free_of_charge=earlliest_kit.free_of_charge,
                                promotion=earlliest_kit.promotion,
                                is_priority=earlliest_kit.is_priority,
                                product_code=adding_product_detail.get("code"),
                                product_name=adding_product_detail.get("name"),
                                product_type=earlliest_kit.product_type,
                                note=earlliest_kit.note,
                                current_status=status.status,
                                current_status_id=status.id,
                                workflow=earlliest_kit.workflow,
                                created_at=get_current_date_time_utc_7(),
                                updated_at=get_current_date_time_utc_7(),
                            )
                            kit_data, _ = await create_kit_v3(adding_kit)
                            subject = await get_subject_by_id(
                                subject_id=sample.subject_id
                            )
                            subject_data = subject.to_dict()
                            user_id = str(subject_data.get("user_id"))
                            if user_id:
                                send_resouce_message_to_queue(
                                    user_id, kit_data.get("barcode")
                                )
                                logger.info(
                                    "Grant user_id %s permission with barcode %s ! ",
                                    user_id,
                                    kit_data.get("barcode"),
                                )
                            # print("Barcode generated!")
                            logger.info(
                                "Kit with barcode %s generated! ",
                                kit_data.get("barcode"),
                            )

                    else:
                        logger.info(
                            "Input Codes %s -- %s need to be generated -- %s",
                            product_codes,
                            len(diff_product_codes),
                            diff_product_codes,
                        )

            else:
                raise HTTPException(
                    status_code=HTTP_406_NOT_ACCEPTABLE, detail="Already scan sample!"
                )

            return updated_sample.to_dict()

    else:
        raise HTTPException(
            status_code=HTTP_404_NOT_FOUND, detail="Sample did not exist!"
        )


def validate_barcode_via_subject_infor(
    kit: dict, user_id: str, full_name: str, yob: str, dob: str, gender: str
):
    if str(user_id) != str(kit.get("user_id")):
        logger.info(f"InvalidUserInfo Input user_id: {user_id}")
        return False, None

    if str(full_name) != str(kit.get("full_name")):
        logger.info(f"InvalidUserInfo Input full_name: {full_name} ")
        return False, None

    if str(yob) != str(kit.get("yob")):
        logger.info(f"InvalidUserInfo Input yob: {yob} ")
        return False, None

    if str(dob) != str(kit.get("dob")):
        logger.info(f"InvalidUserInfo Input dob: {dob} ")
        return False, None

    if gender != kit.get("gender"):
        logger.info(f"InvalidUserInfo Input gender: {gender} ")
        return False, None

    subject_data = {
        "subject_id": kit.get("subject_id"),
        "user_id": kit.get("user_id"),
        "full_name": kit.get("full_name"),
        "email": kit.get("email"),
        "phone_number": kit.get("phone_number"),
        "diagnosis": kit.get("diagnosis"),
        "address": kit.get("address"),
        "dob": kit.get("dob"),
        "gender": kit.get("gender"),
        "yob": kit.get("yob"),
        "validate_account": kit.get("validate_account"),
    }

    return True, subject_data


async def recollect_kit_service_v3(
    first_barcode: str, recollect_kit_req: OperatorRecollectKitV3
):
    # validate user information if specified
    """
    Register new kits:
    [+] SampleID: Available
    """
    try:
        async with db.transaction() as tx:
            # Check SampleID / Samplecode
            if not await existed_samplecode(recollect_kit_req.samplecode):
                err = str(
                    f"Samplecode {recollect_kit_req.samplecode} is deleted or not existed."
                )
                raise ValueError(err)
            if not await available_samplecode(recollect_kit_req.samplecode):
                if await deactivated_samplecode(recollect_kit_req.samplecode):
                    err = str(
                        f"Samplecode {recollect_kit_req.samplecode} was deactivated."
                    )
                    raise ValueError(err)
                elif await duplicated_samplecode(recollect_kit_req.samplecode):
                    err = str(
                        f"Samplecode {recollect_kit_req.samplecode} was already registered."
                    )
                    raise ValueError(err)

            else:
                first_kit = await get_kit_detail_via_barcode_v3(barcode=first_barcode)
                first_kit = convert_rowproxy_to_dict(first_kit)
                subject_id = None  # s

                user_id = recollect_kit_req.user_id

                yob, dob = parse_dob(recollect_kit_req.dob, recollect_kit_req.yob)

                is_valid_barcode, subject_data = validate_barcode_via_subject_infor(
                    kit=first_kit,
                    user_id=user_id,
                    full_name=recollect_kit_req.name or recollect_kit_req.full_name,
                    yob=yob,
                    dob=dob,
                    gender=recollect_kit_req.gender,
                )

                if recollect_kit_req.sample_recollection:
                    if is_valid_barcode:
                        subject_id = first_kit.get("subject_id")
                    else:
                        raise ValueError("Input metadata not match with barcode!")
                else:
                    raise ValueError("Check sample recollection box!")

                sample_collection_date = format_date(
                    recollect_kit_req.sample_collection_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                sample_receipt_date = format_date(
                    recollect_kit_req.sample_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )

                current_sale_account_his = (
                    await get_current_sale_account_history_by_account_id(
                        recollect_kit_req.account_id
                    )
                )
                # [GS-6868] #
                if recollect_kit_req.freelancer_id:
                    freelancer = await get_staff(recollect_kit_req.freelancer_id)
                    if freelancer.role != "FREELANCER":
                        err = "Staff role is not FREELANCER!"
                        raise ValueError(err)
                    source = await create_source_by_staffs_n_account(
                        recollect_kit_req.nominator_id,
                        current_sale_account_his["id"],
                        recollect_kit_req.freelancer_id,
                    )
                else:
                    source = await create_source_by_staff_and_account(
                        recollect_kit_req.nominator_id, current_sale_account_his["id"]
                    )
                # ---GS-6868---#
                expected_report_release_date = None
                if recollect_kit_req.expected_report_release_date is None:
                    expected_report_release_date = (
                        sample_receipt_date.date()
                        + timedelta(
                            days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                        )
                    )
                else:
                    expected_report_release_date = format_date(
                        recollect_kit_req.expected_report_release_date,
                        DEFAULT_DATE_NO_TIME_ZONE,
                        validate_current_time=False,
                    )
                if expected_report_release_date < sample_receipt_date.date():
                    raise ValueError(
                        "expected report release date cannot be before sample receipt date"
                    )
                if recollect_kit_req.lab_receipt_date:
                    lab_receipt_date = format_date(
                        recollect_kit_req.lab_receipt_date,
                        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                        validate_current_time=False,
                    )
                    if lab_receipt_date < sample_receipt_date:
                        raise ValueError(
                            "lab receipt date cannot be before sample receipt date"
                        )
                else:
                    lab_receipt_date = None

                # sample_collection_time = 1 if not recollect_kit_req.sample_collection_time else recollect_kit_req.sample_collection_time

                # Get existed sample with subject_id
                sample_res = await get_sample_v3_via(subject_id=subject_id)
                total_sample = len(sample_res)

                if total_sample:
                    if recollect_kit_req.sample_recollection != True:
                        raise ValueError(
                            "Đối tượng đã được thu mẫu. Vui lòng thực hiện luồng thu lại mẫu."
                        )

                    sample_collection_time = total_sample + 1
                else:
                    raise ValueError("Total Sample should be greater or equal to 1")

                # CURRENT_STATUS
                status_data = init_status_v3()
                status = await Status.create(**status_data)

                sample_req = SampleV3(
                    id=uuid.uuid4(),
                    samplecode=recollect_kit_req.samplecode,
                    subject_id=subject_id,
                    sample_collection_date=sample_collection_date,
                    sample_recollection=recollect_kit_req.sample_recollection,
                    sample_collection_time=sample_collection_time,
                    sample_receipt_date=sample_receipt_date,
                    sample_collector_name=recollect_kit_req.sample_collector_name,
                    sample_receiver_name=recollect_kit_req.sample_receiver_name,
                    source_id=source["id"],
                    # scan_status
                    # lab_check_date <-- lab_sample update this
                    lab_receipt_date=lab_receipt_date,
                    sample_type=recollect_kit_req.sample_type,
                )
                sample_data, err = await create_sample_v3(sample_req)
                current_time = get_current_date_time_utc_7()
                kit_req = KitV3(
                    id=uuid.uuid4(),
                    # barcode=recollect_kit_req.barcode, <--- generated when scan_status==2
                    samplecode=recollect_kit_req.samplecode,
                    nickname=recollect_kit_req.nickname,
                    version=recollect_kit_req.version,
                    expected_report_release_date=expected_report_release_date,
                    # actual_report_release_time <-- update when releasing
                    customer_support_id=recollect_kit_req.customer_support_id,
                    customer_support_name=recollect_kit_req.customer_support_name,
                    free_of_charge=recollect_kit_req.free_of_charge,
                    promotion=recollect_kit_req.promotion_id,
                    is_priority=recollect_kit_req.is_priority,
                    # pdf_generation_date <- Nullable
                    # is_card_issued <-- False by default
                    product_code=recollect_kit_req.product_code,  # before from barcode.detail
                    product_name=recollect_kit_req.product_name,  # before from barcode.detail
                    product_type=recollect_kit_req.product_type,
                    note=recollect_kit_req.note,
                    current_status=status.status,
                    current_status_id=status.id,
                    workflow=config["WORKFLOW_STATUS"]["RECOLLECT_KIT"],
                    created_at=current_time,
                    updated_at=current_time,
                )
                kit_data, err = await create_kit_v3(kit_req)
                data = {**subject_data, **sample_data, **kit_data}
                if err:
                    raise ValueError(err)
                else:
                    # -> Will Update Barcode right after it being generated
                    # -> Send SQS mess to grant permission
                    # await update_code(recollect_kit_req.barcode, config['USED_CODE_STATE'])
                    # if user_info:
                    #     send_resouce_message_to_queue(user_info['data']["uuid"], recollect_kit_req.barcode)
                    return data, err

    except Exception as e:
        raise e


async def upgrade_kit_service_v3(
    upgrade_kit_req: OperatorUpgradeKitWithCCCDV3, version: int
):
    # validate user information if specified
    """
    Register new kits:
    [+] SampleID: Available
    """
    try:
        async with db.transaction() as tx:
            # Check SampleID / Samplecode
            if not await existed_samplecode(upgrade_kit_req.samplecode):
                err = str(
                    f"Samplecode {upgrade_kit_req.samplecode} is deleted or not existed."
                )
                raise ValueError(err)
            if not await available_samplecode(upgrade_kit_req.samplecode):
                if await deactivated_samplecode(upgrade_kit_req.samplecode):
                    err = str(
                        f"Samplecode {upgrade_kit_req.samplecode} was deactivated."
                    )
                    raise ValueError(err)
                # elif await duplicated_samplecode(upgrade_kit_req.samplecode):
                #     err = str(
                #         f"Samplecode {upgrade_kit_req.samplecode} was already registered.")
                #     raise ValueError(err)

            # samplecode_detail = await get_samplecode(samplecode=upgrade_kit_req.samplecode)
            user_id = upgrade_kit_req.user_id
            user_info = None
            yob, dob = parse_dob(upgrade_kit_req.dob, upgrade_kit_req.yob)
            sample_collection_date = format_date(
                upgrade_kit_req.sample_collection_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            sample_receipt_date = format_date(
                upgrade_kit_req.sample_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            subject_id = upgrade_kit_req.subject_id
            res = await get_subject_by_id(subject_id=upgrade_kit_req.subject_id)
            if res:
                subject_data = res.to_dict()
            else:
                raise ValueError("Subject with metadata not found!")

            expected_report_release_date = None
            if upgrade_kit_req.expected_report_release_date is None:
                expected_report_release_date = sample_receipt_date.date() + timedelta(
                    days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                )
            else:
                expected_report_release_date = format_date(
                    upgrade_kit_req.expected_report_release_date,
                    DEFAULT_DATE_NO_TIME_ZONE,
                    validate_current_time=False,
                )
            if expected_report_release_date < sample_receipt_date.date():
                raise ValueError(
                    "expected report release date cannot be before sample receipt date"
                )
            if upgrade_kit_req.lab_receipt_date:
                lab_receipt_date = format_date(
                    upgrade_kit_req.lab_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                if lab_receipt_date < sample_receipt_date:
                    raise ValueError(
                        "lab receipt date cannot be before sample receipt date"
                    )
            else:
                lab_receipt_date = None

            # sample_data, err = await create_sample_v3(sample_req)

            sample = await get_sample_by_samplecode_v3(
                samplecode=upgrade_kit_req.samplecode
            )
            if not sample:
                raise ValueError("Cannot found sample!")
            sample_data = sample.to_dict()

            current_time = get_current_date_time_utc_7()
            size = 1
            i = 0
            while i < size:
                if version == 2:
                    code = generate_used_barcode_v2(
                        product_code=upgrade_kit_req.product_code,
                        product_name=upgrade_kit_req.product_name,
                    )
                elif version == 1:
                    code = generate_used_code(
                        product_code=upgrade_kit_req.product_code,
                        product_name=upgrade_kit_req.product_name,
                    )
                else:
                    raise HTTPException(
                        status_code=HTTP_423_LOCKED,
                        detail="New Barcode Format developing!",
                    )

                print("Barcode: ", code["barcode"])
                if not await get_code(code["barcode"]):
                    await save_code(code)
                    i += 1

            if not upgrade_kit_req.upgraded_at:
                raise HTTPException(
                    status_code=HTTP_422_UNPROCESSABLE_ENTITY,
                    detail="Expecting following value: Upgraded At ",
                )

            upgraded_at = format_date(
                upgrade_kit_req.upgraded_at,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )

            # CURRENT_STATUS
            status_data = init_status_v3()
            status = await Status.create(**status_data)

            kit_req = KitV3(
                id=uuid.uuid4(),
                barcode=code["barcode"],
                samplecode=upgrade_kit_req.samplecode,
                nickname=upgrade_kit_req.nickname,
                version=upgrade_kit_req.version,
                expected_report_release_date=expected_report_release_date,
                # actual_report_release_time <-- update when releasing
                customer_support_id=upgrade_kit_req.customer_support_id,
                customer_support_name=upgrade_kit_req.customer_support_name,
                free_of_charge=upgrade_kit_req.free_of_charge,
                promotion=upgrade_kit_req.promotion_id,
                is_priority=upgrade_kit_req.is_priority,
                # pdf_generation_date <- Nullable
                # is_card_issued <-- False by default
                product_code=upgrade_kit_req.product_code,  # before from barcode.detail
                product_name=upgrade_kit_req.product_name,  # before from barcode.detail
                product_type=upgrade_kit_req.product_type,
                current_status=status.status,
                current_status_id=status.id,
                workflow=config["WORKFLOW_STATUS"]["UPGRADE_KIT"],
                created_at=upgraded_at,
                updated_at=upgraded_at,
            )
            kit_data, err = await create_kit_v3(kit_req)
            data = {**subject_data, **sample_data, **kit_data}
            if err:
                raise ValueError(err)
            else:
                # -> Will Update Barcode right after it being generated
                # -> Send SQS mess to grant permission
                # await update_code(upgrade_kit_req.barcode, config['USED_CODE_STATE'])
                # if user_info:
                #     send_resouce_message_to_queue(user_info['data']["uuid"], upgrade_kit_req.barcode)
                return data, err

    except Exception as e:
        raise e


async def batch_update_kit_status_n_date_v3(
    kit_list: List[str], lab_receipt_date: str, status: NewKitStatus
):
    try:
        async with db.transaction() as tx:
            failed_update_kits = []
            updated_kits = []
            for kit_id in kit_list:
                print("Kit send-to-lab 1: ", kit_id)
                kit = await get_kit_v3(kit_id)
                print("Kit send-to-lab 2: ", kit.samplecode)
                sample = await get_sample_by_samplecode_v3(samplecode=kit.samplecode)
                print("Kit send-to-lab 3")
                if kit.current_status not in [
                    config["REGISTERED_KIT_STATUS"],
                    config["FAILED_LAB_CHECK_KIT_STATUS"],
                    config["MISSING_SAMPLE_KIT_STATUS"],
                ]:
                    failed_update_kits.append(kit_id)
                else:
                    await create_new_kit_status_v3(
                        id=kit_id,
                        kit_status=status,
                        lab_receipt_date=lab_receipt_date,
                        samplecode=kit.samplecode,
                    )
                    updated_kits.append(kit_id)
            return failed_update_kits, updated_kits
    except Exception as e:
        raise e


async def send_kit_info_to_lab_v3(kit_list: List[str], lab_receipt_date: str):
    try:
        return await batch_update_kit_status_n_date_v3(
            kit_list=kit_list,
            lab_receipt_date=lab_receipt_date,
            status=NewKitStatus(status=config["LAB_CHECK_KIT_STATUS"]),
        )
        # send notification to lab operators
    except Exception as e:
        raise e


async def create_new_kit_status_v3(
    id, kit_status, lab_receipt_date=None, samplecode=None
):
    # [TODO] This should be in a transaction
    if not isinstance(kit_status, dict):
        data = kit_status.dict()
    else:
        data = kit_status
    allowed_statuses = config["ALLOWED_KIT_STATUS"]
    if data["status"] not in allowed_statuses:
        err = f"Status '{data['status']}' is not an allowed request status ({allowed_statuses})"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        return None, (http_code, errs)
    kit = await get_kit_v3(id)
    if not kit:
        err = f"Kit with barcode {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        return None, (http_code, errs)

    sample = await get_sample_by_samplecode_v3(samplecode=samplecode)
    if not sample:
        err = f"Sample with samplecode {samplecode} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        return None, (http_code, errs)

    update_data = {**data, "barcode": id}

    saved_status, err = await save_status_v3(update_data)
    # send check booking message
    if err:
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        return None, (http_code, errs)
    # kit_info = kit.to_dict()
    current_time = get_current_date_time_utc_7()
    # new_kit_info = {
    #     **kit_info,
    #     'current_status': saved_status.status,
    #     'current_status_id': saved_status.id,
    #     'updated_time': current_time
    # }

    new_kit_info = kit

    new_kit_info.current_status = saved_status.status
    new_kit_info.current_status_id = saved_status.id
    new_kit_info.updated_time = current_time

    await update_kit_v3(new_kit_info)

    new_sample_info = sample
    new_sample_info.updated_time = current_time

    # Only need to update "lab_receipt_date" the 1st time "Register Kit/Sample" === Send "physical" sample to LAB
    if (new_kit_info.current_status == "LAB_CHECK") and (not sample.lab_receipt_date):
        new_sample_info.lab_receipt_date = format_date(
            lab_receipt_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
        await update_sample_v3(new_sample_info)

    new_kit_status, err = await get_status_v3(id)
    if err:
        await update_kit_v3(kit)
        await update_sample_v3(sample)
        err_msg = "Can not update kit status"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err_msg)
        return None, (http_code, errs)

    # SEND MESSAGE TO REPORT GENERATOR
    # send_index_resource_message_to_report_generator(kit_id=id)

    await send_check_booking_message(barcode=id)

    return new_kit_status, None


async def update_sample_info_w_samplecode_v3(
    update_kit_req: UpdateSampleV3,
):
    try:
        async with db.transaction() as tx:
            current_subject = await get_subject_by_id(update_kit_req.subject_id)

            yob, dob = parse_dob(update_kit_req.dob, update_kit_req.yob)
            sample_collection_date = format_date(
                update_kit_req.sample_collection_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            sample_receipt_date = format_date(
                update_kit_req.sample_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )

            user_id = None
            user_info = None
            logger.info(f"UpdateKitInfo - getting subject id: {current_subject}")
            if update_kit_req.validate_account:
                user_info = await get_user_information(update_kit_req.phone_number)
                user_id = user_info["data"]["uuid"]
            if user_id is not None:
                current_subject.user_id = user_id
            current_subject.full_name = update_kit_req.name
            current_subject.email = update_kit_req.email
            current_subject.phone_number = update_kit_req.phone_number
            current_subject.gender = update_kit_req.gender
            current_subject.dob = dob
            current_subject.yob = yob
            current_subject.legal_guardian = update_kit_req.legal_guardian
            current_subject.address = update_kit_req.address
            current_subject.validate_account = update_kit_req.validate_account
            current_subject.diagnosis = update_kit_req.diagnosis
            if update_kit_req.martyr_name is not None:
                current_subject.martyr_name = str(update_kit_req.martyr_name)
            if update_kit_req.martyr_relationships is not None:
                current_subject.martyr_relationships = update_kit_req.martyr_relationships
            _ = await update_subject(current_subject)

            current_sample = await get_sample_by_samplecode_v3(
                samplecode=update_kit_req.samplecode
            )

            source_detail = await get_source_detail_by_id(current_sample.source_id)
            source = None
            # [GS-6868] #
            if update_kit_req.freelancer_id:
                if (
                    source_detail["account_id"] != update_kit_req.account_id
                    or source_detail["nominator_id"] != update_kit_req.nominator_id
                    or source_detail["freelancer_id"] != update_kit_req.freelancer_id
                ):
                    current_sale_account_his = (
                        await get_current_sale_account_history_by_account_id(
                            update_kit_req.account_id
                        )
                    )
                    source = await create_source_by_staffs_n_account(
                        update_kit_req.nominator_id,
                        current_sale_account_his["id"],
                        update_kit_req.freelancer_id,
                    )
                    logger.info(f"Source: {source}")
                    pass
            else:
                if (
                    source_detail["account_id"] != update_kit_req.account_id
                    or source_detail["nominator_id"] != update_kit_req.nominator_id
                ):
                    current_sale_account_his = (
                        await get_current_sale_account_history_by_account_id(
                            update_kit_req.account_id
                        )
                    )
                    source = await create_source_by_staff_and_account(
                        update_kit_req.nominator_id, current_sale_account_his["id"]
                    )
            # --- 6868 --- #

            if update_kit_req.lab_receipt_date:
                lab_receipt_date = format_date(
                    update_kit_req.lab_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                if lab_receipt_date < sample_receipt_date:
                    raise ValueError(
                        "lab receipt date cannot be before sample receipt date"
                    )
            else:
                lab_receipt_date = None

            # sample_recollection
            sample_collection_time = (
                current_sample.sample_collection_time
                if not update_kit_req.sample_collection_time
                else update_kit_req.sample_collection_time
            )
            current_sample.subject_id = update_kit_req.subject_id
            current_sample.sample_collection_date = sample_collection_date
            current_sample.sample_recollection = (
                current_sample.sample_recollection
                if not update_kit_req.sample_recollection
                else update_kit_req.sample_recollection
            )
            current_sample.sample_collection_time = sample_collection_time
            current_sample.sample_receipt_date = sample_receipt_date
            current_sample.sample_collector_name = update_kit_req.sample_collector_name
            current_sample.sample_receiver_name = update_kit_req.sample_receiver_name
            current_sample.source_id = (
                source["id"] if source is not None else current_sample.source_id
            )
            # scan_status
            current_sample.lab_receipt_date = lab_receipt_date
            current_sample.sample_type = update_kit_req.sample_type
            current_sample.sponsor_id = update_kit_req.sponsor_id
            res = await update_sample_v3(current_sample)

            return res
    except Exception as e:
        raise e


async def update_kit_info_v3_w_barcode(
    update_kit_req: UpdateKitV3viaBarcode,
):
    try:
        async with db.transaction() as tx:
            current_kit = await get_kit_v3_w_barcode(barcode=update_kit_req.barcode)
            if current_kit.current_status == "DELETED":
                raise ValueError("Cannot update deleted kit")

            current_subject = await get_subject_by_id(update_kit_req.subject_id)

            yob, dob = parse_dob(update_kit_req.dob, update_kit_req.yob)
            sample_collection_date = format_date(
                update_kit_req.sample_collection_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            sample_receipt_date = format_date(
                update_kit_req.sample_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )

            user_id = None
            user_info = None
            logger.info(f"UpdateKitInfo 222 - getting subject id: {current_subject}")
            if update_kit_req.validate_account:
                user_info = await get_user_information(update_kit_req.phone_number)
                user_id = user_info["data"]["uuid"]
                print("Update with user_id: ", user_id)
            if user_id is not None:
                current_subject.user_id = user_id
            current_subject.full_name = update_kit_req.name
            current_subject.email = update_kit_req.email
            current_subject.phone_number = update_kit_req.phone_number
            current_subject.gender = update_kit_req.gender
            current_subject.dob = dob
            current_subject.yob = yob
            current_subject.legal_guardian = update_kit_req.legal_guardian
            current_subject.address = update_kit_req.address
            current_subject.validate_account = update_kit_req.validate_account
            current_subject.diagnosis = update_kit_req.diagnosis
            if update_kit_req.martyr_name is not None:
                current_subject.martyr_name = str(update_kit_req.martyr_name)
            if update_kit_req.martyr_relationships is not None:
                current_subject.martyr_relationships = update_kit_req.martyr_relationships
            _ = await update_subject(current_subject)

            current_sample = await get_sample_by_samplecode_v3(
                samplecode=update_kit_req.samplecode
            )

            source_detail = await get_source_detail_by_id(current_sample.source_id)
            source = None
            # [GS-6868] #
            if update_kit_req.freelancer_id:
                if (
                    source_detail["account_id"] != update_kit_req.account_id
                    or source_detail["nominator_id"] != update_kit_req.nominator_id
                    or source_detail["freelancer_id"] != update_kit_req.freelancer_id
                ):
                    current_sale_account_his = (
                        await get_current_sale_account_history_by_account_id(
                            update_kit_req.account_id
                        )
                    )
                    source = await create_source_by_staffs_n_account(
                        update_kit_req.nominator_id,
                        current_sale_account_his["id"],
                        update_kit_req.freelancer_id,
                    )
                    logger.info(f"Source: {source}")
                    pass
            else:
                if (
                    source_detail["account_id"] != update_kit_req.account_id
                    or source_detail["nominator_id"] != update_kit_req.nominator_id
                ):
                    current_sale_account_his = (
                        await get_current_sale_account_history_by_account_id(
                            update_kit_req.account_id
                        )
                    )
                    source = await create_source_by_staff_and_account(
                        update_kit_req.nominator_id, current_sale_account_his["id"]
                    )
            # --- 6868 --- #
            expected_report_release_date = None
            if update_kit_req.expected_report_release_date is None:
                expected_report_release_date = sample_receipt_date.date() + timedelta(
                    days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                )
            else:
                expected_report_release_date = format_date(
                    update_kit_req.expected_report_release_date,
                    DEFAULT_DATE_NO_TIME_ZONE,
                    validate_current_time=False,
                )
            if expected_report_release_date < sample_receipt_date.date():
                raise ValueError(
                    "expected report release date cannot be before sample receipt date"
                )

            if update_kit_req.lab_receipt_date:
                lab_receipt_date = format_date(
                    update_kit_req.lab_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                if lab_receipt_date < sample_receipt_date:
                    raise ValueError(
                        "lab receipt date cannot be before sample receipt date"
                    )
            else:
                lab_receipt_date = None

            # sample_recollection
            sample_collection_time = (
                current_sample.sample_collection_time
                if not update_kit_req.sample_collection_time
                else update_kit_req.sample_collection_time
            )
            current_sample.subject_id = update_kit_req.subject_id
            current_sample.sample_collection_date = sample_collection_date
            current_sample.sample_recollection = (
                current_sample.sample_recollection
                if not update_kit_req.sample_recollection
                else update_kit_req.sample_recollection
            )
            current_sample.sample_collection_time = sample_collection_time
            current_sample.sample_receipt_date = sample_receipt_date
            current_sample.sample_collector_name = update_kit_req.sample_collector_name
            current_sample.sample_receiver_name = update_kit_req.sample_receiver_name
            current_sample.source_id = (
                source["id"] if source is not None else current_sample.source_id
            )
            # scan_status
            current_sample.lab_receipt_date = lab_receipt_date
            current_sample.sample_type = update_kit_req.sample_type
            current_sample.sponsor_id = update_kit_req.sponsor_id

            _ = await update_sample_v3(current_sample)

            # nickname
            current_kit.version = (
                update_kit_req.version
                if update_kit_req.version
                else current_kit.version
            )
            current_kit.expected_report_release_date = expected_report_release_date
            current_kit.customer_support_id = (
                update_kit_req.customer_support_id
                if update_kit_req.customer_support_id
                else current_kit.customer_support_id
            )
            current_kit.customer_support_name = (
                update_kit_req.customer_support_name
                if update_kit_req.customer_support_name
                else current_kit.customer_support_name
            )
            current_kit.free_of_charge = update_kit_req.free_of_charge
            current_kit.promotion = update_kit_req.promotion_id
            current_kit.is_priority = update_kit_req.is_priority
            current_kit.note = update_kit_req.note
            # pdf_generation_date <- Nullable
            # is_card_issued <-- False by default
            product_detail = None
            logger.info(
                f"UpdateKitInfo - getting product detail: {update_kit_req.product_code}"
            )

            product_detail = get_product_by_product_code(update_kit_req.product_code)

            if product_detail is not None:
                if product_detail["name"] != update_kit_req.product_name:
                    raise ValueError(
                        f"Product's name {update_kit_req.product_name} doesnt match with the name from database {product_detail['name']}!"
                    )
                current_kit.product_code = product_detail["code"]
                current_kit.product_name = product_detail["name"]
            else:
                raise ValueError("Product Name and Product Code not found!")

            current_kit.product_type = (
                update_kit_req.product_type
                if update_kit_req.product_type
                else current_kit.product_type
            )

            logger.info(f"UpdateKitInfo - update kit information: {current_kit}")

            res = await update_kit_v3(current_kit)

            return res
    except Exception as e:
        raise e


async def update_kit_info_w_uuid_v3(
    update_kit_req: UpdateKitV3viaUUID,
):
    try:
        async with db.transaction() as tx:
            current_kit = await get_kit_v3_w_kit_uuid(kit_uuid=update_kit_req.kit_uuid)
            if current_kit.current_status == "DELETED":
                raise ValueError("Cannot update deleted kit")

            current_subject = await get_subject_by_id(update_kit_req.subject_id)

            yob, dob = parse_dob(update_kit_req.dob, update_kit_req.yob)


            user_id = None
            user_info = None
            logger.info(f"UpdateKitInfo - getting subject id: {current_subject}")
            if update_kit_req.validate_account:
                user_info = await get_user_information(update_kit_req.phone_number)
                user_id = user_info["data"]["uuid"]
            if user_id is not None:
                current_subject.user_id = user_id
            current_subject.full_name = update_kit_req.name or update_kit_req.full_name
            current_subject.email = update_kit_req.email
            current_subject.phone_number = update_kit_req.phone_number
            current_subject.gender = update_kit_req.gender
            current_subject.dob = dob
            current_subject.yob = yob
            current_subject.legal_guardian = update_kit_req.legal_guardian
            current_subject.address = update_kit_req.address
            current_subject.validate_account = update_kit_req.validate_account
            current_subject.diagnosis = update_kit_req.diagnosis
            if update_kit_req.martyr_name is not None:
                current_subject.martyr_name = str(update_kit_req.martyr_name)
            if update_kit_req.martyr_relationships is not None:
                current_subject.martyr_relationships = update_kit_req.martyr_relationships
            _ = await update_subject(current_subject)
            print("current_subject: ", current_subject)

            current_sample = await get_sample_by_samplecode_v3(
                samplecode=update_kit_req.samplecode
            )

            source_detail = await get_source_detail_by_id(current_sample.source_id)
            source = None
            # [GS-6868] #
            if update_kit_req.freelancer_id:
                if (
                    source_detail["account_id"] != update_kit_req.account_id
                    or source_detail["nominator_id"] != update_kit_req.nominator_id
                    or source_detail["freelancer_id"] != update_kit_req.freelancer_id
                ):
                    current_sale_account_his = (
                        await get_current_sale_account_history_by_account_id(
                            update_kit_req.account_id
                        )
                    )
                    source = await create_source_by_staffs_n_account(
                        update_kit_req.nominator_id,
                        current_sale_account_his["id"],
                        update_kit_req.freelancer_id,
                    )
                    logger.info(f"Source: {source}")
                    pass
            else:
                if (
                    source_detail["account_id"] != update_kit_req.account_id
                    or source_detail["nominator_id"] != update_kit_req.nominator_id
                ):
                    current_sale_account_his = (
                        await get_current_sale_account_history_by_account_id(
                            update_kit_req.account_id
                        )
                    )
                    source = await create_source_by_staff_and_account(
                        update_kit_req.nominator_id, current_sale_account_his["id"]
                    )
            # --- 6868 --- #

            if update_kit_req.sample_receipt_date:
                sample_receipt_date = format_date(
                    update_kit_req.sample_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                current_sample.sample_receipt_date = sample_receipt_date

            sample_receipt_date = current_sample.sample_receipt_date

            expected_report_release_date = None
            if sample_receipt_date is not None:
                if update_kit_req.expected_report_release_date is None:
                    expected_report_release_date = sample_receipt_date.date() + timedelta(
                        days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                    )
                else:
                    expected_report_release_date = format_date(
                        update_kit_req.expected_report_release_date,
                        DEFAULT_DATE_NO_TIME_ZONE,
                        validate_current_time=False,
                    )
                if expected_report_release_date < sample_receipt_date.date():
                    raise ValueError(
                        "expected report release date cannot be before sample receipt date"
                    )

            if update_kit_req.lab_receipt_date:
                lab_receipt_date = format_date(
                    update_kit_req.lab_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                if lab_receipt_date < sample_receipt_date:
                    raise ValueError(
                        "lab receipt date cannot be before sample receipt date"
                    )
            else:
                lab_receipt_date = None

            # sample_recollection
            sample_collection_time = (
                current_sample.sample_collection_time
                if not update_kit_req.sample_collection_time
                else update_kit_req.sample_collection_time
            )
            current_sample.subject_id = update_kit_req.subject_id

            if update_kit_req.sample_collection_date:
                sample_collection_date = format_date(
                    update_kit_req.sample_collection_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                current_sample.sample_collection_date = sample_collection_date

            current_sample.sample_recollection = (
                current_sample.sample_recollection
                if not update_kit_req.sample_recollection
                else update_kit_req.sample_recollection
            )
            current_sample.sample_collection_time = sample_collection_time

            current_sample.sample_collector_name = update_kit_req.sample_collector_name
            current_sample.sample_receiver_name = update_kit_req.sample_receiver_name
            current_sample.sponsor_id = update_kit_req.sponsor_id
            current_sample.source_id = (
                source["id"] if source is not None else current_sample.source_id
            )
            # scan_status
            current_sample.lab_receipt_date = lab_receipt_date
            current_sample.sample_type = update_kit_req.sample_type

            _ = await update_sample_v3(current_sample)

            # nickname
            current_kit.version = (
                update_kit_req.version
                if update_kit_req.version
                else current_kit.version
            )
            current_kit.expected_report_release_date = expected_report_release_date
            current_kit.customer_support_id = (
                update_kit_req.customer_support_id
                if update_kit_req.customer_support_id
                else current_kit.customer_support_id
            )
            current_kit.customer_support_name = (
                update_kit_req.customer_support_name
                if update_kit_req.customer_support_name
                else current_kit.customer_support_name
            )
            current_kit.free_of_charge = update_kit_req.free_of_charge
            current_kit.promotion = update_kit_req.promotion_id
            current_kit.is_priority = update_kit_req.is_priority
            current_kit.note = update_kit_req.note
            # pdf_generation_date <- Nullable
            # is_card_issued <-- False by default
            product_detail = None
            logger.info(
                f"UpdateKitInfo - getting product detail: {update_kit_req.product_code}"
            )

            product_detail = get_product_by_product_code(update_kit_req.product_code)

            if product_detail is not None:
                if product_detail["name"] != update_kit_req.product_name:
                    raise ValueError(
                        f"Product's name {update_kit_req.product_name} doesnt match with the name from database {product_detail['name']}!"
                    )
                current_kit.product_code = product_detail["code"]
                current_kit.product_name = product_detail["name"]
            else:
                raise ValueError("Product Name and Product Code not found!")

            current_kit.product_type = (
                update_kit_req.product_type
                if update_kit_req.product_type
                else current_kit.product_type
            )

            logger.info(f"UpdateKitInfo - update kit information: {current_kit}")

            res = await update_kit_v3(current_kit)

            current_time = get_current_date_time_utc_7()

            tracking_employee_dict = await get_tracking_employee_dict_by_name_n_unit_id(
                update_kit_req.sample_collector_name,
                update_kit_req.tracking_unit_id,
            )

            if not tracking_employee_dict:
                data = {
                    "hoTenNhanVien": update_kit_req.sample_collector_name,
                    "soDinhDanh": "REQUIRE_TO_UPDATE" + str(current_time),
                    "chucVu": "Chuyên Viên",
                    "unit_id": update_kit_req.tracking_unit_id,
                }
                tracking_employee_dict = await create_tracking_employee(data)

            tracking_collection = await get_tracking_collection_by_samplecode(
                samplecode=update_kit_req.samplecode
                )
            tracking_collection_update = tracking_collection.to_dict()

            tracking_collection_update["nhanVienLayMau_id"] = tracking_employee_dict["id"]

            await update_tracking_collection(tracking_collection, tracking_collection_update)

            return res
    except Exception as e:
        raise e


def group_kits_subject_by_sample_collection_time_v3(kits_list: []):
    pass


async def register_identity_card_v3(register_req: addIdentityCardRegistration):
    async with db.transaction() as tx:
        is_existed_cccd = await existed_identity_card(register_req.identifier_code)
        #   IDENTITY_CARD
        if is_existed_cccd:
            err = str(
                f"CCCD/Số định danh  {register_req.identifier_code} đã tồn tại trong hệ thống."
            )
            raise ValueError(err)
        else:
            martyr_name = None
            if register_req.martyr_name is not None:
                martyr_name = str(register_req.martyr_name)
            yob, dob = parse_dob(register_req.dob, None)

            subject_id = None
            subject = Subject(
                id=uuid.uuid4(),
                user_id=None,
                full_name=register_req.name,
                email=None,
                phone_number=None,
                diagnosis=None,
                address=None,
                dob=dob,
                gender=register_req.gender,
                yob=yob,
                legal_guardian=None,
                martyr_name=martyr_name,
                martyr_relationships=register_req.martyr_relationships,
                validate_account=False,
                identifier_code=register_req.identifier_code,
                require_registration=True,
                created_at=get_current_date_time_utc_7(),
                updated_at=get_current_date_time_utc_7(),
            )
            subject_data = await create_subject(subject=subject)
            subject_id = subject_data["id"]

            identity_card = IdentityCard(
                id=uuid.uuid4(),
                identifier_code=register_req.identifier_code,
                full_name=register_req.name,
                dob=dob,
                gender=register_req.gender,
                nationality=register_req.nationality,
                origin=register_req.origin,
                residence=register_req.residence,
                avatar_image=register_req.avatar_image,
                fingerprint_image=register_req.fingerprint_image,
                ethnic=register_req.ethnic,
                email=None,
                phone_number=None,
                customer_support_id=register_req.customer_support_id,
                customer_support_name=register_req.customer_support_name,
                manual_input=register_req.manual_input,
                created_at=get_current_date_time_utc_7(),
                updated_at=get_current_date_time_utc_7(),
            )

            identity_card_data = await create_identity_card(identity_card=identity_card)

            return identity_card_data

    pass


async def update_onboarding_identity_card_v3(
    register_kit_req: onboardIdentityCardRegistration,
):
    async with db.transaction() as tx:
        # Check SampleID / Samplecode
        if not await existed_samplecode(register_kit_req.samplecode):
            err = str(
                f"Samplecode {register_kit_req.samplecode} is deleted or not existed."
            )
            raise ValueError(err)
        if not await available_samplecode(register_kit_req.samplecode):
            if await deactivated_samplecode(register_kit_req.samplecode):
                err = str(f"Samplecode {register_kit_req.samplecode} was deactivated.")
                raise ValueError(err)
            elif await duplicated_samplecode(register_kit_req.samplecode):
                err = str(
                    f"Samplecode {register_kit_req.samplecode} was already registered."
                )
                raise ValueError(err)

        else:
            # get identity_card
            identity_card = await get_identity_card_by_id(
                identifier_code=register_kit_req.identifier_code
            )
            identity_card.email = register_kit_req.email
            identity_card.phone_number = register_kit_req.phone_number
            identity_card.nationality = register_kit_req.nationality
            identity_card.residence = register_kit_req.residence
            identity_card.origin = register_kit_req.origin
            identity_card.ethnic = register_kit_req.ethnic
            _ = await update_identity_card(identity_card)

            if register_kit_req.sample_recollection:
                err = str(
                    f"Using recollection flow for CCCD/Identity Card  {register_kit_req.identifier_code} instead!"
                )
                raise ValueError(err)

            user_id = None
            user_info = None

            if register_kit_req.validate_account:
                user_info = await get_user_information(register_kit_req.phone_number)
                user_id = user_info["data"]["uuid"]

            sample_collection_date = format_date(
                register_kit_req.sample_collection_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
            if register_kit_req.sample_receipt_date:
                sample_receipt_date = format_date(
                    register_kit_req.sample_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
            else:
                sample_receipt_date = None

            # email: Optional[str] = None
            # phone_number: Optional[str] = None
            # diagnosis: Optional[str]
            # legal_guardian: Optional[str] = None
            # identifier_code: Optional[str] = None
            # validate_account: bool
            subject = await get_subject_by_id_card(
                identifier_code=register_kit_req.identifier_code
            )
            subject.email = register_kit_req.email
            subject.phone_number = register_kit_req.phone_number
            subject.diagnosis = register_kit_req.diagnosis
            subject.legal_guardian = register_kit_req.legal_guardian
            subject.validate_account = register_kit_req.validate_account
            subject.require_registration = False
            if register_kit_req.martyr_name is not None:
                subject.martyr_name = str(register_kit_req.martyr_name)
            if register_kit_req.martyr_relationships is not None:
                subject.martyr_relationships = register_kit_req.martyr_relationships
            _ = await update_subject(subject=subject)
            subject_data = subject.to_dict()
            subject_id = subject_data["id"]

            ## ACCOUNT & SOURCE
            current_sale_account_his = (
                await get_current_sale_account_history_by_account_id(
                    register_kit_req.account_id
                )
            )
            if register_kit_req.freelancer_id:
                freelancer = await get_staff(register_kit_req.freelancer_id)
                if freelancer.role != "FREELANCER":
                    err = "Staff role is not FREELANCER!"
                    raise ValueError(err)
                source = await create_source_by_staffs_n_account(
                    register_kit_req.nominator_id,
                    current_sale_account_his["id"],
                    register_kit_req.freelancer_id,
                )
            else:
                source = await create_source_by_staff_and_account(
                    register_kit_req.nominator_id, current_sale_account_his["id"]
                )

            # LAB DATE
            expected_report_release_date = None
            lab_receipt_date = None
            if sample_receipt_date is not None:
                if register_kit_req.expected_report_release_date is None:
                    expected_report_release_date = (
                        sample_receipt_date.date()
                        + timedelta(
                            days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                        )
                    )
                else:
                    expected_report_release_date = format_date(
                        register_kit_req.expected_report_release_date,
                        DEFAULT_DATE_NO_TIME_ZONE,
                        validate_current_time=False,
                    )
                if expected_report_release_date < sample_receipt_date.date():
                    raise ValueError(
                        "expected report release date cannot be before sample receipt date"
                    )

                if register_kit_req.lab_receipt_date:
                    lab_receipt_date = format_date(
                        register_kit_req.lab_receipt_date,
                        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                        validate_current_time=False,
                    )
                    if lab_receipt_date < sample_receipt_date:
                        raise ValueError(
                            "lab receipt date cannot be before sample receipt date"
                        )
                else:
                    lab_receipt_date = None

            # RECOLLECTION
            sample_res = await get_sample_v3_via(subject_id=subject_id)
            total_sample = len(sample_res)
            print("total_sample: ", total_sample)
            if total_sample:
                if register_kit_req.sample_recollection != True:
                    raise ValueError(
                        "Đối tượng đã được thu mẫu. Vui lòng thực hiện luồng thu lại mẫu."
                    )

                sample_collection_time = total_sample + 1
            else:
                if register_kit_req.sample_recollection != False:
                    raise ValueError("Recollection should be set to False")

                sample_collection_time = 1

            # SAMPLE
            # CURRENT_STATUS
            status_data = init_status_v3()
            status = await Status.create(**status_data)
            std_sample_collector_name = standardize_full_name(
                register_kit_req.sample_collector_name
            )
            std_sample_receiver_name = standardize_full_name(
                register_kit_req.sample_receiver_name
            )
            sample_req = SampleV3(
                id=uuid.uuid4(),
                samplecode=register_kit_req.samplecode,
                subject_id=subject_id,
                sample_collection_date=sample_collection_date,
                sample_recollection=register_kit_req.sample_recollection,
                sample_collection_time=sample_collection_time,
                sample_receipt_date=sample_receipt_date,
                sample_collector_name=std_sample_collector_name,
                sample_receiver_name=std_sample_receiver_name,
                source_id=source["id"],
                # scan_status
                # lab_check_date <-- lab_sample update this
                lab_receipt_date=lab_receipt_date,
                sample_type=register_kit_req.sample_type,
            )
            sample_data, err = await create_sample_v3(sample_req)
            current_time = get_current_date_time_utc_7()
            print("Successfully Created Sample!")
            product_detail = get_product_by_product_code(register_kit_req.product_code)

            if product_detail is not None:
                if product_detail["name"] != register_kit_req.product_name:
                    raise ValueError(
                        f"Product's name {register_kit_req.product_name} doesnt match with the name from database {product_detail['name']}!"
                    )
                product_code = product_detail["code"]
                product_name = product_detail["name"]
            else:
                raise ValueError("Product Name and Product Code not found!")

            # KIT
            kit_req = KitV3(
                id=uuid.uuid4(),
                # barcode=register_kit_req.barcode, <--- generated when scan_status==2
                samplecode=register_kit_req.samplecode,
                nickname=register_kit_req.nickname,
                version=register_kit_req.version,
                expected_report_release_date=expected_report_release_date,
                # actual_report_release_time <-- update when releasing
                customer_support_id=register_kit_req.customer_support_id,
                customer_support_name=register_kit_req.customer_support_name,
                free_of_charge=register_kit_req.free_of_charge,
                promotion=register_kit_req.promotion_id,
                is_priority=register_kit_req.is_priority,
                # pdf_generation_date <- Nullable
                # is_card_issued <-- False by default
                product_code=product_code,  # before from barcode.detail
                product_name=product_name,  # before from barcode.detail
                product_type=register_kit_req.product_type,
                note=register_kit_req.note,
                current_status=status.status,
                current_status_id=status.id,
                workflow=config["WORKFLOW_STATUS"]["REGISTER_KIT_W_CCCD"],
                created_at=current_time,
                updated_at=current_time,
            )
            kit_data, err = await create_kit_v3(kit_req)
            print("Successfully Created Kit!")

            data = {**subject_data, **sample_data, **kit_data}

            # TRACKING
            tracking_dict = {
                "samplecode": register_kit_req.samplecode,
                "status": "Thu nhận mẫu",
                "identifier_code": register_kit_req.identifier_code,
            }

            tracking = await create_tracking(tracking_dict)
            # Add tracking_collection
            tracking_unit = await get_tracking_unit_by_id(
                register_kit_req.tracking_unit_id
            )
            std_tracking_employee_name = std_sample_collector_name
            tracking_employee_dict = await get_tracking_employee_dict_by_name_n_unit_id(
                std_tracking_employee_name, register_kit_req.tracking_unit_id
            )
            if not tracking_employee_dict:
                data = {
                    "hoTenNhanVien": std_tracking_employee_name,
                    "soDinhDanh": "REQUIRE_TO_UPDATE" + str(current_time),
                    "chucVu": "Chuyên Viên",
                    "unit_id": register_kit_req.tracking_unit_id,
                }
                tracking_employee_dict = await create_tracking_employee(data)

            tracking_collection_dict = {
                "tracking_id": tracking.get("id"),
                "maThuNhan": register_kit_req.samplecode,
                "donViThuNhanMau_id": register_kit_req.tracking_unit_id,
                "noiThuThapMau": tracking_unit.gs_area_code,
                "ngayGioThuThapMau": sample_collection_date,
                "nhanVienLayMau_id": tracking_employee_dict.get("id"),
                "nhanVienGhiHoSo_id": tracking_employee_dict.get("id"),
            }
            tracking_collection = await create_tracking_collection(
                tracking_collection_dict
            )

            data = {
                **data,
                "tracking": tracking,
                "tracking_collection": tracking_collection,
            }
            if err:
                raise ValueError(err)
            else:
                # -> Will Update Barcode right after it being generated
                # -> Send SQS mess to grant permission
                # await update_code(register_kit_req.barcode, config['USED_CODE_STATE'])
                # if user_info:
                #     send_resouce_message_to_queue(user_info['data']["uuid"], register_kit_req.barcode)
                return data, err


async def register_kit_service_w_cccd_v3(
    register_kit_req: OperatorRegisterKitWithCCCDV4,
):
    # validate user information if specified
    """
    Register new kits:
    [+] SampleID: Available
    """
    try:
        print(register_kit_req.martyr_name)
        print(register_kit_req.martyr_relationships)
        async with db.transaction() as tx:
            # Check SampleID / Samplecode
            if not await existed_samplecode(register_kit_req.samplecode):
                err = str(
                    f"Samplecode {register_kit_req.samplecode} is deleted or not existed."
                )
                raise ValueError(err)
            if not await available_samplecode(register_kit_req.samplecode):
                if await deactivated_samplecode(register_kit_req.samplecode):
                    err = str(
                        f"Samplecode {register_kit_req.samplecode} was deactivated."
                    )
                    raise ValueError(err)
                elif await duplicated_samplecode(register_kit_req.samplecode):
                    err = str(
                        f"Samplecode {register_kit_req.samplecode} was already registered."
                    )
                    raise ValueError(err)

            else:
                is_existed_cccd = await existed_identity_card(
                    register_kit_req.identifier_code
                )
                #   IDENTITY_CARD
                if is_existed_cccd and not register_kit_req.sample_recollection:
                    err = str(
                        f"CCCD/Identity Card  {register_kit_req.identifier_code} is already existed."
                    )
                    raise ValueError(err)

                # FORCE TO VALIDATE ACCOUNT
                elif is_existed_cccd and register_kit_req.sample_recollection:
                    err = str(
                        f"Using recollection flow for CCCD/Identity Card  {register_kit_req.identifier_code} instead!"
                    )
                    raise ValueError(err)

                else:
                    # samplecode_detail = await get_samplecode(samplecode=register_kit_req.samplecode)
                    print("Samplecode: ", register_kit_req.samplecode)
                    user_id = None
                    user_info = None

                    # if not register_kit_req.validate_account:
                    #     err = str(
                    #         f"Require to validate account by phone_number")
                    #     raise ValueError(err)

                    if register_kit_req.validate_account:
                        user_info = await get_user_information(
                            register_kit_req.phone_number
                        )
                        user_id = user_info["data"]["uuid"]
                    print("user_id: ", user_id)
                    yob, dob = parse_dob(register_kit_req.dob, register_kit_req.yob)
                    sample_collection_date = format_date(
                        register_kit_req.sample_collection_date,
                        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                        validate_current_time=False,
                    )
                    if register_kit_req.sample_receipt_date:
                        sample_receipt_date = format_date(
                            register_kit_req.sample_receipt_date,
                            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                            validate_current_time=False,
                        )
                    else:
                        sample_receipt_date = None

                    subject_id = None
                    res = await get_subject(
                        user_id=user_id,
                        full_name=register_kit_req.name,
                        dob=dob,
                        yob=yob,
                        gender=register_kit_req.gender,
                        identifier_code=register_kit_req.identifier_code,
                    )
                    if len(res) > 0:
                        subject_data = res[0].to_dict()
                        subject_id = subject_data["id"]
                        err = "Phone number's already been used!"
                        raise ValueError(err)

                    else:
                        martyr_name = None
                        if register_kit_req.martyr_name is not None:
                            martyr_name = str(register_kit_req.martyr_name)
                        # create sample meta
                        print("Create a new subject!")
                        subject = Subject(
                            id=uuid.uuid4(),
                            user_id=user_id,
                            full_name=register_kit_req.name,
                            email=register_kit_req.email,
                            phone_number=register_kit_req.phone_number,
                            diagnosis=register_kit_req.diagnosis,
                            address=register_kit_req.address,
                            dob=dob,
                            gender=register_kit_req.gender,
                            yob=yob,
                            legal_guardian=register_kit_req.legal_guardian,
                            martyr_name=martyr_name,
                            martyr_relationships=register_kit_req.martyr_relationships,
                            validate_account=register_kit_req.validate_account,
                            identifier_code=register_kit_req.identifier_code,
                            guardian_name=register_kit_req.guardian_name,
                            guardian_gender=register_kit_req.guardian_gender,
                            guardian_phone_number=register_kit_req.guardian_phone_number,
                            guardian_identifier_code=register_kit_req.guardian_identifier_code,
                            created_at=get_current_date_time_utc_7(),
                            updated_at=get_current_date_time_utc_7(),
                        )
                        subject_data = await create_subject(subject=subject)
                        subject_id = subject_data["id"]

                        identity_card = IdentityCard(
                            id=uuid.uuid4(),
                            identifier_code=register_kit_req.identifier_code,
                            full_name=register_kit_req.name,
                            dob=dob,
                            gender=register_kit_req.gender,
                            nationality=register_kit_req.nationality,
                            origin=register_kit_req.origin,
                            residence=register_kit_req.residence,
                            avatar_image=register_kit_req.avatar_image,
                            fingerprint_image=register_kit_req.fingerprint_image,
                            ethnic=register_kit_req.ethnic,
                            email=register_kit_req.email,
                            phone_number=register_kit_req.phone_number,
                            customer_support_id=register_kit_req.customer_support_id,
                            customer_support_name=register_kit_req.customer_support_name,
                            created_at=get_current_date_time_utc_7(),
                            updated_at=get_current_date_time_utc_7(),
                            manual_input=register_kit_req.manual_input,
                        )

                        identity_card_data = await create_identity_card(
                            identity_card=identity_card
                        )

                    print("subject_id: ", subject_id)
                    current_sale_account_his = (
                        await get_current_sale_account_history_by_account_id(
                            register_kit_req.account_id
                        )
                    )
                    print("curr_sale_history: ", current_sale_account_his["id"])
                    # [GS-6868] #
                    if register_kit_req.freelancer_id:
                        freelancer = await get_staff(register_kit_req.freelancer_id)
                        if freelancer.role != "FREELANCER":
                            err = "Staff role is not FREELANCER!"
                            raise ValueError(err)
                        source = await create_source_by_staffs_n_account(
                            register_kit_req.nominator_id,
                            current_sale_account_his["id"],
                            register_kit_req.freelancer_id,
                        )
                    else:
                        source = await create_source_by_staff_and_account(
                            register_kit_req.nominator_id,
                            current_sale_account_his["id"],
                        )
                    # ---GS-6868---#
                    expected_report_release_date = None
                    lab_receipt_date = None
                    if sample_receipt_date is not None:
                        if register_kit_req.expected_report_release_date is None:
                            expected_report_release_date = (
                                sample_receipt_date.date()
                                + timedelta(
                                    days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                                )
                            )
                        else:
                            expected_report_release_date = format_date(
                                register_kit_req.expected_report_release_date,
                                DEFAULT_DATE_NO_TIME_ZONE,
                                validate_current_time=False,
                            )
                        if expected_report_release_date < sample_receipt_date.date():
                            raise ValueError(
                                "expected report release date cannot be before sample receipt date"
                            )

                        if register_kit_req.lab_receipt_date:
                            lab_receipt_date = format_date(
                                register_kit_req.lab_receipt_date,
                                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                                validate_current_time=False,
                            )
                            if lab_receipt_date < sample_receipt_date:
                                raise ValueError(
                                    "lab receipt date cannot be before sample receipt date"
                                )
                        else:
                            lab_receipt_date = None

                    # sample_collection_time = 1 if not register_kit_req.sample_collection_time else register_kit_req.sample_collection_time

                    # Get existed sample with subject_id
                    sample_res = await get_sample_v3_via(subject_id=subject_id)
                    total_sample = len(sample_res)
                    print("total_sample: ", total_sample)
                    if total_sample:
                        if register_kit_req.sample_recollection != True:
                            raise ValueError(
                                "Đối tượng đã được thu mẫu. Vui lòng thực hiện luồng thu lại mẫu."
                            )

                        sample_collection_time = total_sample + 1
                    else:
                        if register_kit_req.sample_recollection != False:
                            raise ValueError("Recollection should be set to False")

                        sample_collection_time = 1

                    # CURRENT_STATUS
                    status_data = init_status_v3()
                    status = await Status.create(**status_data)
                    std_sample_collector_name = standardize_full_name(
                        register_kit_req.sample_collector_name
                    ) if register_kit_req.sample_collector_name else None
                    std_sample_receiver_name = standardize_full_name(
                        register_kit_req.sample_receiver_name
                    ) if register_kit_req.sample_receiver_name else None
                    sample_req = SampleV3(
                        id=uuid.uuid4(),
                        samplecode=register_kit_req.samplecode,
                        subject_id=subject_id,
                        sample_collection_date=sample_collection_date,
                        sample_recollection=register_kit_req.sample_recollection,
                        sample_collection_time=sample_collection_time,
                        sample_receipt_date=sample_receipt_date,
                        sample_collector_name=std_sample_collector_name,
                        sample_receiver_name=std_sample_receiver_name,
                        source_id=source["id"],
                        # scan_status
                        # lab_check_date <-- lab_sample update this
                        lab_receipt_date=lab_receipt_date,
                        sample_type=register_kit_req.sample_type,
                        sponsor_id=register_kit_req.sponsor_id,
                    )
                    sample_data, err = await create_sample_v3(sample_req)
                    current_time = get_current_date_time_utc_7()
                    print("Successfully Created Sample!")
                    kit_data_list = []
                    for product in register_kit_req.products:
                        product_detail = get_product_by_product_code(
                            product.product_code
                        )

                        if product_detail is not None:
                            if product_detail["name"] != product.product_name:
                                raise ValueError(
                                    f"Product's name {product.product_name} doesnt match with the name from database {product_detail['name']}!"
                                )
                            product_code = product_detail["code"]
                            product_name = product_detail["name"]
                        else:
                            raise ValueError("Product Name and Product Code not found!")
                        kit_req = KitV3(
                            id=uuid.uuid4(),
                            # barcode=register_kit_req.barcode, <--- generated when scan_status==2
                            samplecode=register_kit_req.samplecode,
                            nickname=register_kit_req.nickname,
                            version=register_kit_req.version,
                            expected_report_release_date=expected_report_release_date,
                            # actual_report_release_time <-- update when releasing
                            customer_support_id=register_kit_req.customer_support_id,
                            customer_support_name=register_kit_req.customer_support_name,
                            free_of_charge=register_kit_req.free_of_charge,
                            promotion=register_kit_req.promotion_id,
                            is_priority=register_kit_req.is_priority,
                            # pdf_generation_date <- Nullable
                            # is_card_issued <-- False by default
                            product_code=product_code,  # before from barcode.detail
                            product_name=product_name,  # before from barcode.detail
                            product_type=product.product_type,
                            note=register_kit_req.note,
                            current_status=status.status,
                            current_status_id=status.id,
                            workflow=config["WORKFLOW_STATUS"]["REGISTER_KIT_W_CCCD"],
                            created_at=current_time,
                            updated_at=current_time,
                        )
                        kit_data, err = await create_kit_v3(kit_req)
                        print("Successfully Created Kit!")
                        kit_data_list.append(kit_data)

                    data = {**subject_data, **sample_data, "kit_datas": kit_data_list}
                    # Add tracking
                    tracking_dict = {
                        "samplecode": register_kit_req.samplecode,
                        "status": "Thu nhận mẫu",
                        "identifier_code": register_kit_req.identifier_code,
                    }

                    tracking = await create_tracking(tracking_dict)
                    # Add tracking_collection
                    tracking_unit = await get_tracking_unit_by_id(
                        register_kit_req.tracking_unit_id
                    )
                    std_tracking_employee_name = std_sample_collector_name
                    tracking_employee_dict = (
                        await get_tracking_employee_dict_by_name_n_unit_id(
                            std_tracking_employee_name,
                            register_kit_req.tracking_unit_id,
                        )
                    )
                    if not tracking_employee_dict:
                        data = {
                            "hoTenNhanVien": std_tracking_employee_name,
                            "soDinhDanh": "REQUIRE_TO_UPDATE" + str(current_time),
                            "chucVu": "Chuyên Viên",
                            "unit_id": register_kit_req.tracking_unit_id,
                        }
                        tracking_employee_dict = await create_tracking_employee(data)

                    tracking_collection_dict = {
                        "tracking_id": tracking.get("id"),
                        "maThuNhan": register_kit_req.samplecode,
                        "donViThuNhanMau_id": register_kit_req.tracking_unit_id,
                        "noiThuThapMau": tracking_unit.gs_area_code,
                        "ngayGioThuThapMau": sample_collection_date,
                        "nhanVienLayMau_id": tracking_employee_dict.get("id"),
                        "nhanVienGhiHoSo_id": tracking_employee_dict.get("id"),
                    }
                    tracking_collection = await create_tracking_collection(
                        tracking_collection_dict
                    )

                    data = {
                        **data,
                        "tracking": tracking,
                        "tracking_collection": tracking_collection,
                    }
                    if err:
                        raise ValueError(err)
                    else:
                        # -> Will Update Barcode right after it being generated
                        # -> Send SQS mess to grant permission
                        # await update_code(register_kit_req.barcode, config['USED_CODE_STATE'])
                        # if user_info:
                        #     send_resouce_message_to_queue(user_info['data']["uuid"], register_kit_req.barcode)
                        return data, err

    except Exception as e:
        logger.exception(e)
        raise e


@validate_samplecode
async def create_sample_kit_with_cccd_and_samplecode_v3(
    req_body: AddSamplesToCollectSession,
):
    async with db.transaction() as tx:
        # no identity_card update
        subject = await get_subject_by_id_card(identifier_code=req_body.identifier_code)
        subject.require_registration = False
        _ = await update_subject(subject=subject)
        subject_data = subject.to_dict()
        subject_id = subject.id

        if req_body.sample_recollection:
            err = str(
                f"Using recollection flow for CCCD/Identity Card  {req_body.identifier_code} instead!"
            )
            raise ValueError(err)

        if req_body.validate_account:
            err = str(
                f"Using validate account flow for CCCD/Identity Card  {req_body.identifier_code} instead!"
            )
            raise ValueError(err)

        current_time = get_current_date_time_utc_7()
        sample_collection_date_str = current_time.strftime(
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR
        )  # Format to string
        sample_collection_date = datetime.strptime(
            sample_collection_date_str, "%Y-%m-%dT%H:%M:%S"
        )  # Convert back to datetime

        if req_body.sample_receipt_date:
            sample_receipt_date = format_date(
                req_body.sample_receipt_date,
                DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                validate_current_time=False,
            )
        else:
            sample_receipt_date = None

        # no subject update

        # account & source
        if req_body.account_id:
            current_sale_account_his = (
                await get_current_sale_account_history_by_account_id(
                    req_body.account_id
                )
            )
        else:
            account = await get_account_by_name(req_body.account_name)
            if not account:
                err = f"Account with name {req_body.account_name} not found"
                http_code = HTTP_404_NOT_FOUND
                errs = failure_response(err)
                raise HTTPException(status_code=http_code, detail=errs)
            current_sale_account_his = (
                await get_current_sale_account_history_by_account_id(account.id)
            )

        if req_body.freelancer_id:
            freelancer = await get_staff(req_body.freelancer_id)
            if freelancer.role != "FREELANCER":
                err = "Staff role is not FREELANCER!"
                raise ValueError(err)
            source = await create_source_by_staffs_n_account(
                req_body.nominator_id,
                current_sale_account_his["id"],
                req_body.freelancer_id,
            )
        else:
            source = await create_source_by_staff_and_account(
                req_body.nominator_id, current_sale_account_his["id"]
            )

        # LAB DATE
        expected_report_release_date = None
        lab_receipt_date = None

        if sample_receipt_date is not None:
            if req_body.expected_report_release_date is None:
                expected_report_release_date = sample_receipt_date.date() + timedelta(
                    days=int(config["EXPECTED_SAMPLE_REPORT_DELTATIME"])
                )
            else:
                expected_report_release_date = format_date(
                    req_body.expected_report_release_date,
                    DEFAULT_DATE_NO_TIME_ZONE,
                    validate_current_time=False,
                )
            if expected_report_release_date < sample_receipt_date.date():
                raise ValueError(
                    "expected report release date cannot be before sample receipt date"
                )

            if req_body.lab_receipt_date:
                lab_receipt_date = format_date(
                    req_body.lab_receipt_date,
                    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
                    validate_current_time=False,
                )
                if lab_receipt_date < sample_receipt_date:
                    raise ValueError(
                        "lab receipt date cannot be before sample receipt date"
                    )
            else:
                lab_receipt_date = None

        # RECOLLECTION
        sample_res = await get_sample_v3_via(subject_id=subject_id)
        total_sample = len(sample_res)

        if total_sample:
            if req_body.sample_recollection != True:
                raise ValueError(
                    "Đối tượng đã được thu mẫu. Vui lòng thực hiện luồng thu lại mẫu."
                )

            sample_collection_time = total_sample + 1
        else:
            if req_body.sample_recollection != False:
                raise ValueError("Recollection should be set to False")

            sample_collection_time = 1

        # SAMPLE
        # CURRENT_STATUS
        status_data = init_status_v3()
        status = await Status.create(**status_data)
        std_sample_collector_name = (
            standardize_full_name(req_body.sample_collector_name)
            if req_body.sample_collector_name
            else None
        )
        std_sample_receiver_name = (
            standardize_full_name(req_body.sample_receiver_name)
            if req_body.sample_receiver_name
            else None
        )
        sample_req = SampleV3(
            id=uuid.uuid4(),
            samplecode=req_body.samplecode,
            subject_id=subject_id,
            sample_collection_date=sample_collection_date,
            sample_recollection=req_body.sample_recollection,
            sample_collection_time=sample_collection_time,
            sample_receipt_date=sample_receipt_date,
            sample_collector_name=std_sample_collector_name,
            sample_receiver_name=std_sample_receiver_name,
            source_id=source["id"],
            # scan_status
            # lab_check_date <-- lab_sample update this
            lab_receipt_date=lab_receipt_date,
            sample_type=req_body.sample_type,
        )
        sample_data, err = await create_sample_v3(sample_req)
        current_time = get_current_date_time_utc_7()
        product_detail = get_product_by_product_code(req_body.product_code)

        if product_detail is not None:
            if product_detail["name"] != req_body.product_name:
                raise ValueError(
                    f"Product's name {req_body.product_name} doesnt match with the name from database {product_detail['name']}!"
                )
            product_code = product_detail["code"]
            product_name = product_detail["name"]
        else:
            raise ValueError("Product Name and Product Code not found!")

        # KIT
        kit_req = KitV3(
            id=uuid.uuid4(),
            # barcode=register_kit_req.barcode, <--- generated when scan_status==2
            samplecode=req_body.samplecode,
            nickname=req_body.nickname,
            version=req_body.version,
            expected_report_release_date=expected_report_release_date,
            # actual_report_release_time <-- update when releasing
            customer_support_id=req_body.customer_support_id,
            customer_support_name=req_body.customer_support_name,
            free_of_charge=req_body.free_of_charge,
            promotion=req_body.promotion_id,
            is_priority=req_body.is_priority,
            # pdf_generation_date <- Nullable
            # is_card_issued <-- False by default
            product_code=product_code,  # before from barcode.detail
            product_name=product_name,  # before from barcode.detail
            product_type=req_body.product_type,
            note=req_body.note,
            current_status=status.status,
            current_status_id=status.id,
            workflow=config["WORKFLOW_STATUS"]["REGISTER_KIT_W_CCCD"],
            created_at=current_time,
            updated_at=current_time,
        )
        kit_data, err = await create_kit_v3(kit_req)
        print("Successfully Created Kit!")

        data = {**subject_data, **sample_data, **kit_data}

        # TRACKING
        tracking_dict = {
            "samplecode": req_body.samplecode,
            "status": "Thu nhận mẫu",
            "identifier_code": req_body.identifier_code,
        }

        tracking = await create_tracking(tracking_dict)
        # Add tracking_collection
        tracking_unit = await get_tracking_unit_by_id(req_body.tracking_unit_id)
        std_tracking_employee_name = std_sample_collector_name
        tracking_employee_dict = await get_tracking_employee_dict_by_name_n_unit_id(
            std_tracking_employee_name, req_body.tracking_unit_id
        )
        if not tracking_employee_dict:
            data = {
                "hoTenNhanVien": std_tracking_employee_name,
                "soDinhDanh": "REQUIRE_TO_UPDATE" + str(current_time),
                "chucVu": "Chuyên Viên",
                "unit_id": req_body.tracking_unit_id,
            }
            tracking_employee_dict = await create_tracking_employee(data)

        tracking_collection_dict = {
            "tracking_id": tracking.get("id"),
            "maThuNhan": req_body.samplecode,
            "donViThuNhanMau_id": req_body.tracking_unit_id,
            "noiThuThapMau": tracking_unit.gs_area_code,
            "ngayGioThuThapMau": sample_collection_date,
            "nhanVienLayMau_id": tracking_employee_dict.get("id"),
            "nhanVienGhiHoSo_id": tracking_employee_dict.get("id"),
        }
        tracking_collection = await create_tracking_collection(tracking_collection_dict)

        data = {
            **data,
            "tracking": tracking,
            "tracking_collection": tracking_collection,
        }

        if err:
            raise ValueError(err)
        else:
            # -> Will Update Barcode right after it being generated
            # -> Send SQS mess to grant permission
            # await update_code(register_kit_req.barcode, config['USED_CODE_STATE'])
            # if user_info:
            #     send_resouce_message_to_queue(user_info['data']["uuid"], register_kit_req.barcode)
            return data, err

    pass


async def get_all_kits_id_card_via_identifier_code(
    identifier_code: Optional[str] = None,
    size: Optional[int] = None,
    offset: Optional[int] = None,
    order_by: Optional[str] = "created_time",
):
    pass

def preprocess_data(data: List) -> List:
    for item in data:
        item["identifier_code"] = str(item["identifier_code"])
        item["dob"] = item["yob"] if item.get("yob") else item["dob"].strftime("%d/%m/%Y")
        item["is_priority"] = "Ưu tiên" if item.get("is_priority") else "Không ưu tiên"
        item["free_of_charge"] = "Mẫu miễn phí" if item.get("free_of_charge") else "Mẫu tính phí"
        item["gender"] = "Nam" if item.get("gender") == "male" else "Nữ"
        item["sample_collection_date_time"] = item["sample_collection_date"].strftime("%H:%M:%S") if item["sample_collection_date"] else ""
        item["sample_collection_date"] = item["sample_collection_date"].strftime("%d/%m/%Y") if item["sample_collection_date"] else ""
        item["sample_receipt_date"] = item["sample_receipt_date"].strftime("%d/%m/%Y") if item["sample_receipt_date"] else ""
        item["expected_report_release_date"] = item["expected_report_release_date"].strftime("%d/%m/%Y") if item["expected_report_release_date"] else ""
        item["actual_report_release_date"] = item["actual_report_release_date"].strftime("%d/%m/%Y") if item["actual_report_release_date"] else ""
        # item["martyr_relationships"] = ", ".join(item["martyr_relationships"]) if item["martyr_relationships"] else ""

    return data

def export_all_kit(data: List) -> BytesIO:
    data_dicts = [dict(row) for row in data]
    data = preprocess_data(data_dicts)
    columns = [
        {"key": "batch_number ", "title": "Batch"},
        {"key": "samplecode", "title": "Samplecode"},
        {"key": "barcode", "title": "Barcode"},
        {"key": "full_name", "title": "Họ và tên"},
        {"key": "gender", "title": "Giới tính"},
        {"key": "dob", "title": "Ngày tháng năm sinh"},
        {"key": "phone_number", "title": "Sđt"},
        {"key": "sample_type", "title": "Kiểu lấy mẫu"},
        {"key": "email", "title": "Email"},
        {"key": "product_name", "title": "Sản phẩm"},
        {"key": "account_name", "title": "Nguồn"},
        {"key": "cs_pic", "title": "Đơn vị thu mẫu"},
        {"key": "martyr_name", "title": "Tên liệt sỹ"},
        {"key": "martyr_relationships", "title": "Mối quan hệ với liệt sỹ"},
        {"key": "nominator", "title": "Bác sĩ"},
        {"key": "sale_pic", "title": "Sale Pic"},
        {"key": "cs_pic", "title": "Cs Pic"},
        {"key": "promotion_code", "title": "Mã khuyến mại"},
        {"key": "is_priority", "title": "Ưu tiên"},
        {"key": "free_of_charge", "title": "Mẫu miễn phí"},
        {"key": "sample_collection_time", "title": "Số lần lấy mẫu"},
        {"key": "sample_collection_date", "title": "Ngày lấy mẫu"},
        {"key": "sample_collection_date_time", "title": "Thời gian lấy mẫu"},
        {"key": "sample_receipt_date", "title": "Ngày nhận mẫu"},
        {"key": "expected_report_release_date", "title": "Ngày trả kết quả dự kiến"},
        {"key": "actual_report_release_date", "title": "Ngày trả kết quả thực tế"},
        {"key": "note", "title": "Ghi chú"},
        {"key": "sample_collector_name", "title": "Người lấy mẫu"},
        {"key": "sample_receiver_name", "title": "Người nhận mẫu"},
        {"key": "legal_guardian", "title": "Người giám hộ"},
        {"key": "diagnosis", "title": "Chuẩn đoán"},
        {"key": "address", "title": "Địa chỉ"},
        {"key": "identifier_code", "title": "Số CCCD"},
    ]

    return export_to_excel(data=data, columns=columns, include_index=True)

def export_all_kit_send_to_lab(data: List) -> BytesIO:
    data_dicts = [dict(row) for row in data]
    data = preprocess_data(data_dicts)
    columns = [
        {"key": "batch_number ", "title": "Batch"},
        {"key": "samplecode", "title": "Samplecode"},
        {"key": "barcode", "title": "Barcode"},
        {"key": "full_name", "title": "Họ và tên"},
        {"key": "gender", "title": "Giới tính"},
        {"key": "dob", "title": "Ngày tháng năm sinh"},
        {"key": "phone_number", "title": "Sđt"},
        {"key": "sample_type", "title": "Kiểu lấy mẫu"},
        {"key": "email", "title": "Email"},
        {"key": "product_name", "title": "Sản phẩm"},
        {"key": "account_name", "title": "Nguồn"},
        {"key": "cs_pic", "title": "Đơn vị thu mẫu"},
        {"key": "nominator", "title": "Bác sĩ"},
        {"key": "sale_pic", "title": "Sale Pic"},
        {"key": "cs_pic", "title": "Cs Pic"},
        {"key": "promotion_code", "title": "Mã khuyến mại"},
        {"key": "is_priority", "title": "Ưu tiên"},
        {"key": "free_of_charge", "title": "Mẫu miễn phí"},
        {"key": "sample_collection_time", "title": "Số lần lấy mẫu"},
        {"key": "sample_collection_date", "title": "Ngày lấy mẫu"},
        {"key": "sample_collection_date_time", "title": "Thời gian lấy mẫu"},
        {"key": "sample_receipt_date", "title": "Ngày nhận mẫu"},
        {"key": "expected_report_release_date", "title": "Ngày trả kết quả dự kiến"},
        {"key": "actual_report_release_date", "title": "Ngày trả kết quả thực tế"},
        {"key": "note", "title": "Ghi chú"},
        {"key": "sample_collector_name", "title": "Người lấy mẫu"},
        {"key": "sample_receiver_name", "title": "Người nhận mẫu"},
        {"key": "legal_guardian", "title": "Người giám hộ"},
        {"key": "diagnosis", "title": "Chuẩn đoán"},
        {"key": "address", "title": "Địa chỉ"},
        {"key": "identifier_code", "title": "Số CCCD"},
    ]

    return export_to_excel(data=data, columns=columns, include_index=True)


