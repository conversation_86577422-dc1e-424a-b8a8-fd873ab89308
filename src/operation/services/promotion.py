from datetime import datetime, timedelta
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_400_BAD_REQUEST,
    HTTP_423_LOCKED,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from ..utils.utils import (
    failure_response,
    get_current_date_time,
    convert_str_to_iso_datetime,
    convert_datetime_to_iso,
    failure_response_kit_registration,
    success_response,
    format_date,
    get_product_by_product_code,
)

from ..cruds.promotion import *

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, DEFAULT_DATE_STR, DEFAULT_YEAR
)
# async def check_default_sale_pic_exist():
#     data = {
#         "name": "DEFAULT"
#     }

#     data, err = await create_promotion(data=data)
#     # if err:
#     #     data = await get_existed_default_sale_pic()
#     #     return data[0], None
#     return data, err

async def convert_start_promotion_date(start_date):
    start_date = format_date(start_date+'T00:00:00.000000Z', DEFAULT_DATE_STR, validate_current_time=False)
    return start_date

async def convert_end_promotion_date(end_date):
    end_date = format_date(end_date+'T23:59:59.999999Z', DEFAULT_DATE_STR, validate_current_time=False)
    return end_date

async def standardize_promotion_service(data):
    try:
        start_date = data['start_date']+'T00:00:00.000000Z'
        end_date = data['end_date']+'T23:59:59.999999Z'
        formated_start_date=format_date(start_date, DEFAULT_DATE_STR, validate_current_time=False)
        formated_end_date=format_date(end_date, DEFAULT_DATE_STR, validate_current_time=False)
        if formated_end_date < formated_start_date:
            raise ValueError("End date cannot be before Start date")
        data['start_date']  = formated_start_date
        data['end_date']    = formated_end_date
        return data
        
    except Exception as e:
        raise e
