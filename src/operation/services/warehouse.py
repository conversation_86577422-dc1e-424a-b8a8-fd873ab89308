from typing import Optional, List, Dict
import uuid
from io import By<PERSON><PERSON>

from fastapi import <PERSON><PERSON><PERSON><PERSON>xception
from sqlalchemy import desc, asc, and_, func
from starlette.status import HTTP_409_CONFLICT

from ..models.models import <PERSON><PERSON>Box, SampleRack, SampleBoxPosition
from ..models.models import db
from ..cruds.sample_box import get_list_sample_boxes, get_list_sample_box_position, get_list_sample_boxes_all, \
    get_list_sample_box_position_all
from ..schemas.warehouse import SamplePosition
from ..utils.utils import failure_response, get_current_date_time_utc_7


class WarehouseService:
    """
    Service class for handling warehouse operations related to sample boxes and racks.
    """

    def __init__(self):
        pass

    @staticmethod
    async def get_sample_box_list(
            box_code: Optional[str] = None,
            rack_code: Optional[str] = None,
            total: Optional[str] = None,
            created_by: Optional[str] = None,
            updated_by: Optional[str] = None,
            from_date: Optional[str] = None,
            to_date: Optional[str] = None,
            order_by: Optional[str] = "created_at",
            order_direction: Optional[str] = "desc",
            page_number: int = 1,
            page_size: int = 20
    ):
        """
        Get a list of sample boxes with optional filtering by code and date range.
        """
        data, total_count = await get_list_sample_boxes(
            box_code=box_code,
            rack_code=rack_code,
            total=total,
            created_by=created_by,
            updated_by=updated_by,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            # order_direction=order_direction,
            page_number=page_number,
            page_size=page_size
        )

        return data, total_count

    @staticmethod
    async def get_sample_box_list_all(
            box_code: Optional[str] = None,
            rack_code: Optional[str] = None,
            total: Optional[int] = None,
            created_by: Optional[str] = None,
            updated_by: Optional[str] = None,
            from_date: Optional[str] = None,
            to_date: Optional[str] = None,
            order_by: Optional[str] = "created_at",
            order_direction: Optional[str] = "desc",
    ):
        """
        Get a list of sample boxes with optional filtering by code and date range.
        """
        data = await get_list_sample_boxes_all(
            box_code=box_code,
            rack_code=rack_code,
            total=total,
            created_by=created_by,
            updated_by=updated_by,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            order_direction=order_direction,
        )

        return data

    @staticmethod
    async def get_sample_box_position_list(
            barcode: Optional[str] = None,
            position: Optional[str] = None,
            box_code: Optional[str] = None,
            rack_code: Optional[str] = None,
            from_date: Optional[str] = None,
            to_date: Optional[str] = None,
            order_by: Optional[str] = "created_at",
            order_direction: Optional[str] = "desc",
            page_number: int = 1,
            page_size: int = 20
    ):
        """
        Get a list of sample boxes with optional filtering by code and date range.
        """
        data, total_count = await get_list_sample_box_position(
            barcode=barcode,
            position=position,
            box_code=box_code,
            rack_code=rack_code,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            # order_direction=order_direction,
            page_number=page_number,
            page_size=page_size
        )

        return data, total_count

    @staticmethod
    async def get_sample_box_position_list_all(
            barcode: Optional[str] = None,
            position: Optional[str] = None,
            box_code: Optional[str] = None,
            rack_code: Optional[str] = None,
            from_date: Optional[str] = None,
            to_date: Optional[str] = None,
            order_by: Optional[str] = "created_at",
            order_direction: Optional[str] = "desc",
    ):
        """
        Get a list of sample boxes with optional filtering by code and date range.
        """
        data, total_count = await get_list_sample_box_position_all(
            barcode=barcode,
            position=position,
            box_code=box_code,
            rack_code=rack_code,
            from_date=from_date,
            to_date=to_date,
            order_by=order_by,
            # order_direction=order_direction,
        )

        return data, total_count

    async def get_sample_box_detail(
            box_code: str
    ) -> Dict:
        """
        Get detailed information about a sample box, including all sample positions.

        Args:
            box_code: The code of the sample box

        Returns:
            Dictionary with box details and sample positions
        """
        # Get box info
        box_query = (
            db.select([
                SampleBox.id,
                SampleBox.code.label('box_code'),
                SampleRack.id.label('rack_id'),
                SampleRack.code.label('rack_code'),
                SampleBox.created_at,
                SampleBox.updated_at
            ])
            .select_from(
                SampleBox
                .outerjoin(SampleRack, SampleBox.sample_rack_id == SampleRack.id)
            )
            .where(SampleBox.code == box_code)
        )

        box = await db.first(box_query)

        if not box:
            return {"error": f"Sample box with code {box_code} not found"}

        # Get positions
        positions_query = (
            db.select([
                SampleBoxPosition.id,
                SampleBoxPosition.barcode,
                SampleBoxPosition.x_position,
                SampleBoxPosition.y_position,
                SampleBoxPosition.created_at,
                SampleBoxPosition.updated_at
            ])
            .where(SampleBoxPosition.sample_box_id == box[0])
            .order_by(SampleBoxPosition.x_position, SampleBoxPosition.y_position)
        )

        positions = await db.all(positions_query)

        return {
            "box": {
                "id": str(box[0]),
                "code": box[1],
                "rack_id": str(box[2]) if box[2] else None,
                "rack_code": box[3] or "",
                "created_at": box[4].isoformat() if box[4] else None,
                "updated_at": box[5].isoformat() if box[5] else None,
            },
            "positions": [
                {
                    "id": str(pos[0]),
                    "barcode": pos[1],
                    "x_position": pos[2],
                    "y_position": pos[3],
                    "created_at": pos[4].isoformat() if pos[4] else None,
                    "updated_at": pos[5].isoformat() if pos[5] else None,
                }
                for pos in positions
            ]
        }

    @staticmethod
    async def set_sample_positions(
            box_code: str,
            rack_code: Optional[str] = None,
            positions: List[SamplePosition] = None,
            sub_name: Optional[str] = None,
    ) -> Dict:
        """
        Set sample positions in a box. Creates box and rack if they don't exist.
        If a position has an empty barcode, it will be removed.

        Args:
            box_code: Code of the sample box
            rack_code: Optional code of the sample rack
            positions: List of position objects with barcode, x_position, y_position
            sub_name: Username of the person making the change

        Returns:
            Dictionary with operation results
        """
        async with db.transaction():
            barcodes = [pos.barcode for pos in positions]
            duplicates = {b for b in barcodes if (b and barcodes.count(b) > 1)}

            if duplicates:
                err = f"Barcode {', '.join(duplicates)} bị trùng trong 1 khay"
                http_code = HTTP_409_CONFLICT
                errs = failure_response(err)
                raise HTTPException(status_code=http_code, detail=errs)

            # Get or create sample rack if provided
            rack_id = None
            if rack_code:
                rack = await db.first(
                    SampleRack.query.where(SampleRack.code == rack_code)
                )
                if not rack:
                    rack = await SampleRack.create(
                        id=uuid.uuid4(),
                        code=rack_code,
                        created_at=get_current_date_time_utc_7(),
                        updated_at=get_current_date_time_utc_7())
                rack_id = rack.id

            # Get or create sample box
            box = await db.first(
                SampleBox.query.where(SampleBox.code == box_code)
            )
            if not box:
                box = await SampleBox.create(
                    id=uuid.uuid4(),
                    code=box_code,
                    sample_rack_id=rack_id,
                    created_by=sub_name,
                    updated_by=sub_name,
                    created_at=get_current_date_time_utc_7(),
                    updated_at=get_current_date_time_utc_7()
                )
            elif rack_id and box.sample_rack_id != rack_id:
                # Update rack if it changed
                await box.update(
                    sample_rack_id=rack_id).apply()

            # Update box metadata if user is provided
            if sub_name:
                await box.update(updated_by=sub_name).apply()

            # Track if any changes were made
            has_changes = False

            # Set positions
            results = []
            if positions:
                for pos in positions:
                    # Check if position has a valid barcode
                    if pos.barcode and pos.barcode.strip():
                        # Check if this position is already used
                        existing = await db.first(
                            SampleBoxPosition.query.where(
                                and_(
                                    SampleBoxPosition.sample_box_id == box.id,
                                    SampleBoxPosition.x_position == pos.x_position,
                                    SampleBoxPosition.y_position == pos.y_position
                                )
                            )
                        )

                        if existing:
                            # Update existing position
                            await existing.update(
                                barcode=pos.barcode
                            ).apply()

                            if pos.barcode != existing.barcode:
                                has_changes = True

                            results.append({
                                "barcode": pos.barcode,
                                "x_position": pos.x_position,
                                "y_position": pos.y_position,
                                "status": "updated"
                            })
                        else:
                            # Create new position
                            new_pos = await SampleBoxPosition.create(
                                id=uuid.uuid4(),
                                sample_box_id=box.id,
                                barcode=pos.barcode,
                                x_position=pos.x_position,
                                y_position=pos.y_position,
                                created_at=get_current_date_time_utc_7(),
                                updated_at=get_current_date_time_utc_7()
                            )
                            has_changes = True
                            results.append({
                                "barcode": pos.barcode,
                                "x_position": pos.x_position,
                                "y_position": pos.y_position,
                                "status": "created"
                            })
                    else:
                        # If barcode is empty or None, remove the position if it exists
                        delete_query = SampleBoxPosition.delete.where(
                            and_(
                                SampleBoxPosition.sample_box_id == box.id,
                                SampleBoxPosition.x_position == pos.x_position,
                                SampleBoxPosition.y_position == pos.y_position
                            )
                        )
                        result = await db.status(delete_query)

                        if len(result[1]) > 0:  # If any rows were deleted
                            has_changes = True
                            results.append({
                                "barcode": None,
                                "x_position": pos.x_position,
                                "y_position": pos.y_position,
                                "status": "removed"
                            })

            # Update the updated_at field for the sample box if there were changes
            if has_changes:
                await box.update(updated_at=get_current_date_time_utc_7()).apply()

            return {
                "box_id": str(box.id),
                "box_code": box.code,
                "rack_id": str(box.sample_rack_id) if box.sample_rack_id else None,
                "rack_code": rack_code,
                "positions": results
            }
