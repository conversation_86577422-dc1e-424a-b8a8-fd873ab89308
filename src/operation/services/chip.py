from datetime import datetime, timed<PERSON><PERSON>
from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_400_BAD_REQUEST,
    HTTP_423_LOCKED,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from ..utils.utils import (
    failure_response,
    get_current_date_time,
    convert_str_to_iso_datetime,
    convert_datetime_to_iso,
    failure_response_kit_registration,
    success_response,
    format_date,
    get_product_by_product_code,
)

from ..cruds.chip import *

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, DEFAULT_DATE_STR, DEFAULT_YEAR
)

async def convert_start_chip_date(start_date):
    start_date = format_date(start_date+'T00:00:00.000000Z', DEFAULT_DATE_STR, validate_current_time=False)
    return start_date

async def convert_end_chip_date(end_date):
    end_date = format_date(end_date+'T23:59:59.999999Z', DEFAULT_DATE_STR, validate_current_time=False)
    return end_date


async def standardize_chip_service(data):
    try:
        if data.get('technology'):
            data['technology']=data.get('technology').upper()
            
        return data
        
    except Exception as e:
        raise e

async def convert_current_date_to_chip_id_prefix():
    try:
        now = datetime.now()
        date_time=now.strftime("%Y%m%d")
        chip_id_prefix=date_time[2:]
        return chip_id_prefix
        
    except Exception as e:
        raise e

async def get_next_pcr_chip_id(results,current_prefix):
    number_of_chips_created_today = 0
    number_of_chips_created_today = len(results)
    logger.info(f"Filter chips info")
    if number_of_chips_created_today > 0 and number_of_chips_created_today <= 99:
        return current_prefix + str(number_of_chips_created_today+1).zfill(2), None
    elif number_of_chips_created_today > 99:
        err = "Number of chips created per day hit limit: 99"
        return None, err
    else:
        return current_prefix + str(1).zfill(2), None
        

async def get_not_deleted_chip(res):
    try:
        result = None
        for data in res:
            if data.deleted_at == None:
                result = data
        return result
    except Exception as e:
        raise e

async def update_chip_id_w_pipeline_qc_report(message_body):
    updated_chip_info = {
        "pipeline_qc_report": message_body['pipeline_qc_report']
    }
    try:
        chip = await get_chip_by_chip_id(message_body['chip_id'])
        err = await update_chip(chip,updated_chip_info)
        if err:
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
    except AttributeError as error:
        msg = f"CHIP WITH ID: {message_body['chip_id']} NOT FOUND"
        logger.info(msg)
        raise error
