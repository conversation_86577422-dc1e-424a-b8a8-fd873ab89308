from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking_unit import *
from ..cruds.tracking_unit_location import *
from ..schemas.tracking_unit_location import *
from ..utils.utils import failure_response


async def get_all_unit_locations_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tul.created_at",
    name: Optional[str] = None,
    unit_id: Optional[int] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_tracking_unit_locations(
        offset=offset,
        size=size,
        order_by=order_by,
        name=name,
        unit_id=unit_id,
        include_deleted=include_deleted,
    )


async def get_unit_location_by_id_service(id: str):
    data = await get_tracking_unit_location_by_id(id)
    return data.to_dict()


async def create_unit_location_service(data: dict):
    """
    name: str
    unit_id: str
    """

    return await create_tracking_unit_location(data)


async def update_unit_location_service(id: str, data: dict):
    """
    type: str
    """
    unit_location = await get_tracking_unit_location_by_id(id)
    if not unit_location:
        err = f"Unit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_unit_location(unit_location, data)
    data = await get_tracking_unit_location_by_id(id)
    return data.to_dict()


async def delete_unit_location_by_id_service(id: str):
    """
    id: str
    """
    unit_location = await get_tracking_unit_location_by_id(id)

    await delete_tracking_unit_location(unit_location=unit_location)

    data = await get_tracking_unit_location_by_id(id)
    return data.to_dict()
