from datetime import datetime, <PERSON><PERSON><PERSON>
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_400_BAD_REQUEST,
    HTTP_423_LOCKED,
    HTTP_406_NOT_ACCEPTABLE,
    HTTP_500_INTERNAL_SERVER_ERROR,
)
from ..utils.utils import (
    failure_response,
    get_current_date_time,
    convert_str_to_iso_datetime,
    convert_datetime_to_iso,
    failure_response_kit_registration,
    success_response,
    format_date,
    get_product_by_product_code,
    split_text_n_number
)

from ..cruds.sample_mapping import *
from ..services.lab_sample import get_lab_checked_sample_list, get_lab_checked_sample_list_v3

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, DEFAULT_DATE_STR, DEFAULT_YEAR,get_no_required_cols, download_s3_file, remove_file_given_local_path
)
from ..config import config
import csv

async def transfer_samples_to_chips(technology, chips, sample_mappings, type):
    if type in ['GSAV3','ASA']:
        results, err = await transfer_samples_to_chips_rc(technology, chips, sample_mappings, type)
    elif type in ['PMDA']:
        results, err = await transfer_samples_to_chips_an(technology, chips, sample_mappings, type)
    
    return results, err

# AlphaNumeric
# A1
# B1
# C1
async def transfer_samples_to_chips_an(technology, chips, sample_mappings, type):
    CHIP_CAP = config["CHIP"][technology.upper()][type.upper()]["CAPACITY"]
    CHIP_CAP = int(CHIP_CAP)
    results = []
    for idx, chip in enumerate(chips):
        if chip != chips[-1]:
            curr_samples_in_chip = sample_mappings[(0+idx)*CHIP_CAP:(1+idx)*CHIP_CAP]
        else:
            curr_samples_in_chip = sample_mappings[(0+idx)*CHIP_CAP:]
        chip_id = chip.get('chip_id')
        res, err = await  transfer_samples_to_chip_an(chip_id, curr_samples_in_chip, technology, type)
        if err:
            return None, err
        results.extend(res)

    return results, None


# FIXED NUMBER OF SAMPLES
# RowColumn
# R01C01
async def transfer_samples_to_chips_rc(technology, chips, sample_mappings, type):
    CHIP_CAP = config["CHIP"][technology.upper()][type.upper()]["CAPACITY"]
    CHIP_CAP = int(CHIP_CAP)
    curr_samples_in_chip = []
    results = []
    for idx, chip in enumerate(chips):
        if chip != chips[-1]:
            curr_samples_in_chip = sample_mappings[(0+idx)*CHIP_CAP:(1+idx)*CHIP_CAP]
        else:
            curr_samples_in_chip = sample_mappings[(0+idx)*CHIP_CAP:]
        chip_id = chip.get('chip_id')
        ROW_FORMAT, MAX_ROWS = split_text_n_number(config["CHIP"][technology.upper()]["ROW"])
        COL_FORMAT, MAX_COLS = split_text_n_number(config["CHIP"][technology.upper()]["COLUMN"])
        MAX_ROWS = int(MAX_ROWS)
        MAX_COLS = int(MAX_COLS)
        number_of_col = get_no_required_cols(len(curr_samples_in_chip),MAX_ROWS)
        if number_of_col <= MAX_COLS:
            # ZERO-BASED
            for col_index in range(number_of_col):
                if col_index < number_of_col - 1:
                    curr_samples_in_col = curr_samples_in_chip[(0+col_index)*MAX_ROWS:(1+col_index)*MAX_ROWS]
                else:
                    curr_samples_in_col = curr_samples_in_chip[(0+col_index)*MAX_ROWS:]
                
                res, err = await  transfer_samples_to_chip_rc(chip_id, col_index, curr_samples_in_col,ROW_FORMAT,COL_FORMAT)
                if err:
                    return None, err
                results.extend(res)

    return results, None


async def update_sample_mappings_qc_status(message_body):
    # logger.info(f"Get next PLATE NAME w/ technology {type}")
    output_bucket, full_path, local_path, TECHNOLOGY, plate_name, chip_id = parsing_sample_mappings_qc_status_body(message_body)
    logger.info(f"DOWNLOADING: {full_path}")
    local_file_path = download_s3_file(output_bucket, full_path, local_path)
    logger.info(f"PIPELINE QC REPORT output file path: {local_file_path}")
    await parse_sample_mappings_qc_status_from_csv_file(local_file_path, TECHNOLOGY, plate_name)
    remove_file_given_local_path(local_file_path)
    logger.info(f"{local_file_path} removed successfully")
    pass

def parsing_sample_mappings_qc_status_body(message_body):
    """
    message_body:
        chip_id - OPTIONAL - e.g: 205828460010
        batch_barcode - OPTIONAL - e.g: 6
        technology - OPTIONAL - e.g: MICROARRAY
    
    ** MICROARRAY **
    file_format: 205828460010_QC_REPORT.csv
    output_bucket: vgt-data-pipeline-output-qa
    prefix: 205828460010/idat_to_vcf/output/qc

    return:
        output_bucket: vgt-data-pipeline-output-qa
        full_path: 205828460010/idat_to_vcf/output/qc/205828460010_QC_REPORT.csv
        local_path: .

    
    PCR
    file_format: 1.QC.csv
    output_bucket: pcr-data-pipeline-output-qa
    prefix: 1

    return:
        output_bucket: pcr-data-pipeline-output-qa
        full_path: 1/1.QC.csv
        local_path: .
    """

    TECHNOLOGY = message_body['technology'].upper()
    file_format = config['PIPELINE_QC_STATUS']['FILE_FORMAT'][TECHNOLOGY]
    chip_id = message_body.get('chip_id')
    plate_name = message_body.get('plate_name')

    if 'CHIPID' in file_format:
        if not chip_id:
            err = 'SQS MESSAGE OF PIPELINE_QC_STATUS REQUIRED chip_id'
            raise ValueError(err)
        file_format = file_format.replace('CHIPID', chip_id)

    if 'PLATENAME' in file_format:
        if not plate_name:
            err = 'SQS MESSAGE OF PIPELINE_QC_STATUS REQUIRED plate_name'
            raise ValueError(err)
        file_format = file_format.replace('PLATENAME', plate_name)
        
    output_bucket = config['PIPELINE_QC_STATUS']['OUTPUT_BUCKET'][TECHNOLOGY]
    prefix = config['PIPELINE_QC_STATUS']['PREFIX'][TECHNOLOGY]
    if 'CHIPID' in prefix:
        if not chip_id:
            err = 'SQS MESSAGE OF PIPELINE_QC_STATUS REQUIRED chip_id'
            raise ValueError(err)
        prefix = prefix.replace('CHIPID', chip_id)

    if 'PLATENAME' in prefix:
        if not plate_name:
            err = 'SQS MESSAGE OF PIPELINE_QC_STATUS REQUIRED plate_name'
            raise ValueError(err)
        prefix = prefix.replace('PLATENAME', plate_name)

    full_path = prefix + '/' + file_format
    local_path = '.'
    return output_bucket, full_path, local_path, TECHNOLOGY, plate_name, chip_id


async def parse_sample_mappings_qc_status_from_csv_file(local_file_path, TECHNOLOGY, plate_name):
    logger.info(f"File Name {local_file_path}")
    qc_status_cols_name = config['PIPELINE_QC_STATUS']['COLUMN_NAMES'][TECHNOLOGY]
    logger.info(f"UPDATED COLS: {qc_status_cols_name} for TECHNOLOGY {TECHNOLOGY} ")
    identity_col = config['PIPELINE_QC_STATUS']['ID_COL_NAME'][TECHNOLOGY]
    rows = []
    with open(local_file_path, 'r') as csvfile:
        csvreader = csv.DictReader(csvfile, delimiter=',')
        for row in csvreader:
            rows.append(row)
            # Print the current row
            # for col in qc_status_cols:
                # print(row['PROBE_PASS'],row['Call rate'],row['Gender PASS'],row['Call rate PASS'])
                # print(row[col])
    # print("ROWS parsed from QC_STATUS_CSV file: \n",rows)
    sample_mapping_ids =  await update_sample_mappings_qc_status_w_params(TECHNOLOGY,identity_col,rows,qc_status_cols_name, plate_name)
    await update_kit_status_w_sample_mapping_ids(sample_mapping_ids)    

    pass

async def check_valid_sample_mapping_bodies_w_pc_sample(sample_mapping_bodies: list):
    data_arrs = []
    lids = []
    expected_pc_sample_lid = None
    pc_data_index = None
    dna_extraction_ids = [] 
    for idx, sample_mapping_body in enumerate(sample_mapping_bodies):
        sample_mapping_obj = AddPCSampleMappingWNameReq(**sample_mapping_body)
        # Check duplicated DNA_EXTRACTION via dna_extraction_id
        if sample_mapping_obj.dna_extraction_id in dna_extraction_ids:
            err = str(f"DNA EXTRACTION with ID {sample_mapping_obj.dna_extraction_id } is duplicated in payload.")
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        dna_extraction_ids.append(sample_mapping_obj.dna_extraction_id)	
        data = sample_mapping_obj.dict()
        data_arrs.append(data)
        lids.append(sample_mapping_obj.lid)
        
        # Check if filled in "chip_id" then well_position == position == H12 for PC sample
        if sample_mapping_obj.well_position == 'H12':
            pc_sample_mapping_obj = sample_mapping_obj
            expected_pc_sample_lid = pc_sample_mapping_obj.lid
            pc_data_index = idx
            if pc_sample_mapping_obj.chip_id and pc_sample_mapping_obj.position != 'H12':
                err = str(f" Positive Control Sample can't be put in {pc_sample_mapping_obj.position} on chip {pc_sample_mapping_obj.chip_id}.")
                http_code = HTTP_400_BAD_REQUEST
                errs = failure_response(err)
                raise HTTPException(status_code=http_code, detail=errs)
    # lids,
    # valid if plate includes only 1 pc sample & get that lid of pc sample
    is_valid_plate, number_pc_sample_in_lids, pc_lid = await check_valid_lids_w_pc(lids)
    if not is_valid_plate:
        err = str(f"NOT ALLOWED TO SAVE PLATE INCLUDE {number_pc_sample_in_lids} Positive Control Sample(s).")
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    # LID of PC sample should be the same LID of well-pos H12  
    if expected_pc_sample_lid != pc_lid:
        err = str(f" LID {expected_pc_sample_lid } is not positive control sample. Only PC LID {pc_lid} could be stored at H12 position!. Please check {pc_lid} position.")
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    return pc_data_index, data_arrs

async def check_valid_lids_w_pc(lids):
    pc_pool_data, _ = await get_lab_checked_sample_list(
        positive_control=True,
    )
    pc_pool_data = [ convert_rowproxy_to_dict(row) for row in pc_pool_data ]
    # print("pc_pool_data: ",pc_pool_data)
    pc_pool_lids = [pc_sample['lid'] for pc_sample in pc_pool_data]
    print("pc_pool_lids: ",pc_pool_lids)
    print("lids: ",lids)
    number_pc_sample_in_lids = 0
    last_pc_lid = None
    for lid in lids:
        if lid in pc_pool_lids:
            number_pc_sample_in_lids += 1
            last_pc_lid = lid
    
    if number_pc_sample_in_lids == 1:
        return True, number_pc_sample_in_lids, last_pc_lid
    else:
        return False, number_pc_sample_in_lids, None

async def check_valid_lids_w_pc_v3(lids):
    pc_pool_data, _ = await get_lab_checked_sample_list_v3(
        positive_control=True,
    )
    print("pc_pool_data: ", pc_pool_data)
    pc_pool_data = [ convert_rowproxy_to_dict(row) for row in pc_pool_data ]
    # print("pc_pool_data: ",pc_pool_data)
    pc_pool_lids = [pc_sample['lid'] for pc_sample in pc_pool_data]
    print("pc_pool_lids: ",pc_pool_lids)
    print("lids: ",lids)
    number_pc_sample_in_lids = 0
    last_pc_lid = None
    for lid in lids:
        if lid in pc_pool_lids:
            number_pc_sample_in_lids += 1
            last_pc_lid = lid
    
    if number_pc_sample_in_lids == 1:
        return True, number_pc_sample_in_lids, last_pc_lid
    else:
        return False, number_pc_sample_in_lids, None

async def check_valid_sample_mapping_bodies_w_pc_sample_v3(sample_mapping_bodies: list, plate_name: str):
    data_arrs = []
    lids = []
    expected_pc_sample_lid = None
    pc_data_index = None
    dna_extraction_ids = [] 
    for idx, sample_mapping_body in enumerate(sample_mapping_bodies):
        sample_mapping_obj = AddPCSampleMappingWNameReq(**sample_mapping_body)
        # Check sample's plate is correct:
        if sample_mapping_obj.plate_name != plate_name:
            err = str(f"DNA EXTRACTION with ID {sample_mapping_obj.dna_extraction_id } - {sample_mapping_obj.plate_name} is not in correct plate {plate_name}.")
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        
        # Check duplicated DNA_EXTRACTION via dna_extraction_id
        if sample_mapping_obj.dna_extraction_id in dna_extraction_ids:
            err = str(f"DNA EXTRACTION with ID {sample_mapping_obj.dna_extraction_id } is duplicated in payload.")
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)
        
        dna_extraction_ids.append(sample_mapping_obj.dna_extraction_id)	
        data = sample_mapping_obj.dict()
        data_arrs.append(data)
        lids.append(sample_mapping_obj.lid)
        
        # Check if filled in "chip_id" then well_position == position == H12 for PC sample
        if sample_mapping_obj.well_position == 'H12':
            pc_sample_mapping_obj = sample_mapping_obj
            expected_pc_sample_lid = pc_sample_mapping_obj.lid
            pc_data_index = idx
            if pc_sample_mapping_obj.chip_id and pc_sample_mapping_obj.position != 'H12':
                err = str(f" Positive Control Sample can't be put in {pc_sample_mapping_obj.position} on chip {pc_sample_mapping_obj.chip_id}.")
                http_code = HTTP_400_BAD_REQUEST
                errs = failure_response(err)
                raise HTTPException(status_code=http_code, detail=errs)
    # lids,
    # valid if plate includes only 1 pc sample & get that lid of pc sample
    is_valid_plate, number_pc_sample_in_lids, pc_lid = await check_valid_lids_w_pc_v3(lids)
    if not is_valid_plate:
        err = str(f"NOT ALLOWED TO SAVE PLATE INCLUDE {number_pc_sample_in_lids} Positive Control Sample(s).")
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    
    # LID of PC sample should be the same LID of well-pos H12
    if expected_pc_sample_lid != pc_lid:
        err = str(f" LID {expected_pc_sample_lid } is not positive control sample. Only PC LID {pc_lid} could be stored at H12 position!. Please check {pc_lid} position.")
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    return pc_data_index, data_arrs