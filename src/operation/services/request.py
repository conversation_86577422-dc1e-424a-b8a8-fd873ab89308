from datetime import datetime

from fastapi import HTT<PERSON>Exception
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
    HTTP_422_UNPROCESSABLE_ENTITY,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from src.operation.config import config

from ..cruds.adn_integration import *
from ..cruds.kit import get_kit_detail_via_barcode_v3
from ..cruds.request import *
from ..schemas.adn_result_validator import *
from ..schemas.request import *
from ..services.adn_integration import (
    get_all_adn_integrations_service,
    update_adn_integration_service,
)
from ..services.adn_integration_type import *
from ..services.agency import *
from ..services.identity_card import *
from ..services.tracking import *
from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE,
    check_adn_result_from_a_list_w_code_n_lid,
    check_raw_result_from_a_list_w_code_n_lid,
    convert_datetime_flag_format,
    convert_rowproxy_to_dict,
    failure_response,
    format_date,
    generate_request_code_string,
    is_wsq,
    list_files_in_identifier_code_folder,
    send_adn_result_to_vneid_agency,
    vneid_request_reponse,
)


async def get_request_and_cccd_detail_w_request_id(request_id: str):
    data = await get_request_by_id_service(id=request_id)
    request_detail = {
        "ngayGioGiaoDich": convert_datetime_flag_format(data.get("transaction_date")),
        "maGiaoDich": str(data.get("id")),
    }
    cccd_detail = {
        "hoTenCongDan": data["identity_card"].get("full_name"),
        "ngayThangNamSinh": data["identity_card"].get("dob").strftime("%d/%m/%Y"),
        "soDinhDanh": data["identity_card"].get("identifier_code"),
        "maDvXN": config["AGENCY_ACCOUNT_CODE"]["maDonViXetNghiem"],
        "anhMat": data["identity_card"].get("avatar_image"),
    }

    if is_wsq(data["identity_card"].get("fingerprint_image")):
        cccd_detail["vanTay"] = data["identity_card"].get("fingerprint_image")

    return request_detail, cccd_detail


async def get_all_requests_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "req.updated_at",
    id: Optional[str] = None,
    agency_name: Optional[str] = None,
    request_code: Optional[str] = None,
    identifier_code: Optional[str] = None,
    customer_name: Optional[str] = None,
    customer_phone: Optional[str] = None,
    payment_status: Optional[str] = None,
    status: Optional[str] = None,
    customer_support_id: Optional[str] = None,
    start_collect_date: Optional[str] = None,
    end_collect_date: Optional[str] = None,
    start_request_date: Optional[str] = None,
    end_request_date: Optional[str] = None,
    start_response_date: Optional[str] = None,
    end_response_date: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    if start_collect_date:
        start_collect_date_std = format_date(
            start_collect_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if end_collect_date:
        end_collect_date_std = format_date(
            end_collect_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if end_collect_date_std < start_collect_date_std:
            raise ValueError("end date cannot be after start date")

    if start_request_date:
        start_request_date_std = format_date(
            start_request_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if end_request_date:
        end_request_date_std = format_date(
            end_request_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if end_request_date_std < start_request_date_std:
            raise ValueError("end date cannot be after start date")

    if start_response_date:
        start_response_date_std = format_date(
            start_response_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if end_response_date:
        end_response_date_std = format_date(
            end_response_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if end_response_date_std < start_response_date_std:
            raise ValueError("end date cannot be after start date")

    return await get_all_requests(
        offset=offset,
        size=size,
        order_by=order_by,
        id=id,
        agency_name=agency_name,
        request_code=request_code,
        identifier_code=identifier_code,
        customer_name=customer_name,
        customer_phone=customer_phone,
        payment_status=payment_status,
        status=status,
        customer_support_id=customer_support_id,
        start_collect_date=start_collect_date_std if start_collect_date else None,
        end_collect_date=end_collect_date_std if end_collect_date else None,
        start_request_date=start_request_date_std if start_request_date else None,
        end_request_date=end_request_date_std if end_request_date else None,
        start_response_date=start_response_date_std if start_response_date else None,
        end_response_date=end_response_date_std if end_response_date else None,
        include_deleted=include_deleted,
    )


async def get_request_by_id_service(id: str):
    data = await get_request_by_id(id)
    data = data.to_dict()
    # print("data: ", data)
    data["dob"] = data["dob"].strftime("%d/%m/%Y")
    data["adn_integrations"] = []

    data["agency"] = await get_agency_by_id_service(id=data.get("agency_id"))
    identity_card = await get_identity_card_detail_via_cccd_v3(
        identifier_code=data.get("identifier_code")
    )
    data["identity_card"] = identity_card.to_dict()
    results, total = await get_all_adn_integrations_service(
        size=10, offset=0, request_id=id, include_deleted=True
    )

    for item in results:
        result = convert_rowproxy_to_dict(item)
        data["adn_integrations"].append(result)

    return data


async def get_adn_integrations_for_a_specific_sample(
    request_id: str, collect_date: str, adn_types: List[str]
):
    request_detail = await get_request_by_id_service(id=request_id)
    if not request_detail:
        err = f"Request with id {request_id} not found!"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    req_collect_date = str(request_detail.get("collect_date")).split(" ")[0]
    if collect_date != req_collect_date:
        err = f"Input collect_date {collect_date} need to match with request's collect date {req_collect_date}!"
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    samplecode_set = set()
    adn_integration_lst = []
    not_ready_lst = []

    for adn_integration in request_detail.get("adn_integrations"):
        if len(samplecode_set) > 1:
            err = f"Request to generate result for more than one samples: {samplecode_set}. Using another API instead!"
            http_code = HTTP_409_CONFLICT
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        if adn_integration.get("adn_type") in adn_types:
            samplecode_set.add(adn_integration.get("samplecode"))
            adn_integration_lst.append(adn_integration)
            if not adn_integration.get("presigned_s3_url"):
                not_ready_lst.append(adn_integration.get("barcode"))

    return adn_integration_lst, samplecode_set.pop(), not_ready_lst


async def parsing_adn_integration_payload(
    request_dict: dict, adn_integration_dict: dict
):
    barcode = adn_integration_dict.get("barcode")
    kit_detail = await get_kit_detail_via_barcode_v3(barcode=barcode)
    kit_detail = convert_rowproxy_to_dict(kit_detail)

    request_result = VNeIDRequest(
        request_infor=request_dict,
        kit_detail=kit_detail,
        adn_integration_dict=adn_integration_dict,
    )

    return request_result.dict()


async def review_request_result_by_ids_service(
    request_id: str, adn_integration_id: str
):
    # Check adn_integration status == 'REQUIRE_REVIEW'
    curr_adn_integration = await get_adn_integration_by_id(id=adn_integration_id)
    request_detail = await get_request_by_id_service(id=request_id)
    barcode = curr_adn_integration.barcode
    reviewed_adn_result = 0

    async with db.transaction() as tx:
        for adn_integration in request_detail.get("adn_integrations"):
            if adn_integration.get("barcode") == barcode and adn_integration.get(
                "status"
            ) in ["REQUIRE_REVIEW", "REVIEWED"]:
                current_time = get_current_date_time_utc_7()
                data = {
                    "status": config["AGENCY_INTEGRATION_STATUS"]["REVIEWED"],
                    "updated_at": current_time,
                }

                # UPDATE adn_integration -> 'REVIEWED'
                await update_adn_integration_service(
                    id=adn_integration.get("id"), data=data
                )
                reviewed_adn_result += 1

        if reviewed_adn_result:
            # Sync request_id
            updated_status = {
                "status": config["AGENCY_INTEGRATION_STATUS"]["REVIEWED"],
                "updated_at": current_time,
            }
            _ = await update_request_service(id=request_id, data=updated_status)
            return await get_request_by_id_service(id=request_id)

        else:
            err = "ADN RESULT need to be uploaded before review!"
            http_code = HTTP_409_CONFLICT
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)


async def send_request_result_by_ids_service(
    request_id: str, adn_integration_id: str, adn_result: dict
):
    request_detail = await get_request_by_id_service(id=request_id)
    agency_type = request_detail.get("agency").get("type")
    # print("agency_type: ", agency_type)

    if adn_result.get("data").get("duLieuADN").get("tenFileADN"):
        payload = adn_result
    else:
        return None

    for adn_integration in request_detail.get("adn_integrations"):
        if adn_integration_id == adn_integration.get("id"):
            async with db.transaction() as tx:
                # if adn_integration.get('status') not in config['AGENCY_INTEGRATION_STATUS']['READY_TO_SEND']:
                #     err = f"ADN RESULT need to be REVIEWED before sending!"
                #     http_code = HTTP_409_CONFLICT
                #     errs = failure_response(err)
                #     raise HTTPException(status_code=http_code, detail=errs)

                # payload = await parsing_adn_integration_payload(request_dict=request_detail,
                #                                                 adn_integration_dict=adn_integration)

                # print("payload: ", payload)
                if agency_type.upper() == "GOVERNMENT":
                    response, response_entity = await send_adn_result_to_vneid_agency(
                        payload=payload
                    )
                    _ = await update_request_service(id=request_id, data=response)

                    if int(response["status_code"]) != config.get(
                        "AGENCY_INTEGRATION_STATUS"
                    ).get("CODE_MAPPING").get("SEND_REQUEST_SUCCESS"):
                        http_code = HTTP_500_INTERNAL_SERVER_ERROR
                        errs = failure_response(response["error_message"])
                        raise HTTPException(status_code=http_code, detail=errs)

                    curr_adn_integration = await get_adn_integration_by_id(
                        id=adn_integration_id
                    )

                    current_time = get_current_date_time_utc_7()
                    data = {
                        "updated_at": current_time,
                        "response_date": response["response_date"],
                    }

                    await update_adn_integration(
                        adnIntegration=curr_adn_integration, data=data
                    )

                    return response_entity

    return None


async def is_request_code_existed(request_code: str):
    return await is_request_existed(request_code=request_code)


async def generate_unique_request_code():
    while True:
        request_code = generate_request_code_string()
        if not await is_request_code_existed(request_code=request_code):
            return request_code


def check_government_agency_payment(agency: dict, data: dict):
    if agency.get("name") in config.get("AGENCY_FREE_OF_CHARGE"):
        data["payment_status"] = "COMPLETED"
        data["payment_amount"] = 0
        data["payment_method"] = "CASH"

    return data


async def sync_adn_integration(
    data: dict, adn_type: str, adn_result_obj_key_list: list, request_id: str
):
    try:
        adn_integration, _ = await create_adn_integration_w_request_id(
            data=data, request_id=request_id
        )
    except HTTPException as err:
        if int(err.status_code) == 409:
            async with db.transaction() as tx:
                adn_integration = await get_adn_integration_by_samplecode_and_type_id(
                    samplecode=data.get("samplecode"),
                    barcode=data.get("barcode"),
                    type_id=data.get("type_id"),
                )

                # if adn_integration and adn_integration.status in ['REQUIRE_REVIEW', 'REVIEWED', 'SENT']:
                #     return adn_integration.to_dict()
                # else:

                current_time = get_current_date_time_utc_7()
                updated_data = {
                    "status": data.get("status"),
                    "presigned_s3_url": "",
                    "raw_adn_s3_obj_key": "",
                    "updated_at": current_time,
                }
                try:
                    logger.info(
                        f"Begin getting adn result metadata - {data.get('barcode')}!!"
                    )
                    tracking_detail = await get_adn_result_metadata_via_barcode(
                        barcode=data.get("barcode")
                    )
                    print("tracking_detail: ", tracking_detail)
                    lid = tracking_detail.get("maXetNghiem")
                    if lid and len(adn_result_obj_key_list):
                        # check adn result exist
                        # ADN_RESULT_EXTENSION
                        extension = config.get("AGENCY_STORAGE").get(
                            "ADN_RESULT_EXTENSION"
                        )
                        # print("extension: ", extension)
                        isExisted, adn_object_key = (
                            check_adn_result_from_a_list_w_code_n_lid(
                                adn_result_obj_key_list=adn_result_obj_key_list,
                                barcode=data.get("barcode"),
                                lid=lid,
                                adn_type=adn_type,
                                extension=extension.lower(),
                            )
                        )
                        if isExisted:
                            updated_data["status"] = "REQUIRE_REVIEW"
                            updated_data["presigned_s3_url"] = adn_object_key

                        raw_extension = config.get("AGENCY_STORAGE").get(
                            "RAW_RESULT_EXTENSION"
                        )
                        # print("extension: ", extension)
                        isRawExisted, raw_object_key = (
                            check_raw_result_from_a_list_w_code_n_lid(
                                adn_result_obj_key_list=adn_result_obj_key_list,
                                barcode=data.get("barcode"),
                                request_id=request_id,
                                adn_type=adn_type,
                                extension=raw_extension.lower(),
                            )
                        )
                        if isRawExisted:
                            updated_data["raw_adn_s3_obj_key"] = (
                                raw_object_key  # <--- UPDATE this
                            )

                except Exception as err:
                    err = "Cần nhập hành trình mẫu trước khi tiếp tục!!"
                    logger.info(
                        f"Cần nhập hành trình mẫu {data.get('barcode')} trước khi tiếp tục!!"
                    )

                    # UPDATE kit stt --> REQUIRE_REVIEW

                    # print("updated_data: ", updated_data)

                # UPDATE adn_integration stt
                await update_adn_integration(
                    adnIntegration=adn_integration, data=updated_data
                )
                updated_adn_integration = {**adn_integration.to_dict(), **updated_data}
                return updated_adn_integration

        else:
            raise err

    return adn_integration


def matching_request_and_kit_status(adn_integrations):
    if len(adn_integrations):
        status_list = set([item.get("status") for item in adn_integrations])

        if status_list & set(
            config.get("AGENCY_INTEGRATION_STATUS").get("MAPPING").get("SAMPLE_FOUND")
        ):
            return "SAMPLE_FOUND"

        if status_list & set(
            config.get("AGENCY_INTEGRATION_STATUS").get("MAPPING").get("IN_PROGRESS")
        ):
            return "IN_PROGRESS"

        if status_list & set(
            config.get("AGENCY_INTEGRATION_STATUS").get("MAPPING").get("REQUIRE_REVIEW")
        ):
            return "REQUIRE_REVIEW"

        if status_list & set(
            config.get("AGENCY_INTEGRATION_STATUS").get("MAPPING").get("REVIEWED")
        ):
            return "REVIEWED"

        if status_list & set(
            config.get("AGENCY_INTEGRATION_STATUS").get("MAPPING").get("CANCELED")
        ):
            return "CANCELED"

        if status_list & set(
            config.get("AGENCY_INTEGRATION_STATUS").get("MAPPING").get("SENT")
        ):
            return "SENT"

    else:
        return config.get("AGENCY_INTEGRATION_STATUS").get("SAMPLE_NOT_FOUND")

    pass


async def init_adn_integrations_for_request_from_mapping(
    request: dict, adn_kit_map: dict
):
    request_id = request.get("id")
    adn_result_obj_key_list = list_files_in_identifier_code_folder(
        identifier_code=request.get("identifier_code")
    )
    request["adn_integrations"] = []
    # create available adn_integration by avail result
    # print("adn_result_obj_key_list: ", adn_result_obj_key_list)

    async with db.transaction() as tx:
        for adn_type, kit in adn_kit_map.items():
            # URL, json
            # print("inside kit: ", kit)
            for method in kit.get("methods"):
                # Check ADN TYPE & SUPPORTED METHOD mapping
                adn_integration_type = (
                    await get_adn_integration_type_by_mapping_service(
                        adn_type=adn_type, method=method
                    )
                )

                if not adn_integration_type:
                    err = f"Error: ADN Integration with adn_type {adn_type} and method {method} is not  existed!"
                    http_code = HTTP_400_BAD_REQUEST
                    errs = failure_response(err)
                    raise HTTPException(status_code=http_code, detail=errs)

                data = {
                    "samplecode": kit.get("samplecode"),
                    "barcode": kit.get("barcode"),
                    "type_id": adn_integration_type.get("id"),
                    "status": kit.get("current_status"),
                    "presigned_s3_url": "",
                    "raw_adn_s3_obj_key": "",
                    "review_required": True,
                }

                # print("inside data: ", data)

                adn_integration = await sync_adn_integration(
                    data=data,
                    adn_type=adn_type,
                    adn_result_obj_key_list=adn_result_obj_key_list,
                    request_id=request_id,
                )

                res = {
                    **adn_integration,
                    "name": adn_integration_type.get("name"),
                    "adn_type": adn_integration_type.get("adn_type"),
                    "method": adn_integration_type.get("method"),
                }
                request["adn_integrations"].append(res)
                # print("DONE !!!!")

        if request.get("status") != matching_request_and_kit_status(
            request["adn_integrations"]
        ):
            updated_status = {
                "status": matching_request_and_kit_status(request["adn_integrations"])
            }
            # print("status: ", matching_request_and_kit_status(request['adn_integrations']))
            updated_request = await update_request_service(
                id=request.get("id"), data=updated_status
            )

            updated_request["adn_integrations"] = request["adn_integrations"]
            return updated_request

        return request


async def init_adn_integrations_service_w_request_id(
    collect_date: Optional[str],
    request_id: str,
    identifier_code: str = "",
    data: dict = {},
):
    if (not request_id) and (not data):
        err = "Invalid input: Both request_id and data cant  be Null"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    async with db.transaction() as tx:
        # Get available adn integration by identifier_code
        # print("identifier_code: ", identifier_code or data.get('identifier_code') )
        # print("collect_date: ", collect_date)
        adn_kit_map, total = await get_adn_kit_mapping_by_cccd_v3(
            identifier_code=identifier_code or data.get("identifier_code"),
            collect_date=collect_date,
        )
        logger.info(f"Getting total adn_kit_map - {total}!!")

        # print("adn_kit_map: ", adn_kit_map)

        if data:
            request = await get_request_dict_by_id(id=request_id)
            if request:
                updated_status = {
                    "status": config["AGENCY_INTEGRATION_STATUS"]["RENEWED"]
                }
                # print("status: ", matching_request_and_kit_status(request['adn_integrations']))
                _ = await update_request_service(
                    id=request.get("id"), data=updated_status
                )
                return await get_request_by_id_service(id=request.get("id"))

            init_status = "SAMPLE_FOUND" if total > 0 else "SAMPLE_NOT_FOUND"
            data["status"] = config.get("AGENCY_INTEGRATION_STATUS").get(init_status)
            request = await create_request(data)

        else:
            request = await get_request_dict_by_id(id=request_id)

        request = (
            await init_adn_integrations_for_request_from_mapping(
                request=request, adn_kit_map=adn_kit_map
            )
            if total > 0
            else request
        )

    return request


async def init_adn_integrations_service(identifier_code: str = "", data: dict = {}):
    if (not identifier_code) and (not data):
        err = "Invalid input: Both identifier_code and data cant  be Null"
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    async with db.transaction() as tx:
        # Get available adn integration by identifier_code
        print("identifier_code: ", identifier_code or data.get("identifier_code"))
        adn_kit_map, total = await get_adn_kit_mapping_by_cccd_v3(
            identifier_code=identifier_code or data.get("identifier_code")
        )

        # print("adn_kit_map: ", adn_kit_map)

        if data:
            request = await get_all_requests_by_identifier_code(
                identifier_code=data.get("identifier_code")
            )
            if request:
                updated_status = {
                    "status": config["AGENCY_INTEGRATION_STATUS"]["RENEWED"]
                }
                # print("status: ", matching_request_and_kit_status(request['adn_integrations']))
                _ = await update_request_service(
                    id=request.get("id"), data=updated_status
                )
                return await get_request_by_id_service(id=request.get("id"))

            init_status = "SAMPLE_FOUND" if total > 0 else "SAMPLE_NOT_FOUND"
            data["status"] = config.get("AGENCY_INTEGRATION_STATUS").get(init_status)
            request = await create_request(data)

        else:
            request = await get_all_requests_by_identifier_code(
                identifier_code=identifier_code
            )

        request = (
            await init_adn_integrations_for_request_from_mapping(
                request=request, adn_kit_map=adn_kit_map
            )
            if total > 0
            else request
        )

    return request


async def create_vneid_request_service(data: dict = {}):
    async with db.transaction() as tx:
        # Get available adn integration by identifier_code

        # collect_date_start, collect_date_end = calculate_business_days(date_str=data.get('collect_date'), n=config['AGENCY_ACCOUNT_CODE'].get('COLLECT_DATE_RANGE'))

        # logger.info(f"collect_date_start, collect_date_end: {collect_date_start}, {collect_date_end}")
        identifier_code = data.get("identifier_code")
        _, total = await get_all_kits_identity_card_via_id_code_v3(
            identifier_code=identifier_code
        )

        logger.info(f"Total {total} kits binded to CCCD: {identifier_code}")
        request = await get_request_by_id(id=data.get("id"))
        if request:
            request = request.to_dict()
            logger.info(
                f"VNeID request {request.get('id')} already sent by CCCD: {identifier_code}"
            )
            logger.debug(f"Request content: {request}")
            return request

        init_status = "SAMPLE_FOUND" if total > 0 else "SAMPLE_NOT_FOUND"
        data["status"] = config.get("AGENCY_INTEGRATION_STATUS").get(init_status)
        request = await create_request(data)

    return request


async def init_vneid_request_service(requestBody: dict):
    # Generate unique request_code
    requestBody["request_code"] = await generate_unique_request_code()
    # agency id from drop list
    agency_id = config["AGENCY_ACCOUNT_CODE"]["VNeID_AGENCY_ID"]
    agency = await get_agency_by_id_service(id=agency_id)
    identifier_code = requestBody.get("data").get("soDinhDanh")
    identity_card = await get_identity_card_detail_via_cccd_v3(
        identifier_code=identifier_code
    )

    logger.info(f"New VNeID request from CCCD: {identifier_code}")
    if not identity_card:
        return vneid_request_reponse(is_success=False)

    requestBody = check_government_agency_payment(agency=agency, data=requestBody)
    requestBase = RequestBase(
        id=requestBody.get("maGiaoDich"),
        request_code=requestBody.get("request_code"),
        agency_id=agency_id,
        customer_support_name=requestBody.get("customer_support_name"),
        customer_support_id=requestBody.get("customer_support_id"),
        customer_name=requestBody.get("data").get("hoTenCongDan"),
        dob=requestBody.get("data").get("ngayThangNamSinh"),
        gender=identity_card.gender,
        payment_amount=requestBody.get("payment_amount"),
        payment_method=requestBody.get("payment_method"),
        payment_status=requestBody.get("payment_status"),
        # status: str = 'SAMPLE_NOT_FOUND'
        # status_code: Optional[str]
        # error_message: Optional[str] = None
        transaction_date=requestBody.get("ngayGioGiaoDich"),
        collect_date=requestBody.get("data").get("ngayLayMau"),
        request_date=requestBody.get("data").get("ngayYeuCau"),
        # response_date: Optional[str] = None
        identifier_code=identifier_code,
    )
    _ = await create_vneid_request_service(data=requestBase.dict())

    return vneid_request_reponse(is_success=True)


async def create_request_service(data: dict):
    # Generate unique request_code
    data["request_code"] = await generate_unique_request_code()
    # agency id from drop list
    agency = await get_agency_by_id_service(id=data["agency_id"])

    data = check_government_agency_payment(agency=agency, data=data)
    request = await init_adn_integrations_service_w_request_id(
        request_id=data.get("id"), data=data
    )

    return request


async def update_request_service(id: str, data: dict):
    """
    customer_support_id: Optional[str]
    status: str
    status_code: Optional[str]
    error_message: Optional[str] = None
    response_date: Optional[str] = None
    payment_status: str
    """
    request = await get_request_by_id(id)
    if not request:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    current_time = get_current_date_time_utc_7()
    data["updated_at"] = current_time

    await update_request(request, data)
    data = await get_request_by_id(id)
    return data.to_dict()


async def sync_kit_request_w_request_id_service(request_id: str):
    """
    IF no request created with identifier_code, return 404 NOT FOUND

    IF request's status is "SAMPLE_NOT_FOUND", then check whether new kits are created for request
    SAMPLE_NOT_FOUND -> SAMPLE_FOUND
        + Create "ADN_INTEGRATION"

    IF releated kits' status are different than "REGISTERED", then update request's status
    SAMPLE_FOUND -> IN_PROGRESS

    IF releated kits' status are "REVIEWED" or "COMPLETED", then update request's status
    IN_PROGRESS -> REVIEWED

    IF request's presigned links are not empty/null, call TECA request & update request's status:
    REVIEWED -> SENT

    If request's status is 'SENT', then just return request
    """

    data = await get_request_dict_by_id(id=request_id)

    if not data:
        err = f"Request with request_id {request_id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    identifier_code = data.get("identifier_code")

    if data.get("status") in [
        "SAMPLE_NOT_FOUND",
        "SAMPLE_FOUND",
        "IN_PROGRESS",
        "REQUIRE_REVIEW",
        "REVIEWED",
        "RENEWED",
        "SENT",
    ]:
        request = await init_adn_integrations_service_w_request_id(
            collect_date=data.get("collect_date"),
            request_id=request_id,
            identifier_code=identifier_code,
        )
        return request

    if data.get("status") in ["CANCELED"]:
        return data.to_dict()
        pass

    return data.to_dict()


async def sync_kit_request_service(identifier_code: str):
    """
    IF no request created with identifier_code, return 404 NOT FOUND

    IF request's status is "SAMPLE_NOT_FOUND", then check whether new kits are created for request
    SAMPLE_NOT_FOUND -> SAMPLE_FOUND
        + Create "ADN_INTEGRATION"

    IF releated kits' status are different than "REGISTERED", then update request's status
    SAMPLE_FOUND -> IN_PROGRESS

    IF releated kits' status are "REVIEWED" or "COMPLETED", then update request's status
    IN_PROGRESS -> REVIEWED

    IF request's presigned links are not empty/null, call TECA request & update request's status:
    REVIEWED -> SENT

    If request's status is 'SENT', then just return request
    """

    requests = await get_all_requests_by_identifier_code(
        identifier_code=identifier_code
    )
    results = []
    for request in requests:
        data = request.to_dict()
        if not data:
            err = f"Request with identifier_code {identifier_code} can not be found"
            http_code = HTTP_404_NOT_FOUND
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        # identifier_code = data.get('identifier_code')

        if data.get("status") in [
            "SAMPLE_NOT_FOUND",
            "SAMPLE_FOUND",
            "IN_PROGRESS",
            "REQUIRE_REVIEW",
            "REVIEWED",
            "RENEWED",
            "SENT",
        ]:
            # request = await init_adn_integrations_service(identifier_code=identifier_code)
            request = await init_adn_integrations_service_w_request_id(
                request_id=data.get("id"), identifier_code=identifier_code
            )
            results.append(request)
            # return request

        if data.get("status") in ["CANCELED"]:
            results.append(data)
            # return data.to_dict()

    return results


async def delete_request_by_id_service(id: str):
    """
    id: str
    """
    request = await get_request_by_id(id)

    await delete_request(integrationReq=request)

    data = await get_request_by_id(id)
    return data.to_dict()


async def combine_all_adn_results_for_a_specific_sample(
    adn_results: List[dict], samplecode: str, collect_date: str, loaiXetnghiem: str
) -> dict:
    danhSachKetQuaADN = []
    for adn_result in adn_results:
        quyTrinhXetNghiem = adn_result["data"]["thongTinHanhTrinhMau"].pop(
            "quyTrinhXetNghiem"
        )
        duLieuADN = adn_result["data"].pop("duLieuADN")
        loaiDuLieu = duLieuADN.pop("loaiDuLieu")
        adnId = quyTrinhXetNghiem.get("maXetNghiem")

        ketQuaADN = {
            "loaiDuLieu": loaiDuLieu,
            "adnId": adnId,
            "quyTrinhXetNghiem": quyTrinhXetNghiem,
            "duLieuADN": duLieuADN,
        }
        danhSachKetQuaADN.append(ketQuaADN)

    base_result = adn_results[0]
    base_result["data"]["ngayLayMau"] = collect_date
    donViXetNghiemId = base_result["data"].pop("maDvXN")
    base_result["data"]["donViXetNghiemId"] = donViXetNghiemId
    base_result["data"]["thongTinHanhTrinhMau"]["thuNhanMau"]["loaiXetNghiem"] = (
        loaiXetnghiem
    )
    # base_result['data']['danhSachKetQuaADN']=danhSachKetQuaADN

    if (
        base_result["data"]["thongTinHanhTrinhMau"]["thuNhanMau"]["maThuNhan"]
        != samplecode
    ):
        err = f"maThuNhan in ADN RESULT need to matched {samplecode}!"
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
        pass

    request_result = base_result
    request_result["data"]["danhSachKetQuaADN"] = danhSachKetQuaADN

    if len(danhSachKetQuaADN) > 0:
        code_response = vneid_request_reponse(is_success=True)
    else:
        code_response = vneid_request_reponse(is_success=False)
        curr_data = request_result.pop("data")
        request_result["data"] = {
            "hoTenCongDan": curr_data["hoTenCongDan"],
            "ngayThangNamSinh": curr_data["ngayThangNamSinh"],
            "soDinhDanh": curr_data["soDinhDanh"],
            "donViXetNghiemId": curr_data["donViXetNghiemId"],
        }

    request_result["trangThai"] = code_response["trangThai"]
    request_result["moTaLoi"] = code_response["moTaLoi"]

    date_obj = datetime.strptime(request_result["data"]["ngayLayMau"], "%Y-%m-%d")
    request_result["data"]["ngayLayMau"] = date_obj.strftime("%d/%m/%Y")

    try:
        _ = RequestResult(**request_result)
        logger.info("Validate Request ADN Result successful!")

    except ValidationError as e:
        http_code = HTTP_422_UNPROCESSABLE_ENTITY
        errs = failure_response(str(e))
        raise HTTPException(status_code=http_code, detail=errs)

    return request_result
