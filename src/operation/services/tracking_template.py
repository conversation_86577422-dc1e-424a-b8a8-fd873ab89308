from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking_template import *
from ..schemas.tracking_template import *
from ..utils.utils import convert_rowproxy_to_dict, failure_response


async def get_all_templates_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tt.created_at",
    congNghe: Optional[str] = None,
    gs_template_name: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_tracking_templates(
        offset=offset,
        size=size,
        order_by=order_by,
        congNghe=congNghe,
        gs_template_name=gs_template_name,
        include_deleted=include_deleted,
    )


async def get_template_by_id_service(id: str):
    template = await get_tracking_template_by_id(id)
    template = template.to_dict()
    templateDetail = {**template}
    templateDetail["cacBuocThuc<PERSON>ien"] = []

    templateSteps, _ = await get_all_tracking_template_steps(template_id=id)
    for templateStep in templateSteps:
        ts_dict = convert_rowproxy_to_dict(templateStep)
        templateStepDetail = {
            "gs_step_number": ts_dict.get("gs_step_number"),
            "step_id": ts_dict.get("step_id"),
            "tenBuoc": ts_dict.get("tenBuoc"),
            "template_step_id": ts_dict.get("template_step_id"),
        }
        templateDetail["cacBuocThucHien"].append(templateStepDetail)

    return templateDetail


async def create_template_service(data: dict):
    """
    congNghe: Optional[Literal[tuple(config['ALLOWED_TECHNOLOGY'])]]
    tenKit: Optional[str]
    gs_template_name: Optional[str]
    cacBuocThucHien: List[BuocThucHien]
    """
    return await create_tracking_template(data)


async def update_template_service(id: str, data: dict):
    """
    congNghe: Optional[Literal[tuple(config['ALLOWED_TECHNOLOGY'])]]
    tenKit: Optional[str]
    gs_template_name: Optional[str]
    cacBuocThucHien: List[BuocThucHien]
    """
    template = await get_tracking_template_by_id(id)
    if not template:
        err = f"Template with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_template(template, data)
    data = await get_tracking_template_by_id(id)
    return data.to_dict()


async def delete_template_by_id_service(id: str):
    """
    id: str
    """
    template = await get_tracking_template_by_id(id)

    await delete_tracking_template(template=template)

    data = await get_tracking_template_by_id(id)
    return data.to_dict()
