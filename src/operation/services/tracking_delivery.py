from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking import *
from ..cruds.tracking_collection import *
from ..cruds.tracking_delivery import *
from ..schemas.tracking_delivery import *
from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
    convert_rowproxy_to_dict_w_original_type,
    failure_response,
    format_date,
)


async def get_all_deliveries_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "td.created_at",
    maVanChuyen: Optional[str] = None,
    nhietDoChuyenGiao: Optional[str] = None,
    tinhTrangNiemPhong: Optional[bool] = None,
    ngayGioChuyenGiao_start_date: Optional[str] = None,
    ngayGioChuyenGiao_end_date: Optional[str] = None,
    diaDiemChuyenGiao: Optional[int] = None,
    tenDonViBanGiao: Optional[str] = None,
    tenDonViVanChuyen: Optional[str] = None,
    tenDonViNhanMau: Optional[str] = None,
    hoTenNhanVienBanGiao: Optional[str] = None,
    soDinhDanhNhanVienBanGiao: Optional[str] = None,
    hoTenNhanVienNhanMau: Optional[str] = None,
    soDinhDanhNhanVienNhanMau: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    if ngayGioChuyenGiao_start_date:
        ngayGioChuyenGiao_start_date = format_date(
            ngayGioChuyenGiao_start_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )

    if ngayGioChuyenGiao_end_date:
        ngayGioChuyenGiao_end_date = format_date(
            ngayGioChuyenGiao_end_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
        if ngayGioChuyenGiao_end_date < ngayGioChuyenGiao_start_date:
            raise ValueError("end date cannot be after start date")

    return await get_all_deliveries(
        offset=offset,
        size=size,
        order_by=order_by,
        maVanChuyen=maVanChuyen,
        nhietDoChuyenGiao=nhietDoChuyenGiao,
        tinhTrangNiemPhong=tinhTrangNiemPhong,
        ngayGioChuyenGiao_start_date=ngayGioChuyenGiao_start_date,
        ngayGioChuyenGiao_end_date=ngayGioChuyenGiao_end_date,
        diaDiemChuyenGiao=diaDiemChuyenGiao,
        tenDonViBanGiao=tenDonViBanGiao,
        tenDonViVanChuyen=tenDonViVanChuyen,
        tenDonViNhanMau=tenDonViNhanMau,
        hoTenNhanVienBanGiao=hoTenNhanVienBanGiao,
        soDinhDanhNhanVienBanGiao=soDinhDanhNhanVienBanGiao,
        hoTenNhanVienNhanMau=hoTenNhanVienNhanMau,
        soDinhDanhNhanVienNhanMau=soDinhDanhNhanVienNhanMau,
        include_deleted=include_deleted,
    )


async def get_delivery_by_id_service(id: str):
    # data = await get_tracking_delivery_by_id(id)
    # return data.to_dict()
    data, _ = await get_all_deliveries(tracking_delivery_id=id)
    result = convert_rowproxy_to_dict_w_original_type(data[0])
    return result


async def get_delivery_by_samplecode_service(samplecode: str):
    tracking = await get_tracking_by_samplecode(samplecode=samplecode)
    tracking_delivery = await get_tracking_delivery_by_tracking_id(
        tracking_id=tracking.id
    )
    tracking_delivery_id = tracking_delivery.id
    data, _ = await get_all_deliveries(tracking_delivery_id=tracking_delivery_id)
    result = convert_rowproxy_to_dict_w_original_type(data[0])
    return result


async def create_delivery_service(data: dict):
    """
    "tracking_id": str
    "maVanChuyen": str
    "nhietDoChuyenGiao": str
    "ngayGioChuyenGiao": str
    "donViBanGiao_id": int
    "diaDiemChuyenGiao": int,
    "donViVanChuyen_id": int
    "nhanVienBanGiao_id": str
    "tinhTrangNiemPhong": boolean,
    "donViNhanMau_id": int
    "nhanVienNhanMau_id": str
    "khac": str
    """
    data["ngayGioChuyenGiao"] = format_date(
        data.get("ngayGioChuyenGiao"),
        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
        validate_current_time=False,
    )
    return await create_tracking_delivery(data)


async def update_delivery_service(id: str, data: dict):
    """
    type: str
    """
    delivery = await get_tracking_delivery_by_id(id)
    if not delivery:
        err = f"Delivery with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_delivery(delivery, data)
    data = await get_tracking_delivery_by_id(id)
    return data.to_dict()


async def update_delivery_service_via_samplecode(samplecode: str, data: dict):
    tracking = await get_tracking_by_samplecode(samplecode=samplecode)
    tracking_delivery = await get_tracking_delivery_by_tracking_id(
        tracking_id=tracking.id
    )
    if not tracking_delivery:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    data["ngayGioChuyenGiao"] = format_date(
        data.get("ngayGioChuyenGiao"),
        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
        validate_current_time=False,
    )
    await update_tracking_delivery(tracking_delivery, data)

    result = await get_delivery_by_samplecode_service(samplecode=samplecode)
    return result


async def delete_delivery_by_id_service(id: str):
    """
    id: str
    """
    delivery = await get_tracking_delivery_by_id(id)

    await delete_tracking_delivery(delivery=delivery)

    data = await get_tracking_delivery_by_id(id)
    return data.to_dict()
