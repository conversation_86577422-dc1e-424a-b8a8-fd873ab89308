from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from src.operation.config import config

from ..cruds.tracking import *
from ..schemas.tracking import *
from ..utils.utils import (
    convert_datetime_flag_format,
    convert_rowproxy_to_dict_w_original_type,
    failure_response,
)
from .tracking_procedure import *


def transform_result(row):
    return {
        "thuNhanMau": {
            "maThuNhan": row["thuNhanMau_maThu<PERSON>han"],
            "donViThuNhanMau": int(row["thuNhanMau_donViThuNhanMau"]),
            "loaiXetNghiem": config["AGENCY_ACCOUNT_CODE"]["loaiXetNghiem"],
            "ngayGioThuThapMau": convert_datetime_flag_format(
                row["thuNhanMau_ngayGioThuThapMau"]
            ),
            "noiThuThapMau": row["thuNhanMau_noiThuThapMau"],
            "nhanVienLayMau": {
                "hoTenNhanVien": row["thuNhanMau_nhanVienLayMau_hoTenNhanVien"],
                "soDinhDanh": row["thuNhanMau_nhanVienLayMau_soDinhDanh"],
            },
            "nhanVienGhiHoSo": {
                "hoTenNhanVien": row["thuNhanMau_nhanVienGhiHoSo_hoTenNhanVien"],
                "soDinhDanh": row["thuNhanMau_nhanVienGhiHoSo_soDinhDanh"],
            },
            "nhanVienLuuMau": {
                "hoTenNhanVien": row["thuNhanMau_nhanVienLuuMau_hoTenNhanVien"]
                if row["thuNhanMau_nhanVienLuuMau_hoTenNhanVien"]
                else "",
                "soDinhDanh": row["thuNhanMau_nhanVienLuuMau_soDinhDanh"]
                if row["thuNhanMau_nhanVienLuuMau_soDinhDanh"]
                else "",
            },
        },
        "vanChuyenMau": {
            "maVanChuyen": row["vanChuyenMau_maVanChuyen"]
            if row["vanChuyenMau_maVanChuyen"]
            else "",
            "nhietDoChuyenGiao": row["vanChuyenMau_nhietDoChuyenGiao"],
            "tinhTrangNiemPhong": row["vanChuyenMau_tinhTrangNiemPhong"],
            "ngayGioChuyenGiao": convert_datetime_flag_format(
                row["vanChuyenMau_ngayGioChuyenGiao"]
            ),
            "diaDiemChuyenGiao": row["vanChuyenMau_diaDiemChuyenGiao"],
            "donViBanGiao": row["vanChuyenMau_donViBanGiao"],
            "donViVanChuyen": row["vanChuyenMau_donViVanChuyen"]
            if row["vanChuyenMau_donViVanChuyen"]
            else "",
            "donViNhanMau": row["vanChuyenMau_donViNhanMau"],
            "nhanVienBanGiao": {
                "hoTenNhanVien": row["vanChuyenMau_nhanVienBanGiao_hoTenNhanVien"],
                "soDinhDanh": row["vanChuyenMau_nhanVienBanGiao_soDinhDanh"],
            },
            "nhanVienNhanMau": {
                "hoTenNhanVien": row["vanChuyenMau_nhanVienNhanMau_hoTenNhanVien"],
                "soDinhDanh": row["vanChuyenMau_nhanVienNhanMau_soDinhDanh"],
            },
            "khac": row["khac"] if row["khac"] else "",
        },
    }


async def get_all_kits_tracking_releated_to_samplecode(samplecode: str):
    sample_tracking_detail = await get_sample_tracking_detail(samplecode=samplecode)
    tracking_detail_dict = convert_rowproxy_to_dict_w_original_type(
        sample_tracking_detail
    )
    sample_tracking_result = transform_result(tracking_detail_dict)

    return sample_tracking_result


async def get_kit_tracking_detail_w_samplecode_and_barcode(
    samplecode: str, barcode: str
):
    sample_tracking_result = await get_all_kits_tracking_releated_to_samplecode(
        samplecode=samplecode
    )
    sample_tracking_result[
        "quyTrinhXetNghiem"
    ] = await get_vneid_procedure_by_barcode_service(barcode=barcode)
    # kit_tracking_result =''
    return sample_tracking_result


async def get_adn_result_metadata_via_barcode(barcode: str):
    tracking_procedure = await get_tracking_procedure_via_barcode(barcode=barcode)
    tracking_procedure = convert_rowproxy_to_dict(tracking_procedure)
    # print("tracking_procedure: ",tracking_procedure)
    data = {
        "maXetNghiem": tracking_procedure.get("maXetNghiem"),
        "congNghe": tracking_procedure.get("congNghe"),
        "tenKit": tracking_procedure.get("tenKit"),
    }

    return data


async def get_all_trackings_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "t.created_at",
    samplecode: Optional[str] = None,
    barcode: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_trackings(
        size=size,
        offset=offset,
        order_by=order_by,
        samplecode=samplecode,
        barcode=barcode,
        include_deleted=include_deleted,
    )


async def get_tracking_by_barcode_service(barcode: str):
    data = await get_tracking_by_barcode(barcode)
    return data.to_dict()


async def get_tracking_by_id_service(id: str):
    data = await get_tracking_by_id(id)
    return data.to_dict()


async def create_tracking_service(data: dict):
    """
    tenDonVi: str
    gs_area: str
    gs_area_code: str
    """
    return await create_tracking(data)


async def update_tracking_service(id: str, data: dict):
    """
    type: str
    """
    tracking = await get_tracking_by_id(id)
    if not tracking:
        err = f"Unit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking(tracking, data)
    data = await get_tracking_by_id(id)
    return data.to_dict()


async def delete_tracking_by_id_service(id: str):
    """
    id: str
    """
    tracking = await get_tracking_by_id(id)

    await delete_tracking(tracking=tracking)

    data = await get_tracking_by_id(id)
    return data.to_dict()
