from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.adn_integration import *
from ..schemas.adn_integration import *
from ..utils.utils import convert_rowproxy_to_dict, failure_response


async def get_all_adn_integrations_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "ai.created_at",
    adn_integration_id: Optional[str] = None,
    request_id: Optional[str] = None,
    samplecode: Optional[str] = None,
    barcode: Optional[str] = None,
    method: Optional[str] = None,
    status: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    # if receipt_start_date:
    #     start_date_std = format_date(receipt_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if receipt_end_date:
    #     end_date_std = format_date(receipt_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if end_date_std < start_date_std:
    #         raise ValueError("end date cannot be after start date")
    # if release_start_date:
    #     release_start_date_std = format_date(release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if release_end_date:
    #     release_end_date_std = format_date(release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if release_end_date < release_start_date:
    #         raise ValueError("end date cannot be after start date")
    # if collection_start_date:
    #     collection_start_date_std = format_date(collection_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if collection_end_date:
    #     collection_end_date_std = format_date(collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if collection_end_date_std < collection_start_date_std:
    #         raise ValueError("end date cannot be after start date")
    # if actual_report_release_start_date:
    #     actual_report_release_start_date_std = format_date(actual_report_release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if actual_report_release_end_date:
    #     actual_report_release_end_date_std = format_date(actual_report_release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if actual_report_release_end_date_std < actual_report_release_start_date_std:
    #         raise ValueError("end date cannot be after start date")

    return await get_all_adn_integrations(
        offset=offset,
        size=size,
        order_by=order_by,
        adn_integration_id=adn_integration_id,
        request_id=request_id,
        samplecode=samplecode,
        barcode=barcode,
        method=method,
        status=status,
        include_deleted=include_deleted,
    )


async def get_adn_integration_by_id_service(id: str):
    return await get_adn_integration_by_id(id)


async def get_adn_integration_by_codes_and_type(
    samplecode: str, barcode: str, adn_type: str
):
    adn_integration = await get_adn_integrations_w_codes_and_type(
        samplecode=samplecode, barcode=barcode, adn_type=adn_type
    )
    adn_integration_dict = convert_rowproxy_to_dict(adn_integration)
    return adn_integration_dict


async def create_adn_integration_service(data: dict):
    """
    samplecode: str
    barcode: Optional[str]
    type_id: Optional[str]
    status: str
    presigned_s3_url: Optional[str]
    raw_adn_s3_obj_key: Optional[str]
    review_required: bool = True
    """
    return await create_adn_integration(data)


async def update_adn_integration_service(id: str, data: dict):
    """
    type: str
    """
    adn_integration = await get_adn_integration_by_id(id)
    if not adn_integration:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    return await update_adn_integration(adn_integration, data)

    # return await get_adn_integration_by_id(id)


async def delete_adn_integration_by_id_service(id: str):
    """
    id: str
    """
    adn_integration = await get_adn_integration_by_id(id)

    return await delete_adn_integration(adn_integration=adn_integration)
