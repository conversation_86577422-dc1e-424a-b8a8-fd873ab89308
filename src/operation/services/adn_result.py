from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from src.operation.config import config

from ..cruds.adn_result import *
from ..schemas.adn_result import *
from ..utils.utils import (
    convert_rowproxy_to_dict,
    failure_response,
    parse_presigned_s3_url,
    renew_obj_key_w_region,
)
from .adn_integration import *
from .request import *
from .tracking import *


async def get_all_results_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "ar.created_at",
    tenFileADN: Optional[str] = None,
    congNghe: Optional[str] = None,
    tenKit: Optional[str] = None,
    tenThuMucTho: Optional[str] = None,
    loaiDuLieu: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_results(
        offset=offset,
        size=size,
        order_by=order_by,
        tenFileADN=tenFileADN,
        congNghe=congNghe,
        tenKit=tenKit,
        tenThuMucTho=tenThuMucTho,
        loaiDuLieu=loaiDu<PERSON><PERSON>,
        include_deleted=include_deleted,
    )


async def get_result_by_id_service(id: str):
    data = await get_adn_result_by_id(id)
    return data.to_dict()


async def get_result_by_adn_integration_id_service(adn_integration_id: str):
    data = await get_adn_result_via_adn_integration_id(
        adn_integration_id=adn_integration_id
    )
    return data.to_dict()


def get_adn_result_links(adn_integration: dict):
    presigned_s3_url = adn_integration.get("presigned_s3_url")
    raw_adn_s3_obj_key = adn_integration.get("raw_adn_s3_obj_key")
    gs_adn_result_s3_url, gs_raw_result_s3_url = "", ""

    ADN_OUTPUT_BUCKET = config["AGENCY_STORAGE"]["BUCKET"]
    REGION_NAME = config["AGENCY_STORAGE"]["BUCKET_REGION"]
    if presigned_s3_url:
        gs_adn_result_s3_url = renew_obj_key_w_region(
            ADN_OUTPUT_BUCKET, presigned_s3_url, REGION_NAME
        )
    if raw_adn_s3_obj_key:
        gs_raw_result_s3_url = renew_obj_key_w_region(
            ADN_OUTPUT_BUCKET, raw_adn_s3_obj_key, REGION_NAME
        )

    return gs_adn_result_s3_url, gs_raw_result_s3_url


async def generate_result_service(data: dict, returnRawADN: bool = False):
    """
    adn_integration_id: str
    """

    adn_integration_id = data.get("adn_integration_id")
    results, _ = await get_all_adn_integrations(adn_integration_id=adn_integration_id)

    if not results:
        err = f"Error: adn_integration with id {adn_integration_id} not found!"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    adn_integration = convert_rowproxy_to_dict(results[0])
    request_id = adn_integration.get("request_id")

    # if adn_result not exist with adn_integration_id
    gs_adn_result_s3_url, gs_raw_result_s3_url = get_adn_result_links(
        adn_integration=adn_integration
    )

    if not await is_adn_result_existed(adn_integration_id):
        # init
        adn_result_metadata = await get_adn_result_metadata_via_barcode(
            barcode=adn_integration.get("barcode")
        )
        tenFileADN = (
            adn_result_metadata.get("maXetNghiem")
            + config["AGENCY_STORAGE"]["ADN_RESULT_EXTENSION"]
        )
        congNghe = adn_result_metadata.get("congNghe")
        tenKit = adn_result_metadata.get("tenKit")
        tenThuMucTho = request_id + config["AGENCY_STORAGE"]["RAW_RESULT_EXTENSION"]
        loaiDuLieu = adn_integration.get("adn_type")
        curr_adn_result = {
            "adn_integration_id": adn_integration_id,
            "tenFileADN": tenFileADN,
            "congNghe": congNghe,
            "tenKit": tenKit,
            "tenThuMucTho": tenThuMucTho,
            "loaiDuLieu": loaiDuLieu,
            "gs_adn_result_s3_url": gs_adn_result_s3_url,
            "gs_raw_result_s3_url": gs_raw_result_s3_url,
        }
        return await create_adn_result(curr_adn_result)
    else:
        # else update
        # gs_adn_result_s3_url & gs_raw_result_s3_url
        adn_result = await get_adn_result_via_adn_integration_id(
            adn_integration_id=adn_integration_id
        )

        # print("gs_adn_result_s3_url: ", gs_adn_result_s3_url)
        # print("gs_raw_result_s3_url: ", gs_raw_result_s3_url)

        current_time = get_current_date_time_utc_7()
        updated_data = {
            "gs_adn_result_s3_url": gs_adn_result_s3_url,
            "gs_raw_result_s3_url": gs_raw_result_s3_url,
            "updated_at": current_time,
        }

        await update_adn_result(adn_result=adn_result, data=updated_data)
        updated_adn_result = await get_adn_result_via_adn_integration_id(
            adn_integration_id=adn_integration_id
        )
        updated_adn_result = updated_adn_result.to_dict()
        updated_adn_result["noiDungFileADN"] = ""
        updated_adn_result["noiDungThuMucTho"] = ""

        # print("gs_adn_result_s3_url: ", gs_adn_result_s3_url)
        if gs_adn_result_s3_url:
            updated_adn_result["noiDungFileADN"] = parse_presigned_s3_url(
                gs_adn_result_s3_url, updated_adn_result["tenFileADN"]
            )

        if gs_raw_result_s3_url and returnRawADN:
            updated_adn_result["noiDungThuMucTho"] = parse_presigned_s3_url(
                gs_raw_result_s3_url, updated_adn_result["tenThuMucTho"]
            )

        return updated_adn_result


async def generate_result_with_codes_and_type_service(
    request_id: str,
    samplecode: str,
    barcode: str,
    adn_type: str,
    returnRawADN: bool = False,
):
    adn_integration_dict = await get_adn_integration_by_codes_and_type(
        samplecode=samplecode, barcode=barcode, adn_type=adn_type
    )

    data = {"adn_integration_id": adn_integration_dict.get("id")}

    adn_result_detail = await generate_result_service(data, returnRawADN=returnRawADN)
    vneid_adn_result = {
        "tenFileADN": adn_result_detail.get("tenFileADN"),
        "noiDungFileADN": adn_result_detail.get("noiDungFileADN"),
        "congNghe": adn_result_detail.get("congNghe"),
        "tenKit": adn_result_detail.get("tenKit"),
        # "tenThuMucTho": adn_result_detail.get('tenThuMucTho'),
        "loaiDuLieu": adn_result_detail.get("loaiDuLieu"),
    }
    if returnRawADN:
        vneid_adn_result["tenThuMucTho"] = adn_result_detail.get("tenThuMucTho")
        vneid_adn_result["noiDungThuMucTho"] = adn_result_detail.get("noiDungThuMucTho")

    return vneid_adn_result, adn_integration_dict


async def create_result_service(data: dict):
    """
    adn_integration_id: str
    tenFileADN: str
    congNghe: Literal[tuple(config['ALLOWED_TECHNOLOGY'])]
    tenKit: str
    tenThuMucTho: Optional[str]
    loaiDuLieu: Literal['STR','mtADN','SNP']
    gs_adn_result_s3_url: Optional[str]
    gs_raw_result_s3_url: Optional[str]
    """
    return await create_adn_result(data)


async def update_result_service(id: str, data: dict):
    """
    type: str
    """
    result = await get_adn_result_by_id(id)
    if not result:
        err = f"ADN Result with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_adn_result(result, data)
    data = await get_adn_result_by_id(id)
    return data.to_dict()


async def delete_result_by_id_service(id: str):
    """
    id: str
    """
    result = await get_adn_result_by_id(id)

    await delete_adn_result(result=result)

    data = await get_adn_result_by_id(id)
    return data.to_dict()
