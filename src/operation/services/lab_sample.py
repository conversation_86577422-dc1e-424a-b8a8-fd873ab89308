from ..cruds.register_kit import get_kit, create_status, get_kit_v3, get_sample_by_samplecode_v3
from ..cruds.lab_sample import (
    create_lab_sample, 
    get_current_lid,
    count_lab_sample,
    get_filtered_lab_sample_list,
    mark_missing_sample,
    mark_sample_passed,
    mark_sample_failed,
    get_filtered_lab_sample_list_v3,
    mark_sample_passed_v3,
    mark_sample_failed_v3,
    mark_missing_sample_v3,
    get_lab_sample_w_lid,
    get_lab_sample_by_barcode,
    get_all_lab_samples_by_samplecode_v3
)
from ..schemas.lab_sample import LabSampleCreationReq
from ..models.models import LabSample
from ..utils.utils import (
    get_current_date_time_utc_7, 
    format_date, 
    DEFAULT_DATE_NO_TIME_ZONE, 
    get_technology_w_product_code, 
    get_product_n_technology,
    combine_sample_infor_w_product_infor,
    convert_rowproxy_to_dict
)
from .. import logger
from typing import Optional, List
from ..config import config
import requests
from ..services.kit import send_check_booking_message
from ..services.sample import get_sample_list_v3, get_sample_list_labcheck

TECHNOLOGY_MICROARRAY = 'MICROARRAY'
TECHNOLOGY_PCR = 'PCR'

async def create_lab_sample_obj(
    creation_req: LabSampleCreationReq
):
    if creation_req.lab_receipt_date is None:
        creation_req.lab_receipt_date = get_current_date_time_utc_7()
    kit_info = await get_kit(creation_req.barcode)
    if kit_info is None:
        logger.error("create lab sample error: invalid barcode")
        raise ValueError("barcode not found: ", creation_req.barcode)
    #create new lid if not specified
    if creation_req.lid is None:
        creation_req.lid = await get_next_lid()
        
    #auto parse technology from kit
    technology, _ = await get_technology_w_product_code(kit_info.product_code)
    logger.info(f"KIT registered with technology: {technology}")
        
    lab_sample = LabSample(
        lid=creation_req.lid,
        barcode=creation_req.barcode,
        lab_receipt_date=get_current_date_time_utc_7(),
        note=creation_req.note,
        technology=technology,
        created_at = get_current_date_time_utc_7(),
        updated_at = get_current_date_time_utc_7()
    )
    return lab_sample

async def create_lab_sample_srv(
    creation_req: LabSampleCreationReq
):
    lab_sample = await create_lab_sample(creation_req=creation_req)
    await create_lab_sample(lab_sample)
    return lab_sample
    
#logic to get next lid
#lid format: "YYNNNN" -> "{}{:04d}"
async def get_next_lid():
    current_year = str(get_current_date_time_utc_7().year)
    lid_next_year = None
    lid_next_num = None
    count_lab_samples = await count_lab_sample()
    if count_lab_samples == 0:
        lid_next_year = current_year[-2:]
        lid_next_num = 1
    else:
        current_system_lid = await get_current_lid()
        print()
        lid_year_curr = current_system_lid[:2]
        if current_year[-2:] != lid_year_curr:
            lid_next_year = current_year[-2:]
            lid_next_num = 1
        else:
            lid_next_year = lid_year_curr
            lid_next_num = int(current_system_lid[2:]) + 1
    return "{}{:04d}".format(lid_next_year, lid_next_num)

async def get_lab_check_sample_list(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    barcode: Optional[str]=None,
):
    results, total = await get_filtered_lab_sample_list(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        filter_status_list=[
            config['LAB_CHECK_KIT_STATUS']
            # config['MISSING_SAMPLE_KIT_STATUS'],
            # config['MISSING_INFO_KIT_STATUS']
        ]
    )

    if len(results) > 0:
        data = []
        for row in results:
            result = dict(row)
            technology, product_name = await get_technology_w_product_code(result['product_code'])
            result['technology'] = technology
            result['product_name'] = product_name
            data.append(result)
        return data, total
    
    return results, total

async def get_lab_checked_sample_list(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    barcode: Optional[int]=None,
    phone_number: Optional[str]=None,
    lid: Optional[str]=None,
    name: Optional[str]=None,
    product_code: Optional[str]=None,
    account_name: Optional[str]=None,
    current_status: Optional[str]=None,
    lab_check_start_date: Optional[str]=None,
    lab_check_end_date: Optional[str]=None,
    lab_receipt_start_date: Optional[str]=None,
    lab_receipt_end_date: Optional[str]=None,
    release_start_date: Optional[str]=None,
    release_end_date: Optional[str]=None,
    batch_barcode: Optional[int]=None,
    plate_name: Optional[int]=None,
    dna_qc_status: Optional[str]=None,
    technology: Optional[str]=None,
    positive_control: Optional[bool]=None,
    is_all: Optional[bool] = False
):
    if lab_check_start_date:
        lab_check_start_date_std = format_date(lab_check_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if lab_check_end_date:
        lab_check_end_date_std = format_date(lab_check_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if lab_check_end_date < lab_check_start_date:
            raise ValueError("end date cannot be after start date")
    if release_start_date:
        release_start_date_std = format_date(release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if release_end_date:
        release_end_date_std = format_date(release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if release_end_date < release_start_date:
            raise ValueError("end date cannot be after start date")
    results, total = await get_filtered_lab_sample_list(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        phone_number=phone_number,
        lid=lid,
        name=name,
        product_code=product_code,
        account_name=account_name,
        filter_status_list=[
            config['PASSED_LAB_CHECK_KIT_STATUS'],
            config['FAILED_LAB_CHECK_KIT_STATUS'],
            config['COMPLETED_KIT_STATUS'],
            config['REJECTED_KIT_STATUS'],
            config['DELETED_KIT_STATUS'],
            config['CANCELLED_KIT_STATUS'],
        ],
        current_status=current_status,
        lab_check_start_date=lab_check_start_date_std if lab_check_start_date else None,
        lab_check_end_date=lab_check_end_date_std if lab_check_end_date else None,
        lab_receipt_start_date=lab_receipt_start_date,
        lab_receipt_end_date=lab_receipt_end_date,
        release_start_date=release_start_date_std if release_start_date else None,
        release_end_date=release_end_date_std if release_end_date else None,
        batch_barcode=batch_barcode,
        plate_name=plate_name,
        dna_qc_status=dna_qc_status,
        technology=technology,
        positive_control=positive_control,
        is_all=is_all
    )
    return results, total

async def update_lab_check_status(
    barcode: str,
    status: str,
    note: Optional[str]=None,
):
    if status == config['PASSED_LAB_CHECK_KIT_STATUS']:
        lab_sample = await create_lab_sample_obj(LabSampleCreationReq(
            barcode=barcode
        ))
        await mark_sample_passed(
            barcode=barcode,
            lab_sample=lab_sample
        )
    elif status == config['FAILED_LAB_CHECK_KIT_STATUS']:
        await mark_sample_failed(
            barcode=barcode,
        )
    elif status in [config['MISSING_SAMPLE_KIT_STATUS'], config['MISSING_INFO_KIT_STATUS']]:
        await mark_missing_sample(
            barcode=barcode,
            status=status,
            note=note
        )
    else:
        raise ValueError("invalid lab check status")
    await send_check_booking_message(barcode=barcode)

async def update_lab_check_status_by_batch(
    barcode_list: List[str],
    status: str
):
    if status == config['PASSED_LAB_CHECK_KIT_STATUS']:
        for barcode in barcode_list:
            lab_sample = await create_lab_sample_obj(LabSampleCreationReq(
                barcode=barcode
            ))
            await mark_sample_passed(
                barcode=barcode,
                lab_sample=lab_sample
            )
    elif status == config['FAILED_LAB_CHECK_KIT_STATUS']:
        for barcode in barcode_list:
            await mark_sample_failed(
                barcode=barcode,
            )
    elif status in [config['MISSING_SAMPLE_KIT_STATUS'], config['MISSING_INFO_KIT_STATUS']]:
        for barcode in barcode_list:
            await mark_missing_sample(
                barcode=barcode,
                status=status,
            )
    else:
        raise ValueError("invalid lab check status")

def get_mapping_from_product_list():
    product_list_url = config["PRODUCT_LIST_URL"]

    request_headers = {'Content-type': 'application/json', 'Accept': 'text/plain'}
    
    res = requests.get(product_list_url,
                        headers=request_headers,
                        timeout=10)
    res.raise_for_status()
    products = res.json()['data']
    mapping = []
    for product in products:
        entry = dict()
        entry['product_code']=product['code']
        entry['technology']=product['technology']
        entry['product_name']=product['name']
        mapping.append(entry)
    return mapping

def map_samples_w_tech_by_product_code(data, mapping):
    results = []
    for sample in data:
        entry = dict(sample)
        product_code = entry['product_code']
        entry['technology'] = None

        if product_code is None:
            results.append(entry)
            continue

        for product in mapping:
            if product_code == product['product_code']:
                entry['technology'] = product['technology']
                break
        
        if entry['technology'] is None:
            logger.error(f"Can't map technology for product code {product_code}")
            
        entry.pop('identifier_code')
        entry.pop('full_name')
        entry.pop('dob')
        # entry.pop('yob')
        
        if entry['gender']=='male':
            entry['gender'] = 1 
        if entry['gender']=='female':
            entry['gender'] = 2

        results.append(entry)

    return results

async def get_lab_check_sample_list_v3(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    barcode: Optional[str]=None,
):
    # results, total = await get_sample_list_v3(
    results, total = await get_sample_list_labcheck(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        current_status=config['LAB_CHECK_KIT_STATUS']
    )
    
    # print("results lab checK: ", results)

    product_mapping = await get_product_n_technology()

    # print("product_mapping: ",product_mapping)

    data = combine_sample_infor_w_product_infor(results, product_mapping)
    return data, total


async def get_lab_check_passed_sample_list_v3(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    barcode: Optional[str]=None,
):
    results, total = await get_sample_list_v3(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        current_status=config['PASSED_LAB_CHECK_KIT_STATUS']
    )
    
    # print("results lab checK: ", results)

    product_mapping = await get_product_n_technology()

    # print("product_mapping: ",product_mapping)

    data = combine_sample_infor_w_product_infor(results, product_mapping)
    return data, total


async def create_lab_sample_obj_v3(
    creation_req: LabSampleCreationReq
):
    if creation_req.lab_receipt_date is None:
        creation_req.lab_receipt_date = get_current_date_time_utc_7()

    kit_info = await get_kit_v3(creation_req.barcode)
    if kit_info is None:
        logger.error("create lab sample error: invalid barcode")
        raise ValueError("barcode not found: ", creation_req.barcode)
    #create new lid if not specified
    if creation_req.lid is None:
        creation_req.lid = await get_next_lid()
    #auto parse technology from kit
    technology, _ = await get_technology_w_product_code(kit_info.product_code)
    logger.info(f"KIT registered with technology: {technology}")
        
    lab_sample = LabSample(
        lid=creation_req.lid,
        barcode=creation_req.barcode,
        samplecode=kit_info.samplecode,
        lab_receipt_date=get_current_date_time_utc_7(),
        note=creation_req.note,
        technology=technology,
        created_at = get_current_date_time_utc_7(),
        updated_at = get_current_date_time_utc_7()
    )
    return lab_sample

async def update_lab_check_status_v3(
    barcode: str,
    status: str,
    note: Optional[str]=None,
):
    if status == config['PASSED_LAB_CHECK_KIT_STATUS']:
        lab_sample = await create_lab_sample_obj_v3(LabSampleCreationReq(
            barcode=barcode,
        ))
        await mark_sample_passed_v3(
            barcode=barcode,
            lab_sample=lab_sample
        )
    elif status == config['FAILED_LAB_CHECK_KIT_STATUS']:
        await mark_sample_failed_v3(
            barcode=barcode,
        )
    elif status in [config['MISSING_SAMPLE_KIT_STATUS'], config['MISSING_INFO_KIT_STATUS']]:
        await mark_missing_sample_v3(
            barcode=barcode,
            status=status,
            note=note
        )
    else:
        raise ValueError("invalid lab check status")
    await send_check_booking_message(barcode=barcode)


async def get_lab_checked_sample_list_v3(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    order_by: Optional[str]=None,
    barcode: Optional[int]=None,
    phone_number: Optional[str]=None,
    lid: Optional[str]=None,
    name: Optional[str]=None,
    product_code: Optional[str]=None,
    account_name: Optional[str]=None,
    current_status: Optional[str]=None,
    lab_check_start_date: Optional[str]=None,
    lab_check_end_date: Optional[str]=None,
    lab_receipt_start_date: Optional[str]=None,
    lab_receipt_end_date: Optional[str]=None,
    release_start_date: Optional[str]=None,
    release_end_date: Optional[str]=None,
    batch_barcode: Optional[int]=None,
    plate_name: Optional[int]=None,
    dna_qc_status: Optional[str]=None,
    technology: Optional[str]=None,
    positive_control: Optional[bool]=None,
    is_all: Optional[bool] = False
):
    if lab_check_start_date:
        lab_check_start_date_std = format_date(lab_check_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if lab_check_end_date:
        lab_check_end_date_std = format_date(lab_check_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if lab_check_end_date < lab_check_start_date:
            raise ValueError("end date cannot be after start date")
    if release_start_date:
        release_start_date_std = format_date(release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if release_end_date:
        release_end_date_std = format_date(release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if release_end_date < release_start_date:
            raise ValueError("end date cannot be after start date")
    results, total = await get_filtered_lab_sample_list_v3(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        phone_number=phone_number,
        lid=lid,
        name=name,
        product_code=product_code,
        account_name=account_name,
        filter_status_list=[
            config['PASSED_LAB_CHECK_KIT_STATUS'],
            config['FAILED_LAB_CHECK_KIT_STATUS'],
            config['COMPLETED_KIT_STATUS'],
            config['REJECTED_KIT_STATUS'],
            config['DELETED_KIT_STATUS'],
            config['CANCELLED_KIT_STATUS'],
        ],
        current_status=current_status,
        lab_check_start_date=lab_check_start_date_std if lab_check_start_date else None,
        lab_check_end_date=lab_check_end_date_std if lab_check_end_date else None,
        lab_receipt_start_date=lab_receipt_start_date,
        lab_receipt_end_date=lab_receipt_end_date,
        release_start_date=release_start_date_std if release_start_date else None,
        release_end_date=release_end_date_std if release_end_date else None,
        batch_barcode=batch_barcode,
        plate_name=plate_name,
        dna_qc_status=dna_qc_status,
        technology=technology,
        positive_control=positive_control,
        is_all=is_all
    )
    return results, total



async def match_barcode_n_lid(lid: str, barcode: str):
    if not lid and not barcode:
        raise ValueError("Barcode and lid can't both be empty!")
    
    if lid:
        lab_sample = await get_lab_sample_w_lid(lid=lid)
        if not barcode or lab_sample.barcode == barcode:
            print('Ha???')
            return lid, lab_sample.barcode
        else:
            raise ValueError("Barcode does not match lid!")
    
    lab_sample = await get_lab_sample_by_barcode(barcode=barcode)
    return lab_sample.lid, barcode

async def get_all_lids_via_samplecode_v3(samplecode: str):
    lab_samples = await get_all_lab_samples_by_samplecode_v3(samplecode=samplecode)
    
    lid_list = []
    for lab_sample in lab_samples: 
        lid_list.append(lab_sample.lid)
    
    return lid_list