import json
from datetime import datetime, timedelta

from src.operation.config import config

from ..cruds.batch_mapping import *
from ..cruds.sample_mapping import get_all_chip_id_bind_to_plate_name
from ..utils.utils import DEFAULT_DATE_STR, format_date, list_execution_function
from .sample_management import *


async def get_recent_execution_by_batch(type, number):
    technology = type.upper()
    results = None

    if technology == "MICROARRAY":
        results = await get_dry_lab_execution_status_for_microarray_batch(
            technology, number
        )
        pass
    if technology == "PCR":
        result = get_dry_lab_execution_status_for_pcr_batch(technology, number)
        if result:
            results = [result]
        else:
            results = []

    return results


async def get_dry_lab_execution_status_for_microarray_batch(technology, number):
    if technology != "MICROARRAY":
        err = f"Technology's supposed to be MICROARRAY instead of {technology} "
        raise ValueError(err)

    # BATCH : PLATE = 1 : 1
    state_machine_arn = config.get("STATE_MACHINE_ARN").get(technology)
    list_of_executions = list_execution_function(state_machine_arn)
    logger.info(f"Tech {technology} sm ARN {state_machine_arn}")
    results = await get_all_chip_id_bind_to_plate_name(
        type=technology, plate_name=str(number)
    )
    field_names = ["chip_id", "type", "plate_name"]
    chips = [
        {field_names[idx]: res[idx] for idx in range(len(field_names))}
        for res in results
    ]
    chip_ids = [chip["chip_id"] for chip in chips]

    results = get_execution_status_w_microarray(
        technology, list_of_executions, chip_ids
    )
    for res in results:
        logger.info(f"CHIP_ID: {res['batch_barcode']} && STATUS {res['status']}")
        logger.info(res)
    return results


def get_dry_lab_execution_status_for_pcr_batch(technology, number):
    # BATCH : PLATE = 1 : 1
    if technology != "PCR":
        err = f"Technology's supposed to be PCR instead of {technology} "
        raise ValueError(err)

    state_machine_arn = config.get("STATE_MACHINE_ARN").get(technology)
    list_of_executions = list_execution_function(state_machine_arn)
    logger.info(f"Tech {technology} sm ARN {state_machine_arn}")
    result = get_execution_status_w_pcr(technology, list_of_executions, str(number))
    if result:
        logger.info(
            f"BATCH_NUMBER: {result['batch_barcode']} && STATUS {result['status']}"
        )
        logger.info(result)
    else:
        logger.info(f"NO RECENT HISTORY OF BATCH_NUMBER {number}")
    # get_binded_plate_names_w_tech_n_batch
    return result


def standardize_date_w_name_service(data, date_name):
    try:
        logger.info(f"DATA: {data}")
        logger.info(f"DATE NAME: {date_name}")
        now = datetime.now()
        date_time = now.strftime("%m/%d/%Y,%H:%M:%S")
        current_time = date_time.split(",")[1]
        temp_date = data[date_name] + "T" + current_time + ".000000Z"
        formated_date = format_date(
            temp_date, DEFAULT_DATE_STR, validate_current_time=False
        )
        data[date_name] = formated_date
        logger.info(f"DATA: {data}")
        return data

    except Exception as e:
        raise e


def standardize_date_w_name_service_utc_7(data, date_name):
    try:
        logger.info(f"DATA: {data}")
        logger.info(f"DATE NAME: {date_name}")
        now = datetime.now() + timedelta(hours=7)
        date_time = now.strftime("%m/%d/%Y,%H:%M:%S")
        current_time = date_time.split(",")[1]
        temp_date = data[date_name] + "T" + current_time + ".000000Z"
        formated_date = format_date(
            temp_date, DEFAULT_DATE_STR, validate_current_time=False
        )
        data[date_name] = formated_date
        logger.info(f"DATA: {data}")
        return data

    except Exception as e:
        raise e


def get_today_date_service():
    try:
        now = datetime.now()
        date_time = now.strftime("%Y-%m-%d,%H:%M:%S")
        today_date = date_time.split(",")[0]
        return today_date
    except Exception as e:
        raise e

    # return None, err


async def check_is_plate_added_to_batch(technology: str, plate_name: str):
    logger.info(f"Check if plate {plate_name} added to batch")
    # CHECK BATCH NUMBER (=plate_name) existed only applied WHERE batch.number=plate.name
    data, total = await get_available_sample_mappings(
        offset=0,
        size=100,
        plate_name=plate_name,
        order_by="sm.updated_at",
        dna_qc_status="PASS",
        technology=technology.upper(),
        is_added_to_batch=True,
        export=True,
    )

    if data:
        return True, data[0]["batch_barcode"]
    return False, None


def get_execution_status_w_microarray(technology, list_of_executions, chip_ids):
    results = []
    for exec in list_of_executions:
        input = json.loads(exec["input"])
        if input["chip_id"] in chip_ids and exec["status"] != "ABORTED":
            entry = {
                "chip_id": input["chip_id"],
                "batch_barcode": input["batch_barcode"],
                "status": exec["status"],
                "technology": technology,
            }
            results.append(entry)
            chip_ids.remove(input["chip_id"])
    return results


def get_execution_status_w_pcr(technology, list_of_executions, batch_id):
    result = None
    for exec in list_of_executions:
        input = json.loads(exec["input"])
        if input["batch_id"] == batch_id and exec["status"] != "ABORTED":
            result = {
                "batch_barcode": input["batch_id"],
                "status": exec["status"],
                "technology": technology,
            }
            break
    return result
