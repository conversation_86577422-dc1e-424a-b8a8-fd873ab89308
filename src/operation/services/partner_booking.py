import pytz
from datetime import datetime
from ..schemas.partner_booking import (
    PartnerBookingCreationReq, 
    PartnerBookingUpdateReq, 
    PartnerBookingResultReq, 
    SyncBookingInfoReq,
    ResultUpdateReq,
)
from ..schemas.notification import NotificationReq
from ..cruds.partner_booking import *
from ..producer import send_msg_to_queue
from ..config import config
from .. import logger
from ..models.partner_order import *
from .notification import send_notification_to_group
from ..utils.utils import get_product_by_product_code, get_current_time_gmt_plus_7
import json

TIME_FORMAT = "%Y-%m-%dT%H:%M:%SZ"

class MessageType(Enum):
    CREATE='CREATE'
    UPDATE='UPDATE'


async def get_partner_booking_srv(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    partner_name: Optional[str]=None,
    customer_name: Optional[str]=None,
    customer_phone: Optional[str]=None,
    payment_method: Optional[str]=None,
    payment_status: Optional[str]=None,
    status: Optional[str]=None,
    consulted: Optional[str]=None,
    creation_start_date: Optional[str]=None,
    creation_end_date: Optional[str]=None,
    include_deleted: bool=False,
):
    return await get_all_booking(
        offset=offset,
        size=size,
        partner_name=partner_name,
        customer_name=customer_name,
        customer_phone=customer_phone,
        payment_method=payment_method,
        payment_status=payment_status,
        status=status,
        consulted=consulted,
        creation_start_date=creation_start_date,
        creation_end_date=creation_end_date,
        include_deleted=include_deleted,
    )

async def get_partner_booking_srv_v3(
    offset: Optional[int]=None,
    size: Optional[int]=None,
    partner_name: Optional[str]=None,
    customer_name: Optional[str]=None,
    customer_phone: Optional[str]=None,
    payment_method: Optional[str]=None,
    payment_status: Optional[str]=None,
    status: Optional[str]=None,
    consulted: Optional[str]=None,
    creation_start_date: Optional[str]=None,
    creation_end_date: Optional[str]=None,
    include_deleted: bool=False,
):
    return await get_all_booking_v3(
        offset=offset,
        size=size,
        partner_name=partner_name,
        customer_name=customer_name,
        customer_phone=customer_phone,
        payment_method=payment_method,
        payment_status=payment_status,
        status=status,
        consulted=consulted,
        creation_start_date=creation_start_date,
        creation_end_date=creation_end_date,
        include_deleted=include_deleted,
    )

    
async def get_partner_booking_by_id_srv(
    id: UUID,
):
    return await get_booking_by_id(id=id)

    
async def get_partner_booking_by_id_srv_v3(
    id: UUID,
):
    return await get_booking_by_id_v3(id=id)

async def create_partner_booking_srv(req: PartnerBookingCreationReq):
    req.payment_method = PaymentMethod.BANKING.name
    req.payment_status = PaymentStatus.PENDING.name if req.payment_status is None else req.payment_status
    req.status = BookingStatus.IN_PROGRESS.name if req.payment_status == PaymentStatus.COMPLETED.name else BookingStatus.RECEIVED.name
    partner_booking = await create_partner_booking(req=req)
    # [TODO] Disable sending message when calling request
    # message_body = {
    #     "action": config["PARTNER_INTEGRATION"]["ACTION"]["CREATE_BOOKING"],
    #     "body": json.dumps(partner_booking, default=str),
    # }
    # send_msg_to_queue(
    #     queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
    #     message_body=message_body
    # )
    try:
        await send_notification_to_group(req=NotificationReq(
            group_id="cs",
            sender_id="4b0646f7-ae01-481c-a28d-4bc9e5a573f9",
            title=f"PMC booking mới đã được tạo trên hệ thống",
            content=f"Khách hàng: {req.customer_name}, gói sản phẩm cần tư vấn: {req.products}"
        ))
    except Exception as e:
        logger.error(f"Cannot send notification request: {str(e)}")
    try:
        logger.info(f"Partner booking: ", str(partner_booking.id))
        synced_booking = await sync_booking_information_srv(req=SyncBookingInfoReq(
            booking_list=[str(partner_booking.id)]
        ))
    except Exception as e:
        logger.error(f"Cannot sync booking, please re-sync later: {str(e)}")
    return partner_booking

async def create_partner_booking_srv_v3(req: PartnerBookingCreationReq):
    req.payment_method = PaymentMethod.BANKING.name
    req.payment_status = PaymentStatus.PENDING.name if req.payment_status is None else req.payment_status
    req.status = BookingStatus.IN_PROGRESS.name if req.payment_status == PaymentStatus.COMPLETED.name else BookingStatus.RECEIVED.name
    partner_booking = await create_partner_booking_v3(req=req)
    # [TODO] Disable sending message when calling request
    # message_body = {
    #     "action": config["PARTNER_INTEGRATION"]["ACTION"]["CREATE_BOOKING"],
    #     "body": json.dumps(partner_booking, default=str),
    # }
    # send_msg_to_queue(
    #     queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
    #     message_body=message_body
    # )
    try:
        await send_notification_to_group(req=NotificationReq(
            group_id="cs",
            sender_id="4b0646f7-ae01-481c-a28d-4bc9e5a573f9",
            title=f"PMC booking mới đã được tạo trên hệ thống",
            content=f"Khách hàng: {req.customer_name}, gói sản phẩm cần tư vấn: {req.products}"
        ))
    except Exception as e:
        logger.error(f"Cannot send notification request: {str(e)}")
    try:
        logger.info(f"Partner booking: ", str(partner_booking.id))
        synced_booking = await sync_booking_information_srv_v3(req=SyncBookingInfoReq(
            booking_list=[str(partner_booking.id)]
        ))
    except Exception as e:
        logger.error(f"Cannot sync booking, please re-sync later: {str(e)}")
    return partner_booking
    

async def update_partner_booking_srv(
    partner_booking_id: str,
    req: PartnerBookingUpdateReq
):
    partner_booking = await get_booking_by_id(partner_booking_id)
    # if partner_booking['status'] != BookingStatus.RECEIVED.name:
    #     raise ValueError(f'cannot update booking information, booking was already in {partner_booking["status"]}')
    # if partner_booking['payment_status'] == PaymentStatus.COMPLETED.name:
    #     raise ValueError('this booking was already completed')
    if req.payment_status == PaymentStatus.COMPLETED.name and req.status != BookingStatus.COMPLETED.name:
        req.status = BookingStatus.IN_PROGRESS.name
    partner_booking = await update_partner_booking(
        partner_booking_id=partner_booking_id,
        req=req
    )
    # [TODO] Disable sending message when calling request
    # message_body = {
    #     "action": config["PARTNER_INTEGRATION"]["ACTION"]["CREATE_BOOKING"],
    #     "body": json.dumps(partner_booking, default=str),
    # }
    # send_msg_to_queue(
    #     queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
    #     message_body=message_body
    # )
    return partner_booking


async def update_partner_booking_srv_v3(
    partner_booking_id: str,
    req: PartnerBookingUpdateReq
):
    partner_booking = await get_booking_by_id_v3(partner_booking_id)
    # if partner_booking['status'] != BookingStatus.RECEIVED.name:
    #     raise ValueError(f'cannot update booking information, booking was already in {partner_booking["status"]}')
    # if partner_booking['payment_status'] == PaymentStatus.COMPLETED.name:
    #     raise ValueError('this booking was already completed')
    if req.payment_status == PaymentStatus.COMPLETED.name and req.status != BookingStatus.COMPLETED.name:
        req.status = BookingStatus.IN_PROGRESS.name
    partner_booking = await update_partner_booking_v3(
        partner_booking_id=partner_booking_id,
        req=req
    )
    # [TODO] Disable sending message when calling request
    # message_body = {
    #     "action": config["PARTNER_INTEGRATION"]["ACTION"]["CREATE_BOOKING"],
    #     "body": json.dumps(partner_booking, default=str),
    # }
    # send_msg_to_queue(
    #     queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
    #     message_body=message_body
    # )
    return partner_booking



async def delete_booking_by_id_srv(
    booking_id: str,
):
    res = await delete_partner_booking(
        partner_booking_id=booking_id
    )
    if res['partner_booking_id'] is not None:
        body = {
            "booking_id": res['partner_booking_id'],
        }
        message_body = {
            'action': config["PARTNER_INTEGRATION"]["ACTION"]["DELETE_BOOKING"],
            'body': body,
        }
        send_msg_to_queue(
            queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
            message_body=message_body
        )
    return res



async def delete_booking_by_id_srv_v3(
    booking_id: str,
):
    res = await delete_partner_booking_v3(
        partner_booking_id=booking_id
    )
    if res['partner_booking_id'] is not None:
        body = {
            "booking_id": res['partner_booking_id'],
        }
        message_body = {
            'action': config["PARTNER_INTEGRATION"]["ACTION"]["DELETE_BOOKING"],
            'body': body,
        }
        send_msg_to_queue(
            queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
            message_body=message_body
        )
    return res

def parse_date_time(date_time_str: str):
    if date_time_str is None:
        return None
    try:
        print("parsing date:")
        date_time = datetime.strptime(date_time_str, "%Y-%m-%dT%H:%M:%S")
    except Exception as e:
        print("exeption : ", e)
        date_time = datetime.strptime(date_time_str, "%Y-%m-%d")
    new_date_str = date_time.strftime(TIME_FORMAT)
    return new_date_str

def parse_booking_info_to_message(booking: dict, type: str=MessageType.CREATE.name):
    item_list=[]
    for item in booking['item_list']:
        item_list.append({
            'barcode': item['barcode'],
            'barcode_status': item['status'],
            'use_customer_name': item['use_customer_name'],
            'parent_name': None,
            'phone_get_result': item['phone_number'],
            'address_get_test_sample': item['address'],
            'provice_code': 'HN',
            'price': item['checkout_price'],
            'package': item['product_name'],
            'time_get_test_sample': parse_date_time(item['sample_collection_date']),
            'time_estimate_return_result': parse_date_time(item['expected_report_release_date']),
            'time_response_result': parse_date_time(item['actual_release_date']),
            'completed_at': parse_date_time(item['actual_release_date'])
        })
    message = {
        'partner_booking_id': str(booking['id']),
        'booking_date': booking['created_at'].strftime(TIME_FORMAT),
        'booking_status': booking['status'],
        'booking_customer_name': booking['customer_name'],
        'booking_customer_phone': booking['customer_phone'],
        'referral_id': booking['referral_code'],
        'payment_amount': booking['payment_amount'],
        'payment_method': booking['payment_method'],
        'payment_status': booking['payment_status'],
    }
    if booking['status'] == BookingStatus.COMPLETED.name:
        message.update({
            'completed_at': get_current_time_gmt_plus_7().strftime(TIME_FORMAT)
        })
    if type == MessageType.CREATE.name:
        message.update({
            "details": item_list
        })
    if type == MessageType.UPDATE.name:
        message.update({
            "booking_id": str(booking['partner_booking_id']),
            "details": {
                "value": item_list
            }
        })
        
    return message
    

async def sync_booking_information_srv(
    req: SyncBookingInfoReq,
):
    for id in req.booking_list:
        booking = await get_booking_by_id(id=id)
        request_type = MessageType.CREATE.name
        action = config['PARTNER_INTEGRATION']['ACTION']['CREATE_BOOKING']
        if booking["partner_booking_id"] is not None:
            request_type = MessageType.UPDATE.name
            action = config['PARTNER_INTEGRATION']['ACTION']['UPDATE_BOOKING']
        message_body = parse_booking_info_to_message(booking=booking, type=request_type)
        message = {
            'action': action,
            'body': message_body,
        }
        send_msg_to_queue(config['PARTNER_INTEGRATION']['QUEUE_NAME'], message_body=message)
    return req.booking_list


async def sync_booking_information_srv_v3(
    req: SyncBookingInfoReq,
):
    for id in req.booking_list:
        booking = await get_booking_by_id_v3(id=id)
        request_type = MessageType.CREATE.name
        action = config['PARTNER_INTEGRATION']['ACTION']['CREATE_BOOKING']
        if booking["partner_booking_id"] is not None:
            request_type = MessageType.UPDATE.name
            action = config['PARTNER_INTEGRATION']['ACTION']['UPDATE_BOOKING']
        message_body = parse_booking_info_to_message(booking=booking, type=request_type)
        message = {
            'action': action,
            'body': message_body,
        }
        logger.info(f"Message sync: ", message)
        send_msg_to_queue(config['PARTNER_INTEGRATION']['QUEUE_NAME'], message_body=message)
        
    return req.booking_list

    
async def send_booking_results_srv(
    booking_id: str,
    req: PartnerBookingResultReq,
):
    booking = await get_booking_by_id(id=booking_id)
    if booking['partner_booking_id'] is None:
        raise ValueError(f"cannot send result for booking that was not created in partner's system, please help to sync data first")
    if booking['status'] in [BookingStatus.CANCELED.name, BookingStatus.RECEIVED]:
        raise ValueError(f"cannot send result for booking in {booking['status']} state")
    message = {
        "booking_id": booking['partner_booking_id']
    }
    result_map = {}
    for item in booking['item_list']:
        result_map.update({
            item['barcode']: item
        })
    item_list = []
    for barcode in req.barcode_list:
        if item['result_url'] is None:
            raise ValueError(f"result for the kit {barcode} is not ready")
        item_list.append({
            'barcode': barcode,
            'partner_booking_id': item['booking_id'],
            'result_file_url': item['result_url'],
            'result_raw_json': ''
        })
    message.update({
        'item_list': item_list
    })
    mesage_body = {
        "action": config["PARTNER_INTEGRATION"]["ACTION"]["SEND_RESULTS"],
        "body": message,
    }
    send_msg_to_queue(
        queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
        message_body=mesage_body
    )
    return req.barcode_list
    
async def send_booking_results_srv_v3(
    booking_id: str,
    req: PartnerBookingResultReq,
):
    booking = await get_booking_by_id_v3(id=booking_id)
    if booking['partner_booking_id'] is None:
        raise ValueError(f"cannot send result for booking that was not created in partner's system, please help to sync data first")
    if booking['status'] in [BookingStatus.CANCELED.name, BookingStatus.RECEIVED]:
        raise ValueError(f"cannot send result for booking in {booking['status']} state")
    message = {
        "booking_id": booking['partner_booking_id']
    }
    result_map = {}
    for item in booking['item_list']:
        result_map.update({
            item['barcode']: item
        })
    item_list = []
    for barcode in req.barcode_list:
        item = result_map.get(barcode)
        if item['result_url'] is None:
            raise ValueError(f"result for the kit {barcode} is not ready")
        item_list.append({
            'barcode': barcode,
            'partner_booking_id': item['booking_id'],
            'result_file_url': item['result_url'],
            'result_raw_json': ''
        })
    message.update({
        'item_list': item_list
    })
    mesage_body = {
        "action": config["PARTNER_INTEGRATION"]["ACTION"]["SEND_RESULTS"],
        "body": message,
    }
    send_msg_to_queue(
        queue_name=config["PARTNER_INTEGRATION"]["QUEUE_NAME"],
        message_body=mesage_body
    )
    return req.barcode_list
    

async def update_results_by_booking_id_srv(
    booking_id: str,
    results: ResultUpdateReq,
):
    resp = await update_results_by_booking_id(
        booking_id=booking_id,
        results=results
    )
    return resp

async def update_results_by_booking_id_srv_v3(
    booking_id: str,
    results: ResultUpdateReq,
):
    resp = await update_results_by_booking_id_v3(
        booking_id=booking_id,
        results=results
    )
    return resp
