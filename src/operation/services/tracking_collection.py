from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking import *
from ..cruds.tracking_collection import *
from ..schemas.tracking_collection import *
from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
    convert_rowproxy_to_dict_w_original_type,
    failure_response,
    format_date,
)


async def get_all_collections_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tc.created_at",
    maThuNhan: Optional[str] = None,
    tenDonViThuNhanMau: Optional[str] = None,
    noiThuThapMau: Optional[int] = None,
    ngayGioThuThapMau_start_date: Optional[str] = None,
    ngayGioThuThapMau_end_date: Optional[str] = None,
    hoTenNhanVienLayMau: Optional[str] = None,
    soDinhDanhNhanVienLayMau: Optional[str] = None,
    hoTenNhanVienGhiHoSo: Optional[str] = None,
    soDinhDanhNhanVienGhiHoSo: Optional[str] = None,
    hoTenNhanVienLuuMau: Optional[str] = None,
    soDinhDanhNhanVienLuuMau: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    if ngayGioThuThapMau_start_date:
        ngayGioThuThapMau_start_date = format_date(
            ngayGioThuThapMau_start_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )

    if ngayGioThuThapMau_end_date:
        ngayGioThuThapMau_end_date = format_date(
            ngayGioThuThapMau_end_date,
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
            validate_current_time=False,
        )
        if ngayGioThuThapMau_end_date < ngayGioThuThapMau_start_date:
            raise ValueError("end date cannot be after start date")

    return await get_all_collections(
        offset=offset,
        size=size,
        order_by=order_by,
        maThuNhan=maThuNhan,
        tenDonViThuNhanMau=tenDonViThuNhanMau,
        noiThuThapMau=noiThuThapMau,
        ngayGioThuThapMau_start_date=ngayGioThuThapMau_start_date,
        ngayGioThuThapMau_end_date=ngayGioThuThapMau_end_date,
        hoTenNhanVienLayMau=hoTenNhanVienLayMau,
        soDinhDanhNhanVienLayMau=soDinhDanhNhanVienLayMau,
        hoTenNhanVienGhiHoSo=hoTenNhanVienGhiHoSo,
        soDinhDanhNhanVienGhiHoSo=soDinhDanhNhanVienGhiHoSo,
        hoTenNhanVienLuuMau=hoTenNhanVienLuuMau,
        soDinhDanhNhanVienLuuMau=soDinhDanhNhanVienLuuMau,
        include_deleted=include_deleted,
    )


async def get_collection_by_id_service(id: str):
    # data = await get_tracking_collection_by_id(id)
    # return data.to_dict()
    data, _ = await get_all_collections(tracking_collection_id=id)
    result = convert_rowproxy_to_dict_w_original_type(data[0])
    return result


async def get_collection_by_samplecode_service(samplecode: str):
    tracking = await get_tracking_by_samplecode(samplecode=samplecode)
    tracking_collection = await get_tracking_collection_by_tracking_id(
        tracking_id=tracking.id
    )
    tracking_collection_id = tracking_collection.id
    data, _ = await get_all_collections(tracking_collection_id=tracking_collection_id)
    result = convert_rowproxy_to_dict_w_original_type(data[0])
    return result


async def create_collection_service(data: dict):
    """
    tracking_id: str
    maThuNhan: str
    donViThuNhanMau_id: int
    noiThuThapMau: int
    ngayGioThuThapMau: str
    nhanVienLayMau_id: str
    nhanVienGhiHoSo_id: str
    nhanVienLuuMau_id: Optional[str]
    """
    data["ngayGioThuThapMau"] = format_date(
        data.get("ngayGioThuThapMau"),
        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
        validate_current_time=False,
    )
    return await create_tracking_collection(data)


async def update_collection_service(id: str, data: dict):
    """
    type: str
    """
    collection = await get_tracking_collection_by_id(id)
    if not collection:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_collection(collection, data)
    data = await get_tracking_collection_by_id(id)
    return data.to_dict()


async def update_collection_service_via_samplecode(samplecode: str, data: dict):
    tracking_collection = await get_tracking_collection_by_samplecode(
        samplecode=samplecode
    )
    if not tracking_collection:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    data["ngayGioThuThapMau"] = format_date(
        data.get("ngayGioThuThapMau"),
        DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
        validate_current_time=False,
    )

    await update_tracking_collection(tracking_collection, data)
    data = await get_tracking_collection_by_samplecode(samplecode=samplecode)
    return data.to_dict()


async def delete_collection_by_id_service(id: str):
    """
    id: str
    """
    collection = await get_tracking_collection_by_id(id)

    await delete_tracking_collection(collection=collection)

    data = await get_tracking_collection_by_id(id)
    return data.to_dict()
