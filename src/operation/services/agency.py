from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.agency import *
from ..schemas.agency import *
from ..utils.utils import failure_response


async def get_all_agencies_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "ag.created_at",
    name: Optional[str] = None,
    type: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    # if receipt_start_date:
    #     start_date_std = format_date(receipt_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if receipt_end_date:
    #     end_date_std = format_date(receipt_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if end_date_std < start_date_std:
    #         raise ValueError("end date cannot be after start date")
    # if release_start_date:
    #     release_start_date_std = format_date(release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if release_end_date:
    #     release_end_date_std = format_date(release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if release_end_date < release_start_date:
    #         raise ValueError("end date cannot be after start date")
    # if collection_start_date:
    #     collection_start_date_std = format_date(collection_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if collection_end_date:
    #     collection_end_date_std = format_date(collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if collection_end_date_std < collection_start_date_std:
    #         raise ValueError("end date cannot be after start date")
    # if actual_report_release_start_date:
    #     actual_report_release_start_date_std = format_date(actual_report_release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if actual_report_release_end_date:
    #     actual_report_release_end_date_std = format_date(actual_report_release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if actual_report_release_end_date_std < actual_report_release_start_date_std:
    #         raise ValueError("end date cannot be after start date")

    return await get_all_agencies(
        offset=offset,
        size=size,
        order_by=order_by,
        name=name,
        type=type,
        include_deleted=include_deleted,
    )


async def get_agency_by_id_service(id: str):
    data = await get_agency_by_id(id)
    return data.to_dict()


async def create_agency_service(data: dict):
    """
    name: str
    type: str
    """
    return await create_agency(data)


async def update_agency_service(id: str, data: dict):
    """
    type: str
    """
    agency = await get_agency_by_id(id)
    if not agency:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_agency(agency, data)
    data = await get_agency_by_id(id)
    return data.to_dict()


async def delete_agency_by_id_service(id: str):
    """
    id: str
    """
    agency = await get_agency_by_id(id)

    await delete_agency(agency=agency)

    data = await get_agency_by_id(id)
    return data.to_dict()
