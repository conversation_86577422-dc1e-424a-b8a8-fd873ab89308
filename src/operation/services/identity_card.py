from typing import Optional, List
from io import Bytes<PERSON>
from datetime import datetime
import re

from fastapi import HTTPException
from starlette.status import (
    HTTP_400_BAD_REQUEST,
)

from src.operation.config import config

from .. import logger
from ..cruds.identity_card import (
    get_all_kits_identity_card_via_id_code_v3,
    get_identity_card_by_id,
    get_identity_card_by_id_w_orm,
    get_identity_cards,
    get_identity_cards_require_registration,
    update_identity_card,
)
from ..cruds.subject import get_subject_by_id_card, update_subject
from ..models.models import db
from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE,
    calculate_business_days,
    convert_rowproxy_to_dict,
    failure_response,
    format_date,
    get_current_date_time_utc_7,
    get_technology_w_product_code, number_to_vietnamese_words,
)

from ..utils.excel_utils import export_to_excel
import ast


async def get_identity_cards_list_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "created_time",
    identifier_code: Optional[str] = None,
    samplecode: Optional[str] = None,
    full_name: Optional[str] = None,
    gender: Optional[str] = None,
    dob: Optional[str] = None,
    customer_support_id: Optional[str] = None,
    tenDonViThuNhanMau: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    is_scanned: Optional[bool] = None,
    sample_collector_name: Optional[str] = None,
    customer_support_ids: Optional[List[str]] = [],
    idDonViThuNhanMau: Optional[List[str]] = [],
    phone_number: Optional[str] = None,
    bearer_token_credential: Optional[str] = None,
):
    if start_date:
        start_date_std = format_date(start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if end_date:
        end_date_std = format_date(end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")

    return await get_identity_cards(
        offset=offset,
        size=size,
        order_by=order_by,
        identifier_code=identifier_code,
        samplecode=samplecode,
        full_name=full_name,
        gender=gender,
        dob=dob,
        customer_support_id=customer_support_id,
        tenDonViThuNhanMau=tenDonViThuNhanMau,
        start_date=start_date,
        end_date=end_date,
        is_scanned=is_scanned,
        sample_collector_name=sample_collector_name,
        customer_support_ids=customer_support_ids,
        idDonViThuNhanMau=idDonViThuNhanMau,
        phone_number=phone_number,
        bearer_token_credential=bearer_token_credential
    )


async def get_identity_cards_require_registration_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "created_time",
    identifier_code: Optional[str] = None,
    full_name: Optional[str] = None,
    gender: Optional[str] = None,
    dob: Optional[str] = None,
    customer_support_id: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
):
    return await get_identity_cards_require_registration(
        offset=offset,
        size=size,
        order_by=order_by,
        identifier_code=identifier_code,
        full_name=full_name,
        gender=gender,
        dob=dob,
        customer_support_id=customer_support_id,
        start_date=start_date,
        end_date=end_date,
    )


def filter_latest_sample(map: dict):
    # sample_collection_time
    for product_tech, kit_list in map.items():
        latest_sample_collection_time = "0"
        latest_kit = None
        for curr_kit in kit_list:
            # print('curr_kit: ', curr_kit.get('barcode'))
            if latest_kit is None:
                latest_kit = curr_kit
                latest_sample_collection_time = curr_kit.get("sample_collection_time")
                latest_kit_created_at = curr_kit.get("created_at")
            else:
                if (
                    int(curr_kit.get("sample_collection_time"))
                    >= int(latest_sample_collection_time)
                    and curr_kit.get("created_at") > latest_kit_created_at
                ):
                    latest_kit = curr_kit
                    latest_sample_collection_time = curr_kit.get(
                        "sample_collection_time"
                    )
                    latest_kit_created_at = curr_kit.get("created_at")
            # print('curr_kit: ', curr_kit.get('barcode'), 'latest_kit: ', latest_kit.get('barcode'))
        map[product_tech] = [latest_kit]

    return map


def convert_product_tech_to_integration_tech(map: dict, is_concise: bool = False):
    integration_map = {}

    for product_tech, kit_list in map.items():
        integration_tech = config["AGENCY_INTEGRATION_MAXTRIX"]["MAPPING"][product_tech]
        data = kit_list[-1]
        data["methods"] = config["AGENCY_INTEGRATION_MAXTRIX"]["TECHNOLOGY"][
            integration_tech
        ]
        if is_concise:
            fields_to_extract = config["AGENCY_INTEGRATION_MAXTRIX"]["EXTRACT_LIST"]
            concise_data = {key: data[key] for key in fields_to_extract if key in data}
            data = concise_data

        integration_map[integration_tech] = data

    return integration_map


async def get_adn_kit_mapping_by_cccd_v3(
    identifier_code: str,
    collect_date: Optional[str] = None,
    size: Optional[int] = None,
    offset: Optional[int] = None,
    order_by: Optional[str] = None,
    is_concise: Optional[bool] = None,
):
    collect_date_start, collect_date_end = calculate_business_days(
        date_str=collect_date, n=config["AGENCY_ACCOUNT_CODE"].get("COLLECT_DATE_RANGE")
    )

    logger.info(
        f"collect_date_start, collect_date_end: {collect_date_start}, {collect_date_end}"
    )
    kits_list, total = await get_all_kits_identity_card_via_id_code_v3(
        identifier_code=identifier_code,
        collect_date_start=collect_date_start,
        collect_date_end=collect_date_end,
        size=size,
        offset=offset,
        order_by=order_by,
    )
    logger.info(f"total: {total}")

    adn_integration_map = {}

    for kit in kits_list:
        data_dict = convert_rowproxy_to_dict(kit)
        if not data_dict.get("barcode"):
            err = f"Sample with samplecode: {data_dict.get('samplecode')} has not generate barcode!"
            http_code = HTTP_400_BAD_REQUEST
            errs = failure_response(err)
            raise HTTPException(status_code=http_code, detail=errs)

        # print("product code: ", data_dict.get('product_code'))

        technology, _ = await get_technology_w_product_code(
            code=data_dict.get("product_code")
        )
        if not adn_integration_map.get(technology):
            adn_integration_map[technology] = []
        adn_integration_map[technology].append(data_dict)

    adn_integration_map = filter_latest_sample(map=adn_integration_map)
    adn_integration_map = convert_product_tech_to_integration_tech(
        map=adn_integration_map, is_concise=is_concise
    )
    # print("adn_integration_map: ", adn_integration_map)
    return adn_integration_map, total


async def get_identity_card_detail_via_cccd_v3(identifier_code: str):
    return await get_identity_card_by_id(identifier_code=identifier_code)


async def get_identity_card_detail_via_cccd_v3_w_orm(identifier_code: str):
    return await get_identity_card_by_id_w_orm(identifier_code=identifier_code)


async def update_identity_card_detail_via_cccd_v3(
    identifier_code: Optional[str] = None,
    phone_number: Optional[str] = None,
    email: Optional[str] = None,
    residence: Optional[str] = None,
    new_avatar_image: Optional[str] = None,
    new_fingerprint_image: Optional[str] = None,
    martyr_name: Optional[List[str]] = None,
    martyr_relationships: Optional[List[str]] = None,
    guardian_name: Optional[str] = None,
    guardian_gender: Optional[str] = None,
    guardian_phone_number: Optional[str] = None,
    guardian_identifier_code: Optional[str] = None,

):
    try:
        curr_identity_card = await get_identity_card_by_id(
            identifier_code=identifier_code
        )
        curr_subject = await get_subject_by_id_card(identifier_code=identifier_code)

        if (
            curr_identity_card.phone_number == phone_number
            and curr_identity_card.email == email
            and curr_identity_card.avatar_image == new_avatar_image
            and curr_identity_card.fingerprint_image == new_fingerprint_image
            and curr_identity_card.residence == residence
            and curr_subject.martyr_name == martyr_name
            and curr_subject.martyr_relationships == martyr_relationships
            and curr_subject.guardian_name == guardian_name
            and curr_subject.guardian_gender == guardian_gender
            and curr_subject.guardian_phone_number == guardian_phone_number
            and curr_subject.guardian_identifier_code == guardian_identifier_code
        ):
            return curr_identity_card

        async with db.transaction() as tx:
            # user_info = await get_user_information(phone_number)
            # user_id = user_info['data']["uuid"]
            # print("user_info: ", user_info)

            # if not user_id:
            #     err = "Phone number has not been registered!"
            #     raise ValueError(err)

            # if curr_identity_card.phone_number != phone_number:
            #     res = await get_subject(
            #         user_id=user_id)
            #     if len(res) > 0:
            #         err = "Phone number's already been used!"
            #         raise ValueError(err)

            curr_identity_card.phone_number = phone_number
            curr_identity_card.email = email
            if new_avatar_image:
                curr_identity_card.avatar_image = new_avatar_image
            if new_fingerprint_image:
                # print("new_fingerprint_image: ", new_fingerprint_image)
                curr_identity_card.fingerprint_image = new_fingerprint_image
            if residence:
                curr_identity_card.residence = residence
            await update_identity_card(identity_card=curr_identity_card)
            curr_subject.phone_number = phone_number
            if martyr_name is not None:
                curr_subject.martyr_name = str(martyr_name)
            if martyr_relationships is not None:
                curr_subject.martyr_relationships = martyr_relationships
            if guardian_name is not None:
                curr_subject.guardian_name = guardian_name
            if guardian_gender is not None:
                curr_subject.guardian_gender = guardian_gender
            if guardian_phone_number is not None:
                curr_subject.guardian_phone_number = guardian_phone_number
            if guardian_identifier_code is not None:
                curr_subject.guardian_identifier_code = guardian_identifier_code
            # curr_subject.user_id=user_id
            # curr_subject.validate_account=True
            await update_subject(subject=curr_subject)

            return await get_identity_card_by_id(identifier_code=identifier_code)
    except Exception as e:
        raise e


async def validate_checkin_infor_against_id_card(
    identifier_code: Optional[str] = None,
    full_name: Optional[str] = None,
    dob: Optional[str] = None,
    gender: Optional[str] = None,
    nationality: Optional[str] = None,
    origin: Optional[str] = None,
    residence: Optional[str] = None,
    ethnic: Optional[str] = None,
):
    identity_card = await get_identity_card_by_id(identifier_code=identifier_code)
    if (
        identity_card.full_name == full_name
        and identity_card.dob == dob
        and identity_card.gender == gender
        and identity_card.nationality == nationality
        and identity_card.origin == origin
        and identity_card.residence == residence
        and identity_card.ethnic == ethnic
    ):
        return True
    return False


async def delete_identity_card_by_identifier_code_v3(identifier_code: str):
    identity_card = await get_identity_card_detail_via_cccd_v3(
        identifier_code=identifier_code
    )
    if not identity_card:
        err = f"CCCD with no {identifier_code} can not be found"
        raise ValueError(err)
    if identity_card.deleted_at is not None:
        err = f"CCCD with no {identifier_code} is already deleted"
        raise ValueError(err)

    if not identity_card.manual_input:
        err = f"CCCD with no {identifier_code} is not manually input, cannot be deleted"
        raise ValueError(err)

    identity_card.deleted_at = get_current_date_time_utc_7()
    await identity_card.update(**identity_card.to_dict()).apply()
    return identity_card.to_dict()


def format_date_range(start_date: str, end_date: str) -> str:
    # Parse from string
    start = datetime.strptime(start_date, "%Y-%m-%d")
    end = datetime.strptime(end_date, "%Y-%m-%d")

    range_date = f"{start.strftime('%d/%m/%Y')} - {end.strftime('%d/%m/%Y')}"

    if start.month == end.month and start.year == end.year:
        range_month = f"{start.strftime('%m/%Y')}"
    else:
        range_month = f"{start.strftime('%m/%Y')} - {end.strftime('%m/%Y')}"

    return range_month, range_date

def remove_ut_tags(martyr_relationships):
    def clean(item):
        return re.sub(r"\s*\(UT\d+\)", "", item).strip()

    if isinstance(martyr_relationships, list):
        return [clean(item) for item in martyr_relationships]
    elif isinstance(martyr_relationships, str):
        items = re.split(r",\s*|\n", martyr_relationships.strip())
        return "\n".join(clean(item) for item in items if item)
    return ""

async def export_identity_card_to_docx(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    order_by: Optional[str] = "created_time",
    customer_support_id: Optional[str] = None,
    tenDonViThuNhanMau: Optional[str] = None,
    idDonViThuNhanMau: Optional[List[str]] = [],
    identifier_code: Optional[str] = None,
    full_name: Optional[str] = None,
    gender: Optional[str] = None,
    dob: Optional[str] = None,
    is_scanned: Optional[bool] = None,
    sample_collector_name: Optional[str] = None,
    customer_support_ids: Optional[List[str]] = [],
    bearer_token_credential: Optional[str] = None,
) -> BytesIO:
    from docxtpl import DocxTemplate
    # Prepare the context for rendering
    data, total = await get_identity_cards_list_v3(
        order_by=order_by,
        customer_support_id=customer_support_id,
        tenDonViThuNhanMau=tenDonViThuNhanMau,
        idDonViThuNhanMau=idDonViThuNhanMau,
        start_date=start_date,
        end_date=end_date,
        identifier_code=identifier_code,
        full_name=full_name,
        gender=gender,
        dob=dob,
        is_scanned=is_scanned,
        sample_collector_name=sample_collector_name,
        customer_support_ids=customer_support_ids,
        bearer_token_credential=bearer_token_credential
    )

    list_identifier_code = [
        {
            "index": i + 1,
            "identity_code": r["identifier_code"],
            "full_name": r["full_name"],
            "collect_date": r["created_at"].strftime("%d/%m/%Y") if r["created_at"] else "",
            "location": "",
            "martyr_name": (
                re.sub(r",\s*", "\n", r["martyr_name"]) if r["martyr_name"] else ""
            ),
            "martyr_relationship": (
                "\n".join((remove_ut_tags(r["martyr_relationships"]))) if isinstance(r["martyr_relationships"], list)
                else re.sub(r",\s*", "\n", remove_ut_tags(r["martyr_relationships"])) if r["martyr_relationships"] else ""
            ),
            "sponsor": r["sponsor_name"] if r["sponsor_name"] else "",
        }
        for i, r in enumerate(data)
    ]

    if start_date and end_date:
        range_month, range_date = format_date_range(start_date, end_date)
        context = {
            "sponsors": [],
            "list_identifier_code": list_identifier_code,
            "total": total,
            "today": datetime.now().strftime("%d/%m/%Y"),
            "range_date": range_date,
            "total_as_vietnamese": number_to_vietnamese_words(total),
            "month": range_month,
        }
    else:
        context = {
            "sponsors": [],
            "list_identifier_code": list_identifier_code,
            "total": total,
            "today": datetime.now().strftime("%d/%m/%Y"),
            "range_date": "",
            "total_as_vietnamese": number_to_vietnamese_words(total),
            "month": "",
        }

    template = DocxTemplate(r"src/operation/templates/BBNT_Template.docx")

    template.render(context)

    doc_io = BytesIO()

    template.save(doc_io)
    doc_io.seek(0)

    return doc_io

def preprocess_data(data: List) -> List:
    for item in data:
        item["identifier_code"] = str(item["identifier_code"])
        item["dob"] = item["yob"] if item.get("yob") else item["dob"].strftime("%d/%m/%Y")
        item["gender"] = "Nam" if item.get("gender") == "male" else "Nữ"
        item["created_at"] = item["created_at"].strftime("%d/%m/%Y") if item["created_at"] else ""
        martyr_name_dict = item["martyr_name"].split(", ")
        item["martyr_name"] = "\n".join(martyr_name_dict)
        martyr_relationships_dict = item["martyr_relationships"].split(", ")
        item["martyr_relationships"] = "\n".join(martyr_relationships_dict)
    return data

def export_identity_card_to_excel(data: List) -> BytesIO:
    data_dicts = [dict(row) for row in data]
    data = preprocess_data(data_dicts)
    columns = [
        {"key": "identifier_code", "title": "Số định danh"},
        {"key": "full_name", "title": "Họ tên công dân"},
        {"key": "phone_number", "title": "Số điện thoại"},
        {"key": "samplecode", "title": "Samplecode"},
        {"key": "ethnic", "title": "Dân tộc"},
        {"key": "origin", "title": "Quê quán"},
        {"key": "residence", "title": "Địa chỉ"},
        {"key": "nationality", "title": "Quốc tịch"},
        {"key": "dob", "title": "Ngày sinh"},
        {"key": "gender", "title": "Giới tính"},
        {"key": "customer_support_account", "title": "Tài khoản thu mẫu"},
        {"key": "tenDonViThuNhanMau", "title": "Đơn vị thu mẫu"},
        {"key": "martyr_name", "title": "Tên liệt sỹ"},
        {"key": "martyr_relationships", "title": "Mối quan hệ với liệt sỹ"},
        {"key": "sample_collector_name", "title": "Người thu mẫu"},
        {"key": "sponsor_name", "title": "Nhà tài trợ"},
        {"key": "note", "title": "Ghi chú"},
        {"key": "created_at", "title": "Ngày tạo"},
    ]

    return export_to_excel(data=data, columns=columns, include_index=True)

async def delete_identity_card_by_id_card(identifier_code: str):
    kits_list, total = await get_all_kits_identity_card_via_id_code_v3(
        identifier_code=identifier_code
    )
    for kit in kits_list:
        if kit.barcode and kit.scan_status and kit.scan_status > 0:
            err = f"CCCD with identifier_code: {identifier_code} has been scanned, cannot be deleted"
            raise ValueError(err)

    await delete_identity_card_by_identifier_code_v3(identifier_code=identifier_code)


