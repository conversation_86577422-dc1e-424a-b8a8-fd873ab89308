from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.adn_integration_type import *
from ..schemas.adn_integration_type import *
from ..utils.utils import failure_response


async def get_all_adn_integration_types_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "ait.created_at",
    name: Optional[str] = None,
    adn_type: Optional[str] = None,
    method: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    # if receipt_start_date:
    #     start_date_std = format_date(receipt_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if receipt_end_date:
    #     end_date_std = format_date(receipt_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if end_date_std < start_date_std:
    #         raise ValueError("end date cannot be after start date")
    # if release_start_date:
    #     release_start_date_std = format_date(release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if release_end_date:
    #     release_end_date_std = format_date(release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if release_end_date < release_start_date:
    #         raise ValueError("end date cannot be after start date")
    # if collection_start_date:
    #     collection_start_date_std = format_date(collection_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if collection_end_date:
    #     collection_end_date_std = format_date(collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if collection_end_date_std < collection_start_date_std:
    #         raise ValueError("end date cannot be after start date")
    # if actual_report_release_start_date:
    #     actual_report_release_start_date_std = format_date(actual_report_release_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    # if actual_report_release_end_date:
    #     actual_report_release_end_date_std = format_date(actual_report_release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
    #     if actual_report_release_end_date_std < actual_report_release_start_date_std:
    #         raise ValueError("end date cannot be after start date")

    return await get_all_adn_integration_types(
        offset=offset,
        size=size,
        order_by=order_by,
        name=name,
        adn_type=adn_type,
        method=method,
        include_deleted=include_deleted,
    )


async def get_adn_integration_type_by_mapping_service(adn_type: str, method: str):
    data = await get_adn_integration_type_by_mapping(adn_type=adn_type, method=method)

    if not data:
        data = await create_adn_integration_type_service(
            {
                "name": f"Thu thập dữ liệu {adn_type} qua {method}",
                "adn_type": adn_type,
                "method": method,
            }
        )

        return data

    return data.to_dict()


async def get_adn_integration_type_by_id_service(id: str):
    data = await get_adn_integration_type_by_id(id)
    return data.to_dict()


async def create_adn_integration_type_service(data: dict):
    """
    name: str
    type: str
    method: str
    """
    return await create_adn_integration_type(data)


async def update_adn_integration_type_service(id: str, data: dict):
    """
    name: str
    """
    adn_integration_type = await get_adn_integration_type_by_id(id)
    if not adn_integration_type:
        err = f"Kit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_adn_integration_type(adn_integration_type, data)

    data = await get_adn_integration_type_by_id(id)
    return data.to_dict()


async def delete_adn_integration_type_by_id_service(id: str):
    """
    id: str
    """
    adn_integration_type = await get_adn_integration_type_by_id(id)

    await delete_adn_integration_type(adnIntegrationType=adn_integration_type)

    data = await get_adn_integration_type_by_id(id)
    return data.to_dict()
