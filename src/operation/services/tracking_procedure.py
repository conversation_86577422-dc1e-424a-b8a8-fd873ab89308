from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking import *
from ..cruds.tracking_procedure import *
from ..cruds.tracking_procedure_step import *
from ..schemas.tracking_procedure import *
from ..utils.utils import convert_rowproxy_to_dict, failure_response


async def get_tracking_procedure_by_samplecode(samplecode: Optional[str] = None):
    tracking = await get_tracking_by_samplecode(samplecode)
    tracking_procedures, total = await get_all_procedures(tracking_id=tracking.id)
    return tracking_procedures, total


async def get_tracking_procedure_via_barcode(barcode: Optional[str] = None):
    tracking_procedures, _ = await get_all_procedures(gs_barcode=barcode)
    return tracking_procedures[0]


async def get_all_procedures_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tp.created_at",
    maXetNghiem: Optional[str] = None,
    nhietDoLuuTru: Optional[str] = None,
    gs_isComplete: Optional[bool] = None,
    gs_barcode: Optional[str] = None,
    gs_lid: Optional[str] = None,
    congNghe: Optional[str] = None,
    gs_template_name: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_procedures(
        offset=offset,
        size=size,
        order_by=order_by,
        maXetNghiem=maXetNghiem,
        nhietDoLuuTru=nhietDoLuuTru,
        gs_isComplete=gs_isComplete,
        gs_barcode=gs_barcode,
        gs_lid=gs_lid,
        congNghe=congNghe,
        gs_template_name=gs_template_name,
        include_deleted=include_deleted,
    )


async def get_procedure_by_id_service(id: str):
    procedure = await get_tracking_procedure_by_id(id=id)
    procedure = procedure.to_dict()
    procedure["cacBuocXetNghiem"] = {"cacBuocThucHien": []}

    procedureSteps, _ = await get_all_procedure_steps(procedure_id=id)
    for procedureStep in procedureSteps:
        ps_dict = convert_rowproxy_to_dict(procedureStep)
        procedureStepDetail = {
            "tenBuoc": ps_dict.get("tenBuoc"),
            "nhanVienThucHien": {
                "hoTenNhanVien": ps_dict.get("hoTenNhanVien"),
                "chucVu": ps_dict.get("chucVu"),
                "hocVi": ps_dict.get("hocVi"),
                "soDinhDanh": ps_dict.get("soDinhDanh"),
            },
        }
        procedure["cacBuocXetNghiem"]["cacBuocThucHien"].append(procedureStepDetail)

    return procedure


async def get_vneid_procedure_by_id_service(id: str):
    procedure = await get_tracking_procedure_by_id(id=id)
    procedure = procedure.to_dict()
    vneid_procedure = {
        "maXetNghiem": procedure.get("maXetNghiem"),
        "nhietDoLuuTru": procedure.get("nhietDoLuuTru"),
    }
    vneid_procedure["cacBuocXetNghiem"] = {"cacBuocThucHien": []}

    procedureSteps, _ = await get_all_procedure_steps(procedure_id=id)
    for procedureStep in procedureSteps:
        ps_dict = convert_rowproxy_to_dict(procedureStep)
        procedureStepDetail = {
            "tenBuoc": ps_dict.get("tenBuoc"),
            "nhanVienThucHien": {
                "hoTenNhanVien": ps_dict.get("hoTenNhanVien"),
                "chucVu": ps_dict.get("chucVu"),
                "hocVi": ps_dict.get("hocVi"),
                "soDinhDanh": ps_dict.get("soDinhDanh"),
            },
        }
        vneid_procedure["cacBuocXetNghiem"]["cacBuocThucHien"].append(
            procedureStepDetail
        )

    return vneid_procedure


async def get_procedure_by_barcode_service(barcode: str):
    tracking_procedure = await get_tracking_procedure_by_barcode(barcode=barcode)
    return await get_procedure_by_id_service(tracking_procedure.id)


async def get_vneid_procedure_by_barcode_service(barcode: str):
    tracking_procedure = await get_tracking_procedure_by_barcode(barcode=barcode)
    if not tracking_procedure:
        err = f"Tracking Procedure with barcode {barcode} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    return await get_vneid_procedure_by_id_service(tracking_procedure.id)


async def create_procedure_service(data: dict):
    """
    tracking_id: str
    template_id: str
    maXetNghiem: str
    nhietDoLuuTru: str
    gs_barcode: str
    gs_lid: str
    gs_currStep: Optional[int]
    gs_totalStep: Optional[int]
    gs_isComplete: Optional[bool]

    cacBuocThucHien: List[BuocThucHienExtended]

    template_step_id: str
    employee: EmployeeBaseDetail = Field(exclude=True)
    employee_id: Optional[str]
    """
    return await create_tracking_procedure(data)


async def update_procedure_service(id: str, data: dict):
    """
    type: str
    """
    procedure = await get_tracking_procedure_by_id(id)
    if not procedure:
        err = f"Procedure with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_procedure(procedure, data)
    data = await get_tracking_procedure_by_id(id)
    return data.to_dict()


async def delete_procedure_by_id_service(id: str):
    """
    id: str
    """
    procedure = await get_tracking_procedure_by_id(id)

    await delete_tracking_procedure(procedure=procedure)

    data = await get_tracking_procedure_by_id(id)
    return data.to_dict()
