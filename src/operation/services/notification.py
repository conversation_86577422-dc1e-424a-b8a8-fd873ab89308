import json

import boto3

from src.operation.config import config
from src.operation.models.models import Status
from src.operation.schemas.notification import NotificationReq

from .. import logger
from ..cruds.register_kit import get_status, get_status_with_days_ago
from ..cruds.register_sample import get_all_samples


async def get_no_data_kits():
    status = config["NOTIFICATION"]["NO_DATA_KIT_STATUS"]
    days = config["NOTIFICATION"]["LOOK_BACK_DAYS"]
    data, total = await get_status_with_days_ago(status, days)
    registered_kits = [k["barcode"] for k in data]
    no_completed_kits = await get_no_completed_kits(registered_kits)
    batch_infos = await get_kit_batch(no_completed_kits)

    return batch_infos


async def get_no_completed_kits(registered_kits):
    no_completed_kits = []
    for kit_id in registered_kits:
        status = await get_status(kit_id)
        all_status = []
        if status:
            for s in status:
                if s:
                    for r in s:
                        if isinstance(r, Status):
                            all_status.append(r.to_dict())
            latest_status = [k["status"] for k in all_status][-1]
            if latest_status != config["COMPLETED_KIT_STATUS"]:
                kit_status = {"barcode": kit_id, "status": latest_status}
                no_completed_kits.append(kit_status)

    return no_completed_kits


async def get_kit_batch(kit_status):
    updated_kit_status = []
    no = 0
    batchs = set()
    for status in kit_status:
        sample_info, total = await get_all_samples(barcode=status["barcode"])
        if sample_info:
            samples = [r.to_dict() for r in sample_info]
            for s in samples:
                no += 1
                temp = {
                    "barcode": status["barcode"],
                    "status": status["status"],
                    "batch_barcode": s["batch_barcode"],
                    "no": no,
                }
                batchs.add(s["batch_barcode"])
                updated_kit_status.append(temp)
    kit_results = sorted(updated_kit_status, key=lambda x: int(x["batch_barcode"]))

    return kit_results, batchs


# [TODO] send notification to group/users


async def send_notification_to_group(req: NotificationReq):
    # notification_to_group_url = f"{config['NOTIFICATION']['ENDPOINT']}{config['NOTIFICATION']['SEND_TO_GROUP']}"
    # logger.info(f"Notification url: {notification_to_group_url}")
    # res = requests.post(notification_to_group_url, json=req.dict())
    # if res.status_code == 200:
    #     logger.info(f"Successfully send notification to user group: {req.group_id}")
    # else:
    #     logger.info(f"Cannot send notification to user group: {req.group_id}")

    message_req = {
        "METHOD": "POST",
        "ACTION": "SEND_NOTIFICATION_TO_GROUP",
        "BODY": req.dict(),
    }

    sqs_client = boto3.client(
        "sqs",
        aws_access_key_id=config["AWS"]["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=config["AWS"]["AWS_SECRET_ACCESS_KEY"],
        region_name=config["AWS"]["NOTIFICATION_REGION_NAME"],
    )
    queue = sqs_client.get_queue_url(QueueName=config["NOTIFICATION"]["SQS"])
    queue_url = queue["QueueUrl"]
    res = sqs_client.send_message(
        QueueUrl=queue_url, MessageBody=json.dumps(message_req)
    )
    if res["ResponseMetadata"]["HTTPStatusCode"] == 200:
        logger.info(f"Successfully send notification to user group: {req.group_id}")
    else:
        logger.info(f"Cannot send notification to user group: {req.group_id}")


# [TODO] send email to group/users
