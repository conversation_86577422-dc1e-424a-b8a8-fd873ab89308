from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
    HTTP_409_CONFLICT,
)

from ..cruds.tracking_unit import *
from ..schemas.tracking_unit import *
from ..utils.utils import failure_response, admin_get_user_info


async def get_all_units_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "tu.created_at",
    tenDonVi: Optional[str] = None,
    gs_area: Optional[str] = None,
    gs_area_code: Optional[int] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_tracking_units(
        offset=offset,
        size=size,
        order_by=order_by,
        tenDonVi=tenDonVi,
        gs_area=gs_area,
        gs_area_code=gs_area_code,
        include_deleted=include_deleted,
    )


async def get_unit_by_id_service(id: str, credential: str):
    accounts_result = []

    id_list = [int(i.strip()) for i in id.split(",") if i.strip()]

    for unit_id in id_list:
        curr_unit = await get_tracking_unit_by_id(unit_id)
        if not curr_unit:
            continue

        phone_numbers = curr_unit.gs_phone_number.split(",") if curr_unit.gs_phone_number else []
        phone_numbers = list(set(phone_numbers))

        for phone_number in phone_numbers:
            account_response = await admin_get_user_info(phone_number, credential)
            if account_response.get("data"):
                for account in account_response["data"]:
                    account["tenDonVi"] = curr_unit.tenDonVi
                    account["unit_id"] = curr_unit.id
                    accounts_result.append(account)

    return accounts_result


async def create_unit_service(data: dict):
    """
    tenDonVi: str
    gs_area: str
    gs_area_code: str
    """
    return await create_tracking_unit(data)


async def update_unit_service(id: str, data: dict):
    """
    type: str
    """
    unit = await get_tracking_unit_by_id(id)
    if not unit:
        err = f"Unit with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_unit(unit, data)
    data = await get_tracking_unit_by_id(id)
    return data.to_dict()


async def is_linked_phone_number_exist_service(phone_number: str):
    return await is_linked_phone_number_existed(phone_number)


async def link_unit_with_phone_number_service(id: str, data: dict, credential: str):
    # check phone_number is linked with other unit
    is_linked = await is_linked_phone_number_exist_service(data["gs_phone_number"])
    if is_linked:
        err = (
            f"Số điện thoại {data['gs_phone_number']} đã được liên kết với đơn vị khác"
        )
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    curr_unit = await get_tracking_unit_by_id(id)
    phone_numbers = curr_unit.gs_phone_number.split(",") if curr_unit.gs_phone_number else []
    if data["gs_phone_number"] in phone_numbers:
        err = f'SĐT {data["gs_phone_number"]} đã được liên kết với đơn vị {curr_unit.tenDonVi}'
        http_code = HTTP_409_CONFLICT
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    return await link_unit_with_phone_number(
        id=id,
        phone_number=data["gs_phone_number"],
        password=data["password"].get_secret_value(),
        credential=credential,
    )

async def unlink_unit_with_phone_number_service(id: str, phone_number: str):
    # check phone_number is linked with other unit
    is_linked = await is_linked_phone_number_exist_service(phone_number)
    if not is_linked:
        err = (
            f"Số điện thoại {phone_number} chưa được liên kết với đơn vị"
        )
        http_code = HTTP_400_BAD_REQUEST
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)
    return await unlink_unit_with_phone_number(id, phone_number)


async def get_unit_by_phone_number_service(phone_number: str):
    return await get_tracking_unit_by_phone_number(phone_number)

async def check_account_service(phone_number: str):
    return await check_user_existence(phone=phone_number)

async def delete_unit_by_id_service(id: str):
    """
    id: str
    """
    unit = await get_tracking_unit_by_id(id)

    await delete_tracking_unit(unit=unit)

    data = await get_tracking_unit_by_id(id)
    return data.to_dict()
