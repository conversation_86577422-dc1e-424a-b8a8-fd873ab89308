from ..cruds.sample import get_sample_list_detail_v3, get_sample_list_detail_labcheck, get_sample_list_detail_v4
from ..schemas.sample import *
from ..utils.utils import DEFAULT_DATE_NO_TIME_ZONE, format_date


async def get_sample_list_v3(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "updated_at",
    user_id: Optional[str] = None,
    barcode: Optional[int] = None,
    samplecode: Optional[int] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    workflow: Optional[str] = None,
    exclude_status: Optional[str] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    sale_pic_id: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    filter_status_list: Optional[list] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    receipt_start_date: Optional[str] = None,
    receipt_end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    sample_collector_unit_name: Optional[str] = None,
    include_deleted: Optional[bool] = False,
    pic_phone_number: Optional[str] = None,
):
    if receipt_start_date:
        start_date_std = format_date(receipt_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if receipt_end_date:
        end_date_std = format_date(receipt_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")
    if release_start_date:
        release_start_date_std = format_date(
            release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if release_end_date:
        release_end_date_std = format_date(
            release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if release_end_date < release_start_date:
            raise ValueError("end date cannot be after start date")
    if collection_start_date:
        collection_start_date_std = format_date(
            collection_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if collection_end_date:
        collection_end_date_std = format_date(
            collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if collection_end_date_std < collection_start_date_std:
            raise ValueError("end date cannot be after start date")
    if actual_report_release_start_date:
        actual_report_release_start_date_std = format_date(
            actual_report_release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if actual_report_release_end_date:
        actual_report_release_end_date_std = format_date(
            actual_report_release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if actual_report_release_end_date_std < actual_report_release_start_date_std:
            raise ValueError("end date cannot be after start date")

    return await get_sample_list_detail_v3(
        offset=offset,
        size=size,
        order_by=order_by,
        barcode=barcode,
        samplecode=samplecode,
        userid=user_id,
        email=email,
        phone_number=phone_number,
        name=name,
        gender=gender,
        nickname=nickname,
        filter_status_list=filter_status_list,
        current_status=current_status,
        workflow=workflow,
        exclude_status=exclude_status,
        product_code=product_code,
        sale_pic=sale_pic,
        sale_pic_id=sale_pic_id,
        account_name=account_name,
        batch=batch,
        technology=technology,
        sample_collector_name=sample_collector_name,
        sample_receiver_name=sample_receiver_name,
        start_date=start_date_std if receipt_start_date else None,
        end_date=end_date_std if receipt_end_date else None,
        release_start_date=release_start_date_std if release_start_date else None,
        release_end_date=release_end_date_std if release_end_date else None,
        collection_start_date=collection_start_date_std
        if collection_start_date
        else None,
        collection_end_date=collection_end_date_std if collection_end_date else None,
        actual_report_release_start_date=actual_report_release_start_date_std
        if actual_report_release_end_date
        else None,
        actual_report_release_end_date=actual_report_release_end_date_std
        if actual_report_release_end_date
        else None,
        sample_collector_unit_name=sample_collector_unit_name,
        include_deleted=include_deleted,
        pic_phone_number=pic_phone_number,
    )


async def get_sample_list_v4(
    order_by: Optional[str] = "updated_at",
    user_id: Optional[str] = None,
    barcode: Optional[int] = None,
    samplecode: Optional[int] = None,
    email: Optional[str] = None,
    phone_number: Optional[str] = None,
    name: Optional[str] = None,
    gender: Optional[str] = None,
    nickname: Optional[str] = None,
    current_status: Optional[str] = None,
    workflow: Optional[str] = None,
    exclude_status: Optional[str] = None,
    product_code: Optional[List[str]] = None,
    sale_pic: Optional[str] = None,
    sale_pic_id: Optional[str] = None,
    account_name: Optional[List[str]] = None,
    filter_status_list: Optional[list] = None,
    nominator: Optional[str] = None,
    batch: Optional[int] = None,
    technology: Optional[str] = None,
    sample_collector_name: Optional[str] = None,
    sample_receiver_name: Optional[str] = None,
    receipt_start_date: Optional[str] = None,
    receipt_end_date: Optional[str] = None,
    release_start_date: Optional[str] = None,
    release_end_date: Optional[str] = None,
    collection_start_date: Optional[str] = None,
    collection_end_date: Optional[str] = None,
    actual_report_release_start_date: Optional[str] = None,
    actual_report_release_end_date: Optional[str] = None,
    sample_collector_unit_name: Optional[str] = None,
    include_deleted: Optional[bool] = False,
    pic_phone_number: Optional[str] = None,
):
    if receipt_start_date:
        start_date_std = format_date(receipt_start_date, DEFAULT_DATE_NO_TIME_ZONE)
    if receipt_end_date:
        end_date_std = format_date(receipt_end_date, DEFAULT_DATE_NO_TIME_ZONE, False)
        if end_date_std < start_date_std:
            raise ValueError("end date cannot be after start date")
    if release_start_date:
        release_start_date_std = format_date(
            release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if release_end_date:
        release_end_date_std = format_date(
            release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if release_end_date < release_start_date:
            raise ValueError("end date cannot be after start date")
    if collection_start_date:
        collection_start_date_std = format_date(
            collection_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if collection_end_date:
        collection_end_date_std = format_date(
            collection_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if collection_end_date_std < collection_start_date_std:
            raise ValueError("end date cannot be after start date")
    if actual_report_release_start_date:
        actual_report_release_start_date_std = format_date(
            actual_report_release_start_date, DEFAULT_DATE_NO_TIME_ZONE
        )
    if actual_report_release_end_date:
        actual_report_release_end_date_std = format_date(
            actual_report_release_end_date, DEFAULT_DATE_NO_TIME_ZONE, False
        )
        if actual_report_release_end_date_std < actual_report_release_start_date_std:
            raise ValueError("end date cannot be after start date")

    return await get_sample_list_detail_v4(
        order_by=order_by,
        barcode=barcode,
        samplecode=samplecode,
        userid=user_id,
        email=email,
        phone_number=phone_number,
        name=name,
        gender=gender,
        nickname=nickname,
        filter_status_list=filter_status_list,
        current_status=current_status,
        workflow=workflow,
        exclude_status=exclude_status,
        product_code=product_code,
        sale_pic=sale_pic,
        sale_pic_id=sale_pic_id,
        account_name=account_name,
        batch=batch,
        technology=technology,
        sample_collector_name=sample_collector_name,
        sample_receiver_name=sample_receiver_name,
        start_date=start_date_std if receipt_start_date else None,
        end_date=end_date_std if receipt_end_date else None,
        release_start_date=release_start_date_std if release_start_date else None,
        release_end_date=release_end_date_std if release_end_date else None,
        collection_start_date=collection_start_date_std
        if collection_start_date
        else None,
        collection_end_date=collection_end_date_std if collection_end_date else None,
        actual_report_release_start_date=actual_report_release_start_date_std
        if actual_report_release_end_date
        else None,
        actual_report_release_end_date=actual_report_release_end_date_std
        if actual_report_release_end_date
        else None,
        sample_collector_unit_name=sample_collector_unit_name,
        include_deleted=include_deleted,
        pic_phone_number=pic_phone_number,
    )

async def get_sample_list_labcheck(
        barcode: Optional[int],
        offset: Optional[int] = None,
        size: Optional[int] = None,
        order_by: Optional[str] = "updated_at",
        current_status: Optional[str] = None
    ):
    return await get_sample_list_detail_labcheck(
        barcode=barcode,
        offset=offset,
        size=size,
        order_by=order_by,
        current_status=current_status
    )