from fastapi import HTTPException
from starlette.status import (
    HTTP_404_NOT_FOUND,
)

from ..cruds.tracking_step import *
from ..schemas.tracking_step import *
from ..utils.utils import failure_response


async def get_all_steps_service(
    offset: Optional[int] = None,
    size: Optional[int] = None,
    order_by: Optional[str] = "ts.created_at",
    tenBuoc: Optional[str] = None,
    include_deleted: Optional[bool] = False,
):
    return await get_all_steps(
        offset=offset,
        size=size,
        order_by=order_by,
        tenBuoc=tenBuoc,
        include_deleted=include_deleted,
    )


async def get_step_by_id_service(id: str):
    data = await get_tracking_step_by_id(id)
    return data.to_dict()


async def create_step_service(data: dict):
    """
    tenbuoc: str
    """
    return await create_tracking_step(data)


async def update_step_service(id: str, data: dict):
    """
    type: str
    """
    step = await get_tracking_step_by_id(id)
    if not step:
        err = f"Step with id {id} can not be found"
        http_code = HTTP_404_NOT_FOUND
        errs = failure_response(err)
        raise HTTPException(status_code=http_code, detail=errs)

    await update_tracking_step(step, data)
    data = await get_tracking_step_by_id(id)
    return data.to_dict()


async def delete_step_by_id_service(id: str):
    """
    id: str
    """
    step = await get_tracking_step_by_id(id)

    await delete_tracking_step(step=step)

    data = await get_tracking_step_by_id(id)
    return data.to_dict()
