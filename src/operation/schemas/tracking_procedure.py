from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from ..services.lab_sample import match_barcode_n_lid


from .tracking_employee import EmployeeBaseDetail
from .tracking_template import BuocThucHien

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)



# class BuocThucHienDetail(BaseModel):
#     # procedure_id: str
#     employee: EmployeeBaseDetail = Field(exclude=True)
#     employee_id: Optional[str]
#     template_step_id: Optional[str]

def parse_id(key: str, values: dict):
    if key not in values or not values[key]:
        raise ValueError(f'{key} is required!')
    return values[key].id

class BuocThucHienExtended(BuocThucHien):
    template_step_id: str
    employee: EmployeeBaseDetail = Field(exclude=True)
    employee_id: Optional[str]

    @validator("employee_id", always=True)
    def validate_donViBanGiao_id(cls, v, values):
        return parse_id("employee", values)

class cacBuocXetnghiem(BaseModel):
    cacBuocThucHien: List[BuocThucHienExtended]

class ProcedureBase(BaseModel):
    # id
    tracking_id: str
    template_id: str
    maXetNghiem: str
    nhietDoLuuTru: str
    gs_lid: Optional[str] 
    gs_barcode: Optional[str] # get from "lid" if lid exist
    gs_currStep: Optional[int]
    gs_totalStep: Optional[int]
    gs_isComplete: Optional[bool]

    @validator("gs_lid", always=True)
    def validate_gs_lid(cls, v, values):
        return values['maXetNghiem']

    pass

class AddProcedure(ProcedureBase):
    cacBuocXetNghiem: cacBuocXetnghiem
    pass

class UpdateProcedureDetail(BaseModel):
    template_id: Optional[str]
    maXetNghiem: Optional[str]
    nhietDoLuuTru: Optional[str]
    gs_currStep: Optional[int]
    gs_totalStep: Optional[int]
    gs_isComplete: Optional[bool]