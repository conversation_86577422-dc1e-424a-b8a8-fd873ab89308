from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime


class ItemList(BaseModel):
    product_id: str
    barcode: str
    product_code: Optional[str]
    checkout_price: Optional[int]
    status: Optional[str]
    result_id: Optional[str]

class PartnerBookingCreationReq(BaseModel):
    customer_support_id: Optional[str]
    partner_name: str
    partner_booking_id: Optional[str]
    customer_name: str
    customer_phone: str
    referral_code: Optional[str]
    payment_amount: Optional[int]
    payment_method: Optional[str]
    payment_status: Optional[str]
    status: Optional[str]
    consulted: bool
    error_message: Optional[str]
    products: Optional[str]
    note: Optional[str]
    item_list: Optional[List[ItemList]]

class PartnerBookingUpdateReq(BaseModel):
    customer_support_id: Optional[str]
    partner_name: Optional[str]
    partner_booking_id: Optional[str]
    customer_name: Optional[str]
    customer_phone: Optional[str]
    referral_code: Optional[str]
    payment_amount: Optional[int]
    payment_method: Optional[str]
    payment_status: Optional[str]
    status: Optional[str]
    consulted: Optional[bool]
    note: Optional[str]
    error_message: Optional[str]
    item_list: Optional[List[ItemList]]

class PartnerBookingResultReq(BaseModel):
    barcode_list: Optional[List[str]]
    
class SyncBookingInfoReq(BaseModel):
    booking_list: Optional[List[str]]
    
class ResultIDUpdate(BaseModel):
    result_id: str
    barcode: str
    
class ResultUpdateReq(BaseModel):
    results: List[ResultIDUpdate]