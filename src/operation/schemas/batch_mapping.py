from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime


class AddBatchMapping(BaseModel):
    # id: uuid # v4
    plate_id: Optional[str] = None  # 
    batch_id: Optional[str] = None  # 
    wetlab_date: Optional[str] = None
    drylab_date: Optional[str] = None
    raw_data_uploaded_date: Optional[str] = None
    raw_report_to_reviewers: Optional[str] = None

class UpdateBatchMapping(BaseModel):
    wetlab_date: Optional[str] = None # "2023-03-27"
    drylab_date: Optional[str] = None
    raw_data_uploaded_date: Optional[str] = None
    raw_report_to_reviewers: Optional[str] = None

class UpdateBatchMappingWetlabDate(BaseModel):
    wetlab_date: Optional[str] = None # "2023-03-27"

class UpdateBatchMappingRawDataUploadDate(BaseModel):
    raw_data_uploaded_date: Optional[str] = None # "2023-03-27"

class UpdateBatchMappingDrylabDate(BaseModel):
    drylab_date: Optional[str] = None # "2023-03-27"