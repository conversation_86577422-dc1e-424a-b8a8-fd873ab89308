from optparse import Option
import string
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator, ValidationError
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum
from typing import Optional, List, Dict, Any
from ..config import config

class Employee(BaseModel):
    hoTenNhanVien: str
    soDinhDanh: str
    chucVu: Optional[str] = None
    hocVi: Optional[str] = None

class BuocThucHien(BaseModel):
    tenBuoc: str
    nhanVienThucHien: Employee
    

class ThuNhanMau(BaseModel):
    maThuNhan: str
    donViThuNhanMau: int
    loaiXetNghiem: str
    ngayGioThuThapMau: str
    noiThuThapMau: int
    nhanVienLayMau: Employee
    nhanVienGhiHoSo: Employee
    nhanVienLuuMau: Employee

    @validator('ngayGioThuThapMau')
    def validate_date(cls, v):
        datetime.strptime(v, "%d%m%Y%H%M%S")
        return v

class requestThuNhanMau(ThuNhanMau):

    @validator('loaiXetNghiem')
    def validate_loaiXetNghiem(cls, v):
        allowed_values = {"STR", "mtADN", "SNP"}
        split_values = v.split('_')
        if not all(item in allowed_values for item in split_values):
            raise ValueError(f"'loaiXetNghiem' can only contain {allowed_values} when split by underscores")
        return v

class VanChuyenMau(BaseModel):
    maVanChuyen: Optional[str]
    nhietDoChuyenGiao: Optional[str]
    tinhTrangNiemPhong: bool
    ngayGioChuyenGiao: str
    diaDiemChuyenGiao: int
    donViBanGiao: str
    donViVanChuyen: Optional[str]
    donViNhanMau: str
    nhanVienBanGiao: Employee
    nhanVienNhanMau: Employee
    khac: Optional[str]

    @validator('ngayGioChuyenGiao')
    def validate_date(cls, v):
        datetime.strptime(v, "%d%m%Y%H%M%S")
        return v

class QuyTrinhXetNghiem(BaseModel):
    maXetNghiem: str
    nhietDoLuuTru: str
    cacBuocXetNghiem: Dict[str, List[BuocThucHien]]

class ThongTinHanhTrinhMau(BaseModel):
    thuNhanMau: ThuNhanMau
    vanChuyenMau: VanChuyenMau
    quyTrinhXetNghiem: QuyTrinhXetNghiem

class requestThongTinHanhTrinhMau(BaseModel):
    thuNhanMau: requestThuNhanMau
    vanChuyenMau: VanChuyenMau
    pass

class requestDuLieuADN(BaseModel):
    tenFileADN: str
    noiDungFileADN: str
    congNghe: Literal[tuple(config['ALLOWED_TECHNOLOGY'])]
    tenKit: str
    tenThuMucTho: Optional[str]
    noiDungThuMucTho: Optional[str]

    def dict(self, *args, **kwargs):
        exclude_nulls = kwargs.pop('exclude_nulls', True)
        result = super().dict(*args, **kwargs)
        if exclude_nulls:
            if result.get('tenThuMucTho') is None:
                result.pop('tenThuMucTho', None)
            if result.get('noiDungThuMucTho') is None:
                result.pop('noiDungThuMucTho', None)
        return result
    pass

class requestKetQuaADN(BaseModel):
    loaiDuLieu: Literal['STR','mtADN','SNP']
    adnId: str
    quyTrinhXetNghiem: QuyTrinhXetNghiem
    duLieuADN: requestDuLieuADN
    pass



class DuLieuADN(requestDuLieuADN):
    loaiDuLieu: Literal['STR','mtADN','SNP']

class ADNDataValidator(BaseModel):
    hoTenCongDan: str
    ngayThangNamSinh: str
    soDinhDanh: str
    maDvXN: int
    anhMat: str
    vanTay: Optional[str]
    mongMat: Optional[str]
    thongTinHanhTrinhMau: ThongTinHanhTrinhMau
    duLieuADN: DuLieuADN

    @validator('ngayThangNamSinh')
    def validate_date(cls, v):
        datetime.strptime(v, "%d/%m/%Y")
        return v

    def dict(self, *args, **kwargs):
        exclude_nulls = kwargs.pop('exclude_nulls', True)
        result = super().dict(*args, **kwargs)
        if exclude_nulls:
            if result.get('vanTay') is None:
                result.pop('vanTay', None)
            if result.get('mongMat') is None:
                result.pop('mongMat', None)
        return result

class ResultValidator(BaseModel):
    ngayLayMau: str
    hoTenCongDan: str
    ngayThangNamSinh: str
    soDinhDanh: str
    donViXetNghiemId: int
    anhMat: str
    vanTay: Optional[str]
    mongMat: Optional[str]
    thongTinHanhTrinhMau: requestThongTinHanhTrinhMau
    danhSachKetQuaADN: List[requestKetQuaADN]

    @validator('ngayLayMau')
    def validate_collect_date(cls, v):
        datetime.strptime(v, "%d/%m/%Y")
        return v


    @validator('ngayThangNamSinh')
    def validate_dob_date(cls, v):
        datetime.strptime(v, "%d/%m/%Y")
        return v
    
    pass


class ADNResultValidator(BaseModel):
    ngayGioGiaoDich: str
    maGiaoDich: str
    data: ADNDataValidator

    @validator("ngayGioGiaoDich", pre=True, always=True)
    def validate_ngayGioGiaoDich(cls, v):
        try:
            datetime.strptime(v, "%d%m%Y%H%M%S")
        except ValueError:
            raise ValueError("Invalid ngayGioGiaoDich format")
        return v


class RequestResult(BaseModel):
    ngayGioGiaoDich: str
    maGiaoDich: str
    trangThai: Literal['0','1']
    moTaLoi: Literal['Hệ thống tồn tại dữ liệu adn', 'Dữ liệu ADN không tồn tại trong hệ thống']
    data: ResultValidator

    @validator("ngayGioGiaoDich", always=True)
    def validate_ngayGioGiaoDich(cls, v):
        try:
            datetime.strptime(v, "%d%m%Y%H%M%S")
        except ValueError:
            raise ValueError("Invalid ngayGioGiaoDich format")
        return v