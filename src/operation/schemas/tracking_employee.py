from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)


# Chỉ cho tạo mới
class EmployeeBase(BaseModel):
    # id
    hoTenNhanVien: str
    hocVi: Optional[str]
    soDinhDanh: str
    chucVu: Literal['<PERSON>yê<PERSON> Viên', '<PERSON><PERSON>hu<PERSON> Viên']
    unit_id: int
    pass

class EmployeeBaseDetail(EmployeeBase):
    id: str

class AddEmployee(EmployeeBase):
    pass

class UpdateEmployeeDetail(BaseModel):
    hoTenNhanVien: Optional[str]
    soDinhDanh: Optional[str]