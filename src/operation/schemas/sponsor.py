import uuid
from datetime import date
from typing import List, Optional

from fastapi import File, UploadFile
from pydantic import BaseModel

DEFAULT_SPONSORED_PARTY = "GENESTORY"


class SponsorBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    type: Optional[str] = None


class SponsorCreate(SponsorBase):
    pass


class SponsorUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None


class GsAreaBase(BaseModel):
    id: int
    area: str


class GsAreaCreate(GsAreaBase):
    pass


class GsAreaUpdate(BaseModel):
    area: Optional[str] = None


class ContractFile(BaseModel):
    file: UploadFile = File(..., description="Sponsor contract file")


class SponsorContractBase(BaseModel):
    name: str
    code: str
    signing_date: date
    sponsor_id: uuid.UUID
    sponsored_party: Optional[uuid.UUID] = DEFAULT_SPONSORED_PARTY
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    total_quantity: Optional[int] = None


class SponsorContractRawCreate(SponsorContractBase):
    file: ContractFile


class SponsorCampaignInfo(BaseModel):
    start_date: date
    end_date: date
    quantity: Optional[int] = None
    gs_area_code: int
    name: Optional[str] = None
    description: Optional[str] = None


class SponsorContractCreate(BaseModel):
    name: str
    code: str
    signing_date: Optional[date] = None
    sponsor_id: uuid.UUID
    sponsored_party: Optional[str] = DEFAULT_SPONSORED_PARTY
    s3_key: Optional[str] = None
    s3_bucket: Optional[str] = None
    total_quantity: Optional[int] = None
    campaigns: Optional[List[SponsorCampaignInfo]] = []


class SponsorContractUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    signing_date: Optional[date] = None
    sponsor_id: Optional[uuid.UUID] = None
    s3_key: Optional[str] = None
    s3_bucket: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    total_quantity: Optional[int] = None


class SponsorCampaignBase(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    quantity: Optional[int] = None
    contract_id: Optional[uuid.UUID] = None
    gs_area_code: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None


class SponsorCampaignCreate(SponsorCampaignBase):
    pass


class SponsorCampaignUpdate(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    quantity: Optional[int] = None
    gs_area_code: Optional[int] = None
    name: Optional[str] = None
    description: Optional[str] = None
