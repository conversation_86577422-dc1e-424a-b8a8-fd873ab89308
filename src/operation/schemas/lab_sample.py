from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime

class LabSampleUpdateReq(BaseModel):
    lid: Optional[str] = None
    note: Optional[str] = None
    positive_control: Optional[bool] = None
    deleted_at: Optional[datetime] = None

class LabSampleCreationReq(BaseModel):
    barcode: str
    lab_receipt_date: Optional[str] = None
    note: Optional[str] = None
    lid: Optional[str]
    
class LabSampleBatchCreationReq(BaseModel):
    data: List[LabSampleCreationReq]
    
class LabSampleBatchUpdateReq(BaseModel):
    data: List[LabSampleUpdateReq]
    
class DeleteMissingSamplesReq(BaseModel):
    barcode_list: List[str]
    
class LabSampleUpdateStatus(BaseModel):
    barcode: str
    status: str
    note: Optional[str]
    
class LabSampleBatchUpdateStatus(BaseModel):
    barcode_list: List[str]
    status: str

class MissingSampleUpdateReq(BaseModel):
    barcode: str
    deleted_at: Optional[datetime] = None



   