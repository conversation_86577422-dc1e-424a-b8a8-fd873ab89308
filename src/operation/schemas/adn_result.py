from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum
from ..config import config


from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)




class ResultBase(BaseModel):
    # id
    adn_integration_id: str
    tenFileADN: str
    congNghe: Literal[tuple(config['ALLOWED_TECHNOLOGY'])]
    tenKit: str
    tenThuMucTho: Optional[str]
    loaiDuLieu: Literal['STR','mtADN','SNP']
    gs_adn_result_s3_url: Optional[str]
    gs_raw_result_s3_url: Optional[str]
    pass

class AddResult(ResultBase):
    pass

class CheckResult(BaseModel):
    adn_integration_id: str
    returnRawADN: Optional[bool] = False
    pass

class UpdateResultDetail(BaseModel):
    tenFileADN: Optional[str]
    congNghe: Optional[Literal[tuple(config['ALLOWED_TECHNOLOGY'])]]
    tenKit: Optional[str]
    tenThuMucTho: Optional[str]
    loaiDuLieu: Optional[Literal['STR','mtADN','SNP']]
    gs_adn_result_s3_url: Optional[str]
    gs_raw_result_s3_url: Optional[str]
    pass