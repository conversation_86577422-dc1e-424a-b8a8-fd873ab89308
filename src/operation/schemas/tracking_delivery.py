from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum


from .tracking_employee import EmployeeBaseDetail

from .tracking_unit import UnitBaseDetail

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)


def parse_id(key: str, values: dict):
    if key not in values or not values[key]:
        raise ValueError(f'{key} is required!')
    return values[key].id

class DeliveryBase(BaseModel):
    # id
    tracking_id: str
    maVanChuyen: Optional[str]
    nhietDoChuyenGiao: Optional[str]
    ngayGioChuyenGiao: str
    donViBanGiao: UnitBaseDetail = Field(exclude=True) 
    donViBanGiao_id: Optional[int]
    diaDiemChuyenGiao: Optional[int]
    donViVanChuyen: Optional[UnitBaseDetail] = Field(exclude=True, default=None) 
    donViVanChuyen_id: Optional[int]
    nhanVienBanGiao: EmployeeBaseDetail = Field(exclude=True) 
    nhanVienBanGiao_id: Optional[str]
    tinhTrangNiemPhong: bool
    donViNhanMau: UnitBaseDetail = Field(exclude=True) 
    donViNhanMau_id: Optional[int]
    nhanVienNhanMau: EmployeeBaseDetail = Field(exclude=True) 
    nhanVienNhanMau_id: Optional[str]
    khac: Optional[str] = ""


    @validator("donViBanGiao_id", always=True)
    def validate_donViBanGiao_id(cls, v, values):
        return parse_id("donViBanGiao", values)

    @validator("diaDiemChuyenGiao", always=True)
    def validate_diaDiemChuyenGiao(cls, v, values):
        if 'donViBanGiao' not in values.keys():
            raise ValueError('donViBanGiao is required!')
        if v and v != values['donViBanGiao'].gs_area_code:
            raise ValueError('diaDiemChuyenGiao does not match gs_area_code!')
        v = values['donViBanGiao'].gs_area_code
        return v


    @validator("donViVanChuyen_id")
    def validate_donViVanChuyen_id(cls, v, values):
        if 'donViVanChuyen' in values and values['donViVanChuyen']:
            return values['donViVanChuyen'].id
        return None

    @validator("donViNhanMau_id", always=True)
    def validate_donViNhanMau_id(cls, v, values):
        return parse_id("donViNhanMau", values)

    @validator("nhanVienBanGiao_id", always=True)
    def validate_nhanVienBanGiao_id(cls, v, values):
        return parse_id("nhanVienBanGiao", values)

    @validator("nhanVienNhanMau_id", always=True)
    def validate_nhanVienNhanMau_id(cls, v, values):
        return parse_id("nhanVienNhanMau", values)

class AddDelivery(DeliveryBase):
    pass

class UpdateDeliveryDetail(BaseModel):
    maVanChuyen: Optional[str]
    nhietDoChuyenGiao: Optional[str]
    tinhTrangNiemPhong: Optional[bool]
    ngayGioChuyenGiao: Optional[str]
    diaDiemChuyenGiao: Optional[int]
    donViBanGiao_id: Optional[int]
    donViVanChuyen_id: Optional[int]
    donViNhanMau_id: Optional[int]
    nhanVienBanGiao_id: Optional[str]
    nhanVienNhanMau_id: Optional[str]
    khac: Optional[str]