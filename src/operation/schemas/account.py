from pydantic import BaseModel
from typing import Optional
# from vietnam_provinces import Province, District, Ward
# from geosky import geo_plug

class AccountInfo(BaseModel):
    description: Optional[str] = None


class AddAccount(BaseModel):
    # id: str
    name: str
    address: Optional[str] = None
    description: Optional[str] = None
    area: str
    type: str
    pic_id: Optional[str] = None


class UpdateAccount(BaseModel):
    name: Optional[str] = None
    address: Optional[str] = None
    description: Optional[str] = None
    area: Optional[str] = None
    pic_id: Optional[str] = None