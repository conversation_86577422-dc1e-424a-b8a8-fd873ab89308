from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum
from ..config import config

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)



class TemplateBase(BaseModel):
    # id
    congNghe: Literal[tuple(config['ALLOWED_TECHNOLOGY'])]
    tenKit: str
    gs_template_name: str
    pass

class BuocThucHien(BaseModel):
    step_id: str
    tenBuoc: str
    gs_step_number: int
    pass

class AddTemplate(TemplateBase):
    cacBuocThucHien: List[BuocThucHien]
    pass




class UpdateTemplateDetail(BaseModel):
    congNghe: Optional[Literal[tuple(config['ALLOWED_TECHNOLOGY'])]]
    tenKit: Optional[str]
    gs_template_name: Optional[str]
    cacBuocThucHien: List[BuocThucHien]
