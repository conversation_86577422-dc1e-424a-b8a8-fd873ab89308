from datetime import datetime
from typing import List, Literal, Optional

from pydantic import BaseModel, Field, validator

from src.operation.config import config

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE,
    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
    parse_adn_result_on_s3_bucket,
)


class SampleRequestInfor(BaseModel):
    noiThuThapMau: str  # ACCOUNT NAME
    maDonViThuThap: Optional[str]
    ngayGioHoanThanhPhanTich: str  # RELEASE DATE
    ngayGioThuThapMau: str  # COLLECTION DATE

    def __init__(self, kit_detail: dict):
        super().__init__(
            noiThuThapMau=kit_detail.get("account_name"),
            ngayGioHoanThanhPhanTich=kit_detail.get("actual_report_release_time"),
            ngayGioThuThapMau=kit_detail.get("sample_collection_date"),
        )
        self.maDonViThuThap = self.standardized_account_code()

    @validator("ngayGioHoanThanhPhanTich", always=True)
    def standardized_complete_date(cls, v):
        if not v:
            raise ValueError("Invalid ngayGioHoanThanhPhanTich infor")
        v = datetime.strptime(v, "%Y-%m-%d %H:%M:%S").strftime("%d%m%Y%H%M%S")
        return v

    @validator("ngayGioThuThapMau", always=True)
    def standardized_collecting_date(cls, v):
        if not v:
            raise ValueError("Invalid ngayGioThuThapMau infor")
        v = datetime.strptime(v, "%Y-%m-%d %H:%M:%S").strftime("%d%m%Y%H%M%S")
        return v

    def standardized_account_code(self):
        # Assuming config is defined somewhere in your code
        return config.get("AGENCY_ACCOUNT_CODE").get(
            self.noiThuThapMau, config.get("AGENCY_ACCOUNT_CODE").get("DEFAULT")
        )


class CitizenInfo(BaseModel):
    soDinhDanh: str
    hoTenCongDan: str
    ngayThangNamSinh: str
    noiSinh: Optional[str]
    gioiTinh: Literal["male", "female"]  # 1 - Nam, 0 - Nu
    quocTich: str = "VN"
    duLieuADN: dict
    thongTinXuLyMau: SampleRequestInfor

    def __init__(self, identity_card: dict, kit_detail: dict, adn_integration: dict):
        thongTinXuLyMau = SampleRequestInfor(kit_detail=kit_detail)
        super().__init__(
            soDinhDanh=identity_card.get("identifier_code"),
            hoTenCongDan=identity_card.get("full_name"),
            ngayThangNamSinh=str(identity_card.get("dob")),
            noiSinh=identity_card.get("origin"),
            gioiTinh=identity_card.get("gender"),
            quocTich=identity_card.get("nationality", "VN"),
            duLieuADN={},
            thongTinXuLyMau=thongTinXuLyMau,
        )

        self.duLieuADN = self.get_adn_result_from_bucket(
            adn_integration=adn_integration
        )

    @validator("ngayThangNamSinh", always=True)
    def standardized_dob(cls, v):
        if not v:
            raise ValueError("Invalid ngayThangNamSinh infor")
        v = datetime.strptime(v, "%Y-%m-%d %H:%M:%S").strftime("%d/%m/%Y")
        return v

    @validator("gioiTinh", always=True)
    def standardized_gender(cls, v):
        if not v:
            raise ValueError("Invalid gioiTinh infor")
        return "1" if v == "male" else "2"

    def get_adn_result_from_bucket(self, adn_integration: dict):
        # print("adn_integration: ", adn_integration)
        payload = adn_integration
        response = parse_adn_result_on_s3_bucket(payload=payload)

        # print("response: ", response)
        return response.get("adn_data")
        pass


class VNeIDRequest(BaseModel):
    ngayGioGiaoDich: str
    maGiaoDich: str
    duLieuVuAn: bool = False
    maVuAn: Optional[str]
    data: CitizenInfo
    trangThai: str
    moTaLoi: str = ""

    def __init__(
        self, request_infor: dict, kit_detail: dict, adn_integration_dict: dict
    ):
        citizen_infor = CitizenInfo(
            identity_card=request_infor.get("identity_card"),
            kit_detail=kit_detail,
            adn_integration=adn_integration_dict,
        )
        super().__init__(
            ngayGioGiaoDich=str(request_infor.get("request_date")),
            maGiaoDich=str(request_infor.get("id")),
            duLieuVuAn=False,
            maVuAn=None,
            data=citizen_infor,
            trangThai="0",
            moTaLoi="",
        )

    @validator("ngayGioGiaoDich", always=True)
    def standardized_transaction_date(cls, v):
        if not v:
            raise ValueError("Invalid ngayGioGiaoDich infor")
        v = datetime.strptime(v, "%Y-%m-%d %H:%M:%S.%f").strftime("%d%m%Y%H%M%S")
        return v


class RequestBase(BaseModel):
    # id
    # request_code: str <--- generated automatically
    id: Optional[str]
    request_code: Optional[str]
    agency_id: str
    customer_support_name: Optional[str]
    customer_support_id: Optional[str]  # <--- parsing token
    customer_name: Optional[str]  # <--- parsing token
    customer_phone: Optional[str]
    dob: str
    gender: Literal["male", "female"]
    payment_amount: int = 0
    payment_method: str = "cash"
    payment_status: str = "PENDING"
    status: str = "SAMPLE_NOT_FOUND"
    status_code: Optional[str]
    error_message: Optional[str] = None
    transaction_date: Optional[datetime] = None
    collect_date: Optional[str] = None
    request_date: Optional[str]
    response_date: Optional[str] = None
    identifier_code: Optional[str]

    @validator("dob", always=True)
    def standardized_dob(cls, v):
        if not v:
            raise ValueError("Invalid dob information")
        v = datetime.strptime(v, DEFAULT_DATE_NO_TIME_ZONE)
        return v

    @validator("transaction_date", pre=True, always=True)
    def parse_transaction_date(cls, v):
        if v is None:
            return None
        return datetime.strptime(v, DEFAULT_DATE_NO_TIME_ZONE_W_HOUR)

    @validator("collect_date")
    def parse_collect_date(cls, v):
        if v is None:
            return None
        return datetime.strptime(v, DEFAULT_DATE_NO_TIME_ZONE)

    @validator("request_date")
    def validate_request_date(cls, v):
        if not v:
            return None
        else:
            v = datetime.strptime(v, DEFAULT_DATE_NO_TIME_ZONE)
        return v

    @validator("response_date", pre=True, always=True)
    def parse_response_date(cls, v):
        if v is None:
            return None
        return datetime.strptime(v, DEFAULT_DATE_NO_TIME_ZONE)


class AddRequest(RequestBase):
    pass


class cccdInforBase(BaseModel):
    hoTenCongDan: str
    ngayThangNamSinh: str
    soDinhDanh: str
    donViXetNghiemId: int = Field(
        default_factory=lambda: config["AGENCY_ACCOUNT_CODE"]["maDonViXetNghiem"]
    )

    @validator("ngayThangNamSinh", always=True)
    def standardized_dob(cls, v):
        if not v:
            raise ValueError("ngayThangNamSinh infor is required")
        v = datetime.strptime(v, "%d/%m/%Y").strftime("%Y-%m-%d")
        return v


class cccdInfor(cccdInforBase):
    ngayLayMau: str
    ngayYeuCau: Optional[str]

    @validator("ngayLayMau")
    def standardized_ngay_lay_mau(cls, v):
        if not v:
            raise ValueError("ngayLayMau infor is required")
        v = datetime.strptime(v, "%d/%m/%Y").strftime("%Y-%m-%d")
        return v

    @validator("ngayYeuCau")
    def standardized_ngay_yeu_cau(cls, v):
        v = datetime.strptime(v, "%d/%m/%Y").strftime("%Y-%m-%d")
        return v


class internalCCCDInfor(cccdInforBase):
    ngayLayMau: Optional[str]
    ngayYeuCau: Optional[str]

    @validator("ngayLayMau")
    def standardized_ngay_lay_mau(cls, v):
        v = datetime.strptime(v, "%d/%m/%Y").strftime("%Y-%m-%d")
        return v

    @validator("ngayYeuCau")
    def standardized_ngay_yeu_cau(cls, v):
        v = datetime.strptime(v, "%d/%m/%Y").strftime("%Y-%m-%d")
        return v


class metaRequestBase(BaseModel):
    ngayGioGiaoDich: str
    maGiaoDich: str

    @validator("ngayGioGiaoDich", always=True)
    def standardized_transaction_date(cls, v):
        if not v:
            raise ValueError("Invalid ngayGioGiaoDich infor")
        v = datetime.strptime(v, "%d%m%Y%H%M%S").strftime("%Y-%m-%dT%H:%M:%S")
        return v

    pass


class vneidRequestBase(metaRequestBase):
    data: cccdInfor


class addVNeIDRequest(vneidRequestBase):
    pass


class addInternalRequest(metaRequestBase):
    data: internalCCCDInfor


class returnADNResult(BaseModel):
    request_id: str
    samplecode: str
    barcode: str
    adn_type: Literal["STR", "SNP", "mtADN"]
    returnRawADN: bool = False
    pass


class UpdateRequestDetail(BaseModel):
    customer_support_id: Optional[str]
    status: str
    error_message: Optional[str] = None
    status_code: Optional[str] = None
    response_date: Optional[str] = None
    payment_status: Optional[str] = None
    pass


class requestResult(BaseModel):
    request_id: str
    collect_date: str
    adn_types: List[str]

    @validator("collect_date")
    def standardized_ngay_lay_mau(cls, v):
        v = datetime.strptime(v, "%d/%m/%Y").strftime("%Y-%m-%d")
        return v

    @validator("adn_types", always=True)
    def validate_supported_technologies(cls, v):
        for adn_type in v:
            if adn_type not in config["AGENCY_ACCOUNT_CODE"]["VNHID_TECHNOLOGIES"]:
                raise ValueError(f"Not supported technology: {adn_type}")

        return v
