from pydantic import BaseModel
from typing import Optional


class StaffInfo(BaseModel):
    description: Optional[str] = None


class AddStaff(BaseModel):
    # id: str
    name: str
    email: Optional[str]
    userid: Optional[str]
    account_id: str
    phone_number: Optional[str]
    role: str

class UpdateStaff(BaseModel):
    name: Optional[str]
    email: Optional[str] = None
    phone_number: Optional[str] = None