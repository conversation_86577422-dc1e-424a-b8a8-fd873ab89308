from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)




class TrackingBase(BaseModel):
    # id
    samplecode: str
    status: Literal['Thu nhận mẫu', 'Vận chuyển mẫu', 'Quy trình xét nghiệm'] = 'Thu nhận mẫu'
    identifier_code: Optional[str]
    pass

class AddTracking(TrackingBase):
    pass

class UpdateTrackingDetail(BaseModel):
    status: Literal['Thu nhận mẫu', 'Vận chuyển mẫu', 'Quy trình xét nghiệm'] = 'Thu nhận mẫu'