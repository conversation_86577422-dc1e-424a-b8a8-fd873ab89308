from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from .tracking_employee import EmployeeBaseDetail

from .tracking_unit import UnitBaseDetail

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)



class CollectionBase(BaseModel):
    # id
    tracking_id: str
    maThuNhan: str
    ngayGioThuThapMau: str
    donViThuNhanMau: UnitBaseDetail = Field(exclude=True) 
    donViThuNhanMau_id: Optional[int]
    noiThuThapMau: Optional[int]
    nhanVienLayMau: EmployeeBaseDetail = Field(exclude=True) 
    nhanVienGhiHoSo: EmployeeBaseDetail = Field(exclude=True) 
    nhanVienLuuMau: Optional[EmployeeBaseDetail] = Field(exclude=True) 
    nhanVienLayMau_id: Optional[str]
    nhanVienGhiHoSo_id: Optional[str]
    nhanVienLuuMau_id: Optional[str]
    
    @validator("donViThuNhanMau_id", always=True)
    def parse_unit_id(cls, v, values):
        # if not v:
        #     raise ValueError('Invalid ngayGioHoanThanhPhanTich infor')
        # print("donViThuNhanMau: ", values['donViThuNhanMau'])
        if 'donViThuNhanMau' not in values.keys():
            raise ValueError('donViThuNhanMau is required!')
        v = values['donViThuNhanMau'].id
        return v
    
    @validator("noiThuThapMau", always=True)
    def parse_unit_code(cls, v, values):
        # if not v:
        #     raise ValueError('Invalid ngayGioHoanThanhPhanTich infor')
        # print("donViThuNhanMau: ", values['donViThuNhanMau'])
        if 'donViThuNhanMau' not in values.keys():
            raise ValueError('donViThuNhanMau is required!')
        if v and (v != values['donViThuNhanMau'].gs_area_code):
            raise ValueError('donViThuNhanMau does not match gs_area_code!')
        v = values['donViThuNhanMau'].gs_area_code
        return v
    
    @validator("nhanVienLayMau_id", always=True)
    def parse_nvlm_id(cls, v, values):
        # if not v:
        #     raise ValueError('Invalid ngayGioHoanThanhPhanTich infor')
        if 'nhanVienLayMau' not in values.keys():
            raise ValueError('nhanVienLayMau is required!')
        v = values['nhanVienLayMau'].id
        return v
    
    @validator("nhanVienGhiHoSo_id", always=True)
    def parse_nvghs_id(cls, v, values):
        # if not v:
        #     raise ValueError('Invalid ngayGioHoanThanhPhanTich infor')
        if 'nhanVienGhiHoSo' not in values.keys():
            raise ValueError('nhanVienGhiHoSo is required!')
        v = values['nhanVienGhiHoSo'].id
        return v
    
    @validator("nhanVienLuuMau_id", always=True)
    def parse_nvlum_id(cls, v, values):
        # if not v:
        #     raise ValueError('Invalid ngayGioHoanThanhPhanTich infor')
        if values['nhanVienLuuMau']:
            v = values['nhanVienLuuMau'].id
        else:
            v = None
        return v

class AddCollection(CollectionBase):
    pass

class UpdateCollectionDetail(BaseModel):
    donViThuNhanMau_id: Optional[int]
    noiThuThapMau: Optional[int]
    ngayGioThuThapMau: Optional[str]
    nhanVienLayMau_id: Optional[str]
    nhanVienGhiHoSo_id: Optional[str]
    nhanVienLuuMau_id: Optional[str]