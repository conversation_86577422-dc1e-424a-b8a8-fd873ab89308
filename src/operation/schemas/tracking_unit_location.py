from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator, SecretStr
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)

class UnitLocationBase(BaseModel):
    # id
    name: str
    unit_id: int
    pass

class UnitLocationBaseDetail(UnitLocationBase):
    pass

class AddUnitLocation(UnitLocationBase):
    pass

class UpdateUnitLocationDetail(BaseModel):
    name: Optional[str]

