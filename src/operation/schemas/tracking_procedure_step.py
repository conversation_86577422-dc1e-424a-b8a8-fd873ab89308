from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)



class ProcedureStepBase(BaseModel):
    # id
    procedure_id: str
    employee_id: str
    template_step_id: str
    pass

class AddProcedureStep(ProcedureStepBase):
    pass

class UpdateProcedureStepDetail(BaseModel):
    # procedure_id: Optional[str]
    # employee_id: Optional[str]
    # template_step_id: Optional[str]
    pass