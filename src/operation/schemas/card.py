from os import name
from random import sample
from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime

class CreateCardReq(BaseModel):
    # id: Optional[str]=None
    id: str
    card_product_id: str
    barcode: str
    phone_number: str
    report_ver: str # [optional]
    db_ver: str # [optional]
    lang: str # [optional]
    card_status: Optional[str] = None
    full_name: Optional[str] = None
    qr_url: Optional[str] = None
    created_at: Optional[str] = None
    s3_object_key: Optional[str] = None

class GenerateCardReq(BaseModel):
    card_id: Optional[str] = None

class CardUpdateReq(BaseModel):
    card_status: Optional[str] = None
    qr_url: Optional[str] = None
    s3_object_key: Optional[str] = None
    presigned_s3_font_url: Optional[str] = None
    presigned_s3_back_url: Optional[str] = None

class IssueCardReq(BaseModel):
    barcode: Optional[str] = None
    current_status: Optional[str] = None
    phone_number: Optional[str] = None
    user_id: Optional[str] = None
    qr_url: Optional[str] = None
    full_name: Optional[str] = None
    product_name: Optional[str] = None
    lang: Optional[str] = None
    is_new: Optional[bool] = None
    is_valid: Optional[bool] = None
    note: Optional[str] = None

class GenerateCardsReq(BaseModel):
    card_requests: List[GenerateCardReq]
    card_product_id: Optional[str] = None
    card_product_name: Optional[str] = None

class IssueCardsReq(BaseModel):
    card_product_id: Optional[str] = None
    included_qr: Optional[bool] = None
    card_requests: List[IssueCardReq]

class CreateCardProductReq(BaseModel):
    type: str
    card_product_name: str
    policy: str

class CardProductUpdateReq(BaseModel):
    type: Optional[str] = None
    policy: Optional[str] = None

class CardDownloadReq(BaseModel):
    card_id: Optional[str] = None
    card_front_s3_object_uri: Optional[str] = None
    card_back_s3_object_uri: Optional[str] = None

class CardDownloadReqs(BaseModel):
    download_reqs: List[CardDownloadReq]