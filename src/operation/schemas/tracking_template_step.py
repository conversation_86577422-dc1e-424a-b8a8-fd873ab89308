from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)



class TemplateStepBase(BaseModel):
    # id
    step_id: str
    template_id: str
    gs_step_number: str
    pass

class AddTemplateStep(TemplateStepBase):
    pass

class UpdateTemplateStepDetail(BaseModel):
    step_id: Optional[str]
    template_id: Optional[str]
    gs_step_number: Optional[str]