# from sqlalchemy.dialects.postgresql import UUID
from datetime import date
from typing import List, Literal, Optional
from uuid import UUID

from pydantic import BaseModel, validator, root_validator


class NewKitStatus(BaseModel):
    status: str
    note: Optional[str] = None


class KitInfo(BaseModel):
    description: Optional[str] = None


class SampleV3Base(BaseModel):
    samplecode: Optional[str]  # using for QUERY only
    sample_collection_date: Optional[str] = None
    sample_recollection: Optional[bool] = False
    sample_collection_time: Optional[int] = 1
    sample_receipt_date: Optional[str] = None
    sample_collector_name: Optional[str] = None
    sample_receiver_name: Optional[str] = None
    account_id: Optional[str] = None
    nominator_id: Optional[str] = None
    freelancer_id: Optional[str] = None
    lab_check_date: Optional[str] = None
    lab_receipt_date: Optional[str] = None
    sample_type: str = "Máu"
    pass


class KitV3Base(BaseModel):
    nickname: Optional[str] = None
    version: Optional[str] = None
    expected_report_release_date: Optional[str] = None
    actual_report_release_time: Optional[str]
    customer_support_id: Optional[str] = None
    customer_support_name: Optional[str] = None
    promotion_id: Optional[str] = None
    is_priority: bool = False
    free_of_charge: bool = True
    product_name: str = "Dịch vụ định danh công dân"
    product_code: str = "09"
    product_type: Literal["CLINIC", "B2C", "B2B"] = "CLINIC"
    note: Optional[str] = None

    pass


class RegisterKit(BaseModel):
    barcode: str
    sample_meta_id: Optional[str] = None
    nickname: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    name: str
    gender: Literal["male", "female"]
    dob: Optional[str] = None
    yob: Optional[str] = None
    address: str
    validate_account: bool
    version: Optional[str]
    product_code: Optional[str] = None
    product_type: Literal["CLINIC", "B2C", "B2B"] = "CLINIC"
    sample_collection_date: str
    sample_collection_time: Optional[int] = 1
    sample_receipt_date: str
    lab_receipt_date: Optional[str] = None
    sample_collector_name: Optional[str] = None
    sample_receiver_name: Optional[str] = None
    expected_report_release_date: Optional[str]
    source_id: Optional[str]
    note: Optional[str]
    diagnosis: Optional[str]
    account_id: str
    nominator_id: Optional[str] = None
    customer_support_id: Optional[str] = None
    customer_support_name: Optional[str] = None
    free_of_charge: bool
    promotion_id: Optional[str]
    is_priority: bool
    sample_type: Optional[str]


class OperatorRegisterKit(RegisterKit):
    userid: Optional[str]
    # [GS-6723]
    freelancer_id: Optional[str]


class OperatorKitLink(BaseModel):
    barcode: str
    default_pdf_link: str


class UpdateKit(BaseModel):
    barcode: str
    nickname: Optional[str] = None
    name: Optional[str] = None
    # userid: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    gender: Optional[str] = None
    dob: Optional[str] = None
    yob: Optional[str] = None
    address: Optional[str] = None
    sample_meta_id: Optional[str] = None
    product_code: Optional[str] = None
    product_type: Literal["CLINIC", "B2C", "B2B"] = "CLINIC"
    sample_collection_date: Optional[str]
    sample_collection_time: Optional[int] = 1
    sample_receipt_date: Optional[str] = None
    lab_receipt_date: Optional[str] = None
    sample_collector_name: Optional[str] = None
    sample_receiver_name: Optional[str] = None
    expected_report_release_date: Optional[str]
    source_id: str
    account_id: Optional[str]
    nominator_id: Optional[str]
    freelancer_id: Optional[str]
    free_of_charge: Optional[bool] = False
    promotion_id: Optional[str]
    is_priority: Optional[bool] = False
    current_status: Optional[str] = None
    validate_account: Optional[bool] = False
    note: Optional[str] = None
    diagnosis: Optional[str] = None
    sample_type: Optional[str]


class UserSampleBody(BaseModel):
    userid: str
    sample_meta_id: str


class BatchUpdateKitStatus(BaseModel):
    kit_list: List[str]


class SendToLabUpdate(BatchUpdateKitStatus):
    lab_receipt_date: Optional[str] = None


# class RegisterKitV3(BaseModel):
#     barcode: str
#     sample_id: str
#     nickname: Optional[str] = None
#     version: str
#     expected_report_release_date: Optional[str]
#     actual_report_release_time: Optional[str]
#     note: Optional[str]
#     customer_support_id: Optional[str] = None
#     customer_support_name: Optional[str] = None
#     free_of_charge: bool = False
#     promotion_id: Optional[str]
#     is_priority: bool = False
#     is_card_issued: bool = False
#     product_code: str = ''
#     product_name: str = ''
#     product_type: Literal['CLINIC', 'B2C', 'B2B'] = 'CLINIC'
#     pdf_generation_date: Optional[str]
#     default_pdf_link: Optional[str]


class UpdateKitInfoV3(BaseModel):
    ## Subject
    email: Optional[str] = None
    phone_number: Optional[str] = None
    diagnosis: Optional[str]
    legal_guardian: Optional[str] = None
    identifier_code: Optional[str] = None
    martyr_name: Optional[List[str]] = None
    martyr_relationships: Optional[List[str]] = None
    validate_account: bool
    ## sample
    ## id (auto-generated)
    samplecode: str
    subject_id: str = None
    sample_collection_date: Optional[str]
    sample_recollection: Optional[bool]
    sample_collection_time: Optional[int] = 1
    sample_receipt_date: Optional[str]
    sample_collector_name: Optional[str] = None
    sample_receiver_name: Optional[str] = None
    source_id: Optional[str]
    sponsor_id: Optional[str] = None
    account_id: Optional[str]
    scan_status: int = 0
    lab_check_date: Optional[str] = None
    lab_receipt_date: Optional[str] = None
    sample_type: Optional[str]
    ## kit
    barcode: Optional[str]
    nickname: Optional[str] = None
    version: Optional[str]
    expected_report_release_date: Optional[str]
    actual_report_release_time: Optional[str]
    # pdf_generation_date: Optional[str] <-- Generated while releasing report
    customer_support_id: Optional[str] = None
    customer_support_name: Optional[str] = None
    promotion_id: Optional[str]
    is_priority: Optional[bool] = None
    free_of_charge: Optional[bool] = None
    # is_card_issued: bool
    product_name: str
    product_code: str
    product_type: Literal["CLINIC", "B2C", "B2B"] = "CLINIC"
    note: Optional[str]
    current_status: Optional[str] = None
    current_status_id: Optional[int] = None
    # workflow: Optional[str] = None
    nominator_id: Optional[str] = None
    pass


class RegisterKitV3(UpdateKitInfoV3):
    # subject
    ## id (auto-generated)
    ## user_id (route)
    name: Optional[str] = None
    address: Optional[str]
    dob: str = None
    gender: Literal["male", "female"]
    yob: Optional[str] = None


class OperatorRegisterKitV3(RegisterKitV3):
    userid: Optional[str]
    # [GS-6723]
    freelancer_id: Optional[str]


class OperatorMigrateKitV3(BaseModel):
    barcode: str
    target_samplecode: str
    is_target_cccd: bool = False
    is_skipping_yob: bool = False
    pass


class OperatorRecollectKitV3(RegisterKitV3):
    user_id: Optional[str]
    full_name: Optional[str]
    # [GS-6723]
    freelancer_id: Optional[str]


class OperatorUpgradeKitV3(RegisterKitV3):
    user_id: Optional[str]
    full_name: Optional[str]
    # [GS-6723]
    freelancer_id: Optional[str]
    upgraded_at: str


class UpdateSampleV3(BaseModel):
    customer_support_id: Optional[str] = None
    customer_support_name: Optional[str] = None
    # subject
    ## id (auto-generated)
    ## user_id (route)
    name: Optional[str] = None
    full_name: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    diagnosis: Optional[str] = None
    address: Optional[str] = None
    dob: Optional[str] = None
    gender: Optional[str] = None
    yob: Optional[str] = None
    legal_guardian: Optional[str] = None
    validate_account: Optional[bool] = False
    identifier_code: Optional[str] = None
    martyr_name: Optional[str] = None
    martyr_relationships: Optional[List[str]] = None
    # sample
    ## id (auto-generated)
    samplecode: str  # using for QUERY only
    subject_id: str
    sample_collection_date: Optional[str] = None
    sample_recollection: Optional[bool]
    sample_collection_time: Optional[int] = 1
    sample_receipt_date: Optional[str] = None
    sample_collector_name: Optional[str] = None
    sample_receiver_name: Optional[str] = None
    # source_id: Optional[str]
    # scan_status
    account_id: Optional[str]
    nominator_id: Optional[str]
    freelancer_id: Optional[str]
    # note: Optional[str] = None
    lab_check_date: Optional[str] = None
    lab_receipt_date: Optional[str] = None
    sample_type: Optional[str]
    sponsor_id: Optional[str] = None
    tracking_unit_id: Optional[int] = None


class UpdateKitV3(UpdateSampleV3):
    # kit
    # barcode: Optional[str]
    nickname: Optional[str] = None
    version: Optional[str]
    expected_report_release_date: Optional[str]
    actual_report_release_time: Optional[str]
    customer_support_id: Optional[str] = None
    customer_support_name: Optional[str] = None
    promotion_id: Optional[str]
    is_priority: Optional[bool] = False
    free_of_charge: Optional[bool] = False
    # userid: Optional[str] = None
    product_name: str
    product_code: str
    product_type: Literal["CLINIC", "B2C", "B2B"] = "CLINIC"
    note: Optional[str]


class UpdateKitV3viaBarcode(UpdateKitV3):
    barcode: str


class UpdateKitV3viaUUID(UpdateKitV3):
    kit_uuid: str


class OperatorRegisterKitWithCCCDV3(OperatorRegisterKitV3):
    identifier_code: str
    nationality: str
    origin: str
    residence: str
    avatar_image: str
    fingerprint_image: str
    ethnic: str
    tracking_unit_id: int
    manual_input: Optional[bool] = False
    guardian_name: Optional[str] = None
    guardian_gender: Optional[str] = None
    guardian_phone_number: Optional[str] = None
    guardian_identifier_code: Optional[str] = None

    @root_validator
    def check_avatar_or_fingerprint(cls, values):
        avatar = values.get('avatar_image')
        fingerprint = values.get('fingerprint_image')
        if not avatar and not fingerprint:
            raise ValueError("At least one of 'avatar_image' or 'fingerprint_image' must be provided.")
        return values

class addIdentityCardRegistration(BaseModel):
    identifier_code: str
    name: str
    dob: str = None
    gender: Literal["male", "female"]
    nationality: str
    origin: str
    residence: str
    avatar_image: str
    fingerprint_image: Optional[str] = None
    ethnic: str
    martyr_name: Optional[str] = None
    martyr_relationships: Optional[List[str]] = None
    # tracking_unit_id: int
    # shared infor
    # subject
    customer_support_id: Optional[str] = None
    customer_support_name: Optional[str] = None
    manual_input: Optional[bool] = False
    # userid: Optional[str]

    @validator("avatar_image")
    def validate_avatar_image(cls, v):
        if v == "":
            raise ValueError("avatar_image should not be empty string")
        return v

    # @validator("fingerprint_image")
    # def validate_fingerprint_image(cls, v):
    #     if v == '':
    #         raise ValueError('fingerprint_image should not be empty string')
    #     return v

    pass


class onboardIdentityCardRegistration(UpdateKitInfoV3):
    identifier_code: str
    tracking_unit_id: int
    userid: Optional[str]
    freelancer_id: Optional[str]
    nationality: Optional[str]
    residence: Optional[str]
    origin: Optional[str]
    ethnic: Optional[str]
    pass


class OperatorRecollectKitWithCCCDV3(OperatorRecollectKitV3):
    identifier_code: str
    nationality: str
    origin: str
    residence: str
    avatar_image: str
    fingerprint_image: str
    ethnic: str


class OperatorUpgradeKitWithCCCDV3(OperatorUpgradeKitV3):
    identifier_code: str
    nationality: str
    origin: str
    residence: str
    avatar_image: str
    fingerprint_image: str
    ethnic: str


class IdentityCardBase(BaseModel):
    identifier_code: str
    full_name: str
    dob: str
    gender: str
    nationality: str
    origin: str
    residence: str
    avatar_image: str
    fingerprint_image: str


class IdentityCardUpdate(BaseModel):
    phone_number: str
    email: Optional[str] = None,
    residence: Optional[str] = None,
    martyr_name: Optional[List[str]] = None,
    martyr_relationships: Optional[List[str]] = None,
    guardian_name: Optional[str] = None
    guardian_gender: Optional[str] = None
    guardian_phone_number: Optional[str] = None
    guardian_identifier_code: Optional[str] = None
    NewIdentityCard: Optional[IdentityCardBase]


# Define a Pydantic model for serialization
class IdentityCardResponse(BaseModel):
    id: UUID
    identifier_code: str
    full_name: str
    dob: date  # dob is automatically formatted as "YYYY-MM-DD"
    gender: str
    nationality: str
    origin: str
    residence: str
    avatar_image: str
    fingerprint_image: str
    manual_input: Optional[bool] = None
    ethnic: str
    email: Optional[str] = None
    phone_number: Optional[str] = None
    customer_support_name: Optional[str] = None
    customer_support_id: Optional[str] = None
    martyr_name: Optional[str] = None
    martyr_relationships: Optional[str] = None
    guardian_name: Optional[str] = None
    guardian_gender: Optional[str] = None
    guardian_phone_number: Optional[str] = None
    guardian_identifier_code: Optional[str] = None

    created_at: date
    updated_at: Optional[date] = None
    deleted_at: Optional[date] = None

    class Config:
        orm_mode = True  # Allows reading from SQLAlchemy model instances

class Product(BaseModel):
    product_name: str
    product_code: str
    product_type: Literal["CLINIC", "B2C", "B2B"] = "CLINIC"

class UpdateKitInfoV4(BaseModel):
    email: Optional[str] = None
    phone_number: Optional[str] = None
    diagnosis: Optional[str]
    legal_guardian: Optional[str] = None
    identifier_code: Optional[str] = None
    martyr_name: Optional[List[str]] = None
    martyr_relationships: Optional[List[str]] = None
    validate_account: bool
    samplecode: str
    subject_id: str = None
    sample_collection_date: Optional[str]
    sample_recollection: Optional[bool]
    sample_collection_time: Optional[int] = 1
    sample_receipt_date: Optional[str]
    sample_collector_name: Optional[str] = None
    sample_receiver_name: Optional[str] = None
    source_id: Optional[str]
    sponsor_id: Optional[str] = None
    account_id: Optional[str]
    scan_status: int = 0
    lab_check_date: Optional[str] = None
    lab_receipt_date: Optional[str] = None
    sample_type: Optional[str]
    ## kit
    barcode: Optional[str]
    nickname: Optional[str] = None
    version: Optional[str]
    expected_report_release_date: Optional[str]
    actual_report_release_time: Optional[str]
    # pdf_generation_date: Optional[str] <-- Generated while releasing report
    customer_support_id: Optional[str] = None
    customer_support_name: Optional[str] = None
    promotion_id: Optional[str]
    is_priority: Optional[bool] = None
    free_of_charge: Optional[bool] = None
    products : List[Product]
    note: Optional[str]
    current_status: Optional[str] = None
    current_status_id: Optional[int] = None
    nominator_id: Optional[str] = None

class RegisterKitV4(UpdateKitInfoV4):
    name: Optional[str] = None
    address: Optional[str]
    dob: str = None
    gender: Literal["male", "female"]
    yob: Optional[str] = None

class OperatorRegisterKitV4(RegisterKitV4):
    userid: Optional[str]
    # [GS-6723]
    freelancer_id: Optional[str]


class OperatorRegisterKitWithCCCDV4(OperatorRegisterKitV4):
    identifier_code: str
    nationality: str
    origin: str
    residence: str
    avatar_image: str
    fingerprint_image: str
    ethnic: str
    tracking_unit_id: int
    manual_input: Optional[bool] = False
    guardian_name: Optional[str] = None
    guardian_gender: Optional[str] = None
    guardian_phone_number: Optional[str] = None
    guardian_identifier_code: Optional[str] = None

    @root_validator
    def check_avatar_or_fingerprint(cls, values):
        avatar = values.get('avatar_image')
        fingerprint = values.get('fingerprint_image')
        if not avatar and not fingerprint:
            raise ValueError("At least one of 'avatar_image' or 'fingerprint_image' must be provided.")
        return values
