from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr
from typing import Optional, Literal, List


class Subject(BaseModel):
    user_id: Optional[str] = None
    full_name: str
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None
    diagnosis: Optional[str] = None
    address: Optional[str] = None
    dob: str
    gender: Literal['male','female']
    yob: Optional[str] = None
    validate_account: bool