from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime

class AddChip(BaseModel):
    # id: int # 1 ++1
    chip_id: Optional[str] = None # 2302281
    type: Optional[str] = None #GSAv3
    technology: Optional[str] = None #PCR
    # pipeline_qc_report: Optional[str] = None # date w/o timezone

class UpdateChip(BaseModel):
    # pipeline_qc_report: Optional[str] = None
    pass

# class AddBatch(BaseModel):
#     id: str
#     number: Optional[str] = None
#     name: Optional[str] = None
#     note: Optional[str] = None