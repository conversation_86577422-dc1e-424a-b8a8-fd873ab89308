from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime


class DryLabInput(BaseModel):
    type: Optional[str] = None

class DryLabMultiples(BaseModel):
    types: List[str] = []
    inputs: List[DryLabInput] = []

class MicroarrayInput(DryLabInput):
    chip_id: Optional[str] = None # 207419500047
    chip_type: Optional[str] = None # GSAv3
    assembly: Optional[str] = None # hg38
    batch_barcode: Optional[str] = None # 68
    file_paths: Optional[str] = None # s3://vgt-vinmec-qa/207419500047/
    

class PgsInput(MicroarrayInput):
    pass



class PxInput(DryLabInput):
    input_bucket_name: Optional[str] = None # "raw-pipeline-input-qa"
    batch_name: Optional[str] = None # 205540230139
    key_name: Optional[str] = None # 205540230139/reports/px/205540230139.raw.csv
    file_name: Optional[str] = None # 205540230139.raw.csv
    input_px: Optional[str] = None # s3://raw-pipeline-input-qa/px/205540230139.raw.csv
    output_px: Optional[str] = None # s3://vgt-data-pipeline-output-qa/205540230139/reports/px

