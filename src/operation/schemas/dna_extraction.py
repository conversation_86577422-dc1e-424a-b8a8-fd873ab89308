# src>operation>schemas>staff.py
from pydantic import BaseModel
from typing import Optional, Literal


class DnaExtractionInfo(BaseModel):
    lid: Optional[str] = None


class AddDnaExtraction(BaseModel):
    # id: str
    lid: str # 1.0 editable # 2.x immutable
    dna_extraction_date: str
    qubit: Optional[float] = None
    nano_drop: float
    a260_a280: float
    agarose_gel: str
    dna_qc_status: str
    note: Optional[str] = None

class UpdateDnaExtraction(BaseModel):
    dna_extraction_date: Optional[str] = None
    qubit: Optional[float] = None
    nano_drop: Optional[float] = None
    a260_a280: Optional[float] = None
    agarose_gel: Optional[str] = None
    dna_qc_status: Optional[str] = None
    note: Optional[str] = None