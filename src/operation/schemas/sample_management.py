from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime

class BatchUpdateReq(BaseModel):
    id: Optional[str] = None
    number: Optional[str] = None
    name: Optional[str] = None
    note: Optional[str] = None
    
class AddChipToBatchReq(BaseModel):
    batch_id: str
    chip_id_list: list
    
class CreateBatchReq(BaseModel):
    # id: Optional[str]=None
    number: int
    name: str
    note: str
    type: Optional[str] = None

class CreateBatchMappingReqs(CreateBatchReq):
    plate_names: list

class TriggerPipelineReq(BaseModel):
    gene_kit: Optional[str] = None