from os import name
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Literal, List



class AdnIntegrationTypeBase(BaseModel):
    # id
    name: str
    adn_type: str
    method: str
    pass

class AddAdnIntegrationType(AdnIntegrationTypeBase):
    pass

class UpdateAdnIntegrationTypeDetail(BaseModel):
    name: str
    pass

