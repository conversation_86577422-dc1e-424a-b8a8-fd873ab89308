import re
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, validator

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE_W_HOUR,
    get_current_date_time_utc_7,
    standardize_full_name,
)
from .kit import KitV3Base, SampleV3Base


class CollectSessionBase(BaseModel):
    # id
    location_id: str
    user_id: Optional[str]  # --> collect from token
    phone_number: Optional[str]  # --> collect from token
    employee_name: str
    # collect_date: str --> current date

    @validator("phone_number")
    def phone_validation(cls, v):
        regex = r"^(\+)[1-9][0-9\-\(\)\.]{9,15}$"
        if v and not re.search(regex, v, re.I):
            raise ValueError("Phone Number Invalid.")
        return v

    @validator("employee_name", always=True)
    def employee_name_validation(cls, v):
        return standardize_full_name(v)

    pass


class CollectSessionBaseDetail(CollectSessionBase):
    pass


class AddCollectSession(CollectSessionBase):
    pass


class UpdateCollectSessionDetail(BaseModel):
    location_id: Optional[str]
    employee_id: Optional[str]
    collect_date: Optional[str]
    pass


class CollectSessionSampleBase(BaseModel):
    identifier_code: str
    samplecode: Optional[str]
    pass


class AddSamplesToCollectSession(CollectSessionSampleBase, SampleV3Base, KitV3Base):
    tracking_unit_id: Optional[int]
    validate_account: Optional[bool] = False
    account_name: Optional[str] = "CCCD"
    # sample
    sample_type: str = "Máu"
    # kit
    is_priority: bool = False
    free_of_charge: bool = True
    product_name: str = "Dịch vụ định danh công dân"
    product_code: str = "09"

    sample_collector_name: Optional[str] = None
    std_sample_collector_name: Optional[str] = None
    std_sample_receiver_name: Optional[str] = None
    sample_collection_date: Optional[datetime] = None

    @validator("sample_collection_date", pre=True, always=True)
    def set_sample_collection_date(cls, v):
        current_time = get_current_date_time_utc_7()
        sample_collection_date_str = current_time.strftime(
            DEFAULT_DATE_NO_TIME_ZONE_W_HOUR
        )
        sample_collection_date = datetime.strptime(
            sample_collection_date_str, "%Y-%m-%dT%H:%M:%S"
        )
        return sample_collection_date
