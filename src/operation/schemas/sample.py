from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr
from typing import Optional, Literal, List


class Sample(BaseModel):
    id: str # samplecode.id
    subject_id: Optional[str] = None
    sample_collection_date: Optional[str]
    sample_recollection: bool
    sample_collection_time: int = 1
    sample_receipt_date: Optional[str]
    sample_collector_name: Optional[str] = None
    sample_receiver_name: Optional[str] = None
    source_id: Optional[str]
    scan_status: int = 0
    lab_check_date: Optional[str]
    lab_receipt_date: Optional[str]
    sample_type: Optional[str]
    # account_id: str
    # nominator_id: Optional[str] = None