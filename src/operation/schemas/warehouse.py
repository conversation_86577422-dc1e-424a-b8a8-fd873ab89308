from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum
from ..config import config

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)





class SamplePosition(BaseModel):
    barcode: str
    x_position: str
    y_position: str

class SampleBoxDetail(BaseModel):
    sample_box_code: str
    sample_rack_code: Optional[str] = None
    sample_position: Optional[SamplePosition] = None

class SetSamplePosition(BaseModel):
    sample_positions: List[SamplePosition]
    box_code: str
    rack_code: Optional[str] = None



