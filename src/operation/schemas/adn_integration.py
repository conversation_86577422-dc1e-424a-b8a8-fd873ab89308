from httpx import request
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Literal, List



class AdnIntegrationBase(BaseModel):
    # id
    # request_id: str
    samplecode: str
    barcode: Optional[str]    
    type_id: Optional[str]
    status: str
    presigned_s3_url: Optional[str]
    raw_adn_s3_obj_key: Optional[str]
    review_required: bool = True
    pass

class AddAdnIntegration(AdnIntegrationBase):
    pass


class UpdateAdnIntegerationStatus(BaseModel):
    status: Optional[str]
    presigned_s3_url: Optional[str]
    raw_adn_s3_obj_key: Optional[str]
    review_required: Optional[bool] = True