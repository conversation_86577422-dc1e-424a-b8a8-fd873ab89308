# src>operation>schemas>staff.py
from pydantic import BaseModel
from typing import Optional


class PromotionInfo(BaseModel):
    name: Optional[str] = None


class AddPromotion(BaseModel):
    # id: str
    code: str
    name: str
    discount: int
    department: str
    start_date: str
    end_date: str

class UpdatePromotion(BaseModel):
    name: Optional[str] = None
    discount: Optional[int] = None
    department: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None