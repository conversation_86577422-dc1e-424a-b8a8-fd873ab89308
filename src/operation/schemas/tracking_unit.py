from optparse import Option
from sqlalchemy.dialects.postgresql import UUID
from pydantic import BaseModel, EmailStr, Field, validator, SecretStr
from typing import Optional, Literal, List
from datetime import datetime, timedelta
from enum import Enum

from ..utils.utils import (
    DEFAULT_DATE_NO_TIME_ZONE, get_current_date_time_utc_7,
    parse_adn_result_on_s3_bucket
)




class UnitBase(BaseModel):
    # id
    tenDonVi: str
    gs_area: str
    gs_area_code: int
    pass

class UnitBaseDetail(UnitBase):
    id: int

class AddUnit(UnitBase):
    pass

class UpdateUnitDetail(BaseModel):
    tenDonVi: Optional[str]
    gs_area: Optional[str]
    gs_area_code: Optional[int]

class LinkUnitPhoneNumber(BaseModel):
    gs_phone_number: str
    password: SecretStr

class UnLinkUnitPhoneNumber(BaseModel):
    gs_phone_number: str