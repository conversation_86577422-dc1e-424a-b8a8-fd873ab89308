from pydantic import BaseModel
from typing import Optional, Literal, List
from datetime import datetime

class AddSampleMapping(BaseModel):
    # id: int # 1 ++1
    chip_id: Optional[str] = None # PCR -- 23022801  # MICROARRAY -- 206425620033
    position: Optional[str] = None # PCR -- A1 # MICROARRAY -- R01C01
    lid: str = None # PCR -- 230010 # 
    plate_id: str = None
    well_position: str = None


class AddSampleMappingWNameReq(BaseModel):
    # id: int # 1 ++1
    chip_id: Optional[str] = None # PCR -- 23022801  # MICROARRAY -- 206425620033
    position: Optional[str] = None # PCR -- A1 # MICROARRAY -- R01C01
    dna_extraction_id: int = None # PCR -- 230010 # 
    plate_name: str = None
    well_position: str = None

class AddPCSampleMappingWNameReq(BaseModel):
    # id: int # 1 ++1
    lid: Optional[str] = None # used to check positive_control
    chip_id: Optional[str] = None # PCR -- 23022801  # MICROARRAY -- 206425620033
    position: Optional[str] = None # PCR -- A1 # MICROARRAY -- R01C01
    dna_extraction_id: int = None # PCR -- 230010 # 
    plate_name: str = None
    well_position: str = None

class AddToChip(BaseModel):
    plate_name: str = None

class FillChipWSamples(BaseModel):
    plate_name: str = None
    chip_ids: list
    type: str