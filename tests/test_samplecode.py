def test_create_samplecode(client, access_token_patcher, mock_shop_get_requests):
    """
    When an user attempts to create a code with correct request body,
    200 Ok response should be returned to the user
    """
    fake_jwt = "1.2.3"

    access_token_patcher()
    mock_shop_get_requests()
    params = {"size": 5}
    res = client.post(
        "/samplecodes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert len(res.json().get("data")) == 5
