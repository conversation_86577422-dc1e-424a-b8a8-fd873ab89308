# POST MANY
## EMPTY REQUEST BODY 
def test_lims_dna_extraction_create_many_entries_wo_request_body(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data = []
    res = client.post(
        "/lims/dna_extractions", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 400, res.text
    err_response = res.json()
    assert err_response['detail'][0]['msg'] == f"Empty Request Body."

def test_lims_dna_extraction_create_many_entries(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # # 0.0 

    # 1.0 LAB_SAMPLE CREATE

    # 2.1 DNA_EXTRACTION
    data = [
      {
          "lid":"230002",
          "dna_extraction_date":"2023-01-20",
          "qubit": 26.4,
          "nano_drop": 33.9,
          "a260_a280": 1.89,
          "agarose_gel": "PASS",
          "dna_qc_status": "PASS"
      },
      {
          "lid":"230003",
          "dna_extraction_date":"2023-02-20",
          "qubit": 26.4,
          "nano_drop": 33.9,
          "a260_a280": 1.89,
          "agarose_gel": "PASS",
          "dna_qc_status": "PASS"
      }
      ]
    res = client.post(
        "/lims/dna_extractions", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    # res = client.get(
    #     "/lims/dna_extractions", headers={"Authorization": f"bearer {fake_jwt}"}
    # )

    assert res.status_code == 200, res.text
    assert len(res.json()[0]['data']) == 2

    


# {}

## MISSING REQUIRED FIELDS IN ONE OR MORE ENTRIES
def test_lims_dna_extraction_create_many_entries_wo_required_fields_in_one_entry(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data = [
      {
          "lid":"230002",
          "dna_extraction_date":"2023-01-20",
          "qubit": 26.4,
          "nano_drop": 33.9,
          "agarose_gel": "PASS",
          "dna_qc_status": "PASS"
      },
      {
          "lid":"230002",
          "dna_extraction_date":"2023-01-20",
          "agarose_gel": "PASS",
          "dna_qc_status": "PASS"
      },
      {
          "dna_extraction_date":"2023-01-20",
          "qubit": 26.4,
          "nano_drop": 33.9,
          "a260_a280": 1.89,
          "agarose_gel": "PASS",
          "dna_qc_status": "PASS"
      },
      {
          "lid":"2300037",
          "dna_extraction_date":"2023-02-20",
          "qubit": 26.4,
          "nano_drop": 33.9,
          "a260_a280": 1.89,
          "agarose_gel": "PASS",
          "dna_qc_status": "PASS"
      },
      {
          "lid":"2300038",
          "dna_extraction_date":"2023-02-20",
          "qubit": 26.4,
          "nano_drop": 33.9,
          "a260_a280": 1.89,
          "agarose_gel": "PASS",
          "dna_qc_status": "PASS"
      }
      ]
    res = client.post(
        "/lims/dna_extractions", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 422, res.text
    err_response = res.json()
    # Missing a260_a280 field
    assert err_response['detail'][0]['msg'] == f"Missing required fields / Lab Sample ID NOT FOUND in lab_sample"
    assert err_response['detail'][0]['data'][0].get('a260_a280') == None
    assert err_response['detail'][0]['data'][0].get('is_valid') == False
    assert err_response['detail'][0]['data'][1].get('qubit') == None
    assert err_response['detail'][0]['data'][1].get('nano_drop') == None
    assert err_response['detail'][0]['data'][1].get('a260_a280') == None
    assert err_response['detail'][0]['data'][1].get('is_valid') == False
    assert err_response['detail'][0]['data'][2].get('lid') == None
    assert err_response['detail'][0]['data'][2].get('is_valid') == False
    assert err_response['detail'][0]['data'][3].get('lid_not_found') == True
    assert err_response['detail'][0]['data'][3].get('is_valid') == False
    assert err_response['detail'][0]['data'][4].get('lid_not_found') == True
    assert err_response['detail'][0]['data'][4].get('is_valid') == False

# POST SINGLE ENTRY
def test_lims_dna_extraction_create_one_entry_w_missing_required_field(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data = {
        "lid":"230003",
        "dna_extraction_date":"2023-01-20",
        "qubit": 26.4,
        "nano_drop": 33.9,
        # "a260_a280": 1.89,,
        "agarose_gel": "PASS",
        "dna_qc_status": "PASS"
    }

    res = client.post(
        "/lims/dna_extraction", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 422, res.text
    err_response = res.json()
    assert err_response['detail'][0]['msg'] == f"field required"
    assert err_response['detail'][0]['loc'][1] == "a260_a280"


def test_lims_dna_extraction_create_one_entry(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data = {
        "lid":"230003",
        "dna_extraction_date":"2023-01-20",
        "qubit": 26.4,
        "nano_drop": 33.9,
        "a260_a280": 1.89,
        "agarose_gel": "PASS",
        "dna_qc_status": "PASS"
    }

    res = client.post(
        "/lims/dna_extraction", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['lid'] == data['lid']
    assert res.json()[0]['data']['dna_extraction_date'] == data['dna_extraction_date']+"T00:00:00"
    assert res.json()[0]['data']['qubit'] == data['qubit']
    assert res.json()[0]['data']['nano_drop'] == data['nano_drop']
    assert res.json()[0]['data']['a260_a280'] == data['a260_a280']
    assert res.json()[0]['data']['created_at'] != ""
    assert res.json()[0]['data']['updated_at'] != ""
    assert res.json()[0]['data']['deleted_at'] == None

# GET MANY

def test_lims_dna_extraction_get_many_wo_filter(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    params = {}

    res = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    err_response = res.json()
    # Missing a260_a280 field
    assert len(res.json()['data']) == 0

    test_lims_dna_extraction_create_many_entries(client,access_token_patcher)

    params = {}

    res = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    err_response = res.json()
    # Missing a260_a280 field
    assert len(res.json()['data']) == 2


def test_lims_dna_extraction_get_many_w_filter(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    test_lims_dna_extraction_create_many_entries(client, access_token_patcher)

    params = {
        "lid": "230002"
    }

    res = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    err_response = res.json()
    # Missing a260_a280 field
    assert len(res.json()['data']) == 1
    assert res.json()['data'][0]['lid'] == "230002"
    assert res.json()['data'][0]['qubit'] == 26.4
    assert res.json()['data'][0]['nano_drop'] == 33.9
    assert res.json()['data'][0]['a260_a280'] == 1.89
    assert res.json()['data'][0]['dna_extraction_date'] == "2023-01-20T00:00:00"

    params1 = {
        'start_dna_extraction_date': '2023-01-02',
        'end_dna_extraction_date': '2023-02-02'
    }

    res = client.get(
        "/lims/dna_extractions", params=params1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    err_response = res.json()
    assert len(res.json()['data']) == 1
    assert res.json()['data'][0]['lid'] == "230002"

    params2 = {
        'start_dna_extraction_date': '2023-02-02',
        'end_dna_extraction_date': '2023-03-02'
    }

    res = client.get(
        "/lims/dna_extractions", params=params2, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    err_response = res.json()
    # Missing a260_a280 field
    assert len(res.json()['data']) == 1
    assert res.json()['data'][0]['lid'] == "230003"

    data = {
        "lid":"230004",
        "dna_extraction_date":"2023-01-20",
        "qubit": 26.4,
        "nano_drop": 33.9,
        "a260_a280": 1.89,
        "agarose_gel": "PASS",
        "dna_qc_status": "PASS",
        "note": "Chưa chạy do mẫu miễn phí"
    }

    res = client.post(
        "/lims/dna_extraction", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    params4 = {
        "note":"miễn phí"
    }

    res = client.get(
        "/lims/dna_extractions", params=params4, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    err_response = res.json()
    # Missing a260_a280 field
    assert len(res.json()['data']) == 1
    assert res.json()['data'][0]['lid'] == "230004"
  
def test_lims_dna_extraction_get_detail(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    test_lims_dna_extraction_create_many_entries(client,access_token_patcher)

    params = {
    }

    res_all = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_all.status_code == 200, res_all.text
    assert len(res_all.json()['data']) == 2
  
    id = res_all.json()['data'][0]['id']

    res = client.get(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()['data']['lid'] == res_all.json()['data'][0]['lid']
    assert res.json()['data']['dna_extraction_date'] == res_all.json()['data'][0]['dna_extraction_date']
    assert res.json()['data']['qubit'] == res_all.json()['data'][0]['qubit']
    assert res.json()['data']['nano_drop'] == res_all.json()['data'][0]['nano_drop']
    assert res.json()['data']['a260_a280'] == res_all.json()['data'][0]['a260_a280']


def test_lims_dna_extraction_put_detail(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    test_lims_dna_extraction_create_many_entries(client,access_token_patcher)

    params = {
    }

    res_all = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_all.status_code == 200, res_all.text
    assert len(res_all.json()['data']) == 2
  
    id = res_all.json()['data'][0]['id']

    res = client.get(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()['data']['lid'] == res_all.json()['data'][0]['lid']
    assert res.json()['data']['dna_extraction_date'] == res_all.json()['data'][0]['dna_extraction_date']
    assert res.json()['data']['qubit'] == res_all.json()['data'][0]['qubit']
    assert res.json()['data']['nano_drop'] == res_all.json()['data'][0]['nano_drop']
    assert res.json()['data']['a260_a280'] == res_all.json()['data'][0]['a260_a280']
    assert res.json()['data']['agarose_gel'] == res_all.json()['data'][0]['agarose_gel']
    assert res.json()['data']['dna_qc_status'] == res_all.json()['data'][0]['dna_qc_status']

    update_data = {
        "dna_extraction_date": "2023-01-22",
        "qubit": 24.4,
        "nano_drop": 33.1,
        "a260_a280": 1.8,
        "note": "Note Edited"
    }

    res_put = client.put(
        f"/lims/dna_extraction/{id}", json=update_data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_put.status_code == 200, res_put.text

    res_get = client.get(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_get.status_code == 200, res_get.text
    assert res_get.json()['data']['dna_extraction_date'] == update_data['dna_extraction_date']+'T00:00:00'
    assert res_get.json()['data']['qubit'] == update_data['qubit']
    assert res_get.json()['data']['nano_drop'] == update_data['nano_drop']
    assert res_get.json()['data']['a260_a280'] == update_data['a260_a280']
    assert res_get.json()['data']['note'] == update_data['note']

def test_lims_dna_extraction_put_detail_selected_fields(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    test_lims_dna_extraction_create_many_entries(client,access_token_patcher)

    params = {
    }

    res_all = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_all.status_code == 200, res_all.text
    assert len(res_all.json()['data']) == 2
  
    id = res_all.json()['data'][0]['id']

    res = client.get(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()['data']['lid'] == res_all.json()['data'][0]['lid']
    assert res.json()['data']['dna_extraction_date'] == res_all.json()['data'][0]['dna_extraction_date']
    assert res.json()['data']['qubit'] == res_all.json()['data'][0]['qubit']
    assert res.json()['data']['nano_drop'] == res_all.json()['data'][0]['nano_drop']
    assert res.json()['data']['a260_a280'] == res_all.json()['data'][0]['a260_a280']
    assert res.json()['data']['agarose_gel'] == res_all.json()['data'][0]['agarose_gel']
    assert res.json()['data']['dna_qc_status'] == res_all.json()['data'][0]['dna_qc_status']

    update_data = {
        "dna_extraction_date": "2023-01-22",
        "note": "Note Edited",
    }

    res_put = client.put(
        f"/lims/dna_extraction/{id}", json=update_data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_put.status_code == 200, res_put.text

    res_get = client.get(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_get.status_code == 200, res_get.text
    assert res_get.json()['data']['dna_extraction_date'] == update_data['dna_extraction_date']+'T00:00:00'
    assert res_get.json()['data']['qubit'] == res_all.json()['data'][0]['qubit']
    assert res_get.json()['data']['nano_drop'] == res_all.json()['data'][0]['nano_drop']
    assert res_get.json()['data']['a260_a280'] == res_all.json()['data'][0]['a260_a280']
    assert res_get.json()['data']['agarose_gel'] == res_all.json()['data'][0]['agarose_gel']
    assert res_get.json()['data']['dna_qc_status'] == res_all.json()['data'][0]['dna_qc_status']



def test_lims_dna_extraction_put_detail_errors(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    test_lims_dna_extraction_create_many_entries(client,access_token_patcher)

    params = {
    }

    res_all = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_all.status_code == 200, res_all.text
    assert len(res_all.json()['data']) == 2
  
    id = res_all.json()['data'][0]['id']

    res = client.get(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()['data']['lid'] == res_all.json()['data'][0]['lid']
    assert res.json()['data']['dna_extraction_date'] == res_all.json()['data'][0]['dna_extraction_date']
    assert res.json()['data']['qubit'] == res_all.json()['data'][0]['qubit']
    assert res.json()['data']['nano_drop'] == res_all.json()['data'][0]['nano_drop']
    assert res.json()['data']['a260_a280'] == res_all.json()['data'][0]['a260_a280']
    assert res.json()['data']['agarose_gel'] == res_all.json()['data'][0]['agarose_gel']
    assert res.json()['data']['dna_qc_status'] == res_all.json()['data'][0]['dna_qc_status']

    update_data = {
        "dna_extraction_date": "2023-01-22",
        # "qubit": "260.4", #float
        "qubit": "abc", # string not float
    }

    res_put = client.put(
        f"/lims/dna_extraction/{id}", json=update_data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_put.status_code == 422, res_put.text
    # assert res_put.json()['data']['qubit'] == 260.4



def test_lims_dna_extraction_delete_row(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    test_lims_dna_extraction_create_many_entries(client,access_token_patcher)

    params = {
    }

    res_all = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_all.status_code == 200, res_all.text
    assert len(res_all.json()['data']) == 2
  
    id = res_all.json()['data'][0]['id']

    res = client.get(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
  
    id = res_all.json()['data'][0]['id']

    res = client.delete(
        f"/lims/dna_extraction/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text

    res_all = client.get(
        "/lims/dna_extractions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_all.status_code == 200, res_all.text
    assert len(res_all.json()['data']) == 1
    
    