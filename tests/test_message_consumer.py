import pytest

from botocore.exceptions import ClientError

from operation import consumer as message_processing
from operation import producer


@pytest.mark.asyncio
@pytest.mark.parametrize("send_count,receive_count,wait_time", [
    (5, 3, 5), (2, 10, 0), (1, 1, 1), (0, 5, 0)
])
async def test_receive_messages(make_stubber, make_queue, send_count, receive_count, wait_time):
    """Test that receiving various numbers of messages returns the expected
    number of messages."""
    sqs_stubber = make_stubber(message_processing.sqs.meta.client)
    queue = make_queue(sqs_stubber, message_processing.sqs)

    sent_messages = [{
        'body': '{ "kit_id": "425958003819", "status": "COMPLETED" }',
        'attributes': {}
    } for _ in range(0, send_count)]
    if send_count > 0:
        sqs_stubber.stub_send_message_batch(queue.url, sent_messages)
        producer.send_messages(queue, sent_messages)

    sqs_stubber.stub_receive_messages(queue.url, sent_messages, receive_count)

    received_messages = await message_processing.receive_messages(
        queue, receive_count, wait_time)

    if send_count > 0:
        assert received_messages
        assert len(received_messages) <= receive_count
    else:
        assert not received_messages


@pytest.mark.asyncio
@pytest.mark.parametrize("receive_count", [0, 20])
async def test_receive_messages_bad_params(make_stubber, make_queue, receive_count):
    """Test that trying to receive a number of messages that is too large or too small
    raises an exception."""
    sqs_stubber = make_stubber(message_processing.sqs.meta.client)
    queue = make_queue(sqs_stubber, message_processing.sqs)

    sqs_stubber.stub_receive_messages(
        queue.url, [], receive_count, 'InvalidParameterValue')

    with pytest.raises(ClientError):
        await message_processing.receive_messages(queue, receive_count, 1)


@pytest.mark.asyncio
@pytest.mark.parametrize("message_count", [1, 5, 10])
async def test_delete_messages(make_stubber, make_queue, message_count):
    """Test that deleting a single message or a batch of messages returns
    the expected success response."""
    sqs_stubber = make_stubber(message_processing.sqs.meta.client)
    queue = make_queue(sqs_stubber, message_processing.sqs)

    body = '{ "kit_id": "425958003819", "status": "COMPLETED" }'
    wait_time = 1

    messages = [{'body': body, 'attributes': {}}] * message_count

    sqs_stubber.stub_send_message_batch(queue.url, messages)
    sqs_stubber.stub_receive_messages(queue.url, messages, message_count)

    producer.send_messages(queue, messages)
    messages = await message_processing.receive_messages(queue, message_count, wait_time)

    if message_count == 1:
        sqs_stubber.stub_delete_message(queue.url, messages[0])
        messages[0].delete()
    else:
        sqs_stubber.stub_delete_message_batch(queue.url, messages, len(messages), 0)
        message_processing.delete_messages(queue, messages)


@pytest.mark.asyncio
async def test_delete_message_not_exist(make_stubber, make_queue):
    """Test that deleting a message that doesn't exist raises an exception."""
    sqs_stubber = make_stubber(message_processing.sqs.meta.client)
    queue = make_queue(sqs_stubber, message_processing.sqs)
    message = queue.Message(receipt_handle='fake-handle')

    sqs_stubber.stub_delete_message(queue.url, message,
                                    error_code='ReceiptHandleIsInvalid')

    with pytest.raises(ClientError) as exc_info:
        message.delete()
    assert exc_info.value.response['Error']['Code'] == 'ReceiptHandleIsInvalid'


@pytest.mark.asyncio
async def test_delete_messages_not_exist(make_stubber, make_queue):
    """Test that deleting a batch of messages that don't exist succeeds
    and returns the expected list of failed messages."""
    sqs_stubber = make_stubber(message_processing.sqs.meta.client)
    queue = make_queue(sqs_stubber, message_processing.sqs)
    messages = [
        queue.Message(receipt_handle=f'fake-handle-{ind}')
        for ind in range(0, 5)
    ]

    sqs_stubber.stub_delete_message_batch(queue.url, messages, 0, len(messages))

    response = message_processing.delete_messages(queue, messages)

    assert len(response['Failed']) == len(messages)
    assert all([failed['Code'] == 'ReceiptHandleIsInvalid'
                for failed in response['Failed']])
