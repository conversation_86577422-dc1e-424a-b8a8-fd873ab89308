# def test_register_kit(client, )
import json
def test_get_kit_list(client, access_token_patcher, get_kit_list_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    get_kit_list_patcher()
    start_date='2022-09-01'
    current_status='REGISTERED'
    page_size=10
    page_number=1
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data['data']) == 10
    
    page_size=3
    page_number=1
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data["data"]) == 3
    assert int(res_data["pagination"]["total"]) == 12
    assert int(res_data["pagination"]["page_size"]) == 3
    
    page_size=3
    page_number=2
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data["data"]) == 3
    assert int(res_data["pagination"]["total"]) == 12
    assert int(res_data["pagination"]["page_size"]) == 3
    
    page_size=3
    page_number=5
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data["data"]) == 0
    assert int(res_data["pagination"]["total"]) == 12
    assert int(res_data["pagination"]["page_size"]) == 3
    
    
    page_size=3
    page_number=1
    barcode='151927530708'
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}&barcode={barcode}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data["data"]) == 1
    assert int(res_data["pagination"]["total"]) == 1
    assert int(res_data["pagination"]["page_size"]) == 3
    
    
def test_register_kit_v2(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    barcode = '************'
    data = {
        "barcode": barcode,
        "nickname": "Dev Testing",
        "email": "<EMAIL>",
        "phone_number": "**********",
        "name": "Ha Pham TV",
        "gender": "male",
        "dob": "1993-09-01",
        "address": "Trieu Khuc, Thanh Xuan, Ha Noi",
        "validate_account": True,
        "version": "1",
        "product_code": "89",
        "product_type": "B2C",
        "sample_collection_date": "2022-01-01",
        "sample_collection_time": 2,
        "sample_receipt_date": "2022-02-01",
        "expected_report_release_date": "2024-02-01",
        "source_id": None,
        "note": "this is a test kit",
        "diagnosis": "no diag",
        "account_id": "129415b5-aa38-41d4-b346-cff7af9090ce",
        "nominator_id": "c6190921-daab-480d-9c10-d2d18ae78c4d",
        "customer_support_id": "f675d7f3-2bb6-4e0d-a6fe-fff6ae295325",
        "free_of_charge": False,
        "promotion": "no",
        "is_priority": False
    }
    res = client.get(f"/kits/registrations/v2",
                     json=data,
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    
def test_check_kit_list_by_sample_meta(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    name='Ha Pham D'
    gender='male'
    dob='1995-09-01'
    res = client.get(f"/kits/check_by_sample?name={name}&gender={gender}&dob={dob}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data["data"]) == 9
    
    name='Ha Pham D'
    gender='male'
    dob='1995-09-01'
    product_code='88'
    res = client.get(f"/kits/check_by_sample?name={name}&gender={gender}&dob={dob}&product_code={product_code}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data["data"]) == 1
    
def test_delete_kit(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    barcode = '************'
    res = client.delete(f"/kits/registrations/{barcode}",
                        headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert res_data['data']['deleted_at'] != None
    
def test_update_kit_info(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    barcode = '************'
    updated_address = 'Trieu Khuc, Thanh Xuan Nam, Thanh Xuan, Ha Noi'
    data = {
        "barcode": barcode,
        "nickname": "CS PORTAL",
        "email": "<EMAIL>",
        "phone_number": "**********",
        "name": "Ha Pham Dinh Gene",
        "gender": "male",
        "dob": "1995-09-02",
        "address": updated_address,
        "validate_account": False,
        "sample_meta_id": "550216d2-bcbf-481b-a111-87a7466ace45",
        "product_code": "88",
        "product_type": "CLINIC",
        "sample_collection_date": "2022-01-02",
        "sample_collection_time": 1,
        "sample_receipt_date": "2022-02-01",
        "expected_report_release_date": "2025-09-09",
        "source_id": "04334f87-8be9-4889-8d36-ca409dffb627",
        "note": "this is a test kit from dev team",
        "diagnosis": "you are so strong",
        "account_id": "129415b5-aa38-41d4-b346-cff7af9090ce",
        "nominator": "c6190921-daab-480d-9c10-d2d18ae78c4d",
        "customer_support_id": "f675d7f3-2bb6-4e0d-a6fe-fff6ae295325",
        "free_of_charge": False,
        "promotion": "there's no promotion",
        "is_priority": False
    }
    res = client.put(f"/kits/registrations/{barcode}",
                    json=data,
                    headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert res_data['data']['address'] == updated_address
    
def test_get_kit_detail(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    barcode = '************'
    res = client.get(f"/kits/registrations/{barcode}",
                    headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    res_data = json.loads(res.text)
    assert len(res_data['data']) == 1
    assert res_data['data'][0]['barcode'] == barcode
