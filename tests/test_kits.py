# def test_register_kit(client, )

def test_get_kit_list(client, access_token_patcher, get_kit_list_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    get_kit_list_patcher()
    start_date='2022-09-01'
    current_status='REGISTERED'
    page_size=10
    page_number=1
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    assert len(res.text["data"]) == 2
    
    page_size=1
    page_number=1
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    assert len(res.text["data"]) == 1
    assert int(res.text["pagination"]["total"]) == 2
    assert int(res.text["pagination"]["page_size"]) == 1
    
    page_size=1
    page_number=2
    res = client.get(f"/kits/all?page_size={page_size}&page_number={page_number}&start_date={start_date}&current_status={current_status}",
                     headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 200
    assert len(res.text["data"]) == 1
    assert int(res.text["pagination"]["total"]) == 2
    assert int(res.text["pagination"]["page_size"]) == 1