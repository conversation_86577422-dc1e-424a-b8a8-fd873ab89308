def test_analyze_samples(client, access_token_patcher, mock_trigger_pipeline_post_requests):
    """
    When operator trigger pipeline for a correct batch,
    200 Ok response should be returned
    """
    fake_jwt = "1.2.3"

    access_token_patcher()

    params = {
        "batch": 1,
    }

    res = client.get(
        "/samples/analysis", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text


def test_samples_kit_info(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"

    access_token_patcher()
    data1 = ["123456789901", "123456789902", "123456789903", "123456789904"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text

    # Register kits
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    for code in data1:
        data2 = {
            "barcode": code,
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van A",
            "version": 0
        }
        res2 = client.post(
            "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res2.status_code == 200, res2.text

    # Register samples
    for code in data1:
        sample_body = {
            "barcode": code,
            "batch_barcode": 1,
            "vinmec_id": "string",
            "chip_id": "string",
            "chip_type": "ASA",
            "qc_status": "PASSED"
        }
        res3 = client.post("/samples", json=sample_body, headers={"Authorization": f"bearer {fake_jwt}"})

        assert res3.status_code == 200, res3.text

    # Get sample kit info
    params = {
        "batch_barcode": "1",
        "page_number": 1,
        "page_size": 96
    }
    res4 = client.get("/samples/kit_info", params=params, headers={"Authorization": f"bearer {fake_jwt}"})
    assert res4.status_code == 200, res4.text
    assert res4.json()['pagination']['total'] == 4

    params2 = {
        "id": "123456789904"
    }

    # Delete a kit
    res5 = client.delete(f"/kits/registrations/{params2['id']}", params=params2,
                         headers={"Authorization": f"bearer {fake_jwt}"})
    assert res5.status_code == 200, res5.text

    # Get samples/kit_info after delete a kit
    res6 = client.get("/samples/kit_info", params=params, headers={"Authorization": f"bearer {fake_jwt}"})
    assert res6.status_code == 200, res6.text
    assert res6.json()['pagination']['total'] == 3


def test_samples_status(client, access_token_patcher):
    fake_jwt = "1.2.3"

    access_token_patcher()
    data = {
        'barcode': '123456789901',
        'status': 'RECEIVED_KIT_STATUS',
        'note': ''
    }
    res = client.post(
        "/samples/status", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text


def test_samples_status_missing_data(client, access_token_patcher):
    fake_jwt = "1.2.3"

    access_token_patcher()
    data = {
        'status': 'RECEIVED_KIT_STATUS',
        'note': ''
    }
    res = client.post(
        "/samples/status", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 422, res.text

    data = {
        'barcode': '123456789901',
        'note': ''
    }
    res = client.post(
        "/samples/status", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 422, res.text

    data = {
        'barcode': '123456789901',
        'status': 'RECEIVED_KIT_STATUS'
    }
    res = client.post(
        "/samples/status", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
