from datetime import datetime

from src.operation.utils import utils


def test_convert_str_to_iso_datetime():
    datetime_str = "2022-04-25T00:00:00.000000Z"
    expected_output = datetime(2022, 4, 25)
    actual_output = utils.convert_str_to_iso_datetime(datetime_str)
    assert expected_output == actual_output[0]


def test_convert_datetime_to_iso():
    date_input = datetime(2022, 4, 25)
    expected_output = datetime(2022, 4, 25, 0, 0)
    actual_output = utils.convert_datetime_to_iso(date_input)
    assert expected_output == actual_output[0]


def test_success_response_w_pagination():
    pagination = {
        "page_size": 10,
        "page_number": 1,
        "total": 10,
    }
    body = ""
    expected_output = {
        "data": body,
        "pagination": {
            "page_size": pagination["page_size"],
            "page_number": pagination["page_number"],
            "total": pagination["total"],
        },
        "detail": [],
    }
    actual_output = utils.success_response_w_pagination(body, pagination)
    assert expected_output == actual_output


def test_success_response():
    body = ""
    expected_output = {"data": body, "detail": []}
    actual_output = utils.success_response(body)
    assert expected_output == actual_output


def test_failure_response():
    err = ""
    data = ""
    expected_output = [
        {
            "msg": str(err),
            "data": data,
        }
    ]
    actual_output = utils.failure_response(err, data)
    assert expected_output == actual_output


def test_failure_response_kit_registration():
    err = ""
    remain_attempts = 1
    expected_output = [{"msg": str(err), "remain_attempts": int(remain_attempts)}]
    actual_output = utils.failure_response_kit_registration(err, remain_attempts)
    assert expected_output == actual_output


def test_validate_email():
    email = "<EMAIL>"
    actual_output = utils.validate_email(email)
    assert actual_output


def test_random_unique_string():
    product_code = 11
    code_len = 2
    barcode = utils.gen_random_unique_string(product_code)
    assert len(barcode) == 10 + len(str(product_code))

    barcode = utils.gen_random_unique_string(product_code, length=code_len)
    assert len(barcode) == code_len


def test_ensure_trailing_slash():
    url1 = "http://abc/"
    url2 = "http://abc"
    assert utils.ensure_trailing_slash(url1) == utils.ensure_trailing_slash(url2)


def test_success_response_w_pagination():
    body = "fake_body"
    pagination = {"page_size": 10, "page_number": 1, "total": 2}
    res = utils.success_response_w_pagination(body, pagination)
    expected_res = {
        "data": body,
        "pagination": {
            "page_size": pagination.get("page_size"),
            "page_number": pagination.get("page_number"),
            "total": pagination.get("total"),
        },
        "detail": [],
    }
    assert res == expected_res

    pagination = None
    res = utils.success_response_w_pagination(body, pagination)
    expected_res = {"data": body, "detail": []}
    assert res == expected_res


def test_failure_response_kit_registration():
    err = "fake_err"
    remain_attempts = 3
    res = utils.failure_response_kit_registration(err, remain_attempts=remain_attempts)
    expected_res = [{"msg": err, "remain_attempts": remain_attempts}]
    assert res == expected_res

    remain_attempts = None
    res = utils.failure_response_kit_registration(err, remain_attempts=remain_attempts)
    expected_res = [
        {
            "msg": err,
        }
    ]
    assert res == expected_res


def test_convert_str_to_datetime():
    correct_date_str = "2022-07-21T00:41:23.179364+00:00"
    wrong_date_str = "2022-07-21T00:41:23.179364Z"
    res, err = utils.convert_str_to_datetime(correct_date_str)
    assert err is None
    res, err = utils.convert_str_to_datetime(wrong_date_str)
    assert err is not None


def test_convert_datetime_to_iso():
    date_obj = datetime.now()
    date_str = "2022-07-21T00:41:23.179364+00:00"
    res, err = utils.convert_datetime_to_iso(date_obj)
    assert err is None
    res, err = utils.convert_datetime_to_iso(date_str)
    assert err is not None


def test_convert_datetime_to_str():
    date_obj = datetime.now()
    date_str = "2022-07-21T00:41:23.179364+00:00"
    res, err = utils.convert_datetime_to_str(date_obj)
    assert err is None
    res, err = utils.convert_datetime_to_str(date_str)
    assert err is not None


def test_convert_current_utc_to_tz():
    tz = "Asia/Ho_Chi_Minh"
    res = utils.convert_current_utc_to_tz(tz)
    assert res
    res = utils.convert_current_utc_to_tz()
    assert res


def test_extract_current_date_from_time():
    tz = "Asia/Ho_Chi_Minh"
    res = utils.extract_current_date_from_time()
    assert isinstance(res, str)
    assert len(res) == 10
    res = utils.extract_current_date_from_time(tz)
    assert isinstance(res, str)
    assert len(res) == 10


def test_get_time_range_days_ago():
    start_date, end_date = utils.get_time_range_days_ago()
    assert start_date
    assert end_date
