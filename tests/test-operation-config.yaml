####################
# SERVER           #
####################

DEBUG: true
TEST_KEEP_DB: false
DOCS_URL_PREFIX:

####################
# DATABASE         #
####################

DB_DRIVER: postgresql
DB_HOST: gt-qa-rds.cntsuccfkujx.ap-southeast-1.rds.amazonaws.com
DB_PORT: 5432
DB_USER: operation_user
DB_PASSWORD: operation_pass
DB_DATABASE: operation_v6_qa_rollback_5

DB_POOL_MIN_SIZE: 1
DB_POOL_MAX_SIZE: 16
DB_ECHO: False
DB_SSL:
DB_USE_CONNECTION_FOR_REQUEST: true
DB_RETRY_LIMIT: 1
DB_RETRY_INTERVAL: 1

REGISTER_URL: https://qa.genestory.ai/register-kit/


ALLOWED_KIT_STATUS:
  - REGISTERED
  - RECEIVED
  - QC
  - SEQUENCING
  - COMPLETED
  - DISABLED
  - LAB_CHECK
  - PASSED_LAB_CHECK
  - FAILED_LAB_CHECK
  - MISSING_SAMPLE
  - MISSING_INFO
  - ON_CHIP
  - DELETED

REGISTERED_KIT_STATUS: REGISTERED
RECEIVED_KIT_STATUS: RECEIVED
QC_KIT_STATUS: QC
SEQUENCING_KIT_STATUS: SEQUENCING
COMPLETED_KIT_STATUS: COMPLETED
DISABLED_KIT_STATUS: DISABLED

ALLOWED_CODE_STATE:
  - AVAILABLE
  - USED
  - KILLED
  - STORED

DEFAULT_CODE_STATE: AVAILABLE
USED_CODE_STATE: USED
PRINTED_CODE_STATE: PRINTED
DEACTIVATED_CODE_STATE: KILLED

ALLOWED_SAMPLE_STATUS:
  - UNKNOWN
  - PASSED
  - FAILED

KIT_REPORT_GROUPS_URL: "https://internal-qa.genestory.ai/report-generator/report/kit/status"

MAXIMUM_REQUEST_LENGTH: 100
MAXIMUM_NUM_SAMPLES: 96

MAX_ATTEMPTS: 10

ALLOWED_CHIP_TYPES:
  - GSAV3
  - ASA

SQS_CFG:
  AWS_REGION: ap-southeast-1
  SQSCONSUMER_QUEUENAME: myqueue
  MAX_NUMBER_OF_MESSAGES: 1
  WAIT_TIME_SECONDS: 1
  SQS_CHECK_DURATION: 1

AWS:
  AWS_ACCESS_KEY_ID: test
  AWS_SECRET_ACCESS_KEY: test
  ENDPOINT_URL: "http://localhost:4566" # Only use this config in local development environment
  CELERY_REGION_NAME: "us-east-1"
  NOTIFICATION_REGION_NAME: "ap-southeast-1"
  CONSUMER_REGION_NAME: "ap-southeast-1"

ENV: DEV

SHOP_URL:
  LOCAL: "https://internal-qa.genestory.ai/shop"
  DEV: "http://shop"
  QA: "http://shop"
  PROD: "http://shop"

AUTHEN_URL:
  LOCAL: "http://internal-dev.genestory.ai/gt-authen"
  DEV: "http://gt-authen"
  QA: "http://gt-authen"
  PROD: "http://gt-authen"
  VALIDATE_USER: /internal/users/uuid/validate

PIPELINE_URL: https://e1sfgg2di0-vpce-0a928dbfce9b8733d.execute-api.ap-southeast-1.amazonaws.com/dev/vgtPipeline

AUTH:
  ISSUER:
    DEV: "https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_QqDTrX7vO/.well-known/jwks.json"

MAIL:
  TEMPLATE_NAME_AS_SUBJECT: true
  TEMPLATES:
    CHANGE_EMAIL_KIT_TEMPLATE_ID: 103 # 103 on QA, 11 on PROD
    NO_DATA_KITS: 195
  CONNECTION:
    INTERNAL_ADDRESS: "http://mailservice/internal"  
  USERS:
    SENDER_ID: 1

REPORT_GENERATOR:
  QUEUE_NAME: gx-report-generator-q-qa
  BATCH_INDEX_RESOURCES: BATCH_INDEX_RESOURCES

CELERY:
  BROKER_URL: "redis://127.0.0.1:6379/0"
  BACKEND_URL: "redis://127.0.0.1:6379/0"
  BROKER_TYPE: "sqs"
  TIMEZONE: 'Asia/Ho_Chi_Minh'
  NO_DATA_NOTIFICATION_STATUS: 'COMPLETED'
  NO_DATA_DAYS_CHECK: 7
  QUEUE_NAME: "celery-queue-testing"
  SCHEDULE:
    HOUR: 16
    MINUTE: 47

