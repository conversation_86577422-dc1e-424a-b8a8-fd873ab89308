# ACCOUNT
# CREATE ACCOUNT: 

"""
(MOCK -> DEFAULT ->) GENESTORY -> SALE_PIC -> CLINIC -> DOCTOR
(without_pic_id)

"""
def test_admin_create_an_account_wo_pic_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
        "name": "BV Vinmec 10",
        "address": "1 <PERSON><PERSON>,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC"
        },
        {
        "name": "BV Vinmec 20",
        "address": "2 <PERSON><PERSON> <PERSON>,",
        "description": "Testing 2",
        "area": "Hà Nội",
        "type": "CLINIC"
        },
        {
        "name": "BV Vinmec 30",
        "address": "3 <PERSON><PERSON> <PERSON>,",
        "description": "Testing 3",
        "area": "Hà Nội",
        "type": "CLINIC"
        }
    ]

    pic_id = None
    for d in data:
        res = client.post(
            "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['address'] == d['address']
        assert res.json()[0]['data']['description'] == d['description']
        assert res.json()[0]['data']['area'] == d['area']

        pic_id = res.json()[0]['data']['pic_id']

        res = client.get(
            f"/admin/partner/staff/{pic_id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.json()['data']['name'] == 'DEFAULT'

def test_admin_create_an_account_business_w_default_pic_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        # 'pic_id': None
        }

    pic_id = None
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    pic_id = res.json()[0]['data']['pic_id']

    res = client.get(
        f"/admin/partner/staff/{pic_id}", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.json()['data']['name'] == 'DEFAULT'

def test_admin_create_an_account_w_duplicated_name(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        # 'pic_id': None
        }

    pic_id = None
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    pic_id = res.json()[0]['data']['pic_id']

    res = client.get(
        f"/admin/partner/staff/{pic_id}", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.json()['data']['name'] == 'DEFAULT'

    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 400, res.text

def test_admin_create_an_account_clinic_w_specific_pic_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY -> SALE_PIC -> CLINIC
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }

    # GENESTORY
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    # SALE_PIC
    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    params = {
        'page_number': 1,
        'page_size': 10,
        'name': 'Nguyễn Văn Admin'
    }

    # GET ID
    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == 1

    pic_id = res.json()['data'][0]['id']

    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }

    # CLINIC
    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']


# GET ACCOUNTS
def test_admin_list_all_accounts(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY -> SALE_PIC -> CLINIC
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }

    # GENESTORY
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    # SALE_PIC
    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    pic_id = res.json()[0]['data']['id']

    number_of_accounts = 10
    for i in range(number_of_accounts):

        d3 = {
            "name": "BV Vinmec " + str(i),
            "address": "1 P. Minh Khai,",
            "description": "Testing " + str(i),
            "area": "Hà Nội",
            "type": "CLINIC",
            "pic_id": pic_id
            }

        # CLINIC
        res = client.post(
            "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['name'] == d3['name']
        assert res.json()[0]['data']['address'] == d3['address']
        assert res.json()[0]['data']['description'] == d3['description']
        assert res.json()[0]['data']['area'] == d3['area']
        assert res.json()[0]['data']['type'] == d3['type']
        assert res.json()[0]['data']['pic_id'] == d3['pic_id']
    
    # 1 BUSINESS & 10 CLINIC ACCOUNTS
    params = {
        'page_number': 1,
        'page_size': 20
    }

    res = client.get(
        "/admin/partner/accounts", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_accounts + 1
    
    # 10 CLINIC ACCOUNTS
    params = {
        'page_number': 1,
        'page_size': 20,
        'type': 'CLINIC'
    }

    res = client.get(
        "/admin/partner/accounts", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_accounts
    
    # 1 BUSINESS 
    params = {
        'page_number': 1,
        'page_size': 20,
        'type': 'BUSINESS'
    }

    res = client.get(
        "/admin/partner/accounts", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == 1

# GET ACCOUNT ID
## BUSINESS AND CLINIC
def test_admin_get_business_account_detail_with_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY -> SALE_PIC -> CLINIC
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }

    # GENESTORY
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']
    assert res.json()[0]['data']['type'] == d['type']

    # GET
    res_get = client.get(
        f"/admin/partner/account/{res.json()[0]['data']['id']}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_get.status_code == 200, res.text
    # print('Detail: ',res.json())
    assert res_get.json()['data']['name'] == d['name']
    assert res_get.json()['data']['address'] == d['address']
    assert res_get.json()['data']['description'] == d['description']
    assert res_get.json()['data']['area'] == d['area']
    assert res_get.json()['data']['type'] == d['type']

def test_admin_get_clinic_account_detail_with_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY -> SALE_PIC -> CLINIC
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }

    # GENESTORY
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    # SALE_PIC
    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    params = {
        'page_number': 1,
        'page_size': 10,
        'name': 'Nguyễn Văn Admin'
    }

    # GET ID
    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == 1

    pic_id = res.json()['data'][0]['id']

    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }

    # CLINIC
    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']

    res_get = client.get(
        f"/admin/partner/account/{res.json()[0]['data']['id']}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_get.status_code == 200, res.text
    assert res_get.json()['data']['name'] == d3['name']
    assert res_get.json()['data']['address'] == d3['address']
    assert res_get.json()['data']['description'] == d3['description']
    assert res_get.json()['data']['area'] == d3['area']
    assert res_get.json()['data']['type'] == d3['type']

# PUT ACCOUNT
def test_admin_get_clinic_account_detail_with_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY -> SALE_PIC -> CLINIC
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }

    # GENESTORY
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    # SALE_PIC
    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    params = {
        'page_number': 1,
        'page_size': 10,
        'name': 'Nguyễn Văn Admin'
    }

    # GET ID
    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == 1

    pic_id = res.json()['data'][0]['id']

    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }

    # CLINIC
    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']

    res_get = client.get(
        f"/admin/partner/account/{res.json()[0]['data']['id']}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_get.status_code == 200, res.text
    assert res_get.json()['data']['name'] == d3['name']
    assert res_get.json()['data']['address'] == d3['address']
    assert res_get.json()['data']['description'] == d3['description']
    assert res_get.json()['data']['area'] == d3['area']
    assert res_get.json()['data']['type'] == d3['type']

    d4 = {
        "name": "BV Vinmec 11",
        "address": "11 P. Minh Khai, HCM",
        "description": "Testing 11",
        "area": "Hồ Chí Minh",
        "pic_id": pic_id
        }
    
    res_put = client.put(
        f"/admin/partner/account/{res.json()[0]['data']['id']}", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_put.status_code == 200, res.text

    res_get = client.get(
        f"/admin/partner/account/{res.json()[0]['data']['id']}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_get.status_code == 200, res.text
    assert res_get.json()['data']['name'] == d4['name']
    assert res_get.json()['data']['address'] == d4['address']
    assert res_get.json()['data']['description'] == d4['description']
    assert res_get.json()['data']['area'] == d4['area']
    

# PUT ACCOUNT

def test_admin_put_clinic_account_detail_with_new_pic_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY -> SALE_PIC -> CLINIC
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }

    # GENESTORY
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']

    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    # SALE_PIC
    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    pic_id = res.json()[0]['data']['id']

    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }

    # CLINIC
    res_post = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_post.status_code == 200, res.text
    assert res_post.json()[0]['data']['name'] == d3['name']
    assert res_post.json()[0]['data']['address'] == d3['address']
    assert res_post.json()[0]['data']['description'] == d3['description']
    assert res_post.json()[0]['data']['area'] == d3['area']
    assert res_post.json()[0]['data']['type'] == d3['type']
    assert res_post.json()[0]['data']['pic_id'] == d3['pic_id']
    
    d2 = {
            "name": "Nguyễn Văn Admin 2",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    # SALE_PIC
    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    pic_id_2 = res.json()[0]['data']['id']

    d4 = {
        "pic_id": pic_id_2
    }
    
    res_put = client.put(
        f"/admin/partner/account/{res_post.json()[0]['data']['id']}", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_put.status_code == 200, res.text
    assert res_put.json()['data']['name'] == d3['name']
    assert res_put.json()['data']['address'] == d3['address']
    assert res_put.json()['data']['description'] == d3['description']
    assert res_put.json()['data']['area'] == d3['area']
    assert res_put.json()['data']['type'] == d3['type']
    assert res_put.json()['data']['pic_id'] == pic_id_2

# DELETE ACCOUNT
def test_admin_delete_accounts(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY -> SALE_PIC -> CLINIC
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }

    # GENESTORY
    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    # SALE_PIC
    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    pic_id = res.json()[0]['data']['id']

    number_of_accounts = 10
    delete_account_id = None

    for i in range(number_of_accounts):

        d3 = {
            "name": "BV Vinmec " + str(i),
            "address": "1 P. Minh Khai,",
            "description": "Testing " + str(i),
            "area": "Hà Nội",
            "type": "CLINIC",
            "pic_id": pic_id
            }

        # CLINIC
        res = client.post(
            "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['name'] == d3['name']
        assert res.json()[0]['data']['address'] == d3['address']
        assert res.json()[0]['data']['description'] == d3['description']
        assert res.json()[0]['data']['area'] == d3['area']
        assert res.json()[0]['data']['type'] == d3['type']
        assert res.json()[0]['data']['pic_id'] == d3['pic_id']

        if i == 7:
            delete_account_id = res.json()[0]['data']['id']

    
    
    # 10 CLINIC ACCOUNTS
    params = {
        'page_number': 1,
        'page_size': 20,
        'type': 'CLINIC'
    }

    res = client.get(
        "/admin/partner/accounts", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_accounts #10

    res = client.delete(
        f"/admin/partner/account/{delete_account_id}", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    params = {
        'page_number': 1,
        'page_size': 20,
        'type': 'CLINIC'
    }

    res = client.get(
        "/admin/partner/accounts", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_accounts - 1 #10
    


# STAFF
# CREATE STAFF
def test_admin_create_a_staff_sale_pic(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }


    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

def test_admin_create_a_staff_doctor(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)

    # SALE_PIC
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }


    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    params = {
        'page_number': 1,
        'page_size': 10,
        'name': 'Nguyễn Văn Admin'
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == 1

    pic_id = res.json()['data'][0]['id']

    # CLINIC
    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }


    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']

    CLINIC_ID = res.json()[0]['data']['id']

    # DOCTOR
    d4 = {
            "name": "Nguyễn Văn Doctor",
            "email": "<EMAIL>",
            "account_id": CLINIC_ID,
            "phone_number": "**********",
            "role": "DOCTOR"
        }


    res = client.post(
        "/admin/partner/staff", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d4['name']
    assert res.json()[0]['data']['email'] == d4['email']
    assert res.json()[0]['data']['account_id'] == d4['account_id']
    assert res.json()[0]['data']['phone_number'] == d4['phone_number']
    assert res.json()[0]['data']['role'] == d4['role']


    pass

def test_admin_list_all_staffs_sale_pic(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)
    d2 = {
            "name": "Nguyễn Văn Admin ",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }

    number_of_staff = 10
    for i in range(number_of_staff):
        temp = d2.copy()
        temp['name'] += str(i)



        res = client.post(
            "/admin/partner/staff", json=temp, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d2['name'] + str(i)
        assert res.json()[0]['data']['email'] == d2['email']
        assert res.json()[0]['data']['account_id'] == d2['account_id']
        assert res.json()[0]['data']['phone_number'] == d2['phone_number']
        assert res.json()[0]['data']['role'] == d2['role']
    

    params = {
        'page_number': 1,
        'page_size': 10
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_staff
    
    # for i in range(number_of_staff):
    #     assert res.json()['data'][i]['name'] == d2['name'] + str(number_of_staff-1-i)


# GET STAFFS

def test_admin_list_all_staffs(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)

    # SALE_PIC

    number_of_sale_pic = 6

    for i in range(number_of_sale_pic):
        d2 = {
                "name": "Nguyễn Văn Admin " + str(i),
                "email": "<EMAIL>",
                "account_id": GENESTORY_ID,
                "phone_number": "**********",
                "role": "SALE_PIC"
            }


        res = client.post(
            "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d2['name']
        assert res.json()[0]['data']['email'] == d2['email']
        assert res.json()[0]['data']['account_id'] == d2['account_id']
        assert res.json()[0]['data']['phone_number'] == d2['phone_number']
        assert res.json()[0]['data']['role'] == d2['role']

    pic_id = res.json()[0]['data']['id']

    # CLINIC
    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }


    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']

    CLINIC_ID = res.json()[0]['data']['id']

    # DOCTOR
    number_of_doctors = 8
    for i in range(number_of_doctors):
        d4 = {
                "name": "Nguyễn Văn Doctor " + str(i),
                "email": "<EMAIL>",
                "account_id": CLINIC_ID,
                "phone_number": "**********",
                "role": "DOCTOR"
            }


        res = client.post(
            "/admin/partner/staff", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d4['name']
        assert res.json()[0]['data']['email'] == d4['email']
        assert res.json()[0]['data']['account_id'] == d4['account_id']
        assert res.json()[0]['data']['phone_number'] == d4['phone_number']
        assert res.json()[0]['data']['role'] == d4['role']


    params = {
        'page_number': 1,
        'page_size': 20
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_sale_pic + number_of_doctors #10s


    params = {
        'page_number': 1,
        'page_size': 20,
        'role': 'SALE_PIC'
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_sale_pic 


    params = {
        'page_number': 1,
        'page_size': 20,
        'role': 'DOCTOR'
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_doctors 


# GET STAFF ID

def test_admin_get_sale_pic_detail_with_id(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)

    # SALE_PIC

    number_of_sale_pic = 6

    for i in range(number_of_sale_pic):
        d2 = {
                "name": "Nguyễn Văn Admin " + str(i),
                "email": "<EMAIL>",
                "account_id": GENESTORY_ID,
                "phone_number": "**********",
                "role": "SALE_PIC"
            }


        res = client.post(
            "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d2['name']
        assert res.json()[0]['data']['email'] == d2['email']
        assert res.json()[0]['data']['account_id'] == d2['account_id']
        assert res.json()[0]['data']['phone_number'] == d2['phone_number']
        assert res.json()[0]['data']['role'] == d2['role']

        staff_id = res.json()[0]['data']['id']
        res_get = client.get(
            f"/admin/partner/staff/{staff_id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res_get.status_code == 200, res.text
        # print('SALE PIC: ',res_get.json())
        assert res_get.json()['data']['name'] == d2['name']
        assert res_get.json()['data']['email'] == d2['email']
        assert res_get.json()['data']['account_id'] == d2['account_id']
        assert res_get.json()['data']['phone_number'] == d2['phone_number']
        assert res_get.json()['data']['role'] == "SALE_PIC"

def test_admin_get_doctor_detail_with_id(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)

    # SALE_PIC

    number_of_sale_pic = 6

    for i in range(number_of_sale_pic):
        d2 = {
                "name": "Nguyễn Văn Admin " + str(i),
                "email": "<EMAIL>",
                "account_id": GENESTORY_ID,
                "phone_number": "**********",
                "role": "SALE_PIC"
            }


        res = client.post(
            "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d2['name']
        assert res.json()[0]['data']['email'] == d2['email']
        assert res.json()[0]['data']['account_id'] == d2['account_id']
        assert res.json()[0]['data']['phone_number'] == d2['phone_number']
        assert res.json()[0]['data']['role'] == d2['role']

    pic_id = res.json()[0]['data']['id']

    # CLINIC
    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }


    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']

    CLINIC_ID = res.json()[0]['data']['id']

    # DOCTOR
    number_of_doctors = 8
    for i in range(number_of_doctors):
        d4 = {
                "name": "Nguyễn Văn Doctor " + str(i),
                "email": "<EMAIL>",
                "account_id": CLINIC_ID,
                "phone_number": "**********",
                "role": "DOCTOR"
            }


        res = client.post(
            "/admin/partner/staff", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d4['name']
        assert res.json()[0]['data']['email'] == d4['email']
        assert res.json()[0]['data']['account_id'] == d4['account_id']
        assert res.json()[0]['data']['phone_number'] == d4['phone_number']
        assert res.json()[0]['data']['role'] == d4['role']

        staff_id = res.json()[0]['data']['id']
        res_get = client.get(
            f"/admin/partner/staff/{staff_id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res_get.status_code == 200, res.text
        # print('SALE PIC: ',res_get.json())
        assert res_get.json()['data']['name'] == d4['name']
        assert res_get.json()['data']['email'] == d4['email']
        assert res_get.json()['data']['account_id'] == d4['account_id']
        assert res_get.json()['data']['phone_number'] == d4['phone_number']
        assert res_get.json()['data']['role'] == "DOCTOR"


# PUT STAFF
def test_admin_update_a_staff_doctor(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)

    # SALE_PIC
    d2 = {
            "name": "Nguyễn Văn Admin",
            "email": "<EMAIL>",
            "account_id": GENESTORY_ID,
            "phone_number": "**********",
            "role": "SALE_PIC"
        }


    res = client.post(
        "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d2['name']
    assert res.json()[0]['data']['email'] == d2['email']
    assert res.json()[0]['data']['account_id'] == d2['account_id']
    assert res.json()[0]['data']['phone_number'] == d2['phone_number']
    assert res.json()[0]['data']['role'] == d2['role']

    params = {
        'page_number': 1,
        'page_size': 10,
        'name': 'Nguyễn Văn Admin'
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == 1

    pic_id = res.json()['data'][0]['id']

    # CLINIC
    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }


    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']

    CLINIC_ID = res.json()[0]['data']['id']

    # DOCTOR
    d4 = {
            "name": "Nguyễn Văn Doctor",
            "email": "<EMAIL>",
            "account_id": CLINIC_ID,
            "phone_number": "**********",
            "role": "DOCTOR"
        }


    res = client.post(
        "/admin/partner/staff", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d4['name']
    assert res.json()[0]['data']['email'] == d4['email']
    assert res.json()[0]['data']['account_id'] == d4['account_id']
    assert res.json()[0]['data']['phone_number'] == d4['phone_number']
    assert res.json()[0]['data']['role'] == d4['role']

    doctor_id = res.json()[0]['data']['id']
    d5 = {
            "email": "<EMAIL>",
            "phone_number": "**********",
        }


    res_put = client.put(
        f"/admin/partner/staff/{doctor_id}", json=d5, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res_put.status_code == 200, res.text
    # print('DOCTOR: ',res_put.json())
    assert res_put.json()['data']['email'] == d5['email']
    assert res_put.json()['data']['phone_number'] == d5['phone_number']

# DELETE STAFF

def test_admin_delete_staffs(client,access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    # GENESTORY
    d = {
        "name": "GENESTORY",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "BUSINESS",
        }


    res = client.post(
        "/admin/partner/account", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d['name']
    assert res.json()[0]['data']['address'] == d['address']
    assert res.json()[0]['data']['description'] == d['description']
    assert res.json()[0]['data']['area'] == d['area']

    GENESTORY_ID = res.json()[0]['data']['id']
    # print('GENESTORY_DATA: ',res.json()[0]['data'])
    # print('GENESTORY_ID: ',GENESTORY_ID)

    # SALE_PIC

    number_of_sale_pic = 6

    for i in range(number_of_sale_pic):
        d2 = {
                "name": "Nguyễn Văn Admin " + str(i),
                "email": "<EMAIL>",
                "account_id": GENESTORY_ID,
                "phone_number": "**********",
                "role": "SALE_PIC"
            }


        res = client.post(
            "/admin/partner/staff", json=d2, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d2['name']
        assert res.json()[0]['data']['email'] == d2['email']
        assert res.json()[0]['data']['account_id'] == d2['account_id']
        assert res.json()[0]['data']['phone_number'] == d2['phone_number']
        assert res.json()[0]['data']['role'] == d2['role']

        if i == 1:
            res = client.delete(
                f"/admin/partner/staff/{res.json()[0]['data']['id']}", headers={"Authorization": f"bearer {fake_jwt}"}
            )

    pic_id = res.json()[0]['data']['id']

    # CLINIC
    d3 = {
        "name": "BV Vinmec 1",
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC",
        "pic_id": pic_id
        }


    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    assert res.json()[0]['data']['pic_id'] == d3['pic_id']

    CLINIC_ID = res.json()[0]['data']['id']

    # DOCTOR
    number_of_doctors = 8
    for i in range(number_of_doctors):
        d4 = {
                "name": "Nguyễn Văn Doctor " + str(i),
                "email": "<EMAIL>",
                "account_id": CLINIC_ID,
                "phone_number": "**********",
                "role": "DOCTOR"
            }


        res = client.post(
            "/admin/partner/staff", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text
        # print('ADMIN: ',res.json()[0]['data'])
        assert res.json()[0]['data']['name'] == d4['name']
        assert res.json()[0]['data']['email'] == d4['email']
        assert res.json()[0]['data']['account_id'] == d4['account_id']
        assert res.json()[0]['data']['phone_number'] == d4['phone_number']
        assert res.json()[0]['data']['role'] == d4['role']

        if i == 7:
            res = client.delete(
                f"/admin/partner/staff/{res.json()[0]['data']['id']}", headers={"Authorization": f"bearer {fake_jwt}"}
            )


    params = {
        'page_number': 1,
        'page_size': 20
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_sale_pic + number_of_doctors - 2 #10s


    params = {
        'page_number': 1,
        'page_size': 20,
        'role': 'SALE_PIC'
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_sale_pic - 1


    params = {
        'page_number': 1,
        'page_size': 20,
        'role': 'DOCTOR'
    }

    res = client.get(
        "/admin/partner/staffs", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_doctors - 1


