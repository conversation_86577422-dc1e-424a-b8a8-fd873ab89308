--
-- PostgreSQL database dump
--

-- Dumped from database version 12.14
-- Dumped by pg_dump version 16.2 (Ubuntu 16.2-1.pgdg22.04+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: operation_user
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO operation_user;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: account; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.account (
    id uuid NOT NULL,
    name character varying(200) NOT NULL,
    address character varying(255) NOT NULL,
    description character varying(120) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    area character varying(50) NOT NULL,
    type character varying(50) DEFAULT 'CLINIC'::character varying NOT NULL
);


ALTER TABLE public.account OWNER TO operation_user;

--
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO operation_user;

--
-- Name: attempt; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.attempt (
    email character varying(50),
    block boolean NOT NULL,
    attempt integer NOT NULL,
    note character varying(500),
    created_time timestamp with time zone NOT NULL,
    updated_time timestamp with time zone NOT NULL,
    userid character varying(50) NOT NULL
);


ALTER TABLE public.attempt OWNER TO operation_user;

--
-- Name: barcode_dob; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.barcode_dob (
    barcode character varying(255) NOT NULL,
    dob date
);


ALTER TABLE public.barcode_dob OWNER TO operation_user;

--
-- Name: batch; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.batch (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    number integer NOT NULL,
    name character varying(20) NOT NULL,
    note character varying(120) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone,
    type character varying(24) NOT NULL
);


ALTER TABLE public.batch OWNER TO operation_user;

--
-- Name: batch_chip; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.batch_chip (
    id bigint NOT NULL,
    chip_id character varying(12) NOT NULL,
    batch_id character varying(12) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.batch_chip OWNER TO operation_user;

--
-- Name: batch_chip_id_seq; Type: SEQUENCE; Schema: public; Owner: operation_user
--

CREATE SEQUENCE public.batch_chip_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.batch_chip_id_seq OWNER TO operation_user;

--
-- Name: batch_chip_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: operation_user
--

ALTER SEQUENCE public.batch_chip_id_seq OWNED BY public.batch_chip.id;


--
-- Name: batch_mapping; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.batch_mapping (
    id bigint NOT NULL,
    batch_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    raw_data_uploaded_date timestamp without time zone,
    raw_report_to_reviewers timestamp without time zone,
    plate_id uuid NOT NULL,
    wetlab_date timestamp without time zone,
    drylab_date timestamp without time zone,
    analysis_date date
);


ALTER TABLE public.batch_mapping OWNER TO operation_user;

--
-- Name: batch_mapping_id_seq; Type: SEQUENCE; Schema: public; Owner: operation_user
--

CREATE SEQUENCE public.batch_mapping_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.batch_mapping_id_seq OWNER TO operation_user;

--
-- Name: batch_mapping_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: operation_user
--

ALTER SEQUENCE public.batch_mapping_id_seq OWNED BY public.batch_mapping.id;


--
-- Name: card; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.card (
    id character varying(32) NOT NULL,
    card_product_id uuid NOT NULL,
    barcode character varying(120) NOT NULL,
    report_ver character varying(14) NOT NULL,
    db_ver character varying(14) NOT NULL,
    card_status character varying(14),
    qr_url character varying(255),
    full_name character varying(255),
    created_at timestamp with time zone NOT NULL,
    s3_object_key character varying(500),
    phone_number character varying(120) NOT NULL,
    user_id character varying(120) NOT NULL,
    presigned_s3_font_url character varying(500),
    presigned_s3_back_url character varying(500),
    lang character varying(5) DEFAULT 'vi'::character varying NOT NULL
);


ALTER TABLE public.card OWNER TO operation_user;

--
-- Name: card_product; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.card_product (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    type character varying(50) NOT NULL,
    card_product_name character varying(200) NOT NULL,
    policy character varying(50),
    created_at timestamp with time zone NOT NULL
);


ALTER TABLE public.card_product OWNER TO operation_user;

--
-- Name: chip; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.chip (
    id bigint NOT NULL,
    type character varying(12) DEFAULT 'GSAv3'::character varying NOT NULL,
    technology character varying(20) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone,
    chip_id character varying(22) NOT NULL
);


ALTER TABLE public.chip OWNER TO operation_user;

--
-- Name: chip_id_seq; Type: SEQUENCE; Schema: public; Owner: operation_user
--

CREATE SEQUENCE public.chip_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.chip_id_seq OWNER TO operation_user;

--
-- Name: chip_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: operation_user
--

ALTER SEQUENCE public.chip_id_seq OWNED BY public.chip.id;


--
-- Name: sample_mapping; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.sample_mapping (
    id bigint NOT NULL,
    chip_id character varying(22),
    "position" character varying(30),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    well_position character varying(12),
    plate_id uuid,
    prob_pass boolean,
    call_rate double precision,
    call_rate_pass boolean,
    gender_pass boolean,
    qc_status character varying(12),
    dna_extraction_id bigint NOT NULL
);


ALTER TABLE public.sample_mapping OWNER TO operation_user;

--
-- Name: chip_sample_id_seq; Type: SEQUENCE; Schema: public; Owner: operation_user
--

CREATE SEQUENCE public.chip_sample_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.chip_sample_id_seq OWNER TO operation_user;

--
-- Name: chip_sample_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: operation_user
--

ALTER SEQUENCE public.chip_sample_id_seq OWNED BY public.sample_mapping.id;


--
-- Name: code; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.code (
    barcode character varying NOT NULL,
    qrcode character varying NOT NULL,
    state character varying,
    created_time timestamp with time zone NOT NULL,
    updated_time timestamp with time zone NOT NULL,
    printed boolean DEFAULT false NOT NULL,
    deleted_at timestamp without time zone,
    note character varying(120) DEFAULT ''::character varying,
    product_code character varying(10),
    product_name character varying(100)
);


ALTER TABLE public.code OWNER TO operation_user;

--
-- Name: dna_box; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.dna_box (
    id character varying(12) NOT NULL,
    capacity integer DEFAULT 0 NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.dna_box OWNER TO operation_user;

--
-- Name: dna_box_mappings; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.dna_box_mappings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    "position" character varying(12) NOT NULL,
    dna_box_id character varying(12) NOT NULL,
    lid character varying(12) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.dna_box_mappings OWNER TO operation_user;

--
-- Name: dna_extractions; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.dna_extractions (
    id bigint NOT NULL,
    lid character varying(12) NOT NULL,
    dna_extraction_date timestamp without time zone NOT NULL,
    qubit double precision,
    nano_drop double precision NOT NULL,
    a260_a280 double precision NOT NULL,
    agarose_gel character varying(12) DEFAULT 'FAIL'::character varying NOT NULL,
    dna_qc_status character varying(12) DEFAULT 'FAIL'::character varying NOT NULL,
    note character varying(120),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.dna_extractions OWNER TO operation_user;

--
-- Name: dna_extractions_id_seq; Type: SEQUENCE; Schema: public; Owner: operation_user
--

CREATE SEQUENCE public.dna_extractions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.dna_extractions_id_seq OWNER TO operation_user;

--
-- Name: dna_extractions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: operation_user
--

ALTER SEQUENCE public.dna_extractions_id_seq OWNED BY public.dna_extractions.id;


--
-- Name: kit; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.kit (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    barcode character varying(36),
    samplecode character varying(36) NOT NULL,
    nickname character varying(50),
    version character varying(50),
    expected_report_release_date timestamp without time zone,
    actual_report_release_time timestamp without time zone,
    pdf_generation_date timestamp without time zone,
    default_pdf_link character varying(500),
    customer_support_id uuid,
    customer_support_name character varying(50),
    free_of_charge boolean DEFAULT false NOT NULL,
    promotion uuid,
    is_priority boolean DEFAULT false NOT NULL,
    is_card_issued boolean DEFAULT false NOT NULL,
    product_code character varying(12) DEFAULT ''::character varying NOT NULL,
    product_name character varying(50) DEFAULT ''::character varying NOT NULL,
    product_type character varying(50) DEFAULT 'CLINIC'::character varying NOT NULL,
    note character varying(100),
    current_status character varying(50),
    current_status_id integer,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.kit OWNER TO operation_user;

--
-- Name: lab_id; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.lab_id (
    lid character varying(12)
);


ALTER TABLE public.lab_id OWNER TO operation_user;

--
-- Name: lab_sample; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.lab_sample (
    lid character varying(12) NOT NULL,
    barcode character varying(12) NOT NULL,
    lab_receipt_date timestamp without time zone NOT NULL,
    technology character varying(20) NOT NULL,
    note character varying(120),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone,
    positive_control boolean DEFAULT false NOT NULL,
    samplecode character varying(36)
);


ALTER TABLE public.lab_sample OWNER TO operation_user;

--
-- Name: missing_sample; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.missing_sample (
    barcode character varying(12) NOT NULL,
    status character varying(50) NOT NULL,
    note character varying(50),
    deleted_at timestamp with time zone,
    samplecode character varying(36)
);


ALTER TABLE public.missing_sample OWNER TO operation_user;

--
-- Name: old_kit; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.old_kit (
    barcode character varying NOT NULL,
    nickname character varying,
    current_status character varying,
    current_status_id integer NOT NULL,
    created_time timestamp with time zone NOT NULL,
    updated_time timestamp with time zone NOT NULL,
    version character varying(50),
    product_code character varying(50) DEFAULT ''::character varying NOT NULL,
    sample_collection_date timestamp without time zone,
    sample_receipt_date timestamp without time zone,
    expected_report_release_date timestamp without time zone,
    deleted_at timestamp without time zone,
    note character varying(100),
    discount_campain character varying(120),
    customer_support_id uuid,
    product_name character varying(50) DEFAULT ''::character varying NOT NULL,
    sample_collection_time integer DEFAULT 1 NOT NULL,
    free_of_charge boolean DEFAULT false NOT NULL,
    is_priority boolean DEFAULT false NOT NULL,
    promotion uuid,
    customer_support_name character varying(120),
    sample_meta_id uuid,
    default_pdf_link character varying(500),
    source_id uuid,
    product_type character varying(50) DEFAULT 'CLINIC'::character varying,
    pdf_generation_date date,
    lab_check_date date,
    sample_type character varying(30),
    is_card_issued boolean DEFAULT false NOT NULL,
    sample_collector_name character varying(50),
    sample_receiver_name character varying(50),
    lab_receipt_date timestamp without time zone,
    actual_report_release_time timestamp without time zone
);


ALTER TABLE public.old_kit OWNER TO operation_user;

--
-- Name: old_sample; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.old_sample (
    id integer NOT NULL,
    operation_id character varying(50) NOT NULL,
    num integer NOT NULL,
    "position" character varying(50) NOT NULL,
    barcode character varying(50) NOT NULL,
    batch_barcode character varying(50) NOT NULL,
    vinmec_id character varying(50),
    chip_id character varying(50) NOT NULL,
    gender character varying(50) NOT NULL,
    technician_name character varying(50) NOT NULL,
    qc_status character varying(50) NOT NULL,
    created_time timestamp with time zone NOT NULL,
    updated_time timestamp with time zone NOT NULL,
    chip_type character varying(50) NOT NULL,
    assembly character varying(50) NOT NULL,
    positive_tested boolean NOT NULL,
    physical_position character varying(50) NOT NULL
);


ALTER TABLE public.old_sample OWNER TO operation_user;

--
-- Name: partner_booking; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.partner_booking (
    id uuid NOT NULL,
    customer_support_id uuid,
    partner_name character varying(50),
    partner_booking_id character varying(100),
    customer_name character varying(100),
    customer_phone character varying(50),
    payment_amount integer NOT NULL,
    payment_method character varying(50),
    payment_status character varying(50),
    status character varying(30),
    error_message character varying(200),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone,
    referral_code character varying(20),
    products character varying(200),
    note character varying(200),
    consulted boolean DEFAULT true NOT NULL
);


ALTER TABLE public.partner_booking OWNER TO operation_user;

--
-- Name: partner_booking_kit; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.partner_booking_kit (
    id uuid NOT NULL,
    product_code character varying(10),
    product_name character varying(50),
    product_id character varying(100),
    booking_id uuid,
    checkout_price integer,
    barcode character varying(50),
    status character varying(50),
    result_id character varying(100),
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone
);


ALTER TABLE public.partner_booking_kit OWNER TO operation_user;

--
-- Name: plate; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.plate (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(12) NOT NULL,
    status character varying(12) NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone,
    type character varying(24) DEFAULT 'DEFAULT'::character varying NOT NULL
);


ALTER TABLE public.plate OWNER TO operation_user;

--
-- Name: promotion; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.promotion (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    code character varying(250) NOT NULL,
    name character varying(500) NOT NULL,
    discount integer NOT NULL,
    department character varying(250) NOT NULL,
    start_date timestamp without time zone NOT NULL,
    end_date timestamp without time zone NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at timestamp with time zone
);


ALTER TABLE public.promotion OWNER TO operation_user;

--
-- Name: sale_account_history; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.sale_account_history (
    id uuid NOT NULL,
    account_id uuid NOT NULL,
    pic_id uuid NOT NULL,
    created_at timestamp without time zone
);


ALTER TABLE public.sale_account_history OWNER TO operation_user;

--
-- Name: sample; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.sample (
    id uuid NOT NULL,
    samplecode character varying(36) NOT NULL,
    subject_id uuid NOT NULL,
    sample_collection_date timestamp without time zone,
    sample_recollection boolean NOT NULL,
    sample_collection_time integer DEFAULT 1 NOT NULL,
    sample_receipt_date timestamp without time zone,
    sample_collector_name character varying(100),
    sample_receiver_name character varying(100),
    source_id uuid,
    scan_status integer DEFAULT 0 NOT NULL,
    lab_check_date date,
    lab_receipt_date timestamp without time zone,
    sample_type character varying(30),
    run_id uuid,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.sample OWNER TO operation_user;

--
-- Name: sample_id_seq; Type: SEQUENCE; Schema: public; Owner: operation_user
--

CREATE SEQUENCE public.sample_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sample_id_seq OWNER TO operation_user;

--
-- Name: sample_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: operation_user
--

ALTER SEQUENCE public.sample_id_seq OWNED BY public.old_sample.id;


--
-- Name: sample_meta; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.sample_meta (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    userid uuid,
    full_name character varying(50) NOT NULL,
    address character varying(250) NOT NULL,
    email character varying(50),
    phone_number character varying(50),
    validate_account boolean NOT NULL,
    gender character varying(50) NOT NULL,
    dob timestamp without time zone NOT NULL,
    diagnosis character varying(200),
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    yob character varying(4)
);


ALTER TABLE public.sample_meta OWNER TO operation_user;

--
-- Name: samplecode; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.samplecode (
    samplecode character varying(36) NOT NULL,
    printed boolean NOT NULL,
    state character varying(100),
    note character varying(100),
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone
);


ALTER TABLE public.samplecode OWNER TO operation_user;

--
-- Name: source; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.source (
    id uuid NOT NULL,
    account_history_id uuid,
    nominator_id uuid,
    freelancer_id uuid
);


ALTER TABLE public.source OWNER TO operation_user;

--
-- Name: staff; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.staff (
    id uuid NOT NULL,
    name character varying(50) NOT NULL,
    email character varying(50),
    userid uuid,
    account_id uuid NOT NULL,
    phone_number character varying(50),
    role character varying(50) DEFAULT 'DOCTOR'::character varying NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.staff OWNER TO operation_user;

--
-- Name: status; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.status (
    id integer NOT NULL,
    barcode character varying,
    status character varying,
    note character varying,
    created_time timestamp with time zone NOT NULL,
    updated_time timestamp with time zone NOT NULL
);


ALTER TABLE public.status OWNER TO operation_user;

--
-- Name: status_id_seq; Type: SEQUENCE; Schema: public; Owner: operation_user
--

CREATE SEQUENCE public.status_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.status_id_seq OWNER TO operation_user;

--
-- Name: status_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: operation_user
--

ALTER SEQUENCE public.status_id_seq OWNED BY public.status.id;


--
-- Name: subject; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.subject (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid,
    full_name character varying(120) NOT NULL,
    email character varying(50),
    phone_number character varying(50),
    diagnosis character varying(200),
    address character varying(250),
    dob timestamp without time zone NOT NULL,
    gender character varying(50) NOT NULL,
    yob character varying(4),
    validate_account boolean NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.subject OWNER TO operation_user;

--
-- Name: temp_products; Type: TABLE; Schema: public; Owner: operation_user
--

CREATE TABLE public.temp_products (
    name character varying(100),
    code character varying(100)
);


ALTER TABLE public.temp_products OWNER TO operation_user;

--
-- Name: batch_chip id; Type: DEFAULT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.batch_chip ALTER COLUMN id SET DEFAULT nextval('public.batch_chip_id_seq'::regclass);


--
-- Name: batch_mapping id; Type: DEFAULT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.batch_mapping ALTER COLUMN id SET DEFAULT nextval('public.batch_mapping_id_seq'::regclass);


--
-- Name: chip id; Type: DEFAULT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.chip ALTER COLUMN id SET DEFAULT nextval('public.chip_id_seq'::regclass);


--
-- Name: dna_extractions id; Type: DEFAULT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.dna_extractions ALTER COLUMN id SET DEFAULT nextval('public.dna_extractions_id_seq'::regclass);


--
-- Name: old_sample id; Type: DEFAULT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.old_sample ALTER COLUMN id SET DEFAULT nextval('public.sample_id_seq'::regclass);


--
-- Name: sample_mapping id; Type: DEFAULT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sample_mapping ALTER COLUMN id SET DEFAULT nextval('public.chip_sample_id_seq'::regclass);


--
-- Name: status id; Type: DEFAULT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.status ALTER COLUMN id SET DEFAULT nextval('public.status_id_seq'::regclass);


--
-- Name: account account_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.account
    ADD CONSTRAINT account_pkey PRIMARY KEY (id);


--
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- Name: attempt attempt_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.attempt
    ADD CONSTRAINT attempt_pkey PRIMARY KEY (userid);


--
-- Name: barcode_dob barcode_dob_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.barcode_dob
    ADD CONSTRAINT barcode_dob_pkey PRIMARY KEY (barcode);


--
-- Name: batch_chip batch_chip_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.batch_chip
    ADD CONSTRAINT batch_chip_pkey PRIMARY KEY (id);


--
-- Name: batch_mapping batch_mapping_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.batch_mapping
    ADD CONSTRAINT batch_mapping_pkey PRIMARY KEY (id);


--
-- Name: batch batch_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.batch
    ADD CONSTRAINT batch_pkey PRIMARY KEY (id);


--
-- Name: card card_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.card
    ADD CONSTRAINT card_pkey PRIMARY KEY (id);


--
-- Name: card_product card_product_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.card_product
    ADD CONSTRAINT card_product_pkey PRIMARY KEY (id);


--
-- Name: chip chip_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.chip
    ADD CONSTRAINT chip_pkey PRIMARY KEY (id);


--
-- Name: sample_mapping chip_sample_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sample_mapping
    ADD CONSTRAINT chip_sample_pkey PRIMARY KEY (id);


--
-- Name: code code_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.code
    ADD CONSTRAINT code_pkey PRIMARY KEY (barcode);


--
-- Name: dna_box_mappings dna_box_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.dna_box_mappings
    ADD CONSTRAINT dna_box_mappings_pkey PRIMARY KEY (id);


--
-- Name: dna_box dna_box_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.dna_box
    ADD CONSTRAINT dna_box_pkey PRIMARY KEY (id);


--
-- Name: dna_extractions dna_extractions_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.dna_extractions
    ADD CONSTRAINT dna_extractions_pkey PRIMARY KEY (id);


--
-- Name: old_kit kit_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.old_kit
    ADD CONSTRAINT kit_pkey PRIMARY KEY (barcode);


--
-- Name: kit kit_pkey1; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.kit
    ADD CONSTRAINT kit_pkey1 PRIMARY KEY (id);


--
-- Name: lab_sample lab_sample_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.lab_sample
    ADD CONSTRAINT lab_sample_pkey PRIMARY KEY (lid);


--
-- Name: plate m_plate_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.plate
    ADD CONSTRAINT m_plate_pkey PRIMARY KEY (id);


--
-- Name: partner_booking_kit partner_booking_kit_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.partner_booking_kit
    ADD CONSTRAINT partner_booking_kit_pkey PRIMARY KEY (id);


--
-- Name: partner_booking partner_booking_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.partner_booking
    ADD CONSTRAINT partner_booking_pkey PRIMARY KEY (id);


--
-- Name: promotion promotion_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.promotion
    ADD CONSTRAINT promotion_pkey PRIMARY KEY (id);


--
-- Name: sale_account_history sale_account_history_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sale_account_history
    ADD CONSTRAINT sale_account_history_pkey PRIMARY KEY (id);


--
-- Name: sample_meta sample_meta_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sample_meta
    ADD CONSTRAINT sample_meta_pkey PRIMARY KEY (id);


--
-- Name: old_sample sample_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.old_sample
    ADD CONSTRAINT sample_pkey PRIMARY KEY (id);


--
-- Name: sample sample_pkey1; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sample
    ADD CONSTRAINT sample_pkey1 PRIMARY KEY (id);


--
-- Name: samplecode samplecode_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.samplecode
    ADD CONSTRAINT samplecode_pkey PRIMARY KEY (samplecode);


--
-- Name: source source_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.source
    ADD CONSTRAINT source_pkey PRIMARY KEY (id);


--
-- Name: staff staff_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.staff
    ADD CONSTRAINT staff_pkey PRIMARY KEY (id);


--
-- Name: status status_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.status
    ADD CONSTRAINT status_pkey PRIMARY KEY (id);


--
-- Name: subject subject_pkey; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.subject
    ADD CONSTRAINT subject_pkey PRIMARY KEY (id);


--
-- Name: lab_sample unique_barcode; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.lab_sample
    ADD CONSTRAINT unique_barcode UNIQUE (barcode);


--
-- Name: card_product unique_card_product_name; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.card_product
    ADD CONSTRAINT unique_card_product_name UNIQUE (card_product_name);


--
-- Name: lab_sample unique_lid; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.lab_sample
    ADD CONSTRAINT unique_lid UNIQUE (lid);


--
-- Name: sample unique_sample_samplecode; Type: CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sample
    ADD CONSTRAINT unique_sample_samplecode UNIQUE (samplecode);


--
-- Name: kit kit_samplecode_fkey; Type: FK CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.kit
    ADD CONSTRAINT kit_samplecode_fkey FOREIGN KEY (samplecode) REFERENCES public.sample(samplecode);


--
-- Name: partner_booking_kit partner_booking_kit_booking_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.partner_booking_kit
    ADD CONSTRAINT partner_booking_kit_booking_id_fkey FOREIGN KEY (booking_id) REFERENCES public.partner_booking(id);


--
-- Name: sample sample_source_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sample
    ADD CONSTRAINT sample_source_id_fkey FOREIGN KEY (source_id) REFERENCES public.source(id);


--
-- Name: sample sample_subject_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: operation_user
--

ALTER TABLE ONLY public.sample
    ADD CONSTRAINT sample_subject_id_fkey FOREIGN KEY (subject_id) REFERENCES public.subject(id);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: operation_user
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;
GRANT ALL ON SCHEMA public TO PUBLIC;


--
-- PostgreSQL database dump complete
--

