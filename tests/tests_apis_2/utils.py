import datetime
import random

def generate_random_6_digits():
    """
    Generate a random 6-digit number.

    Returns:
        int: The generated random 6-digit number.
    """
    return str(random.randint(100000, 999999))

def get_random_phone_numbers(n):
    results = []
    for i in range(n):
        suffix = generate_random_6_digits()
        results.append("039"+suffix)
    
    return results

def get_today_date():
    today_date = datetime.date.today() + datetime.timedelta(hours=7)
    formatted_date = today_date.strftime("%Y-%m-%d")
    return formatted_date

def get_date_plus_n_days(n):
    # Get today's date
    today_date = datetime.date.today() + datetime.timedelta(hours=7)
    # Add 14 days to today's date
    future_date = today_date + datetime.timedelta(days=int(n))
    # Format the future date as "YYYY-MM-DD"
    formatted_date = future_date.strftime("%Y-%m-%d")
    return formatted_date

def get_today_date_tz():
    current_datetime = datetime.datetime.now() + datetime.timedelta(hours=7)
    # Format the datetime as "YYYY-MM-DDTHH:MM:SS"
    formatted_datetime = current_datetime.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_datetime


def get_today_date_tz_add_one():
    current_datetime = datetime.datetime.now()
    # Add one hour to the current datetime
    updated_datetime = current_datetime + datetime.timedelta(hours=7) + datetime.timedelta(hours=1)
    # Format the updated datetime as "YYYY-MM-DDTHH:MM:SS"
    formatted_datetime = updated_datetime.strftime("%Y-%m-%dT%H:%M:%S")
    return formatted_datetime

def get_today_date_tz_in_text():
    # Get current datetime
    current_datetime = datetime.datetime.now() + datetime.timedelta(hours=7)
    # Format the datetime as "Wed Feb 20 2024 14:03:19 GMT+0700 (Indochina Time)"
    formatted_datetime = current_datetime.strftime("%a %b %d %Y %H:%M:%S GMT%z (Indochina Time)")
    return formatted_datetime

def get_mock_subject(phone_number, validate_account=False):
    
    return {
        "name": "D B S",
        "email": "",
        "phone_number": phone_number,
        "diagnosis": "",
        "address": "",
        "dob": "2002-08-08",
        "gender": "female",
        "yob": None,
        "validate_account": validate_account
        # // subject_id<-- auto-generated 
    }

def get_mock_sample(samplecode, account_id, staff_id):
    
    curr_date = get_today_date()
    curr_date_tz = get_today_date_tz()
    curr_date_tz_text = get_today_date_tz_in_text()
    
    return {
        "samplecode": samplecode,
        "sample_collection_date": curr_date,
        "sample_recollection": False,
        # // "sample_collection_time": 1, // auto-calculate
        "sample_receipt_date": curr_date_tz,
        "sample_receipt_time": curr_date_tz_text,
        "sample_collector_name": "",
        "sample_receiver_name": "",
        # // source_id <-- auto-generated
        "account_id": account_id,
        # "account": {
        #     "text": "BV DH Y HN",
        #     "value": account_id
        # },
        "freelancer": {},
        "scan_status": 0,
        # // lab_check_date
        # // lab_receipt_date
        "sample_type": "Máu",
        "nominator": {
            "text": "BS Ha Ha 3",
            "value": staff_id
        },
        "nominator_id": staff_id
    }

def get_mock_kit(product_name="GENE-LIFE PREMIUM",product_code="25",validate_account=False):
    
    curr_date_plus_n_days = get_date_plus_n_days(14)
    if validate_account:
        product_type = "B2C"
    else:
        product_type = "B2B"
        
    return {
        # // "barcode": "************", <-- auto-generated
        # // nickname
        # // version
        "expected_report_release_date": curr_date_plus_n_days,
        # // actual_report_release_time
        # // customer_support_id
        # // customer_support_name
        # // "promotion": {
        # //     "text": "PM2024",
        # //     "value": "5e5bd270-3dc1-4a45-b684-9f2ed98d765f"
        # // },
        "promotion": {
        },
        # // "promotion_id": "5e5bd270-3dc1-4a45-b684-9f2ed98d765f",
        "is_priority": True,
        "free_of_charge": True,
        # // is_card_issued
        "product_name": product_name,
        "product_code": product_code,
        "product_type": product_type,
        "note": "",
        # // current_status <-- auto-generated after BARCODE
        # // current_status_id <-- auto-generated after BARCODE
        "technology": "MICROARRAY"
    }

def get_payload_register_kit_request(phone_number,validate_account,samplecode, account_id, staff_id,product_name="GENE-LIFE PREMIUM", product_code="25"):
    subject = get_mock_subject(phone_number, validate_account)
    sample = get_mock_sample(samplecode, account_id, staff_id)
    kit = get_mock_kit(product_name=product_name,product_code=product_code,validate_account=validate_account)
    
    body = dict()
    body.update(subject)
    body.update(sample)
    body.update(kit)
    
    return body

def get_dna_extraction_payload(lids):
    
    curr_date = get_today_date()
    
    data = []
    for lid in lids:
        data.append({
                "lid": str(lid),
                "dna_extraction_date":str(curr_date),
                "qubit": 26.4,
                "nano_drop": 33.9,
                "a260_a280": 1.89,
                "agarose_gel": "PASS",
                "dna_qc_status": "PASS"
                
        })
    
    return data

def get_update_sample_payload(detail, sample_collection_date, sample_receipt_date, product_code=None, product_name=None):
    
    body = {
    # // subject
    "name": detail.get("full_name"),
    "email": detail.get("email"),
    "phone_number": detail.get("phone_number"),
    "diagnosis": detail.get("diagnosis"),
    "address": detail.get("address"),
    "dob": detail.get("dob"), #// without hour
    "gender": detail.get("gender"),
    "yob": detail.get("yob"),
    "validate_account": detail.get("validate_account"),

    # // sample
    "samplecode": detail.get("samplecode"),
    "subject_id": detail.get("subject_id"),
    # // "current_status": "REGISTERED",
    # // "current_status_id": 4490,
    "sample_collection_date": sample_collection_date,#  // without hour
    "sample_recollection": detail.get("sample_recollection"),
    "sample_collection_time": detail.get("sample_collection_time"),
    "sample_receipt_date": sample_receipt_date, # // with hour
    "sample_collector_name": detail.get("sample_collector_name"),
    "sample_receiver_name": detail.get("sample_receiver_name"),
    # // "source_id": "5aae2b04-4ff2-4719-a9cb-4c5f4172d286",
    "account_id": detail.get("account_id"),
    "nominator_id": detail.get("nominator_id"),
    "freelancer_id": detail.get("freelancer_id"),
    "note": detail.get("note"),
    "lab_check_date": detail.get("lab_check_date"),
    "lab_receipt_date": detail.get("lab_receipt_date"),
    "sample_type": detail.get("sample_type"),
    # // "scan_status": 0,
    # // "run_id": None,
    # // kit
    "nickname": detail.get("nickname"),
    "version": detail.get("version"),
    "expected_report_release_date": detail.get("expected_report_release_date"),
    "actual_report_release_time": detail.get("actual_report_release_time"),
    # // "customer_support_id": "439c52cd-3072-4d15-b5f0-17fd813b4ad4", --> extract from token
    # // "customer_support_name": "Man Nguyen",  --> extract from token
    "promotion_id": detail.get("promotion_id"), #//uuidv4
    "is_priority": detail.get("is_priority"),
    "free_of_charge": detail.get("free_of_charge"),
    "product_name": product_name if product_name else detail.get("product_name"), # define test case
    "product_code": product_code if product_code else detail.get("product_code"), # define test case
    "product_type": detail.get("product_type"),
        # // "barcode": None
    }
    
    return body
    pass

def get_payload_add_samples_to_plate(lids, dna_extraction_ids, plate_name):
    
    body = [
        
            {
            "dna_extraction_id": dna_extraction_ids[1],
            "lid": lids[1],
            "plate_name": plate_name,
            "well_position": "A1",
            "chip_id": None,
            "position": None
        },
            {
            "dna_extraction_id": dna_extraction_ids[0],
            "lid": lids[0],
            "plate_name": plate_name,
            "well_position": "H12",
            "chip_id": None,
            "position": None
        }
    ]
    
    return body


def get_fill_samples_into_chip_payload(plate_name, CHIP_ID, type="PMDA"):
    
    return {
                "plate_name": plate_name,
                "chip_ids": [
                    CHIP_ID
                ],
                "type": type
            }
    pass

def get_payload_to_create_a_batch(batch_number, plate_name):
    
    body = {
        "number": int(batch_number),
        "name": "New microarray batch",
        "note": "This is a test batch",
        "plate_names": [
            int(plate_name)
        ]
    }
    
    return body

def get_recollect_sample_payload(samplecode, payload):
    
    payload['samplecode'] = samplecode
    payload['sample_collection_date'] = '2024-03-01'
    payload['sample_receipt_date'] = '2024-03-01T14:03:00'
    payload['expected_report_release_date'] = '2024-04-04'
    # payload['lab_receipt_date'] <---- Update when send-to-lab
    payload['note'] = 'Recollected sample'
    payload['sample_recollection'] = True
    
    return payload 

def get_upgrade_kit_payload(product_name, product_code, payload):
    
    curr_date_tz = get_today_date_tz_add_one()
    payload['product_name'] = product_name
    payload['product_code'] = product_code
    payload['sample_recollection'] = False
    payload['upgraded_at'] = curr_date_tz
    payload.pop("barcode")
    payload.pop("created_at")
    payload.pop("updated_at")
    
    return payload