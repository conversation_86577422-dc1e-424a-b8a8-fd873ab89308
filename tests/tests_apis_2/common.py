
import secrets
import string

def generate_samplecode_string(length: int = 6):
    characters = string.ascii_uppercase + string.digits  # Include uppercase letters and digits
    random_str = ''.join(secrets.choice(characters) for _ in range(length))
    return random_str

def test_create_account_clinic(client, access_token_patcher):

    fake_jwt = "1.2.3"

    access_token_patcher()
    
    RANDOM_STR = generate_samplecode_string(3)
    
    d3 = {
        "name": "BV DH Y " + RANDOM_STR,
        "address": "1 P<PERSON>,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC"
        }

    # CLINIC
    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    
    account_id = res.json()[0]['data']['id']
    
    return account_id
    
def test_create_staff_doctor(client, access_token_patcher):
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    RANDOM_STR = generate_samplecode_string(3)
    
    d3 = {
        "name": "BV DH Y " + RANDOM_STR,
        "address": "1 P. Minh Khai,",
        "description": "Testing 1",
        "area": "Hà Nội",
        "type": "CLINIC"
        }

    # CLINIC
    res = client.post(
        "/admin/partner/account", json=d3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0]['data']['name'] == d3['name']
    assert res.json()[0]['data']['address'] == d3['address']
    assert res.json()[0]['data']['description'] == d3['description']
    assert res.json()[0]['data']['area'] == d3['area']
    assert res.json()[0]['data']['type'] == d3['type']
    
    account_id = res.json()[0]['data']['id']
    
    RANDOM_NAME_STR = generate_samplecode_string(3)
    
    # CLINIC_ID = account_id = test_create_account_clinic(client, access_token_patcher)
    # DOCTOR
    d4 = {
            "name": "BS Ha Ha " + RANDOM_NAME_STR,
            "email": "<EMAIL>",
            "account_id": account_id,
            "phone_number": "**********",
            "role": "DOCTOR"
        }


    res = client.post(
        "/admin/partner/staff", json=d4, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text
    # print('ADMIN: ',res.json()[0]['data'])
    assert res.json()[0]['data']['name'] == d4['name']
    assert res.json()[0]['data']['email'] == d4['email']
    assert res.json()[0]['data']['account_id'] == d4['account_id']
    assert res.json()[0]['data']['phone_number'] == d4['phone_number']
    assert res.json()[0]['data']['role'] == d4['role']
    
    staff_id = res.json()[0]['data']['id']
    
    return account_id, staff_id

def test_create_a_single_samplecode(client, access_token_patcher):
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    params = {
        "size": 1
    }
    res = client.post(
        "/samplecodes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert len(res.json().get("data")) == 1
    
    samplecode = res.json().get("data")[0].get("samplecode")
    
    return samplecode