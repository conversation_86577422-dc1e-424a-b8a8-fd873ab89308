
from utils import (
    get_today_date,
    get_today_date_tz,
    get_random_phone_numbers,
    get_payload_register_kit_request,
    get_dna_extraction_payload,
    get_update_sample_payload,
    get_payload_add_samples_to_plate,
    get_fill_samples_into_chip_payload,
    get_payload_to_create_a_batch
    )

from common import (
    test_create_account_clinic,
    test_create_staff_doctor,
    test_create_a_single_samplecode
)

from test_register_new_kit import (
    test_operation_register_kit_B2C_with_n_samplecode,
    test_operation_scan_samplecode
)

from test_upgrade_sample import (
    test_operation_upgrade_kit_w_samplecode,
    test_operation_get_sample_detail_with_samplecode
)

# PMC - Pharmacity

# Register kits -> Barcodes -> Booking
def test_create_barcodes(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    fake_jwt = "1.2.3"
    
    access_token_patcher()

    params = {
        "size": 2,
        "product_code": 25,
        "version": 1
    }
    
    mocked_requests_get_products_list_via_shop()
    
    res_post = client.post(
        f"/codes", params = params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    data =  res_post.json().get("data")
    
    avail_barocdes = [data[0].get('barcode'), data[1].get('barcode') ]
    return avail_barocdes
    
    
    pass

def test_create_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    
    avail_barcodes = test_create_barcodes(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    # Create 2 new sample code
    # 745213005025
    # 047346916325
    
    body = {
    "partner_name": "PMC",
    "customer_name": "Tron Tron Vietnam 3",
    "customer_phone": "**********",
    "payment_amount": "1000000000",
    "payment_method": "Tra Gop",
    "referral_id": "PPPtest",
    "consulted": False,
    "item_list": [
            {
                "product_id": "31dd4d43-a0cd-412d-9057-aa1974c1abb1",
                "barcode": avail_barcodes[0]
            },
            {
                "product_id": "31dd4d43-a0cd-412d-9057-aa1974c1abb1",
                "barcode": avail_barcodes[1]
            }
        ]
    }
    
    fake_jwt = "1.2.3"

    get_token_claims_patcher()
    mock_get_product_w_product_code("25")
    
    mock_send_notification_to_group_partner()
    mock_send_msg_to_queue_partner_booking()
    
    res_post = client.post(
        "/partner/booking", json=body , headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    assert len(res_post.json().get("data").get('item_list')) == 2
    assert res_post.json().get("data").get('customer_name') == "Tron Tron Vietnam 3"
    assert res_post.json().get("data").get('customer_phone') == "**********"
    assert res_post.json().get("data").get('payment_method') == "BANKING"
    assert res_post.json().get("data").get('payment_status') == "PENDING"
    assert res_post.json().get("data").get('status') == "RECEIVED"
    assert res_post.json().get("data").get('item_list')[0].get('product_id') == "31dd4d43-a0cd-412d-9057-aa1974c1abb1"
    assert res_post.json().get("data").get('item_list')[0].get('barcode') in avail_barcodes
    assert res_post.json().get("data").get('item_list')[1].get('barcode') in avail_barcodes
    
    booking_detail = res_post.json().get("data")
    
    return booking_detail.get('id'), booking_detail

def test_get_detail_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    
    id, booking_detail = test_create_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                               
    
    
    fake_jwt = "1.2.3"
    
    res_get = client.get(
        f"/partner/booking/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data").get('id') == id
    assert res_get.json().get("data").get('customer_name') == booking_detail.get('customer_name')
    
    return id, booking_detail


def test_update_detail_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    id, booking_detail = test_get_detail_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                              
    
    
    fake_jwt = "1.2.3"
    
    access_token_patcher()
    get_token_claims_patcher()
    
    booking_detail['customer_name'] = 'Ho Van Nhat'
    
    res_put = client.put(
        f"/internal/partner/booking/{id}", json=booking_detail, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_put.status_code == 200, res_put.text
    assert res_put.json().get("data").get('customer_name') == 'Ho Van Nhat'
    
    
    pass

def test_get_all_partner_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    id_1, booking_detail_1 = test_get_detail_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                              
    id_2, booking_detail_2 = test_get_detail_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                              
    
    bookking_ids = [id_1, id_2]
    
    fake_jwt = "1.2.3"
    
    access_token_patcher()
    get_token_claims_patcher()
    
    params = {
        "page_size": 100,
        "page_number": 1,
        "partner_name": "PMC"
    }
    
    res_get = client.get(
        f"/partner/booking", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("pagination").get('total') == 2
    assert res_get.json().get("data")[0].get('id') in bookking_ids
    assert res_get.json().get("data")[1].get('id') in bookking_ids
    
    return bookking_ids
    
    pass

def test_get_delete_partner_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    
    fake_jwt = "1.2.3"
    
    access_token_patcher()
    get_token_claims_patcher()
    
    bookking_ids = test_get_all_partner_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    id = bookking_ids[0]
    
    res_del = client.delete(
        f"/partner/booking/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_del.status_code == 200, res_del.text
    assert res_del.json().get("data").get('id') == id
    assert res_del.json().get("data").get('deleted_at') is not None
    
    params = {
        "page_size": 100,
        "page_number": 1,
        "partner_name": "PMC"
    }
    
    res_get = client.get(
        f"/partner/booking", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("pagination").get('total') == 1
    assert res_get.json().get("data")[0].get('id') in bookking_ids[-1]
    
    pass


def test_sync_partner_booking_information(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    fake_jwt = "1.2.3"
    
    access_token_patcher()
    get_token_claims_patcher()
    
    
    id, booking_detail = test_get_detail_parter_booking(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    barcode_list = [kit.get('barcode') for kit in booking_detail.get('item_list')]
    
    assert len(barcode_list) == 2
    
    booking_list = [id]
    
    body = {
        "booking_list": booking_list
    }
    
    mock_send_msg_to_queue_partner_booking()
    
    res_post = client.post(
        f"/partner/booking/sync", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    
    
    return id, barcode_list

    pass

# def test_send_booking_results(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    
#     fake_jwt = "1.2.3"
    
#     access_token_patcher()
#     get_token_claims_patcher()
    
#     id, barcode_list = test_sync_partner_booking_information(client, mock_send_notification_to_group_partner, mock_send_msg_to_queue_partner_booking, access_token_patcher, mock_get_all_products, get_token_claims_patcher, mocked_requests_get_products_list_via_shop, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
#     body = {
#         "barcode_list": barcode_list
#     }
    
#     mock_send_notification_to_group_partner()
#     mock_send_msg_to_queue_partner_booking()
    
#     res_post = client.post(
#         f"/partner/booking/results/{id}", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
#     )
    
#     assert res_post.status_code == 200, res_post.text
#     assert res_post.json().get("data") == barcode_list
    
#     pass
