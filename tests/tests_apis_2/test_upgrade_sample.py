from re import T
from utils import (
    get_today_date,
    get_today_date_tz,
    get_random_phone_numbers,
    get_payload_register_kit_request,
    get_dna_extraction_payload,
    get_update_sample_payload,
    get_payload_add_samples_to_plate,
    get_fill_samples_into_chip_payload,
    get_payload_to_create_a_batch,
    get_recollect_sample_payload,
    get_upgrade_kit_payload
    )

from common import (
    test_create_account_clinic,
    test_create_staff_doctor,
    test_create_a_single_samplecode
)

from test_register_new_kit import (
    test_operation_create_samplecode,
    test_operation_check_sample_duplication,
    test_operation_register_kit_B2C_with_samplecode,
    test_operation_get_sample_detail_with_samplecode,
    test_operation_scan_samplecode
)

from test_recollect_sample import *

def test_operation_get_detail_of_origin_kit_via_barcode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                                
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    

    barcode = test_operation_scan_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_get_product_w_product_code)
    
    res_get = client.get(
        f"/kits/registrations/barcode/{barcode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data").get("barcode") == barcode
    
    return barcode, res_get.json().get("data")


def test_operation_upgrade_kit_w_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code, first_kit=None):                                                                
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    product_code = "61"
    product_name = "GeneHealth"
    
    if not first_kit:
        _, first_kit = test_operation_get_detail_of_origin_kit_via_barcode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                                
    subject_id = first_kit.get('subject_id')
    samplecode = first_kit.get('samplecode')
    origin_barcode = first_kit.get('barcode')
    print("upgrading samplecode: ", samplecode)
    
    payload = get_upgrade_kit_payload( product_name=product_name, product_code=product_code, payload=first_kit) # remove barcode from payload
    
    res_post = client.post(
        f"/kits/upgrade/1", json=payload , headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    assert res_post.json().get("data").get("product_code") == product_code
    assert res_post.json().get("data").get("product_name") == product_name
    assert "61" in res_post.json().get("data").get("barcode")
    assert res_post.json().get("data").get("samplecode") == samplecode
    assert res_post.json().get("data").get("subject_id") == subject_id
    
    return res_post.json().get("data").get("barcode"), res_post.json().get("data"), origin_barcode

def test_operation_get_upgraded_kit_detail_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    barcode, detail, origin_barcode = test_operation_upgrade_kit_w_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code, None)
    
    res_get = client.get(
        f"/kits/registrations/barcode/{barcode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data").get("product_code") == detail.get("product_code")
    assert res_get.json().get("data").get("product_name") == detail.get("product_name")
    assert res_get.json().get("data").get("barcode") == barcode
    assert res_get.json().get("data").get("scan_status") == 2
    
    
    res_get_origin_kit = client.get(
        f"/kits/registrations/barcode/{origin_barcode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get_origin_kit.status_code == 200, res_get_origin_kit.text
    assert res_get.json().get("data").get("samplecode") == res_get_origin_kit.json().get("data").get("samplecode")
    assert res_get.json().get("data").get("barcode") != res_get_origin_kit.json().get("data").get("barcode")

    first_kit = res_get_origin_kit.json().get("data")
    return barcode, detail

def test_operation_upgrade_existed_kit(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    _ , first_sample, expected_origin_barcode = test_operation_get_recollected_sample_detail_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                                
    
    res_get = client.get(
        f"/kits/registrations/barcode/{expected_origin_barcode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    
    first_kit = res_get.json().get("data")
    assert first_kit.get("barcode") == expected_origin_barcode
    
    new_barcode, new_kit_detail, origin_barcode = test_operation_upgrade_kit_w_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code, first_kit)
    

    assert expected_origin_barcode == origin_barcode
    assert new_kit_detail.get('samplecode') == first_sample.get('samplecode')
    
    return new_kit_detail.get('subject_id'), new_kit_detail.get('samplecode')
    
    pass

def test_operation_get_all_kits_given_subject(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    subject_id, recollected_samplecode = test_operation_upgrade_existed_kit(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    res_get = client.get(
        f"/kits/all/sub/{subject_id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("pagination").get('total') == 3
    assert res_get.json().get("data")[0].get('subject_id') == subject_id
    assert res_get.json().get("data")[-1].get('samplecode') == recollected_samplecode
    assert res_get.json().get("data")[-1].get('sample_collection_time') == 2