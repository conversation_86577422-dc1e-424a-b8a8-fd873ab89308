from re import T
from utils import (
    get_today_date,
    get_today_date_tz,
    get_random_phone_numbers,
    get_payload_register_kit_request,
    get_dna_extraction_payload,
    get_update_sample_payload,
    get_payload_add_samples_to_plate,
    get_fill_samples_into_chip_payload,
    get_payload_to_create_a_batch,
    get_recollect_sample_payload
    )

from common import (
    test_create_account_clinic,
    test_create_staff_doctor,
    test_create_a_single_samplecode
)

from test_register_new_kit import (
    test_operation_create_samplecode,
    test_operation_check_sample_duplication,
    test_operation_register_kit_B2C_with_samplecode,
    test_operation_get_sample_detail_with_samplecode,
    test_operation_scan_samplecode
)

def test_operation_get_detail_of_recollected_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                                
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    barcode = test_operation_scan_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_get_product_w_product_code)
    
    res_get = client.get(
        f"/kits/registrations/barcode/{barcode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data").get("barcode") == barcode
    
    return barcode, res_get.json().get("data")
    

def test_operation_recollect_sample_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                                
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    barcode, first_kit = test_operation_get_detail_of_recollected_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                                
    subject_id = first_kit.get('subject_id')
    
    res = test_operation_create_samplecode(client, access_token_patcher)
    samplecode = res[0].get('samplecode')
    
    payload = get_recollect_sample_payload(samplecode, first_kit)
    
    res_post = client.post(
        f"/kits/recollection/{barcode}", json=payload, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    assert res_post.json().get("data").get("subject_id") == subject_id
    assert res_post.json().get("data").get("samplecode") == samplecode
    assert res_post.json().get("data").get("sample_receipt_date") == '2024-03-01T14:03:00'
    assert res_post.json().get("data").get("sample_collection_time") == 2
    assert res_post.json().get("data").get("sample_recollection") == True
    assert res_post.json().get("data").get("scan_status") == 0
    
    return samplecode
    pass

def test_operation_scan_samplecode_of_recollected_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code) :
    """
    UAT: Scan "Samplecode" 2 times -> Generate "Barcode"
    """
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    recent_samplecode = test_operation_recollect_sample_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)                                                            
    
    
    res = client.put(
        f"/kits/scan-sample/{recent_samplecode}/1", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res.status_code == 200, res.text
    assert res.json().get("data").get("scan_status") == 1
    assert res.json().get("data").get("samplecode") == recent_samplecode
    
    mock_send_msg_to_queue()
    
    res = client.put(
        f"/kits/scan-sample/{recent_samplecode}/1", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res.status_code == 200, res.text
    assert res.json().get("data").get("scan_status") == 2
    assert res.json().get("data").get("samplecode") == recent_samplecode
    assert "25" in res.json().get("data").get("barcode")
    
    return res.json().get("data").get("barcode"), recent_samplecode

def test_operation_get_recollected_sample_detail_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    barcode, samplecode = test_operation_scan_samplecode_of_recollected_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    res_samplecode, detail = test_operation_get_sample_detail_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, phone_number = "0394363564", email = "", samplecode=samplecode)
    
    assert res_samplecode == samplecode
    # assert detail.get("barcode") == barcode
    assert detail.get("sample_collection_time") == 2
    assert detail.get("scan_status") == 2
    
    return samplecode, detail, barcode

