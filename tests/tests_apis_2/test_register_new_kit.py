

from re import T
from utils import (
    get_today_date,
    get_today_date_tz,
    get_random_phone_numbers,
    get_payload_register_kit_request,
    get_dna_extraction_payload,
    get_update_sample_payload,
    get_payload_add_samples_to_plate,
    get_fill_samples_into_chip_payload,
    get_payload_to_create_a_batch
    )

from common import (
    test_create_account_clinic,
    test_create_staff_doctor,
    test_create_a_single_samplecode
)

def test_operation_create_samplecode(client, access_token_patcher):
    """
    When an user attempts to create a code with correct request body,
    200 Ok response should be returned to the user
    """
    fake_jwt = "1.2.3"

    access_token_patcher()
    params = {
        "size": 5
    }
    res = client.post(
        "/samplecodes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert len(res.json().get("data")) == 5
    
    return res.json().get("data")

def test_operation_check_sample_duplication(client, access_token_patcher):
    """
    UAT:  Check "Sample Duplication" with name, account_id, gender & dob
    """
    fake_jwt = "1.2.3"

    access_token_patcher()
    params = {
        "name": "D B S",
        "account_id": "c1e43ae4-a8f1-40a2-8e17-7b58e461a6cb",
        "gender": "female",
        "dob": "2002-08-08"
    }
    
    res = client.get(
        "/kits/check_by_sample", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert len(res.json().get("data")) == 0
    pass

def test_operation_register_kit_B2C_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, phone_number = "**********", email = ""):
    """
    UAT: Register Kit by filling form "Sample" with Samplecode & other information
    
    Duration: 14 days
    """
    fake_jwt = "1.2.3"

    access_token_patcher()
    
      
    samplecode = test_create_a_single_samplecode(client, access_token_patcher)

    account_id, staff_id = test_create_staff_doctor(client, access_token_patcher)
    
    product_name="GENE-LIFE PREMIUM"
    product_code="25"

    body = get_payload_register_kit_request(
        phone_number=phone_number,
        validate_account=True,
        samplecode=samplecode, 
        account_id=account_id, 
        staff_id=staff_id,
        product_name=product_name, 
        product_code=product_code
    )

    # required to validate user when it comes to B2C
    mock_get_product_w_product_code(product_code="25")
    mock_validate_user_requests(phone_number=phone_number, email=email)
    
    res_post = client.post(
        "/kits/registrations", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    # print("data: ", res_post.json().get("data"))
    assert res_post.status_code == 200, res_post.text
    assert res_post.json().get("data").get("phone_number") == phone_number
    assert res_post.json().get("data").get("samplecode") == samplecode
    assert res_post.json().get("data").get("product_name") == product_name
    assert res_post.json().get("data").get("product_code") == product_code
    
    return samplecode

def test_operation_get_sample_detail_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, phone_number = "**********", email = "", samplecode=""):
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    if not samplecode:
        samplecode = test_operation_register_kit_B2C_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, phone_number, email)
    
    res_get = client.get(
        f"/kits/registrations/sample/{samplecode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data").get("samplecode") == samplecode
    assert res_get.json().get("data").get("subject_id") not in [None, ""]
    
    detail = res_get.json().get("data")
    return samplecode, detail
    
    pass

# def test_operation_successfully_update_sample_infor(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, phone_number = "**********", email = ""):
#     """
#     phone_number: VALID -- validate_account True
#     subject_id: VALID <=> Subject does exist
#     samplecode: VALID <=> Sample does exist
#     sample_collection_date -- required
#     sample_receipt_date -- required
#     expected_report_release_date -- required
#     lab_receipt_date -- optional
#     """
    
#     fake_jwt = "1.2.3"

#     access_token_patcher()
    
#     samplecode, detail = test_operation_get_sample_detail_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, phone_number, email, "")
    

#     body = get_update_sample_payload(
#         detail,
#         sample_collection_date="2024-02-26", 
#         sample_receipt_date="2024-02-26T14:03:00")
    
#     mock_get_product_w_product_code(product_code="25")
#     res_put = client.put(
#         f"/kits/registrations/{samplecode}", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
#     )
    
#     assert res_put.status_code == 200, res_put.text
#     assert res_put.json().get("data").get("samplecode") == samplecode
    
#     pass
    
def test_operation_register_kit_B2C_with_n_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, number = 10):
    """
    UAT: Register Kit by filling form "Sample" with Samplecode & other information
    
    Duration: 14 days
    """
    fake_jwt = "1.2.3"

    access_token_patcher()

    account_id, staff_id = test_create_staff_doctor(client, access_token_patcher)
    
    product_name="GENE-LIFE PREMIUM"
    product_code="25"
    
    phone_numbers = get_random_phone_numbers(n=number)
    for phone_number in phone_numbers:
    
        samplecode = test_create_a_single_samplecode(client, access_token_patcher)

        body = get_payload_register_kit_request(
            phone_number=phone_number,
            validate_account=True,
            samplecode=samplecode, 
            account_id=account_id, 
            staff_id=staff_id,
            product_name=product_name,
            product_code=product_code
        )

        # required to validate user when it comes to B2C
        mock_validate_user_requests(phone_number=phone_number, email="")
        mock_get_product_w_product_code(product_code="25")
        
        res_post = client.post(
            "/kits/registrations", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print("data: ", res_post.json().get("data"))
        assert res_post.status_code == 200, res_post.text
        assert res_post.json().get("data").get("phone_number") == phone_number
        assert res_post.json().get("data").get("samplecode") == samplecode
        assert res_post.json().get("data").get("product_name") == product_name
        assert res_post.json().get("data").get("product_code") == product_code

def test_operation_listing_samples_with_samplecodes(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, number=10):
    """
    UAT: List recent "Sample(s)" with Samplecode(s) & other information
    """
    number=10
    test_operation_register_kit_B2C_with_n_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, number)
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    params = {
        "page_number": 1,
        "page_size": 25,
        "include_deleted": True
    }
    
    res = client.get(
        "/kits/all", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert len(res.json().get("data")) >= number
    
    return res

def test_operation_scan_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_get_product_w_product_code):
    """
    UAT: Scan "Samplecode" 2 times -> Generate "Barcode"
    """
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    res = test_operation_listing_samples_with_samplecodes(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, number=10)
     
    recent_kit = res.json().get("data")[0]
    recent_samplecode = recent_kit.get("samplecode")
    
    res = client.put(
        f"/kits/scan-sample/{recent_samplecode}/1", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res.status_code == 200, res.text
    assert res.json().get("data").get("scan_status") == 1
    
    mock_send_msg_to_queue()
    
    res = client.put(
        f"/kits/scan-sample/{recent_samplecode}/1", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res.status_code == 200, res.text
    assert res.json().get("data").get("scan_status") == 2
    assert "25" in res.json().get("data").get("barcode")
    
    return res.json().get("data").get("barcode")


def test_operation_send_a_single_sample_to_lab(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_product_w_product_code):
    """
    UAT: Send (selected) "samples" -> LIMS -> Change their current_status
    """
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    barcode = test_operation_scan_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_get_product_w_product_code)
    lab_receipt_date = get_today_date_tz()
    
    body = {
        "kit_list": [
            barcode
        ],
        "lab_receipt_date": lab_receipt_date
    }
     
    mock_send_msg_to_queue()
    mock_send_notification_to_group()
    
    res = client.post(
        "/kits/send-to-lab", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res.status_code == 200, res.text
    assert res.json().get("data").get("updated_kits") == [barcode]
    
    return barcode
    
    
    pass

def test_lims_gate_1_list_all_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    """
    UAT: Listing all barcode with appropriate status at GATE 1 - e.g LAB_CHECK
    """
    
    fake_jwt = "1.2.3"

    access_token_patcher()
       
    barcode = test_operation_send_a_single_sample_to_lab(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_product_w_product_code)
    
    
    params = {
        "page_number": 1,
        "page_size": 25
    }
    
    
    mock_get_all_products()
    
    res = client.get(
        "/lims/lab_sample/samples/lab_check", params = params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res.status_code == 200, res.text
    assert res.json().get("data")[0].get("barcode") == barcode
    assert res.json().get("data")[0].get("current_status") == "LAB_CHECK"
    
    return barcode
    


def test_lims_gate_1_scan_barcode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    """
    UAT: Scan barcode at GATE 1 to verify information - change "LAB_CHECK" -> "PASSED_LAB_CHECK"
    """
    
    barcode = test_lims_gate_1_list_all_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    mock_get_technology_w_product_code(product_code=25)
    
    body = {
        "barcode": barcode,
        "status": "PASSED_LAB_CHECK",
        "note": "",
    }
    
    res = client.post(
        "/lims/lab_sample/status", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res.status_code == 200, res.text
    assert res.json().get("data") == "lab sample status updated"
    
    params = {
        "page_number": 1,
        "page_size": 25,
        "include_deleted": True
    }
    
    res = client.get(
        "/kits/all", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get("data")[0].get("current_status") == "PASSED_LAB_CHECK"
    assert res.json().get("data")[0].get("barcode") == barcode
    
    return res.json().get("data")[0], barcode
     
    # recent_kit = res.json().get("data")[0]
    # recent_sample_status = recent_kit.get("current_status")
    # recent_sample_barcode = recent_kit.get("barcode")
    
    # assert recent_sample_barcode == barcode
    # assert recent_sample_status == "PASSED_LAB_CHECK"

def test_lims_gate_3_list_all_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    """
    UAT: List all SAMPLES with DNA information
    """
    
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    _, barcode = test_lims_gate_1_scan_barcode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    
    params = {
        "page_number": 1,
        "page_size": 25,
        "lid": "",
        "order_by": "lid",
        "order_option": "desc",
        "is_all": True
    }
    
    res = client.get(
        "/lims/lab_sample/checked", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get("data")[0].get("barcode") == barcode
    assert res.json().get("data")[0].get("current_status") == "PASSED_LAB_CHECK"
    assert res.json().get("data")[0].get("lid") not in [None, ""]
    
    lid = res.json().get("data")[0].get("lid")
    
    return lid, barcode

def test_lims_dna_extraction_import(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    """
    UAT: Import DNA Extraction for "PASSED_LAB_CHECK" sample at GATE 3 
    """
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    lid, barcode = test_lims_gate_3_list_all_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    body = get_dna_extraction_payload([lid])
    
    res = client.post(
        "/lims/dna_extractions", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json()[0].get("data")[0].get("lid") == lid
    assert res.json()[0].get("data")[0].get("dna_qc_status") == "PASS"
    assert res.json()[0].get("data")[0].get("agarose_gel") == "PASS"
    
    return lid, barcode

def test_lims_dna_qc_total_list_all(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    """
    UAT: List all SAMPLES with DNA information
    """
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    params = {
        "page_size": 25,
        "page_number": 1,
        "lid": "",
        "dna_qc_status": "ALL",
        "export": True,
        "order_by": "lid",
        "order_option": "desc"
        
    }
    
    lid, barcode = test_lims_dna_extraction_import(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    res = client.get(
        "/lims/sample_management/available", params = params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get("data")[0].get("lid") == lid
    assert res.json().get("data")[0].get("barcode") == barcode
    
    return lid, barcode


def test_lims_gate_4_add_a_positive_control_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    lid, barcode = test_lims_dna_qc_total_list_all(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    res = client.put(
        f"/lims/lab_sample/positive_control/add/{lid}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get("data").get("__values__").get("lid") == lid
    
    return lid
    
    pass    

def test_lims_gate_4_list_all_positive_control_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    lid = test_lims_gate_4_add_a_positive_control_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    params = {
        "page_size": 30,
        "page_number": 1,
        "barcode": "",
        "positive_control": True,
        "order_by": "lid",
        "order_option": "desc",
        "is_all": True
        
    }
    
    res = client.get(
        "/lims/lab_sample/checked", params = params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get("data")[0].get("lid") == lid
    
    return lid
    

# MICROARRAY

def test_lims_listing_all_dna_microarray_extractions(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    lid_pc = test_lims_gate_4_list_all_positive_control_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    recent_lid, recent_barcode = test_lims_dna_qc_total_list_all(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    params = {
        "page_size": 100,
        "page_number": 1,
        "lid": "",
        "technology": "MICROARRAY",
        "order_by": "well_position_column",
        "order_option": "asc",
        "export": True,
        
    }
    
    res_get = client.get(
        "/lims/sample_management/available", params = params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res_get.status_code == 200, res_get.text
    samples = res_get.json().get("data")
    assert samples[0].get("lid") == lid_pc
    assert samples[1].get("lid") == recent_lid
    
    return [samples[0].get("lid"), samples[1].get("lid")], [samples[0].get("dna_extraction_id"), samples[1].get("dna_extraction_id")]
    pass



# def test_lims_listing_all_dna_pcr_extractions(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests):
    
#     pass

def test_lims_create_a_new_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    type="microarray"
    
    res_post = client.post(
        f"/lims/plate/{type}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    assert res_post.json()[0].get("data").get("status") == "CREATED"
    assert res_post.json()[0].get("data").get("type").lower() == type
    
    plate_name = res_post.json()[0].get("data").get("name")
    
    return plate_name
    
    pass


def test_get_all_microarray_plates(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    type = "microarray"
    
    plate_name = test_lims_create_a_new_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    
    params = {
        "status": "CREATED,PREPARING,RUNNING,COMPLETED",
        "page_size": 100
    }
    
    res_get = client.get(
        f"/lims/plates/{type}", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("pagination").get("total") == 1
    assert res_get.json().get("data")[0].get("name") == plate_name
    
    pass


def test_list_samples_to_add_to_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    type = "MICROARRAY"
    lids, dna_extraction_ids = test_lims_listing_all_dna_microarray_extractions(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    params = {
        "page_size": 100,
        "page_number": 1,
        "technology": type,
        "order_by": "lid",
        "is_not_filled_into_chips": True,
        "order_option": "asc",
        "plate_name_null_only": True
    }
    
    res_get = client.get(
        "/lims/sample_management/available", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    
    total = res_get.json().get("pagination").get("total")
    for idx in range(total):
        assert res_get.json().get("data")[idx].get("lid") in lids
    
    return lids, dna_extraction_ids

def test_lims_add_samples_to_plate_w_positive_control_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    lids, dna_extraction_ids = test_list_samples_to_add_to_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    plate_name = test_lims_create_a_new_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    body = get_payload_add_samples_to_plate(lids, dna_extraction_ids, plate_name)
    
    res_put = client.put(
        f"/lims/sample_mappings/plate/microarray/{plate_name}/pc", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_put.status_code == 200, res_put.text
    
    return plate_name, lids[0]
    pass


# def test_lims_update_samples_in_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests):
    
#     pass

def test_lims_get_all_samples_in_a_specific_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    plate_name, pc_sample  = test_lims_add_samples_to_plate_w_positive_control_sample(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    type = "MICROARRAY"
    params = {
        "page_size": 100,
        "page_number": 1,
        "lid": "",
        "technology": type,
        "order_by": "well_position_column",
        "order_option": "asc",
        "plate_name": plate_name,
        "export": True
        
    }
    
    res_get = client.get(
        "/lims/sample_management/available", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("detail").get("pc_sample") == pc_sample
    assert res_get.json().get("data")[0].get("well_position") == "A1"
    assert res_get.json().get("data")[0].get("plate_name") == plate_name
    assert res_get.json().get("data")[0].get("physical_position") is None
    assert res_get.json().get("data")[0].get("plate_status") == 'PREPARING'
    assert res_get.json().get("data")[-1].get("well_position") == "H12"
    assert res_get.json().get("data")[-1].get("lid") == pc_sample
    
    return plate_name
    
    pass

def calculate_chips_for_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    plate_name = test_lims_get_all_samples_in_a_specific_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    params = {
        "chip_type": "PMDA"
    }
    
    res_get = client.get(
        f"/lims/sample_mappings/plate/add/microarray/{plate_name}", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data").get("number_of_chips") == 1
    
    return plate_name, 1
    
    pass

def test_lims_fill_samples_into_chips(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    plate_name, _ = calculate_chips_for_plate(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    CHIP_ID = "000111000111000111"
    type="PMDA"
    
    body = get_fill_samples_into_chip_payload(plate_name, CHIP_ID, type)
    
    res_post = client.post(
        "/lims/sample_mappings/plate/fill/microarray", json = body, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    assert res_post.json().get("data")[0].get("position") == "A1"
    assert res_post.json().get("data")[0].get("well_position") == "A1"
    assert res_post.json().get("data")[0].get("plate_name") == plate_name
    assert res_post.json().get("data")[0].get("chip_id") == CHIP_ID
    assert res_post.json().get("data")[-1].get("position") == "H12"
    assert res_post.json().get("data")[-1].get("well_position") == "H12"
    assert res_post.json().get("data")[-1].get("plate_name") == plate_name
    assert res_post.json().get("data")[-1].get("chip_id") == CHIP_ID
    
    # print("Fill Chip payload: ", res_post.json())
    # plate_id
    plate_id = res_post.json().get("data")[0].get("plate_id")
    
    return plate_name, plate_id, CHIP_ID
    
    pass

def test_lims_get_next_batch(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    type = "microarray"
    
    res_get = client.get(
        f"/lims/batch/next_batch_number/{type}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data") == "1"
    
    batch_number = res_get.json().get("data")
    
    return batch_number

def test_lims_create_a_new_batch(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    batch_number = test_lims_get_next_batch(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    plate_name, plate_id, CHIP_ID = test_lims_fill_samples_into_chips(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    assert batch_number == plate_name
    
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    type = "microarray"
    body = get_payload_to_create_a_batch(batch_number, plate_name)
    
    res_post = client.post(
        f"/lims/batch/{type}", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    # print("res batch: ", res_post.json())
    assert res_post.json().get("data")[0].get("id") == int(batch_number)
    assert res_post.json().get("data")[0].get("plate_id") == plate_id
    assert res_post.json().get("data")[0].get("wetlab_date") is None
    assert res_post.json().get("data")[0].get("drylab_date") is None
    
    return int(batch_number), plate_id, CHIP_ID

def test_lims_all_samples_in_a_specific_batch(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    """
    Before run "Wet Lab"
    """
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    type = "MICROARRAY"
    batch_number, plate_id, CHIP_ID = test_lims_create_a_new_batch(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    params = {
        "page_size": 96,
        "page_number": 1,
        "lid": "",
        "export": True,
        "order_by": "lid",
        "order_option": "desc",
        "technology": type,
        "plate_name": batch_number,
        "is_added_to_batch": True
        
    }
    
    res_get = client.get(
        "/lims/sample_management/available", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data")[0].get("well_position") == "A1"
    assert res_get.json().get("data")[0].get("physical_position") == "A1"
    assert res_get.json().get("data")[0].get("plate_name") == str(batch_number)
    assert res_get.json().get("data")[0].get("batch_barcode") == str(batch_number)
    assert res_get.json().get("data")[0].get("plate_status") == "PREPARING"
    assert res_get.json().get("data")[0].get("batch_id") is not None
    assert res_get.json().get("data")[0].get("chip_id") == CHIP_ID
    assert res_get.json().get("data")[0].get("technology") == type
    
    batch_id = res_get.json().get("data")[0].get("batch_id")
    
    return int(batch_number), batch_id
    pass

def test_lims_get_all_batches(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    batch_number, batch_id = test_lims_all_samples_in_a_specific_batch(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    type = "microarray"
    
    params = {
        "page_size": 100,
        "page_number": 1
    }
    
    res_get = client.get(
        "/lims/batch/microarray", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("data")[0].get("number") == batch_number
    assert res_get.json().get("data")[0].get("type") == type.upper()
    assert res_get.json().get("data")[0].get("status") == "PREPARING"
    assert res_get.json().get("data")[0].get("id") == batch_id
    
    return int(batch_number), batch_id
    pass

def test_lims_run_wetlab_on_a_specific_batch(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):
    
    batch_number, batch_id = test_lims_get_all_batches(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    type = "microarray"
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    wetlab_date = get_today_date()  
    
    body = {
        "wetlab_date": wetlab_date
    }
    
    res_post = client.post(
        f"/lims/batch/wetlab/{type}/{batch_number}", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_post.status_code == 200, res_post.text
    assert res_post.json().get("data")[0].get("id") == int(batch_number)
    assert res_post.json().get("data")[0].get("batch_id") == str(batch_id)
    assert res_post.json().get("data")[0].get("wetlab_date").split('T')[0] == wetlab_date
    pass 






