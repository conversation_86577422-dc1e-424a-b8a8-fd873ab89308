
from utils import (
    get_today_date,
    get_today_date_tz,
    get_random_phone_numbers,
    get_payload_register_kit_request,
    get_dna_extraction_payload,
    get_update_sample_payload,
    get_payload_add_samples_to_plate,
    get_fill_samples_into_chip_payload,
    get_payload_to_create_a_batch
    )

from common import (
    test_create_account_clinic,
    test_create_staff_doctor,
    test_create_a_single_samplecode
)

from test_register_new_kit import (
    test_operation_register_kit_B2C_with_n_samplecode,
    test_operation_scan_samplecode
)

from test_upgrade_sample import (
    test_operation_upgrade_kit_w_samplecode,
    test_operation_get_sample_detail_with_samplecode
)

import time

def test_get_all_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code, number=10):                                                               
    
    number = 10
    scanned_barcode = test_operation_scan_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_get_product_w_product_code)
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    res_get = client.get(
        "/samples/all", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("pagination").get("total") == number
    
    # Check is barcode exist 
    
    expected_origin_barcode = scanned_barcode
    
    res_get_origin_kit = client.get(
        f"/kits/registrations/barcode/{expected_origin_barcode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get_origin_kit.status_code == 200, res_get_origin_kit.text
    
    first_kit = res_get_origin_kit.json().get("data")
    expected_origin_samplecode = first_kit.get('samplecode')
    assert first_kit.get("barcode") == expected_origin_barcode
    subject_id = first_kit.get('subject_id')
    
    new_barcode, new_kit_detail, origin_barcode = test_operation_upgrade_kit_w_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code, first_kit)
    
    print("expected_origin_samplecode: ", expected_origin_samplecode)

    assert expected_origin_barcode == origin_barcode
    assert new_kit_detail.get('samplecode') == expected_origin_samplecode
    
    
    # print("new_kit_detail: ",new_kit_detail)
    
    params = {
        "page_number": 1,
        "page_size": 25,
        "include_deleted": True
    }
    
    res_get_2 = client.get(
        "/samples/all", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    # print("res_get_2: ", res_get_2.json().get("data"))
    
    assert res_get_2.status_code == 200, res_get_2.text
    assert res_get_2.json().get("pagination").get("total") == number
    # assert res_get_2.json().get("data")[0].get('barcode') == new_barcode
    
    # res_get_all_kit = client.get(
    #     f"/kits/all/sub/{subject_id}", headers={"Authorization": f"bearer {fake_jwt}"}
    # )
    
    # assert res_get_all_kit.status_code == 200, res_get_2.text
    
    # kits = res_get_all_kit.json().get("data")
    
    # print("all kits: ", kits)
    
    params = {
        "samplecode": expected_origin_samplecode
    }
    
    res_get_3 = client.get(
        "/samples/all", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    

    # print("res_get_3: ",res_get_3.json().get("data"))
    assert res_get_3.status_code == 200, res_get_3.text
    assert res_get_3.json().get("pagination").get("total") == 1
    assert res_get_3.json().get("data")[0].get('samplecode') == expected_origin_samplecode
    assert res_get_3.json().get("data")[0].get('barcode') == new_barcode
    assert res_get_3.json().get("data")[0].get('barcode') != expected_origin_barcode
    
    return new_barcode, expected_origin_samplecode, expected_origin_barcode, first_kit.get("subject_id")
    
   
    

def test_get_kit_detail_via_kit_uuid(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    """
    
    kit_uuid: returned after "Register Kit" or get "Sample Detail"
    
    """
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    
    number = 10
    scanned_barcode = test_operation_scan_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_get_product_w_product_code)
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    res_get = client.get(
        "/samples/all", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get("pagination").get("total") == number
    
    # RANDOM PICK subject_id
    
    kit_uuid = res_get.json().get("data")[1].get("kit_uuid")
    samplecode = res_get.json().get("data")[1].get("samplecode")
    
    res_get_kit = client.get(
        f"/kits/registrations/uuid/{kit_uuid}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get_kit.status_code == 200, res_get_kit.text
    assert res_get_kit.json().get('data').get('kit_uuid') == kit_uuid
    assert res_get_kit.json().get('data').get('samplecode') == samplecode
    
    return kit_uuid, samplecode, res_get_kit.json().get('data')
    
    

def test_update_kit_detail_via_kit_uuid(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    kit_uuid, samplecode, kit_detail = test_get_kit_detail_via_kit_uuid(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code)
    
    kit_detail.pop("full_name")
    kit_detail["name"] = "N V E 40"
    
    print("kit_detail: ",kit_detail)
    
    res_put = client.put(
        f"/kits/registrations/uuid/{kit_uuid}", json = kit_detail, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_put.status_code == 200, res_put.text
    assert res_put.json().get('data').get('name') == "N V E 40"
    
    pass

def test_update_kit_detail_via_barcode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    
    scanned_barcode = test_operation_scan_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_get_product_w_product_code)
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    res_get = client.get(
        f"/kits/registrations/barcode/{scanned_barcode}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get('data').get('barcode') == scanned_barcode
    
    kit_detail = res_get.json().get('data')
    kit_detail['name'] = "N V E 9"
    kit_detail.pop('full_name')
    
    res_put = client.put(
        f"/kits/registrations/{scanned_barcode}", json=kit_detail, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_put.status_code == 200, res_put.text
    assert res_put.json().get('data').get('name') == "N V E 9"
    assert res_put.json().get('data').get('barcode') == scanned_barcode
    
    pass

def test_update_sample_detail_via_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    samplecode, sample_detail = test_operation_get_sample_detail_with_samplecode(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_get_product_w_product_code, phone_number = "0394363564", email = "", samplecode="")
    
    # assert sample_detail['sample_collection_date'] != '2024-03-12'
    assert sample_detail['sample_receipt_date'] != '2024-03-12T14:03:00'
    
    # sample_detail['sample_collection_date'] = '2024-03-12'
    sample_detail['sample_receipt_date'] = '2024-03-12T14:03:00'
    sample_detail['name'] = "N V E 19"
    sample_detail.pop('full_name')
    
    res_put = client.put(
        f"/kits/registrations/sample/{samplecode}", json=sample_detail, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_put.status_code == 200, res_put.text
    assert res_put.json().get('data').get('samplecode') == samplecode
    assert res_put.json().get('data').get('name') == "N V E 19"
    assert res_put.json().get('data').get('sample_receipt_date') == "2024-03-12T14:03:00"
     



def test_get_all_kits_of_subject_via_subject_id(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code):                                                               
    
    fake_jwt = "1.2.3"

    access_token_patcher()
    
    new_barcode, expected_origin_samplecode, expected_origin_barcode, subject_id = test_get_all_samples(client, access_token_patcher, mock_get_all_products, mock_validate_user_requests, mock_send_msg_to_queue, mock_send_notification_to_group, mock_get_technology_w_product_code, mock_get_product_w_product_code, number=10)
        
    res_get = client.get(
        f"/kits/all/sub/{subject_id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    
    assert res_get.status_code == 200, res_get.text
    assert res_get.json().get('pagination').get('total') == 2
    assert res_get.json().get('data')[0].get('barcode') == expected_origin_barcode
    assert res_get.json().get('data')[0].get('samplecode') == expected_origin_samplecode
    assert res_get.json().get('data')[1].get('barcode') == new_barcode
    assert res_get.json().get('data')[1].get('samplecode') == expected_origin_samplecode
    pass