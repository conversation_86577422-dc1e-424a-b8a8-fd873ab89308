import asyncio
import copy
import os
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import requests
from starlette.config import environ
from starlette.testclient import TestClient

# Set OPERATION_CONFIG_PATH *before* loading the configuration
CURRENT_DIR = os.path.dirname(os.path.realpath(__file__))
environ["OPERATION_CONFIG_PATH"] = os.path.join(
    CURRENT_DIR, "test-operation-config.yaml"
)
import subprocess

from test_tools.fixtures.common import *
from test_tools.stubber_factory import stubber_factory

from src.operation.app import app_init
from src.operation.config import config

environ["OPERATION_TEST_RESTORE_BASH"] = os.path.join(CURRENT_DIR, "restore.sh")
environ["OPERATION_TEST_CREATE_DB_BASH"] = os.path.join(CURRENT_DIR, "create_db.sh")
environ["OPERATION_TEST_CREATE_EXTENSION_BASH"] = os.path.join(
    CURRENT_DIR, "create_extension.sh"
)

environ["OPERATION_TEST_DROP_DB_BASH"] = os.path.join(CURRENT_DIR, "drop_db.sh")


PRODUCT_LIST_VIA_SHOP = [
    {
        "id": "31dd4d43-a0cd-412d-9057-aa1974c1abb1",
        "name": "GENE-LIFE PREMIUM",
        "description": "<p><strong>Làm chủ sức khỏe. Nâng tầm cuộc sống</strong></p><ul><li>Giải mã gen cao cấp dành cho người lớn</li><li>230+ sự thật từ gen</li><li>Tư vấn 1-1 cùng bác sĩ di truyền</li></ul>",
        "thumbnail": "https://qa.genestory.ai/api/uploader/s3upload?key=vingentest-qa/shop/1707101707858_GENE-LIFE Premium.png",
        "content": '{"vi":{"sampleReport":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/06022024/MaleDemo25_789122254025_genestory_book (3).pdf","kitInfo":{"image":"https://qa.genestory.ai/api/uploader/s3upload?key=vingentest-qa/shop/1707104562572_GENE-LIFE Premium.png","priceDesc":"","originPrice":"10.000.000 đ","price":"","buttons":[{"title":"Mua ngay"},{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124624674_buy-now-ic.svg"}],"shortDesc":"Làm chủ sức khỏe. Nâng tầm cuộc sống","charactics":[{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124411345_100-charactics.svg","desc":"230+ sự thật từ gen"},{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124445933_gene-icon.svg","desc":"Giải mã gen cao cấp dành cho người lớn"},{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639472352046_advise-doctor.svg","desc":"Tư vấn 1-1 cùng bác sĩ di truyền"}]},"highlights":{"sampleReportFileUrl":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/skin_report.pdf","heading":"Báo cáo nổi bật của<br /> sản phẩm!","headerImage":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","list":[{"title":"Chủ động sức khoẻ","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>Đánh giá nguy cơ bệnh (ung thư, tim mạch, chuyển hóa) qua điểm số nguy cơ đa gen và nguy cơ trọn đời theo độ tuổi.\\n<li>Bao gồm ung thư vú, ung thư đại trực tràng; tăng huyết áp, rung tâm nhĩ, bệnh động mạch vành; tiểu đường type 2.\\n<li>Khuyến nghị lối sống và tầm soát định kỳ từ chuyên gia.\\n</li></ul>","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"title":"Tương tác thuốc","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>Cách cơ thể chuyển hóa 80 loại thuốc, ảnh hưởng đến khả năng đáp ứng thuốc hoặc xuất hiện phản ứng có hại.</li><li>Bao gồm khả năng đáp ứng với các thuốc điều trị và hỗ trợ điều trị COVID-19.</li></ul>","subtitle":"","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/19_04_2022/trait+-+adult+-+19_04_22.pdf","title":"Thể chất - Dinh dưỡng - Lối sống","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>Xu hướng béo phì, sức mạnh cơ bắp, thói quen ăn vặt</li><li>Chất lượng giấc ngủ, lệ thuộc thuốc lá, nhạy cảm với cơn đau</li><li>Khả năng tiêu hóa - hấp thụ sữa động vật, trà, cafe</li></ul>","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/19_04_2022/sample+report+-+skin+-+adult+-+19_04_22.pdf","title":"Sức khỏe làn da","image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>25 chỉ số về sức khỏe làn da.</li><li>Xu hướng lão hóa: Nếp nhăn, rạn da, suy thoái collagen, đường có hại gây lão hóa da…</li><li>Sức khỏe làn da: Da mất nước, mụn trứng cá, độ nhạy cảm…</li><li>Sắc tố da và 10 vi chất nuôi dưỡng</li></ul>","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/1_12_2022/adult_mental_big5_122022_short.pdf","title":"Chăm sóc sức khoẻ tinh thần","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>Chỉ số hạnh phúc, lo âu, khả năng quản trị cảm xúc, hồi phục tinh thần.</li><li>Giải thích tính cách và hành vi từ gen.</li><li>Ứng dụng Mô hình tính cách BIG 5 trong tuyển dụng nhân sự, định hướng sự nghiệp.</li></ul>","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]}]},"services":{"heading":"Gói dịch vụ giải mã GeneStory","products":[{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GENE-LIFE Standard","desc":"80+ đặc điểm cá nhân","productId":"c315a925-f3aa-4640-a549-de075007a524","price":"8.000.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GENE-LIFE Premium","desc":"120+ đặc điểm cá nhân","productId":"dbebc118-d35e-4fba-8c85-06da33aa9613","price":"10.000.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]}]},"partners":{"desc":"(bao gồm đối tác kinh doanh, phát triển sản phẩm và đối tác thông qua các dự án nghiên cứu thuộc Trung tâm Tin y sinh, Viện nghiên cứu dữ liệu lớn)","heading":"Đối tác của chúng tôi","images":["https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430563932_Allelica.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430574669_Amazon Web Services.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430581386_Bioinformatics Institute of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430592077_Biomedic.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430601176_Department of Biomedical Engineering, John Hopskin University, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430615246_Institute for Molecular Bioscience, University of Queensland, Australia.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430635796_Mahidol University, Thailand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430653598_Nalagenetics.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430666295_Nanyang Technological University, Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430678615_National University of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430690735_Swiss Institute of Bioinformatics (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430710893_University of California, San Diego.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430734357_University of Patras, Greece (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430748022_University of Patras, Greece.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430772191_UPMC Hillman Cancer Center, University of Pittsburgh, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430785162_Victoria University of Wellington, New Zealand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430793692_Vinmec.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447396688_ĐH Bách Khoa Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447405110_ĐH Công nghệ, ĐH QG Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447414646_ĐH Dược Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447424035_ĐH Quốc tế, ĐH QG TP HCM.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447433724_ĐH Y Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447443421_HV Quân Y.png"]},"props":{"heading":"Đơn giản và<br /> dễ sử dụng!","propsList":[{"description":"Tư vấn và lựa chọn gói dịch vụ giải mã gen. Nhận bộ kit DNA tại nhà.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786799161_atHome.png","title":"Tại nhà"},{"description":"Lấy DNA từ mẫu nước bọt, không đau, không xâm lấn. Gửi mẫu DNA đi phân tích.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786883452_clock.png","title":"1 Phút"},{"description":"Kết quả giải mã gen sẽ được trả cho khách hàng sau khoảng 14 ngày làm việc kể từ khi phòng lab nhận được mẫu, ngoại trừ các trường hợp bất khả kháng. Vui lòng truy cập tài khoản trên Website/App GeneStory để đọc kết quả.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786908471_truck.png","title":"14 Ngày"}],"videoUrl":"https://www.youtube.com/embed/GIRwCVCkYGg"}},"en":{"kitInfo":{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650427089483_product-detail.png","priceDesc":"","originPrice":"10.000.000 đ","price":"","buttons":[{"title":"BUY NOW\\n"},{"title":"Add to cart","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124624674_buy-now-ic.svg"}],"shortDesc":"Live life to the fullest","charactics":[{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124411345_100-charactics.svg","desc":"120+ personal traits"},{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124445933_gene-icon.svg","desc":"Unlock your genome for life"},{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639472352046_advise-doctor.svg","desc":"1-1 consultation with experts"}]},"highlights":{"sampleReportFileUrl":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/20_7_2022/20220720_GenestoryBook_Eng_v2.pdf","heading":"Feature reports","headerImage":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","list":[{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/20_7_2022/20220720_GenestoryBook_Eng_v2.pdf","title":"Stay Healthy Actively\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>Assess health risk (cancer, cardiovascular) by polygenic risk score and lifetime risk by age.</li><li>Includes breast cancers, colorectal cancers, prostate cancers; atrial fibrillation, and Coronary Artery Disease (CAD).\\n</li><li>Lifestyle recommendations and routine screenings from experts.</li></ul>","buttons":[{"title":"CONSULT NOW\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Sample Report\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/20_7_2022/20220720_GenestoryBook_Eng_v2.pdf","title":"Drug Response\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>How your body absorbs 80 drugs (pharmacogenetics) which affects your ability to respond or have side effects to a drug\\n.</li><li>How the body responds to COVID-19 treatment support medicines.</li></ul>","subtitle":"","buttons":[{"title":"CONSULT NOW\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Sample Report\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/20_7_2022/20220720_GenestoryBook_Eng_v2.pdf","title":"Physical traits. Nutrition. Lifestyle\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>Obesity risk, muscle strength, junk-food addiction.</li><li>Sleep depth, smoking addiction, pain sensitivity.</li><li>Metabolism, food digestion and absorption (milk, tea, coffee).</li></ul>","buttons":[{"title":"CONSULT NOW\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Sample Report\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/20_7_2022/20220720_GenestoryBook_Eng_v2.pdf","title":"Skin Health\\n","image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>25 reports on skin health.</li><li>Aging tendency: wrinkles, stretches, collagen decrease, skin aging glycation...</li><li>Skin dehydration, acnes, sensitivity…</li><li>Skin pigmentation and 10 vitamins need</li></ul>","buttons":[{"title":"CONSULT NOW\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Sample Report\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"hyperLink":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/sample_pdf/20_7_2022/20220720_GenestoryBook_Eng_v2.pdf","title":"Mental Health Care","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","desc":"<ul><li>Happiness, Anxiety, Emotion Management, Mental Strength.</li><li>Genetic explanation on characteristics & behaviors.</li><li>BIG5 Psychological model in recruitment and career guidance.\\n</li></ul>","buttons":[{"title":"CONSULT NOW\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Sample Report\\n","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]}]},"services":{"heading":"GeneStory Packages","products":[{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650427089483_product-detail.png","title":"GENE-LIFE Premium","desc":"120+ traits","price":"10.000.000 đ","buttons":[{"title":"Add to cart","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650427089483_product-detail.png","title":"GENE-LIFE Standard","desc":"80+ traits","price":"8.000.000 đ","buttons":[{"title":"Add to cart","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]}]},"partners":{"desc":"(including partners in business, product development and partners in scientific research projects under Biomedical Informatics Center of The Institute of Big Data)","heading":"Our partners","images":["https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430563932_Allelica.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430574669_Amazon Web Services.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430581386_Bioinformatics Institute of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430592077_Biomedic.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430601176_Department of Biomedical Engineering, John Hopskin University, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430615246_Institute for Molecular Bioscience, University of Queensland, Australia.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430635796_Mahidol University, Thailand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430653598_Nalagenetics.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430666295_Nanyang Technological University, Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430678615_National University of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430690735_Swiss Institute of Bioinformatics (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430710893_University of California, San Diego.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430734357_University of Patras, Greece (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430748022_University of Patras, Greece.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430772191_UPMC Hillman Cancer Center, University of Pittsburgh, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430785162_Victoria University of Wellington, New Zealand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430793692_Vinmec.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447396688_ĐH Bách Khoa Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447405110_ĐH Công nghệ, ĐH QG Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447414646_ĐH Dược Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447424035_ĐH Quốc tế, ĐH QG TP HCM.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447433724_ĐH Y Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447443421_HV Quân Y.png"]},"props":{"heading":"Simple and easy","propsList":[{"description":"Explore and select your decoding gene package. Receive the DNA test kit at home. ","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786799161_atHome.png","title":"At home"},{"description":"Extract DNA from saliva. Painless and non-invasive. Send out the DNA sample for analysis. ","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786883452_clock.png","title":"1 minute"},{"description":"Results will be available after 14 working days since samples arrive lab (except for inevitable accidents). Please log in to your account on GeneStory’s website or mobile app to explore your genetic testing report(s).","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786908471_truck.png","title":"14 working days"}],"videoUrl":"https://www.youtube.com/embed/GIRwCVCkYGg"}}}',
        "price": ********,
        "code": "25",
        "is_b2c": True,
        "hide_price": True,
        "hide_product": False,
        "special_price": None,
        "special_price_valid_to": "0001-01-01T07:14:00+07:06",
        "special_price_is_expire": 0,
        "priority": 4,
        "url_slug": "GeneLifePre",
        "technology": "MICROARRAY",
        "duration": 14,
        "deleted_at": None,
        "created_at": "2023-06-06T12:04:55.404238+07:00",
        "modified_at": "2024-02-06T03:44:55.262965Z",
    },
    {
        "id": "ca6a4aaa-7d7a-44a5-9197-eb597e1467a3",
        "name": "GeneHealth",
        "description": "<p><strong>Khỏe mạnh toàn diện từ gen</strong></p><ul><li>Dự phòng nguy cơ bệnh và khả năng đáp ứng thuốc từ gen</li><li>Kết hợp với các gói khám sức khỏe tổng quát cho doanh nghiệp</li></ul>",
        "thumbnail": "https://qa.genestory.ai/api/uploader/s3upload?key=vingentest-qa/shop/1690432782625_GY kit.png",
        "content": '{"vi":{"kitInfo":{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","priceDesc":"","originPrice":"4.000.000 đ","price":"","buttons":[{"title":"Mua ngay"},{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124624674_buy-now-ic.svg"}],"shortDesc":"Khỏe mạnh toàn diện từ gen","charactics":[{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124411345_100-charactics.svg","desc":"Dự phòng nguy cơ bệnh và khả năng đáp ứng thuốc từ gen "},{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124445933_gene-icon.svg","desc":"Kết hợp với các gói khám sức khỏe tổng quát cho doanh nghiệp"}]},"highlights":{"sampleReportFileUrl":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/skin_report.pdf","heading":"Báo cáo nổi bật của<br /> sản phẩm!","headerImage":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","list":[{"title":"Sàng lọc sớm nguy cơ mắc các bệnh mang yếu tố di truyền","image":"https://qa.genestory.ai/api/uploader/s3upload?key=vingentest-qa/shop/1690529753678_gene-health-4.png","desc":"Đánh giá nguy cơ mắc các bệnh: Ung thư đại trực tràng, ung thư vú (cho nữ), tăng huyết áp, rung tâm nhĩ, bệnh động mạch vành, tiểu đường type 2.","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"title":"Thuốc đúng từ gen","image":"https://qa.genestory.ai/api/uploader/s3upload?key=vingentest-qa/shop/1690447838359_gene-health-2.png","desc":"Dự phòng nguy cơ phản ứng có hại và khả năng đáp ứng thuốc của 04 nhóm thuốc thông dụng: Ho và cảm cúm, chống viêm & giảm đau, chống nấm, điều trị axit dạ dày.​","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]}]},"services":{"heading":"Gói dịch vụ giải mã GeneStory","products":[{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneLife Mini","desc":"70+ đặc điểm cá nhân","productId":"c315a925-f3aa-4640-a549-de075007a524","price":"6.500.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneLife Standard","desc":"80+ đặc điểm cá nhân","productId":"c315a925-f3aa-4640-a549-de075007a524","price":"8.000.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneLife Premium","desc":"120+ đặc điểm cá nhân","productId":"dbebc118-d35e-4fba-8c85-06da33aa9613","price":"10.000.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneHealth"}]},"partners":{"desc":"(bao gồm đối tác kinh doanh, phát triển sản phẩm và đối tác thông qua các dự án nghiên cứu thuộc Trung tâm Tin y sinh, Viện nghiên cứu dữ liệu lớn)","heading":"Đối tác của chúng tôi","images":["https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430563932_Allelica.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430574669_Amazon Web Services.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430581386_Bioinformatics Institute of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430592077_Biomedic.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430601176_Department of Biomedical Engineering, John Hopskin University, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430615246_Institute for Molecular Bioscience, University of Queensland, Australia.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430635796_Mahidol University, Thailand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430653598_Nalagenetics.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430666295_Nanyang Technological University, Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430678615_National University of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430690735_Swiss Institute of Bioinformatics (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430710893_University of California, San Diego.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430734357_University of Patras, Greece (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430748022_University of Patras, Greece.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430772191_UPMC Hillman Cancer Center, University of Pittsburgh, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430785162_Victoria University of Wellington, New Zealand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430793692_Vinmec.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447396688_ĐH Bách Khoa Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447405110_ĐH Công nghệ, ĐH QG Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447414646_ĐH Dược Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447424035_ĐH Quốc tế, ĐH QG TP HCM.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447433724_ĐH Y Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447443421_HV Quân Y.png"]},"props":{"heading":"Đơn giản và<br /> dễ sử dụng!","propsList":[{"description":"Tư vấn và lựa chọn gói dịch vụ giải mã gen. Nhận bộ kit DNA tại nhà.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786799161_atHome.png","title":"Tại nhà"},{"description":"Lấy DNA từ mẫu nước bọt, không đau, không xâm lấn. Gửi mẫu DNA đi phân tích.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786883452_clock.png","title":"1 Phút"},{"description":"Kết quả giải mã gen sẽ được trả cho khách hàng sau khoảng 14 ngày làm việc kể từ khi phòng lab nhận được mẫu, ngoại trừ các trường hợp bất khả kháng. Vui lòng truy cập tài khoản trên Website/App GeneStory để đọc kết quả.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786908471_truck.png","title":"14 Ngày"}],"videoUrl":"https://www.youtube.com/embed/GIRwCVCkYGg"}},"en":{"kitInfo":{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","priceDesc":"","originPrice":"4.000.000 đ","price":"","buttons":[{"title":"Mua ngay"},{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124624674_buy-now-ic.svg"}],"shortDesc":"Khỏe mạnh toàn diện từ gen","charactics":[{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124411345_100-charactics.svg","desc":"Dự phòng nguy cơ bệnh và khả năng đáp ứng thuốc từ gen "},{"image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124445933_gene-icon.svg","desc":"Kết hợp với các gói khám sức khỏe tổng quát cho doanh nghiệp"}]},"highlights":{"sampleReportFileUrl":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/skin_report.pdf","heading":"Báo cáo nổi bật của<br /> sản phẩm!","headerImage":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/<EMAIL>","list":[{"title":"Sàng lọc sớm nguy cơ mắc các bệnh mang yếu tố di truyền","image":"https://qa.genestory.ai/api/uploader/s3upload?key=vingentest-qa/shop/1690529753678_gene-health-4.png","desc":"Đánh giá nguy cơ mắc các bệnh: Ung thư đại trực tràng, ung thư vú (cho nữ), tăng huyết áp, rung tâm nhĩ, bệnh động mạch vành, tiểu đường type 2.","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]},{"title":"Thuốc đúng từ gen","image":"https://qa.genestory.ai/api/uploader/s3upload?key=vingentest-qa/shop/1690447838359_gene-health-2.png","desc":"Dự phòng nguy cơ phản ứng có hại và khả năng đáp ứng thuốc của 04 nhóm thuốc thông dụng: Ho và cảm cúm, chống viêm & giảm đau, chống nấm, điều trị axit dạ dày.​","buttons":[{"title":"Tư vấn ngay","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124912212_message-icon.svg"},{"title":"Kết quả mẫu","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639124963344_dna.svg"}]}]},"services":{"heading":"Gói dịch vụ giải mã GeneStory","products":[{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneLife Mini","desc":"70+ đặc điểm cá nhân","productId":"c315a925-f3aa-4640-a549-de075007a524","price":"6.500.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneLife Standard","desc":"80+ đặc điểm cá nhân","productId":"c315a925-f3aa-4640-a549-de075007a524","price":"8.000.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneLife Premium","desc":"120+ đặc điểm cá nhân","productId":"dbebc118-d35e-4fba-8c85-06da33aa9613","price":"10.000.000 đ","buttons":[{"title":"Thêm vào giỏ hàng","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1639126776593_buy-now-ic.svg"}]},{"image":"https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650426719680_product-detail.png","title":"GeneHealth"}]},"partners":{"desc":"(bao gồm đối tác kinh doanh, phát triển sản phẩm và đối tác thông qua các dự án nghiên cứu thuộc Trung tâm Tin y sinh, Viện nghiên cứu dữ liệu lớn)","heading":"Đối tác của chúng tôi","images":["https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430563932_Allelica.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430574669_Amazon Web Services.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430581386_Bioinformatics Institute of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430592077_Biomedic.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430601176_Department of Biomedical Engineering, John Hopskin University, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430615246_Institute for Molecular Bioscience, University of Queensland, Australia.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430635796_Mahidol University, Thailand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430653598_Nalagenetics.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430666295_Nanyang Technological University, Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430678615_National University of Singapore.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430690735_Swiss Institute of Bioinformatics (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430710893_University of California, San Diego.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430734357_University of Patras, Greece (1).png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430748022_University of Patras, Greece.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430772191_UPMC Hillman Cancer Center, University of Pittsburgh, USA.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430785162_Victoria University of Wellington, New Zealand.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650430793692_Vinmec.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447396688_ĐH Bách Khoa Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447405110_ĐH Công nghệ, ĐH QG Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447414646_ĐH Dược Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447424035_ĐH Quốc tế, ĐH QG TP HCM.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447433724_ĐH Y Hà Nội.png","https://vingentest-qa.s3-ap-southeast-1.amazonaws.com/shop/1650447443421_HV Quân Y.png"]},"props":{"heading":"Đơn giản và<br /> dễ sử dụng!","propsList":[{"description":"Tư vấn và lựa chọn gói dịch vụ giải mã gen. Nhận bộ kit DNA tại nhà.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786799161_atHome.png","title":"Tại nhà"},{"description":"Lấy DNA từ mẫu nước bọt, không đau, không xâm lấn. Gửi mẫu DNA đi phân tích.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786883452_clock.png","title":"1 Phút"},{"description":"Kết quả giải mã gen sẽ được trả cho khách hàng sau khoảng 14 ngày làm việc kể từ khi phòng lab nhận được mẫu, ngoại trừ các trường hợp bất khả kháng. Vui lòng truy cập tài khoản trên Website/App GeneStory để đọc kết quả.","image":"https://vingentest-prod.s3.ap-southeast-1.amazonaws.com/shop/1638786908471_truck.png","title":"14 Ngày"}],"videoUrl":"https://www.youtube.com/embed/GIRwCVCkYGg"}}}',
        "price": 4000000,
        "code": "61",
        "is_b2c": False,
        "hide_price": True,
        "hide_product": True,
        "special_price": None,
        "special_price_valid_to": "0001-01-01T07:10:00+07:06",
        "special_price_is_expire": 0,
        "priority": 10,
        "url_slug": "GeneHealth",
        "technology": "MICROARRAY",
        "duration": 14,
        "deleted_at": None,
        "created_at": "2023-07-27T11:40:03.783407+07:00",
        "modified_at": "2023-08-11T03:01:43.901964Z",
    },
]


PRODUCT_LIST_VIA_SHOP_MAP = {
    "25": PRODUCT_LIST_VIA_SHOP[0],
    "61": PRODUCT_LIST_VIA_SHOP[1],
}


@pytest.fixture(scope="session")
def app():
    app = app_init()
    return app


@pytest.fixture(autouse=True, scope="session")
def setup_test_database():
    """
    At teardown, restore original config and reset test DB.
    """
    saved_config = copy.deepcopy(config._configs)

    # alembic_main(["--raiseerr", "upgrade", "head"])
    # using import (old)
    # rc = subprocess.call(environ["OPERATION_TEST_CREATE_DB_BASH"], shell=True)
    # rc = subprocess.call(environ["OPERATION_TEST_CREATE_EXTENSION_BASH"], shell=True)
    # rc = subprocess.call(environ["OPERATION_TEST_RESTORE_BASH"], shell=True)

    yield

    # restore old configs
    config.update(saved_config)

    # if not config["TEST_KEEP_DB"]:
    # alembic_main(["--raiseerr", "downgrade", "base"])
    # using import (old)
    # rc = subprocess.call(environ["OPERATION_TEST_DROP_DB_BASH"], shell=True)


@pytest.fixture()
def client():
    rc = subprocess.call(environ["OPERATION_TEST_DROP_DB_BASH"], shell=True)
    rc = subprocess.call(environ["OPERATION_TEST_CREATE_DB_BASH"], shell=True)
    rc = subprocess.call(environ["OPERATION_TEST_CREATE_EXTENSION_BASH"], shell=True)
    rc = subprocess.call(environ["OPERATION_TEST_RESTORE_BASH"], shell=True)
    with TestClient(app_init()) as client:
        yield client


@pytest.fixture(scope="function")
def access_token_patcher(client, request):
    def do_patch(
        email="<EMAIL>",
        sub="439c52cd-3072-4d15-b5f0-17fd813b4ad4",
        phone_number="+84999999999",
    ):
        access_token_mock = MagicMock()
        access_token_mock.return_value = {
            "email": email,
            "sub": sub,
            "name": "Man Nguyen",
            "phone_number": phone_number,
            "pur": "access",
            "aud": ["user"],
        }
        access_token_patch = patch("operation.auth.get_signing_keys", access_token_mock)
        access_token_patch.start()
        request.addfinalizer(access_token_patch.stop)
        return access_token_mock

    return do_patch


@pytest.fixture(scope="function")
def get_token_claims_patcher(client, request):
    def do_patch(sub="762a3e64-b831-4280-b8ff-6ea5b56271d9"):
        claim_mock = AsyncMock()
        claim = {
            "custom:is_active": "t",
            "sub": sub,
            "email_verified": True,
            "gender": "male",
            "iss": "https://cognito-idp.ap-southeast-1.amazonaws.com/ap-southeast-1_N66D6KJrj",
            "phone_number_verified": True,
            "cognito:username": "dfc4c3b3-dca8-48d1-9e6b-a3c864e1909a",
            "pur": "",
            "type": "access_token",
            "origin_jti": "a5fd3620-4fb4-46d4-a4bb-d3285378f45b",
            "aud": "17do5v4por67pbfuopeu6e0gup",
            "event_id": "fb5f8137-5a32-41dc-8d05-954ab2c3a393",
            "user_id": "dfc4c3b3-dca8-48d1-9e6b-a3c864e1909a",
            "token_use": "id",
            "auth_time": 1695027370,
            "name": "Gene Shoppi",
            "phone_number": "+84354976688",
            "exp": 1695110221,
            "iat": 1695106621,
            "jti": "4c2c4389-3875-430c-b630-3c88836cafb5",
            "email": "<EMAIL>",
        }
        claim_mock.return_value = (claim, None)

        claim_mock_patch = patch("operation.auth.Auth.get_token_claims", claim_mock)
        claim_mock_patch.start()
        request.addfinalizer(claim_mock_patch.stop)
        return claim_mock

    return do_patch


@pytest.fixture(scope="function")
def mocked_requests_get_products_list_via_shop(request):
    def do_patch():
        user_response_data = {
            "data": PRODUCT_LIST_VIA_SHOP,
            "errors": None,
            "pagination": None,
        }
        urls_to_responses = {
            "http://internal-qa.genestory.ai/shop/admin/product/": (
                user_response_data,
                200,
            ),
            "http://shop/admin/product/": (user_response_data, 200),
        }

        def make_mock_response(*args, **kwargs):
            mocked_response = MagicMock(requests.Response)
            if args[0] not in urls_to_responses:
                mocked_response.status_code = 404
                mocked_response.text = "NOT FOUND"
            else:
                content, code = urls_to_responses[args[0]]
                mocked_response.status_code = code

                if isinstance(content, dict):
                    mocked_response.json.return_value = content
                else:
                    mocked_response.text = content

            return mocked_response

        mocked_method = MagicMock(side_effect=make_mock_response)
        patch_method = patch("requests.get", mocked_method)

        patch_method.start()
        request.addfinalizer(patch_method.stop)

    return do_patch


@pytest.fixture(autouse=True)
def clean_db(client, access_token_patcher):
    """
    Before each test, delete all existing requests from the DB
    """
    # The code below doesn't work because of this issue
    # https://github.com/encode/starlette/issues/440, so for now reset
    # using alembic.
    # pytest-asyncio = "^0.14.0"
    # from requestor.models import Request as RequestModel
    # @pytest.mark.asyncio
    # async def clean_db():
    #     await RequestModel.delete.gino.all()
    #     yield

    # alembic_main(["--raiseerr", "downgrade", "base"])
    # alembic_main(["--raiseerr", "upgrade", "head"])
    # using import (old)
    # rc = subprocess.call(environ["OPERATION_TEST_DROP_DB_BASH"], shell=True)
    # rc = subprocess.call(environ["OPERATION_TEST_CREATE_DB_BASH"], shell=True)
    # rc = subprocess.call(environ["OPERATION_TEST_CREATE_EXTENSION_BASH"], shell=True)
    # rc = subprocess.call(environ["OPERATION_TEST_RESTORE_BASH"], shell=True)

    yield


@pytest.fixture(scope="function")
def mock_get_user_id(request):
    """
    This fixture return a function that return user id from gt-authen service
    """

    def do_patch():
        user_response_data = {
            "error": {"code": 200, "message": ""},
            "data": {"result": True, "uuid": "123"},
        }
        urls_to_responses = {
            "http://internal-dev.genestory.ai/gt-authen/internal/users/uuid/validate": (
                user_response_data,
                200,
            ),
            "http://gt-authen/internal/users/uuid/validate": (user_response_data, 200),
        }

        def make_mock_response(*args, **kwargs):
            mocked_response = MagicMock(requests.Response)
            if args[0] not in urls_to_responses:
                mocked_response.status_code = 404
                mocked_response.text = "NOT FOUND"
            else:
                content, code = urls_to_responses[args[0]]
                mocked_response.status_code = code

                if isinstance(content, dict):
                    mocked_response.json.return_value = content
                else:
                    mocked_response.text = content

            return mocked_response

        mocked_method = MagicMock(side_effect=make_mock_response)
        patch_method = patch("requests.get", mocked_method)

        patch_method.start()
        request.addfinalizer(patch_method.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_send_msg_to_queue(request):
    def do_patch():
        send_msg_to_queue_mock = MagicMock()
        send_msg_to_queue_mock.return_value = {}
        send_msg_to_queue_mock_patch = patch(
            "operation.services.kit.send_msg_to_queue", send_msg_to_queue_mock
        )
        send_msg_to_queue_mock_patch.start()
        request.addfinalizer(send_msg_to_queue_mock_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_send_msg_to_queue_partner_booking(request):
    def do_patch():
        send_msg_to_queue_mock = MagicMock()
        send_msg_to_queue_mock.return_value = {}
        send_msg_to_queue_mock_patch = patch(
            "operation.services.partner_booking.send_msg_to_queue",
            send_msg_to_queue_mock,
        )
        send_msg_to_queue_mock_patch.start()
        request.addfinalizer(send_msg_to_queue_mock_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_send_notification_to_group(request):
    def do_patch():
        send_notification_to_group = AsyncMock()
        send_notification_to_group.return_value = {}
        send_notification_to_group_patch = patch(
            "operation.routes.user.kit.send_notification_to_group",
            send_notification_to_group,
        )
        send_notification_to_group_patch.start()
        request.addfinalizer(send_notification_to_group_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_send_notification_to_group_partner(request):
    def do_patch():
        send_notification_to_group = AsyncMock()
        send_notification_to_group.return_value = {}
        send_notification_to_group_patch = patch(
            "operation.services.partner_booking.send_notification_to_group",
            send_notification_to_group,
        )
        send_notification_to_group_patch.start()
        request.addfinalizer(send_notification_to_group_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_get_technology_w_product_code(request):
    def do_patch(product_code=None):
        get_technology_w_product_code = AsyncMock()
        if product_code == 25:
            get_technology_w_product_code.return_value = (
                "MICROARRAY",
                "GeneLife Premium",
            )
        else:
            get_technology_w_product_code.return_value = None, None

        get_technology_w_product_code_patch = patch(
            "operation.services.lab_sample.get_technology_w_product_code",
            get_technology_w_product_code,
        )
        get_technology_w_product_code_patch.start()
        request.addfinalizer(get_technology_w_product_code_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_get_product_w_product_code(request):
    def do_patch(product_code):
        get_product_w_product_code = MagicMock()
        if product_code == "25":
            get_product_w_product_code.return_value = PRODUCT_LIST_VIA_SHOP[0]
        elif product_code == "61":
            get_product_w_product_code.return_value = PRODUCT_LIST_VIA_SHOP[1]
        else:
            get_product_w_product_code.return_value = None

        get_product_w_product_code_patch = patch(
            "operation.services.kit.get_product_by_product_code",
            get_product_w_product_code,
        )
        get_product_w_product_code_patch.start()
        request.addfinalizer(get_product_w_product_code_patch.stop)

        # "operation.cruds.partner_booking.get_product_by_product_code"

        get_product_w_product_code_patch = patch(
            "operation.cruds.partner_booking.get_product_by_product_code",
            get_product_w_product_code,
        )
        get_product_w_product_code_patch.start()
        request.addfinalizer(get_product_w_product_code_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_get_all_products(request):
    def do_patch():
        get_all_products = AsyncMock()
        get_all_products.return_value = PRODUCT_LIST_VIA_SHOP_MAP

        get_all_products_patch = patch(
            "operation.services.lab_sample.get_product_n_technology", get_all_products
        )
        get_all_products_patch.start()
        request.addfinalizer(get_all_products_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_shop_requests(request):
    """
    This fixture returns a function which you call to mock the call to shop service.
    By default, it returns a 200 response.
    """

    def do_patch():
        # URLs to reponses: { URL: ( content, code ) }
        product_kit_response_data = {"fake_data": "ok"}
        kit_report_groups_response_data = {
            "data": [
                {
                    "kit_id": "123456789901",
                    "report_type_list": [
                        {
                            "id": "child_dev_big5_careers",
                            "name": "Mô hình Tâm lý BIG 5",
                            "icon": "https://dev.genestory.ai/api/uploader/s3upload?key=vingentest-dev/shop/1655786662364_big5.png",
                        },
                        {
                            "id": "child_dev_intelligence",
                            "name": "Tiềm năng phát triển",
                            "icon": "https://dev.genestory.ai/api/uploader/s3upload?key=vingentest-dev/shop/1655786714197_intelligence.png",
                        },
                        {
                            "id": "child_dev_medication",
                            "name": "Thuốc đúng cho bé",
                            "icon": "https://dev.genestory.ai/api/uploader/s3upload?key=vingentest-dev/shop/1655786637452_medication_child.png",
                        },
                        {
                            "id": "child_dev_personality",
                            "name": "Thể chất & Lối sống",
                            "icon": "https://dev.genestory.ai/api/uploader/s3upload?key=vingentest-dev/shop/1655786791985_personality.png",
                        },
                        {
                            "id": "child_dev_physical_sports",
                            "name": "Thể chất & Lối sống",
                            "icon": "https://dev.genestory.ai/api/uploader/s3upload?key=vingentest-dev/shop/1655786686905_physicaly.png",
                        },
                        {
                            "id": "child_nutrition_skin",
                            "name": "Dinh dưỡng & Làn da",
                            "icon": "https://dev.genestory.ai/api/uploader/s3upload?key=vingentest-dev/shop/1655786622225_nutrition.png",
                        },
                    ],
                }
            ],
            "detail": [],
            "total": 1,
        }
        urls_to_responses = {
            "http://shop/internal/product-kit": (product_kit_response_data, 200),
            "https://internal-dev.genestory.ai/shop/internal/product-kit/": (
                product_kit_response_data,
                200,
            ),
            "http://shop/internal/product-report/group": (
                kit_report_groups_response_data,
                200,
            ),
            "https://internal-dev.genestory.ai/shop/internal/product-report/group": (
                kit_report_groups_response_data,
                200,
            ),
            "https://internal-dev.genestory.ai/shop/product-report/group/kitlist": (
                kit_report_groups_response_data,
                200,
            ),
        }

        def make_mock_response(*args, **kwargs):
            # method = method.upper()
            mocked_response = MagicMock(requests.Response)
            if args[0] not in urls_to_responses:
                mocked_response.status_code = 404
                mocked_response.text = "NOT FOUND"
            else:
                content, code = urls_to_responses[args[0]]
                mocked_response.status_code = code
                if isinstance(content, dict):
                    mocked_response.json.return_value = content
                else:
                    mocked_response.text = content

            return mocked_response

        mocked_method = MagicMock(side_effect=make_mock_response)
        patch_method = patch("requests.post", mocked_method)

        patch_method.start()
        request.addfinalizer(patch_method.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_shop_put_requests(request):
    """
    This fixture returns a function which you call to mock the call to shop service.
    By default, it returns a 200 response.
    """

    def do_patch():
        # URLs to reponses: { URL: ( content, code ) }
        product_kit_response_data = {"fake_data": "ok"}
        urls_to_responses = {
            "http://shop/internal/product-kit": (product_kit_response_data, 200),
            "https://internal-dev.genestory.ai/shop/internal/product-kit/": (
                product_kit_response_data,
                200,
            ),
        }

        def make_mock_response(*args, **kwargs):
            # method = method.upper()
            mocked_response = MagicMock(requests.Response)
            if kwargs["url"] not in urls_to_responses:
                mocked_response.status_code = 404
                mocked_response.text = "NOT FOUND"
            else:
                content, code = urls_to_responses[kwargs["url"]]
                mocked_response.status_code = code
                if isinstance(content, dict):
                    mocked_response.json.return_value = content
                else:
                    mocked_response.text = content

            return mocked_response

        mocked_method = MagicMock(side_effect=make_mock_response)
        patch_method = patch("requests.put", mocked_method)

        patch_method.start()
        request.addfinalizer(patch_method.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_validate_user_requests(request):
    def do_patch(phone_number: str, email: str):
        status_code = 400
        validate_user_response_data = {"detail": [{"msg": "Invalid Email & Phone!"}]}
        # URLs to reponses: { URL: ( content, code ) }
        if phone_number and email == "":
            status_code = 200

            validate_user_response_data = {
                "error": {"code": 200, "message": ""},
                "data": {
                    "result": True,
                    "uuid": uuid.uuid4(),
                    "phone_number": phone_number,
                },
            }

        urls_to_responses = {
            "http://gt-authen/internal/users/uuid/validate": (
                validate_user_response_data,
                status_code,
            ),
            "http://gt-authen-qa.genestory.ai/gt-authen/internal/users/uuid/validate": (
                validate_user_response_data,
                status_code,
            ),
        }

        def make_mock_response(*args, **kwargs):
            # method = method.upper()
            mocked_response = MagicMock(requests.Response)

            if args[0] not in urls_to_responses:
                mocked_response.status_code = 404
                mocked_response.text = "NOT FOUND"
            else:
                content, code = urls_to_responses[args[0]]
                mocked_response.status_code = code
                if isinstance(content, dict):
                    print("Written Response!: ", content)
                    mocked_response.json.return_value = content
                else:
                    mocked_response.text = content

            return mocked_response

        print("Get Ready!")
        mocked_method = MagicMock(side_effect=make_mock_response)
        patch_method = patch("requests.get", mocked_method)

        patch_method.start()
        request.addfinalizer(patch_method.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_shop_get_requests(request):
    """
    This fixture returns a function which you call to mock the call to shop service.
    By default, it returns a 200 response.
    """

    def do_patch():
        # URLs to reponses: { URL: ( content, code ) }
        product_response_data = {
            "data": [
                {
                    "id": "c315a925-f3aa-4640-a549-de075007a524",
                    "name": "GeneLife",
                    "description": "Test",
                    "price": 8000000,
                    "code": "01",
                    "is_b2c": True,
                    "hide_price": False,
                    "hide_product": False,
                    "special_price": 4000000,
                    "special_price_valid_to": "2023-05-31T07:08:00+07:00",
                    "special_price_is_expire": 1,
                    "priority": 1,
                    "deleted_at": None,
                    "created_at": "2022-05-04T10:41:48.562876+07:00",
                    "modified_at": "2022-09-19T07:14:39.243668Z",
                }
            ],
            "errors": None,
            "pagination": None,
        }
        urls_to_responses = {
            "http://shop/admin/product/": (product_response_data, 200),
            "https://internal-dev.genestory.ai/shop/admin/product/": (
                product_response_data,
                200,
            ),
        }

        def make_mock_response(*args, **kwargs):
            # method = method.upper()
            mocked_response = MagicMock(requests.Response)

            if args[0] not in urls_to_responses:
                mocked_response.status_code = 404
                mocked_response.text = "NOT FOUND"
            else:
                content, code = urls_to_responses[args[0]]
                mocked_response.status_code = code
                if isinstance(content, dict):
                    mocked_response.json.return_value = content
                else:
                    mocked_response.text = content

            return mocked_response

        mocked_method = MagicMock(side_effect=make_mock_response)
        patch_method = patch("requests.get", mocked_method)

        patch_method.start()
        request.addfinalizer(patch_method.stop)

    return do_patch


@pytest.fixture(scope="function")
def mock_trigger_pipeline_post_requests(request):
    """
    This fixture returns a function which you call to mock the call to step function.
    By default, it returns a 200 response.
    """

    def do_patch():
        pipeline_response_data = {"fake_data": "ok"}
        urls_to_responses = {
            config["PIPELINE_URL"]: (pipeline_response_data, 200),
        }

        def make_mock_response(*args, **kwargs):
            mocked_response = MagicMock(requests.Response)
            if kwargs["url"] not in urls_to_responses:
                mocked_response.status_code = 404
                mocked_response.text = "NOT FOUND"
            else:
                content, code = urls_to_responses[kwargs["url"]]
                mocked_response.status_code = code
                if isinstance(content, dict):
                    mocked_response.json.return_value = content
                else:
                    mocked_response.text = content

            return mocked_response

        mocked_method = MagicMock(side_effect=make_mock_response)
        patch_method = patch("requests.post", mocked_method)

        patch_method.start()
        request.addfinalizer(patch_method.stop)

    return do_patch


@pytest.fixture(name="make_queue", scope="function")
def fixture_make_queue(request, make_unique_name):
    """
    Return a factory function that can be used to make a queue for testing.

    :param request: The Pytest request object that contains configuration data.
    :param make_unique_name: A fixture that returns a unique name.
    :return: The factory function to make a test queue.
    """

    def _make_queue(sqs_stubber, sqs_resource):
        """
        Make a queue that can be used for testing. When stubbing is used, a stubbed
        queue is created. When AWS services are used, the queue is deleted after
        the test completes.

        :param sqs_stubber: The SqsStubber object, configured for stubbing or AWS.
        :param sqs_resource: The SQS resource, used to create the queue.
        :return: The test queue.
        """
        queue_name = make_unique_name("queue")
        sqs_stubber.add_response(
            "create_queue",
            expected_params={"QueueName": queue_name, "Attributes": {}},
            service_response={"QueueUrl": "url-" + queue_name},
        )
        queue = sqs_resource.create_queue(QueueName=queue_name, Attributes={})

        def fin():
            if not sqs_stubber.use_stubs:
                queue.delete()

        request.addfinalizer(fin)

        return queue

    return _make_queue


@pytest.fixture(name="make_stubber", scope="function")
def fixture_make_stubber(request, monkeypatch):
    """
    Return a factory function that makes an object configured either
    to pass calls through to AWS or to use stubs.

    :param request: An object that contains configuration parameters.
    :param monkeypatch: The Pytest monkeypatch object.
    :return: A factory function that makes the stubber object.
    """

    def _make_stubber(service_client):
        """
        Create a class that wraps the botocore Stubber and implements a variety of
        stub functions that can be used in unit tests for the specified service client.

        After tests complete, the stubber checks that no more responses remain in its
        queue. This lets tests verify that all expected calls were actually made during
        the test.

        When tests are run against an actual AWS account, the stubber does not
        set up stubs and passes all calls through to the Boto 3 client.

        :param service_client: The Boto 3 service client to stub.
        :return: The stubber object, configured either for actual AWS or for stubbing.
        """
        fact = stubber_factory(service_client.meta.service_model.service_name)
        stubber = fact(service_client)

        def fin():
            stubber.assert_no_pending_responses()
            stubber.deactivate()

        request.addfinalizer(fin)
        stubber.activate()

        return stubber

    return _make_stubber


@pytest.fixture(scope="function")
def send_kit_change_email_patcher(request):
    def do_patch():
        future = asyncio.Future()
        future.set_result({"error": {"code": 200, "message": ""}, "html": ""})

        send_kit_change_email_mock = MagicMock()
        send_kit_change_email_mock.return_value = future
        send_kit_change_email_patch = patch(
            "operation.service.mail.send_email", send_kit_change_email_mock
        )
        send_kit_change_email_patch.start()
        request.addfinalizer(send_kit_change_email_patch.stop)

    return do_patch


@pytest.fixture(scope="function")
def get_kit_list_patcher(request):
    def do_patch(
        offset=0,
        size=10,
        start_date=None,
        end_date=None,
    ):
        test_data_1 = {
            "barcode": "151927530708",
            "nickname": "TEST CS PORTAL",
            "full_name": "Ha Pham D",
            "dob": "1995-09-01",
            "gender": "male",
            "phone_number": "0965569559",
            "product_name": "GenePx 1 ALLO",
            "product_code": "93",
            "current_status": "REGISTERED",
            "source": "BV Dai Hoc Y",
            "nominator": "BS HOA SUNG",
            "sale_pic": "Tham Hoang Hong",
            "sample_collection_date": "2022-01-01",
            "sample_collection_time": 2,
            "sample_receipt_date": "2022-02-01",
            "expected_report_release_date": "2022-02-15",
            "customer_support_id": "762a3e64-b831-4280-b8ff-6ea5b56271d9",
            "customer_support_name": "Gene Shoppi",
            "free_of_charge": False,
            "is_priority": False,
            "note": "this is a test kit",
            "promotion": "no",
            "diagnosis": "no diag",
            "created_time": "2023-01-09T02:44:41.547623+00:00",
            "updated_time": "2023-01-09T02:44:41.547623+00:00",
            "deleted_at": None,
        }

        test_data_2 = {
            "barcode": "199901721210",
            "nickname": "TEST CS PORTAL",
            "full_name": "Ha Pham D",
            "dob": "1995-09-01",
            "gender": "male",
            "phone_number": "0965569559",
            "product_name": "GenePx 1 ALLO",
            "product_code": "93",
            "current_status": "REGISTERED",
            "source": "BV Dai Hoc Y",
            "nominator": "BS HOA SUNG",
            "sale_pic": "Tham Hoang Hong",
            "sample_collection_date": "2022-01-01",
            "sample_collection_time": 2,
            "sample_receipt_date": "2022-02-01",
            "expected_report_release_date": "2022-02-15",
            "customer_support_id": "762a3e64-b831-4280-b8ff-6ea5b56271d9",
            "customer_support_name": "Gene Shoppi",
            "free_of_charge": False,
            "is_priority": False,
            "note": "this is a test kit",
            "promotion": "no",
            "diagnosis": "no diag",
            "created_time": "2023-01-09T02:47:09.195781+00:00",
            "updated_time": "2023-01-09T02:47:09.195781+00:00",
            "deleted_at": False,
        }

        if offset == 0 and size == 1:
            get_kit_list_mock = MagicMock()
            get_kit_list_mock.return_value = {
                "data": [test_data_1],
                "pagination": {"page_size": 1, "page_number": 1, "total": 2},
                "detail": [],
            }
            get_kit_list_patch = patch(
                "operation.services.kit.get_kit_list", get_kit_list_mock
            )
        if offset == 1 and size == 1:
            get_kit_list_mock = MagicMock()
            get_kit_list_mock.return_value = {
                "data": [test_data_2],
                "pagination": {"page_size": 1, "page_number": 2, "total": 2},
                "detail": [],
            }
            get_kit_list_patch = patch(
                "operation.services.kit.get_kit_list", get_kit_list_mock
            )
        if offset == 0 and size == 10:
            get_kit_list_mock = MagicMock()
            get_kit_list_mock.return_value = {
                "data": [test_data_1, test_data_2],
                "pagination": {"page_size": 10, "page_number": 1, "total": 2},
                "detail": [],
            }
            get_kit_list_patch = patch(
                "operation.services.kit.get_kit_list", get_kit_list_mock
            )

        get_kit_list_patch.start()
        request.addfinalizer(get_kit_list_patch.stop)
        return get_kit_list_patch

    return do_patch
