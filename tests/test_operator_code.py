def test_create_barcode(client, access_token_patcher, mock_shop_get_requests):
    """
    When an user attempts to create a code with correct request body,
    200 Ok response should be returned to the user
    """
    fake_jwt = "1.2.3"

    access_token_patcher()
    mock_shop_get_requests()
    params = {"product_code": "01", "size": 2}
    res = client.post(
        "/codes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text


def test_create_barcode_without_product_code(client, access_token_patcher):
    fake_jwt = "1.2.3"

    access_token_patcher()
    params = {"size": 2}
    res = client.post(
        "/codes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 422, res.text


def test_create_barcode_without_params(client, access_token_patcher):
    fake_jwt = "1.2.3"

    access_token_patcher()
    res = client.post("/codes", headers={"Authorization": f"bearer {fake_jwt}"})

    assert res.status_code == 422, res.text


def test_create_barcode_without_permission(
    client, access_token_patcher, mock_shop_get_requests
):
    fake_jwt = "1.2.3"
    params = {"product_code": "01"}
    mock_shop_get_requests()

    res = client.post(
        "/codes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 401, res.text


def test_get_barcodes_with_no_record(client, access_token_patcher):
    fake_jwt = "1.2.3"

    access_token_patcher()
    params = {
        "page_size": 10,
        "page_number": 1,
    }

    res = client.get(
        "/codes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get("data") == [], res.text


def test_get_barcodes_with_records(
    client, access_token_patcher, mock_shop_get_requests
):
    fake_jwt = "1.2.3"
    access_token_patcher()
    mock_shop_get_requests()
    params = {"product_code": "01", "size": 2}
    res = client.post(
        "/codes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text

    params = {
        "page_size": 10,
        "page_number": 1,
    }

    res = client.get(
        "/codes", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get("data") != [], res.text


def test_create_barcode_manually(client, access_token_patcher):
    """
    When an user attempts to create a code manually with correct request body,
    200 Ok response should be returned to the user
    """
    fake_jwt = "1.2.3"

    access_token_patcher()
    data = ["123456789901"]
    res = client.post(
        "/codes/manual", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
