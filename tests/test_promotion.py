# ACCOUNT
# CREATE ACCOUNT: 
from datetime import date, datetime, timedelta

"""
(MOCK -> DEFAULT ->) GENESTORY -> SALE_PIC -> CLINIC -> DOCTOR
(without_pic_id)

"""

def test_admin_create_a_promotion_code_w_required_info(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            "code":"PX801",
            "name":"<PERSON><PERSON><PERSON><PERSON><PERSON> dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-02"
        },
        {
            "code":"PX802",
            "name":"<PERSON><PERSON><PERSON><PERSON>n <PERSON> dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-05"
        },
        {
            "code":"PX803",
            "name":"<PERSON><PERSON><PERSON><PERSON><PERSON> dung tu gen quy I - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        }
    ]

    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        res = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print('res: ',res.json())
        assert res.json()['data']['code'] == d['code']


# TOMMORROW
def test_admin_create_a_promotion_code_wo_required_info(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            # "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-02"
        },
        {
            "code":"PX802",
            # "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-05"
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            # "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        },
        {
            "code":"PX804",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":40,
            # "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        },
        {
            "code":"PX805",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":50,
            "department":"MKT",
            # "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        },
        {
            "code":"PX806",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":60,
            "department":"MKT",
            "start_date":"2023-01-06",
            # "end_date":"2023-02-06"
        }
    ]

    missing_field_names = ['code','name','discount','department','start_date','end_date']
    for idx, d in enumerate(data):
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 422, res.text
        err_response = res.json()
        assert err_response['detail'][0]['msg'] == f"field required"
        assert err_response['detail'][0]['loc'][1] == missing_field_names[idx]


def test_admin_get_all_promotion_codes_wo_filters(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-02"
        },
        {
            "code":"PX802",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-05"
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        }
    ]

    number_of_promotion_code = len(data)

    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        res = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print('res: ',res.json())
        assert res.json()['data']['code'] == d['code']

    res = client.get(
        f"/promotions", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_promotion_code

def test_admin_get_all_promotion_codes_w_filters(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-02"
        },
        {
            "code":"PX802",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy II - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-03-06",
            "end_date":"2023-04-05"
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy III - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-05-06",
            "end_date":"2023-06-06"
        }
    ]

    number_of_promotion_code = len(data)

    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        res = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print('res: ',res.json())
        assert res.json()['data']['code'] == d['code']
    

    params = {
        'code': 'PX801'
    }

    res1 = client.get(
        f"/promotions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    assert len(res1.json()['data']) == 1
    assert res1.json()['data'][0]['code'] == data[0]['code']
    assert res1.json()['data'][0]['name'] == data[0]['name']
    assert res1.json()['data'][0]['discount'] == data[0]['discount']
    assert res1.json()['data'][0]['department'] == data[0]['department']
    assert res1.json()['data'][0]['start_date'] == data[0]['start_date'] + 'T00:00:00'
    assert res1.json()['data'][0]['end_date'] == data[0]['end_date'] + 'T23:59:59.999999'
    

    params = {
        's_start_date': '2023-01-01',
        'e_start_date': '2023-02-02'
    }

    res1 = client.get(
        f"/promotions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    assert len(res1.json()['data']) == 1
    assert res1.json()['data'][0]['code'] == data[0]['code']
    assert res1.json()['data'][0]['name'] == data[0]['name']
    assert res1.json()['data'][0]['discount'] == data[0]['discount']
    assert res1.json()['data'][0]['department'] == data[0]['department']
    assert res1.json()['data'][0]['start_date'] == data[0]['start_date'] + 'T00:00:00'
    assert res1.json()['data'][0]['end_date'] == data[0]['end_date'] + 'T23:59:59.999999'
    

    params = {
        's_end_date': '2023-02-01',
        'e_end_date': '2023-03-01'
    }

    res1 = client.get(
        f"/promotions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    assert len(res1.json()['data']) == 1
    assert res1.json()['data'][0]['code'] == data[0]['code']
    assert res1.json()['data'][0]['name'] == data[0]['name']
    assert res1.json()['data'][0]['discount'] == data[0]['discount']
    assert res1.json()['data'][0]['department'] == data[0]['department']
    assert res1.json()['data'][0]['start_date'] == data[0]['start_date'] + 'T00:00:00'
    assert res1.json()['data'][0]['end_date'] == data[0]['end_date'] + 'T23:59:59.999999'
    

    params1 = {
        # 'name': 'khuyễn mãi'
        'name': 'tu gen quy ii ' # not tu gen quy II (lower + upper case)
    }

    res2 = client.get(
        f"/promotions", params=params1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    no_data = 1
    assert res2.status_code == 200, res2.text
    print('res2: ',res2.json())
    assert len(res2.json()['data']) == 1
    assert res2.json()['data'][0]['code'] == data[no_data]['code']
    assert res2.json()['data'][0]['name'] == data[no_data]['name']
    assert res2.json()['data'][0]['discount'] == data[no_data]['discount']
    assert res2.json()['data'][0]['department'] == data[no_data]['department']
    assert res2.json()['data'][0]['start_date'] == data[no_data]['start_date'] + 'T00:00:00'
    assert res2.json()['data'][0]['end_date'] == data[no_data]['end_date'] + 'T23:59:59.999999'
    

    params2 = {
        'discount': 30
    }

    res3 = client.get(
        f"/promotions", params=params2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    no_data = 2
    assert res3.status_code == 200, res3.text
    assert len(res3.json()['data']) == 1
    assert res3.json()['data'][0]['code'] == data[no_data]['code']
    assert res3.json()['data'][0]['name'] == data[no_data]['name']
    assert res3.json()['data'][0]['discount'] == data[no_data]['discount']
    assert res3.json()['data'][0]['department'] == data[no_data]['department']
    assert res3.json()['data'][0]['start_date'] == data[no_data]['start_date'] + 'T00:00:00'
    assert res3.json()['data'][0]['end_date'] == data[no_data]['end_date'] + 'T23:59:59.999999'



def test_admin_get_all_active_promotion_codes(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']

    current_day = datetime.utcnow().strptime(datetime.utcnow().strftime("%Y-%m-%d"), "%Y-%m-%d")
    end_date_1 = str(current_day - timedelta(days=11)).split(' ')[0] # e.g 2023-02-15 ---> 2023-02-04
    end_date_2 = str(current_day - timedelta(days=10)).split(' ')[0] # e.g 2023-02-15 ---> 2023-02-05
    end_date_3 = str(current_day - timedelta(days=9)).split(' ')[0] # e.g 2023-02-15 ---> 2023-02-06

    data = [
        {
            "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date": end_date_1
        },
        {
            "code":"PX802",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date": end_date_2
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date": end_date_3
        }
    ]

    number_of_promotion_code = len(data)

    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        res = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print('res: ',res.json())
        assert res.json()['data']['code'] == d['code']
    

    params = {
        'is_active': 'true'
    }

    res = client.get(
        f"/promotions", params=params, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    # print('Result: ',res.json())
    assert len(res.json()['data']) == number_of_promotion_code - 1


def test_admin_get_promotion_code_detail_w_existed_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-01"
        },
        {
            "code":"PX802",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-04"
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-05"
        }
    ]

    number_of_promotion_code = len(data)

    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        print('promotion code id: ', id)
        res_detail = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res_detail.status_code == 200, res_detail.text
        # print(res_detail.json())
        assert res_detail.json()['data']['code'] == d['code']
        assert res_detail.json()['data']['name'] == d['name']
        assert res_detail.json()['data']['discount'] == d['discount']
        assert res_detail.json()['data']['department'] == d['department']
        assert res_detail.json()['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res_detail.json()['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'


"""
promotion code id:  5483749b-0c57-48a3-ba71-482aced6db74
promotion code id:  cd524bc8-34d9-4239-94bc-7f3fd2fae39a
promotion code id:  d502dfd6-2aca-4c43-8cb7-97b3a280f967
"""

def test_admin_get_promotion_code_detail_w_unexisted_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()

    id = "d502dfd6-2aca-4c43-8cb7-97b3a280f967"

    res_detail = client.get(
        f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res_detail.status_code == 404, res_detail.text
    err_response = res_detail.json()
    assert err_response['detail'][0]['msg'] == f"Kit with id {id} can not be found"


def test_admin_update_a_promotion_code_editable_fields(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-02"
        },
        {
            "code":"PX802",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-05"
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        }
    ]

    promotion_id_arrs = []
    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        promotion_id_arrs.append(id)
        res = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print('res: ',res.json())
        assert res.json()['data']['code'] == d['code']

    for idx, id in enumerate(promotion_id_arrs):
        updated_data = data[idx].copy()
        updated_data['name'] += ' updated'
        updated_data['department'] += ' updated'
        updated_data['discount'] += 10
        updated_res = client.put(
            f"/promotion/{id}", json=updated_data, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert updated_res.status_code == 200, updated_res.text

        res_detail = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res_detail.json()['data']['code'] == data[idx]['code']
        assert res_detail.json()['data']['name'] == data[idx]['name'] + ' updated'
        assert res_detail.json()['data']['department'] == data[idx]['department'] + ' updated'
        assert res_detail.json()['data']['discount'] == data[idx]['discount'] + 10

def test_admin_update_a_promotion_code_immutable_fields(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-02"
        },
        {
            "code":"PX802",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-05"
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        }
    ]

    promotion_id_arrs = []
    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        promotion_id_arrs.append(id)
        res = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print('res: ',res.json())
        assert res.json()['data']['code'] == d['code']

    for idx, id in enumerate(promotion_id_arrs):
        updated_data = data[idx].copy()
        updated_data['code'] += '123'
        print('Updated infor: \n', updated_data)
        updated_res = client.put(
            f"/promotion/{id}", json=updated_data, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert updated_res.status_code == 200, updated_res.text

        res_detail = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res_detail.json()['data']['code'] == data[idx]['code'] # Unchange #Immutable

        
def test_admin_delete_promotion_code_w_id(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    # mock new_history_data['pic_id'] = default_[0]['id']
    data = [
        {
            "code":"PX801",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":10,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-02"
        },
        {
            "code":"PX802",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":20,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-05"
        },
        {
            "code":"PX803",
            "name":"Khuyễn Mãi Thuoc dung tu gen quy I - 2023",
            "discount":30,
            "department":"MKT",
            "start_date":"2023-01-06",
            "end_date":"2023-02-06"
        }
    ]

    promotion_id_arrs = []
    for d in data:
        res = client.post(
            "/promotion", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text
        assert res.json()[0]['data']['code'] == d['code']
        assert res.json()[0]['data']['name'] == d['name']
        assert res.json()[0]['data']['discount'] == d['discount']
        assert res.json()[0]['data']['department'] == d['department']
        assert res.json()[0]['data']['start_date'] == d['start_date'] + 'T00:00:00'
        assert res.json()[0]['data']['end_date'] == d['end_date'] + 'T23:59:59.999999'

        id = res.json()[0]['data']['id']
        promotion_id_arrs.append(id)
        res = client.get(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        # print('res: ',res.json())
        assert res.json()['data']['code'] == d['code']

    for _, id in enumerate(promotion_id_arrs):
        _ = client.delete(
            f"/promotion/{id}", headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert _.status_code == 200, _.text



    deleted_res = client.get(
        f"/promotions", headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert deleted_res.status_code == 200, deleted_res.text
    # print('Result: ',res.json())
    assert len(deleted_res.json()['data']) == 0

