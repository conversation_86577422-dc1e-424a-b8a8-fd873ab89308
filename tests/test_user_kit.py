def test_register_kit(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    """
    When an user attempts to create a code with correct request body,
    200 Ok response should be returned to the user
    """
    fake_jwt = "1.2.3"

    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id()
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "phone_number": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "<PERSON><PERSON><PERSON>",
        "version": 0
    }
    
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res2.status_code == 200, res2.text


def test_register_kit_field_missing(client, access_token_patcher, mock_shop_requests):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        # "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res2.status_code == 422, res2.text


def test_register_kit_wrong_date_format(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id()
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "email": "<EMAIL>",
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res2.status_code == 400, res2.text
    assert res2.json()['detail'][0][
               'msg'] == '1990-05-14T17:00:00.000 is not accepted format, the correct format is %Y-%m-%dT%H:%M:%S.%fZ'


def test_register_kit_invalid_barcode(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id()
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789903",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res2.status_code == 422, res2.text
    assert res2.json()['detail'][0]['msg'] == "Can not register kit with id 123456789903. You have 9 input times left"


def test_register_kit_get_id(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901", "123456789902"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id()
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res2.status_code == 200, res2.text

    data3 = {
        "barcode": "123456789902",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res3 = client.post(
        "/operator/kits/registrations", json=data3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res2.status_code == 200, res3.text

    params1 = {
        "id": "123456789901"
    }

    res4 = client.get(
        "/operator/kits/registrations", params=params1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res2.status_code == 200, res4.text

    params2 = {
        "id": "123456789902"
    }

    res4 = client.get(
        "/operator/kits/registrations", params=params2, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res4.status_code == 405, res4.text

    access_token_patcher()
    params3 = {
        "id": "123456789902"
    }

    res5 = client.get(
        "/operator/kits/registrations", params=params3, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res5.status_code == 405, res5.text


def test_register_kit_get_internal_kit_id(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901", "123456789902"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text

    # Register kits
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text

    # Gte kit info
    params1 = {
        "id": "123456789901"
    }
    res4 = client.get(
        "/internal/kits/operator/registrations", params=params1
    )
    assert res2.status_code == 200, res4.text

    # Get non existed kit
    params2 = {
        "id": "123456789902"
    }
    res4 = client.get(
        "/internal/kits/operator/registrations", params=params2
    )
    assert res4.status_code == 404, res4.text


def test_update_kit_data(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id()
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "phone_number": "+847854785765",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    
    sample_body = {
        "barcode": data2["barcode"],
        "batch_barcode": 1,
        "vinmec_id": "string",
        "chip_id": "string",
        "chip_type": "ASA",
        "qc_status": "PASSED"
    }
    
    res3 = client.post("/samples", json=sample_body, headers={"Authorization": f"bearer {fake_jwt}"})
                       
    assert res3.status_code == 200, res3.text
    
    mock_shop_put_requests()
    params2 = {
        "id": "123456789901"
    }
    data3 = {
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "phone_number": "<EMAIL>",
        "gender": "female",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res3 = client.put(f"/kits/registrations/{params2['id']}", json=data3,
                      headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 200, res3.text
    
    data4 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "phone_number": "+847854785765",
        "gender": "female",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    
    res4 = client.get("/samples", json={"barcode": data4["barcode"]},
                      headers={"Authorization": f"bearer {fake_jwt}"})
    res = res4.json()
    if res and res.get("data"):
        for r in res["data"]:
            assert r["gender"] == data4["gender"]


def test_update_kit_data_no_email(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "email": "<EMAIL>",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    mock_shop_put_requests()
    params2 = {
        "id": "123456789901"
    }
    data3 = {
        "dob": "1990-05-14T17:00:00.000Z",
        "gender": "male",
        "myself": True,
        "email": "<EMAIL>",
        "name": "Nguyen Van A",
        "version": 0
    }
    res3 = client.put(f"/kits/registrations/{params2['id']}", json=data3,
                      headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 200, res3.text


def test_update_kit_data_invalid_email(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "email": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    mock_shop_put_requests()
    params2 = {
        "id": "123456789901"
    }
    data3 = {
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "sampleexample.com",
        "email": 1,
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res3 = client.put(f"/kits/registrations/{params2['id']}", json=data3,
                      headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 400, res3.text
    assert res3.json()['detail'][0]['msg'] == "Email format is not correct"


def test_update_kit_data_with_different_code(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    mock_shop_put_requests()
    params2 = {
        "id": "123456789901"
    }
    data3 = {
        "barcode": "123456789902",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res3 = client.put(f"/kits/registrations/{params2['id']}", json=data3,
                      headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 200, res3.text


def test_update_kit_data_with_different_email(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    mock_shop_put_requests()
    params2 = {
        "id": "123456789901"
    }
    data3 = {
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res3 = client.put(f"/kits/registrations/{params2['id']}", json=data3,
                      headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 200, res3.text


def test_kit_without_token(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2
    )
    assert res2.status_code == 500, res2.text


def test_delete_kit_data(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    params2 = {
        "id": "123456789901"
    }
    res3 = client.delete(f"/kits/registrations/{params2['id']}", params=params2,
                         headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 200, res3.text
    
    
def test_get_specific_kit(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "email": "<EMAIL>",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    params2 = {
        "id": "123456789901"
    }
    res3 = client.get(f"/kits/registrations/{params2['id']}",
                         headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 200, res3.text
    
    params = {
      'id': '123456789902'
    }
    res = client.get(f"/kits/registrations/{params['id']}",
                         headers={"Authorization": f"bearer {fake_jwt}"})
    assert res.status_code == 404, res.text
    
  
def test_get_kit_status(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "email": "<EMAIL>",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    params2 = {
        "id": "123456789901"
    }
    res3 = client.get(f"/kits/registrations/{params2['id']}/status",
                         headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 200, res3.text


def test_delete_kit_doesnt_exist(client, access_token_patcher, mock_shop_requests, mock_shop_put_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res2 = client.post(
        "/operator/kits/registrations", json=data2, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res2.status_code == 200, res2.text
    params2 = {
        "id": "123456789902"
    }
    res3 = client.delete(f"/kits/registrations/{params2['id']}", params=params2,
                         headers={"Authorization": f"bearer {fake_jwt}"})
    assert res3.status_code == 404, res3.text


def test_register_kit_get_ids(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901", "123456789902", "123456789903"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = [{
            "barcode": "123456789901",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van A",
            "version": 0
        },
        {
            "barcode": "123456789902",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van B",
            "version": 0
        },
        {
            "barcode": "123456789903",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van C",
            "version": 0
        }
    ]

    access_token_patcher()
    for d in data2:
        res = client.post(
            "/operator/kits/registrations", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text

    access_token_patcher()
    body = ["123456789901", "123456789902"]

    res = client.post(
        "/admin/kits/registrations/ids", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get('data') != []


def test_register_kit_get_ids_empty_body(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901", "123456789902", "123456789903"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = [{
            "barcode": "123456789901",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van A",
            "version": 0
        },
        {
            "barcode": "123456789902",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van B",
            "version": 0
        },
        {
            "barcode": "123456789903",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van C",
            "version": 0
        }
    ]

    access_token_patcher()
    for d in data2:
        res = client.post(
            "/operator/kits/registrations", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text

    access_token_patcher()
    body = []

    res = client.post(
        "/admin/kits/registrations/ids", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 422, res.text


def test_register_kit_get_ids_unmap(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data1 = ["123456789901", "123456789902", "123456789903"]
    res1 = client.post(
        "/codes/manual", json=data1, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res1.status_code == 200, res1.text
    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data2 = [{
            "barcode": "123456789901",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van A",
            "version": 0
        },
        {
            "barcode": "123456789902",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van B",
            "version": 0
        },
        {
            "barcode": "123456789903",
            "dob": "1990-05-14T17:00:00.000Z",
            "userid": "<EMAIL>",
            "gender": "male",
            "myself": True,
            "name": "Nguyen Van C",
            "version": 0
        }
    ]

    access_token_patcher()
    for d in data2:
        res = client.post(
            "/operator/kits/registrations", json=d, headers={"Authorization": f"bearer {fake_jwt}"}
        )

        assert res.status_code == 200, res.text

    access_token_patcher()
    body = ["123456789904", "123456789902"]

    res = client.post(
        "/admin/kits/registrations/ids", json=body, headers={"Authorization": f"bearer {fake_jwt}"}
    )

    assert res.status_code == 200, res.text
    assert res.json().get('err_data') == ["123456789904"]


def test_get_kits_report_groups(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data = ["123456789901"]
    res = client.post(
        "/codes/manual", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text

    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "123",
        "email": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res = client.post(
        "/operator/kits/registrations", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text

    res = client.post(
        "/kits/registrations/report/groups", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 405, res.text
    res = client.get(
        "/kits/registrations/report/groups", headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text


def test_update_kit_status(client, access_token_patcher, mock_shop_requests, mock_get_user_id, mock_send_msg_to_queue):
    fake_jwt = "1.2.3"
    access_token_patcher()
    data = ["123456789901"]
    res = client.post(
        "/codes/manual", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text

    mock_shop_requests()
    mock_get_user_id() 
    mock_send_msg_to_queue()
    data = {
        "barcode": "123456789901",
        "dob": "1990-05-14T17:00:00.000Z",
        "userid": "123",
        "email": "<EMAIL>",
        "gender": "male",
        "myself": True,
        "name": "Nguyen Van A",
        "version": 0
    }
    res = client.post(
        "/operator/kits/registrations", json=data, headers={"Authorization": f"bearer {fake_jwt}"}
    )
    assert res.status_code == 200, res.text

    status_data = {"status":"DISABLED"}
    res = client.put(
        "/kits/registrations/123456789901/status", json=status_data, headers={"content-type": "application/json", "Authorization": f"bearer {fake_jwt}"}
    )
    assert res.json()['data'][1]["__values__"]['status'] == 'DISABLED', res.status == 200
