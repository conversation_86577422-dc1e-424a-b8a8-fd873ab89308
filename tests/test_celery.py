import boto3
import pytest
from moto import mock_sqs

from src.operation.celery_app.celery_config import get_celery_app
from src.operation.config import config


def test_config():
    with mock_sqs():
        queue_name = config["CELERY"]["QUEUE_NAME"]
        sqs_client = boto3.client("sqs", config["AWS"]["CELERY_REGION_NAME"])
        sqs_client.create_queue(QueueName=queue_name)
        sqs = get_celery_app()
        assert sqs.conf.broker_url is not None
        assert "sqs" in sqs.conf.broker_url
        assert (
            sqs_client.get_queue_url(QueueName=queue_name)["QueueUrl"]
            == sqs.conf.broker_transport_options["predefined_queues"]["celery"]["url"]
        )

    with mock_sqs():
        with pytest.raises(Exception):
            sqs = get_celery_app()
