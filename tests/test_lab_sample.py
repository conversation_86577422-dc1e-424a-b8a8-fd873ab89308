
def test_create_update_lab_samples(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    
    #get_next_lid
    res = client.get(
        f"/lims/lab_sample/lid/current"
    )
    assert res.status_code == 200
    next_lid = res.json()['data']
    
    barcode = ""
    body = {
        "barcode": barcode,
    }
    res = client.post(
        "/lims/lab_sample", json=body,
    )
    lid = res.json()['data']['lid']
    assert res.json()['data']['lid'] == next_lid
    assert res.json()['data']['barcode'] == body['barcode']
    
    body = {
        "note": "This is updated note",
    }
    params = {
        "id": barcode,
    }
    updated_sample = client.put(
        f"/lims/lab_sample/{lid}", json=body
    )
    assert res.json()['data']['lid'] == ''
    assert res.json()['data']['barcode'] == barcode
    assert res.json()['data']['note'] == body['note']
    
    res = client.delete(
        f"/lims/lab_sample/{lid}"
    )
    assert res.status_code == 200

def test_get_lab_check_samples(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    params = {
        "page_size": 10,
        "page_number": 1,
    }
    lab_check_samples = client.get(
        f"lims/lab_sample", params=params,
    )
    
    assert lab_check_samples.status_code == 200, lab_check_samples.text
    assert len(lab_check_samples.json()['data']) == 3
    
def test_send_samples_to_lab(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    kit_list = []
    res = client.post(
        "/kits/send-to-lab", json={
            "kit_list": kit_list
        }
    )
    assert res.status_code == 200
    
def test_get_checked_samples(client, access_token_patcher):
    fake_jwt = "1.2.3"
    access_token_patcher()
    res = client.get(
        "/lims/lab_sample/checked"
    )
    assert res.status_code == 200
    assert len(res.json()['data']) == 1
    barcode = "665896078315"
    body = {
        "barcode": barcode,
        "status": "PASSED_LAB_CHECK",
        "note": ""
    }
    res = client.post(
        "/lims/lab_sample/status", json=body
    )
    assert res.status_code == 200
    assert res.json()['data'] == 'lab sample status updated'
    
    res = client.get(
        "/lims/lab_sample/checked"
    )
    assert res.status_code == 200
    assert len(res.json()['data']) == 2
    
    barcode = "665896078315"
    body = {
        "barcode": barcode,
        "status": "FAILED_LAB_CHECK",
        "note": ""
    }
    res = client.post(
        "/lims/lab_sample/status", json=body
    )
    assert res.status_code == 200
    assert res.json()['data'] == 'lab sample status updated'
    
    res = client.get(
        "/lims/lab_sample/checked"
    )
    assert res.status_code == 200
    assert len(res.json()['data']) == 2
    
    #update missing sample info
    res = client.get(
        "/lims/lab_sample/missing_sample"
    )
    assert res.status_code == 200
    assert len(res.json()['data']) == 2

    barcode = "665896078316"
    body = {
        "barcode": barcode,
        "status": "MISSING_INFO",
        "note": ""
    }
    res = client.post(
        "/lims/lab_sample/status", json=body
    )
    assert res.status_code == 200
    assert res.json()['data'] == 'lab sample status updated'
    
    res = client.get(
        "/lims/lab_sample/missing_sample"
    )
    assert res.status_code == 200
    assert len(res.json()['data']) == 3