import pytest

from src.operation.services import notification


def init_data(func):
    """
    When an user attempts to create a code with correct request body,
    200 Ok response should be returned to the user
    """

    def inner(
        client,
        access_token_patcher,
        mock_shop_requests,
        mock_get_user_id,
        mock_send_msg_to_queue,
        *args,
        **kwargs,
    ):
        fake_jwt = "1.2.3"

        access_token_patcher()
        codes = [
            "123456789901",
            "123456789902",
            "123456789903",
            "123456789904",
            "123456789905",
        ]
        res = client.post(
            "/codes/manual", json=codes, headers={"Authorization": f"bearer {fake_jwt}"}
        )
        assert res.status_code == 200, res.text

        mock_shop_requests()
        mock_get_user_id()
        mock_send_msg_to_queue()
        for code in codes:
            data = {
                "barcode": code,
                "dob": "1990-05-14T17:00:00.000Z",
                "email": "<EMAIL>",
                "gender": "male",
                "userid": "123",
                "myself": True,
                "name": "<PERSON><PERSON><PERSON>",
                "version": 0,
            }
            res = client.post(
                "/operator/kits/registrations",
                json=data,
                headers={"Authorization": f"bearer {fake_jwt}"},
            )

            assert res.status_code == 200, res.text

        # Register samples
        for code in codes:
            sample_body = {
                "barcode": code,
                "batch_barcode": 1,
                "vinmec_id": "string",
                "chip_id": "string",
                "chip_type": "ASA",
                "qc_status": "PASSED",
            }
            res = client.post(
                "/samples",
                json=sample_body,
                headers={"Authorization": f"bearer {fake_jwt}"},
            )

            assert res.status_code == 200, res.text

        returned_value = func(*args, **kwargs)

        return returned_value

    return inner


@pytest.mark.asyncio
@init_data
async def test_get_no_data_kits():
    results, batchs = await notification.get_no_data_kits()

    assert batchs == {"1"}
    assert len(results) == 5
