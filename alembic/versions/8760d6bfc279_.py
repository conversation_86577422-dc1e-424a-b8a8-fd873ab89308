"""empty message

Revision ID: 6ef17a4f239e
Revises:
Create Date: 2025-03-20 09:43:36.066152

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "8760d6bfc279"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "account",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=True),
        sa.Column("address", sa.String(length=255), nullable=True),
        sa.Column("area", sa.String(length=50), nullable=True),
        sa.Column("description", sa.String(length=120), nullable=True),
        sa.Column("type", sa.String(length=50), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "adn_integration",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("samplecode", sa.String(length=120), nullable=False),
        sa.Column("barcode", sa.String(length=120), nullable=False),
        sa.Column("type_id", postgresql.UUID(), nullable=True),
        sa.Column("status", sa.String(length=120), nullable=False),
        sa.Column("presigned_s3_url", sa.String(length=500), nullable=True),
        sa.Column("raw_adn_s3_obj_key", sa.String(length=500), nullable=True),
        sa.Column("review_required", sa.Boolean(), nullable=False),
        sa.Column("response_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "adn_integration_type",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=120), nullable=False),
        sa.Column("adn_type", sa.String(length=120), nullable=False),
        sa.Column("method", sa.String(length=120), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "adn_result",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("adn_integration_id", postgresql.UUID(), nullable=False),
        sa.Column("tenFileADN", sa.String(length=120), nullable=False),
        sa.Column("congNghe", sa.String(length=120), nullable=False),
        sa.Column("tenKit", sa.String(length=120), nullable=False),
        sa.Column("tenThuMucTho", sa.String(length=120), nullable=True),
        sa.Column("loaiDuLieu", sa.String(length=120), nullable=False),
        sa.Column("gs_adn_result_s3_url", sa.String(length=500), nullable=True),
        sa.Column("gs_raw_result_s3_url", sa.String(length=500), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "agency",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=120), nullable=False),
        sa.Column("type", sa.String(length=120), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "attempt",
        sa.Column("userid", sa.String(length=50), nullable=False),
        sa.Column("email", sa.String(length=50), nullable=True),
        sa.Column("block", sa.Boolean(), nullable=False),
        sa.Column("attempt", sa.Integer(), nullable=False),
        sa.Column("note", sa.String(length=500), nullable=True),
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("userid"),
    )
    op.create_table(
        "batch",
        sa.Column("id", sa.String(length=12), nullable=False),
        sa.Column("number", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=20), nullable=False),
        sa.Column("note", sa.String(length=120), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("type", sa.String(length=24), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "batch_mapping",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("plate_id", postgresql.UUID(), nullable=False),
        sa.Column("batch_id", postgresql.UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("wetlab_date", sa.DateTime(), nullable=True),
        sa.Column("drylab_date", sa.DateTime(), nullable=True),
        sa.Column("raw_data_uploaded_date", sa.DateTime(), nullable=True),
        sa.Column("raw_report_to_reviewers", sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "billcode",
        sa.Column("billcode", sa.String(length=36), nullable=False),
        sa.Column("printed", sa.Boolean(), nullable=False),
        sa.Column("status", sa.String(length=100), nullable=True),
        sa.Column("created_at", sa.Date(), nullable=False),
        sa.Column("updated_at", sa.Date(), nullable=False),
        sa.PrimaryKeyConstraint("billcode"),
    )
    op.create_table(
        "card",
        sa.Column("id", sa.String(length=32), nullable=False),
        sa.Column("card_product_id", postgresql.UUID(), nullable=False),
        sa.Column("barcode", sa.String(length=120), nullable=False),
        sa.Column("phone_number", sa.String(length=120), nullable=False),
        sa.Column("user_id", sa.String(length=120), nullable=False),
        sa.Column("report_ver", sa.String(length=14), nullable=False),
        sa.Column("db_ver", sa.String(length=14), nullable=False),
        sa.Column("lang", sa.String(length=5), nullable=False),
        sa.Column("card_status", sa.String(length=14), nullable=True),
        sa.Column("full_name", sa.String(length=255), nullable=True),
        sa.Column("qr_url", sa.String(length=255), nullable=True),
        sa.Column("s3_object_key", sa.String(length=500), nullable=True),
        sa.Column("presigned_s3_font_url", sa.String(length=500), nullable=True),
        sa.Column("presigned_s3_back_url", sa.String(length=500), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "card_product",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("type", sa.String(length=50), nullable=False),
        sa.Column("card_product_name", sa.String(length=200), nullable=False),
        sa.Column("policy", sa.String(length=50), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "chip",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chip_id", sa.String(length=12), nullable=False),
        sa.Column("type", sa.String(length=12), nullable=False),
        sa.Column("technology", sa.String(length=12), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "code",
        sa.Column("barcode", sa.String(length=50), nullable=False),
        sa.Column("qrcode", sa.String(length=100), nullable=False),
        sa.Column("printed", sa.Boolean(), nullable=False),
        sa.Column("state", sa.String(length=50), nullable=True),
        sa.Column("note", sa.String(length=120), nullable=True),
        sa.Column("product_code", sa.String(length=10), nullable=True),
        sa.Column("product_name", sa.String(length=100), nullable=True),
        sa.Column("created_time", sa.Date(), nullable=False),
        sa.Column("updated_time", sa.Date(), nullable=False),
        sa.PrimaryKeyConstraint("barcode"),
    )
    op.create_table(
        "dna_box",
        sa.Column("id", sa.String(length=12), nullable=False),
        sa.Column("capacity", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "dna_box_mappings",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("position", sa.String(length=12), nullable=False),
        sa.Column("dna_box_id", sa.String(length=12), nullable=False),
        sa.Column("lid", sa.String(length=12), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "dna_extractions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("lid", sa.String(length=12), nullable=False),
        sa.Column("dna_extraction_date", sa.Date(), nullable=False),
        sa.Column("qubit", sa.Float(), nullable=True),
        sa.Column("nano_drop", sa.Float(), nullable=False),
        sa.Column("a260_a280", sa.Float(), nullable=False),
        sa.Column("agarose_gel", sa.String(length=50), nullable=False),
        sa.Column("dna_qc_status", sa.String(length=50), nullable=False),
        sa.Column("note", sa.String(length=100), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "identity_card",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("identifier_code", sa.String(length=50), nullable=False),
        sa.Column("full_name", sa.String(length=120), nullable=False),
        sa.Column("dob", sa.Date(), nullable=False),
        sa.Column("gender", sa.String(length=50), nullable=False),
        sa.Column("nationality", sa.String(length=250), nullable=False),
        sa.Column("origin", sa.String(length=250), nullable=False),
        sa.Column("residence", sa.String(length=250), nullable=False),
        sa.Column("avatar_image", sa.Text(), nullable=False),
        sa.Column("fingerprint_image", sa.Text(), nullable=False),
        sa.Column("ethnic", sa.String(length=50), nullable=False),
        sa.Column("email", sa.String(length=50), nullable=True),
        sa.Column("phone_number", sa.String(length=50), nullable=True),
        sa.Column("customer_support_name", sa.String(length=120), nullable=True),
        sa.Column("customer_support_id", postgresql.UUID(), nullable=True),
        sa.Column("created_at", sa.Date(), nullable=False),
        sa.Column("updated_at", sa.Date(), nullable=True),
        sa.Column("deleted_at", sa.Date(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "kit",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("barcode", sa.String(length=50), nullable=True),
        sa.Column("samplecode", sa.String(length=12), nullable=False),
        sa.Column("nickname", sa.String(length=50), nullable=True),
        sa.Column("version", sa.String(length=50), nullable=True),
        sa.Column("expected_report_release_date", sa.Date(), nullable=True),
        sa.Column("actual_report_release_time", sa.TIMESTAMP(), nullable=True),
        sa.Column("customer_support_id", postgresql.UUID(), nullable=True),
        sa.Column("customer_support_name", sa.String(length=120), nullable=True),
        sa.Column("free_of_charge", sa.Boolean(), nullable=True),
        sa.Column("promotion", postgresql.UUID(), nullable=True),
        sa.Column("is_priority", sa.Boolean(), nullable=False),
        sa.Column("product_code", sa.String(length=50), nullable=False),
        sa.Column("product_name", sa.String(length=50), nullable=False),
        sa.Column("product_type", sa.String(length=50), nullable=False),
        sa.Column("pdf_generation_date", sa.Date(), nullable=True),
        sa.Column("default_pdf_link", sa.String(length=500), nullable=True),
        sa.Column("is_card_issued", sa.Boolean(), nullable=False),
        sa.Column("note", sa.String(length=100), nullable=True),
        sa.Column("current_status", sa.String(length=50), nullable=True),
        sa.Column("current_status_id", sa.Integer(), nullable=True),
        sa.Column("workflow", sa.String(length=120), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "lab_sample",
        sa.Column("lid", sa.String(length=12), nullable=False),
        sa.Column("barcode", sa.String(length=12), nullable=False),
        sa.Column("samplecode", sa.String(length=12), nullable=True),
        sa.Column("lab_receipt_date", sa.DateTime(), nullable=False),
        sa.Column("note", sa.String(length=120), nullable=True),
        sa.Column("technology", sa.String(length=20), nullable=False),
        sa.Column("positive_control", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("lid"),
    )
    op.create_table(
        "missing_sample",
        sa.Column("barcode", sa.String(length=12), nullable=False),
        sa.Column("samplecode", sa.String(length=12), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("note", sa.String(length=50), nullable=True),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
    )
    op.create_table(
        "old_kit",
        sa.Column("barcode", sa.String(length=50), nullable=False),
        sa.Column("sample_meta_id", postgresql.UUID(), nullable=False),
        sa.Column("nickname", sa.String(length=50), nullable=True),
        sa.Column("version", sa.String(length=50), nullable=False),
        sa.Column("current_status", sa.String(length=50), nullable=True),
        sa.Column("current_status_id", sa.Integer(), nullable=False),
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("product_code", sa.String(length=50), nullable=False),
        sa.Column("product_name", sa.String(length=50), nullable=False),
        sa.Column("product_type", sa.String(length=50), nullable=False),
        sa.Column("sample_collection_date", sa.Date(), nullable=True),
        sa.Column("sample_collection_time", sa.Integer(), nullable=False),
        sa.Column("sample_receipt_date", sa.Date(), nullable=False),
        sa.Column("lab_receipt_date", sa.Date(), nullable=True),
        sa.Column("sample_collector_name", sa.String(length=50), nullable=True),
        sa.Column("sample_receiver_name", sa.String(length=50), nullable=True),
        sa.Column("expected_report_release_date", sa.Date(), nullable=False),
        sa.Column("source_id", postgresql.UUID(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("customer_support_id", postgresql.UUID(), nullable=True),
        sa.Column("customer_support_name", sa.String(length=120), nullable=True),
        sa.Column("note", sa.String(length=100), nullable=True),
        sa.Column("free_of_charge", sa.Boolean(), nullable=True),
        sa.Column("promotion", postgresql.UUID(), nullable=True),
        sa.Column("is_priority", sa.Boolean(), nullable=False),
        sa.Column("pdf_generation_date", sa.Date(), nullable=True),
        sa.Column("default_pdf_link", sa.String(length=500), nullable=True),
        sa.Column("lab_check_date", sa.Date(), nullable=True),
        sa.Column("sample_type", sa.String(length=30), nullable=True),
        sa.Column("actual_report_release_time", sa.TIMESTAMP(), nullable=True),
        sa.PrimaryKeyConstraint("barcode"),
    )
    op.create_table(
        "old_sample",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("operation_id", sa.String(length=50), nullable=False),
        sa.Column("num", sa.Integer(), nullable=False),
        sa.Column("position", sa.String(length=50), nullable=False),
        sa.Column("physical_position", sa.String(length=50), nullable=False),
        sa.Column("barcode", sa.String(length=50), nullable=False),
        sa.Column("batch_barcode", sa.String(length=50), nullable=False),
        sa.Column("vinmec_id", sa.String(length=50), nullable=True),
        sa.Column("chip_id", sa.String(length=50), nullable=False),
        sa.Column("chip_type", sa.String(length=50), nullable=False),
        sa.Column("assembly", sa.String(length=50), nullable=False),
        sa.Column("gender", sa.String(length=50), nullable=False),
        sa.Column("technician_name", sa.String(length=50), nullable=False),
        sa.Column("qc_status", sa.String(length=50), nullable=False),
        sa.Column("positive_tested", sa.Boolean(), nullable=False),
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "plate",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=12), nullable=False),
        sa.Column("status", sa.String(length=12), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("type", sa.String(length=24), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "promotion",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("code", sa.String(length=250), nullable=False),
        sa.Column("name", sa.String(length=500), nullable=False),
        sa.Column("discount", sa.String(length=250), nullable=False),
        sa.Column("department", sa.String(length=250), nullable=False),
        sa.Column("start_date", sa.DateTime(), nullable=False),
        sa.Column("end_date", sa.DateTime(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "request",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("request_code", sa.String(length=120), nullable=False),
        sa.Column("agency_id", postgresql.UUID(), nullable=False),
        sa.Column("customer_support_name", sa.String(length=120), nullable=True),
        sa.Column("customer_support_id", postgresql.UUID(), nullable=True),
        sa.Column("customer_name", sa.String(length=120), nullable=False),
        sa.Column("customer_phone", sa.String(length=120), nullable=False),
        sa.Column("dob", sa.Date(), nullable=False),
        sa.Column("gender", sa.String(length=50), nullable=False),
        sa.Column("payment_amount", sa.Integer(), nullable=False),
        sa.Column("payment_method", sa.String(length=120), nullable=False),
        sa.Column("payment_status", sa.String(length=50), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("status_code", sa.String(length=50), nullable=True),
        sa.Column("error_message", sa.String(length=120), nullable=True),
        sa.Column("transaction_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("collect_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("request_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("response_date", sa.DateTime(timezone=True), nullable=True),
        sa.Column("identifier_code", sa.String(length=50), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "request_adn_integration",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("request_id", postgresql.UUID(), nullable=False),
        sa.Column("adn_integration_id", postgresql.UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sale_account_history",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("account_id", postgresql.UUID(), nullable=False),
        sa.Column("pic_id", postgresql.UUID(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sample",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("samplecode", sa.String(length=12), nullable=False),
        sa.Column("subject_id", postgresql.UUID(), nullable=False),
        sa.Column("sample_collection_date", sa.Date(), nullable=True),
        sa.Column("sample_recollection", sa.Boolean(), nullable=False),
        sa.Column("sample_collection_time", sa.Integer(), nullable=False),
        sa.Column("sample_receipt_date", sa.Date(), nullable=True),
        sa.Column("sample_collector_name", sa.String(length=50), nullable=True),
        sa.Column("sample_receiver_name", sa.String(length=50), nullable=True),
        sa.Column("source_id", postgresql.UUID(), nullable=False),
        sa.Column("scan_status", sa.Integer(), nullable=False),
        sa.Column("lab_check_date", sa.Date(), nullable=True),
        sa.Column("lab_receipt_date", sa.Date(), nullable=True),
        sa.Column("sample_type", sa.String(length=30), nullable=True),
        sa.Column("run_id", sa.String(length=100), nullable=True),
        sa.Column("created_at", sa.Date(), nullable=False),
        sa.Column("updated_at", sa.Date(), nullable=True),
        sa.Column("deleted_at", sa.Date(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sample_mapping",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("chip_id", sa.String(length=12), nullable=True),
        sa.Column("position", sa.String(length=30), nullable=True),
        sa.Column("dna_extraction_id", sa.Integer(), nullable=False),
        sa.Column("plate_id", postgresql.UUID(), nullable=False),
        sa.Column("well_position", sa.String(length=12), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("prob_pass", sa.Boolean(), nullable=True),
        sa.Column("call_rate", sa.Float(), nullable=True),
        sa.Column("call_rate_pass", sa.Boolean(), nullable=True),
        sa.Column("gender_pass", sa.Boolean(), nullable=True),
        sa.Column("qc_status", sa.String(length=12), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sample_meta",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("userid", sa.String(length=50), nullable=True),
        sa.Column("full_name", sa.String(length=50), nullable=False),
        sa.Column("address", sa.String(length=100), nullable=True),
        sa.Column("email", sa.String(length=50), nullable=True),
        sa.Column("phone_number", sa.String(length=50), nullable=True),
        sa.Column("validate_account", sa.Boolean(), nullable=False),
        sa.Column("gender", sa.String(length=50), nullable=False),
        sa.Column("dob", sa.Date(), nullable=False),
        sa.Column("yob", sa.String(length=4), nullable=True),
        sa.Column("diagnosis", sa.String(length=120), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "samplecode",
        sa.Column("samplecode", sa.String(length=12), nullable=False),
        sa.Column("printed", sa.Boolean(), nullable=False),
        sa.Column("state", sa.String(length=50), nullable=True),
        sa.Column("note", sa.String(length=120), nullable=True),
        sa.Column("created_at", sa.Date(), nullable=False),
        sa.Column("updated_at", sa.Date(), nullable=False),
        sa.PrimaryKeyConstraint("samplecode"),
    )
    op.create_table(
        "source",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("account_history_id", postgresql.UUID(), nullable=False),
        sa.Column("nominator_id", postgresql.UUID(), nullable=True),
        sa.Column("freelancer_id", postgresql.UUID(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "status",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("barcode", sa.String(length=50), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=True),
        sa.Column("note", sa.String(length=500), nullable=True),
        sa.Column("created_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_time", sa.DateTime(timezone=True), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "subject",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("user_id", postgresql.UUID(), nullable=True),
        sa.Column("full_name", sa.String(length=120), nullable=False),
        sa.Column("email", sa.String(length=50), nullable=True),
        sa.Column("phone_number", sa.String(length=50), nullable=True),
        sa.Column("diagnosis", sa.String(length=200), nullable=True),
        sa.Column("address", sa.String(length=250), nullable=False),
        sa.Column("identifier_code", sa.String(length=50), nullable=False),
        sa.Column("legal_guardian", sa.String(length=120), nullable=True),
        sa.Column("dob", sa.Date(), nullable=False),
        sa.Column("gender", sa.String(length=50), nullable=False),
        sa.Column("yob", sa.String(length=4), nullable=True),
        sa.Column("validate_account", sa.Boolean(), nullable=False),
        sa.Column("require_registration", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.Date(), nullable=False),
        sa.Column("updated_at", sa.Date(), nullable=True),
        sa.Column("deleted_at", sa.Date(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("samplecode", sa.String(length=120), nullable=False),
        sa.Column("identifier_code", sa.String(length=120), nullable=True),
        sa.Column("status", sa.String(length=120), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_collect_session",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("location_id", postgresql.UUID(), nullable=False),
        sa.Column("user_id", postgresql.UUID(), nullable=False),
        sa.Column("phone_number", sa.String(length=50), nullable=False),
        sa.Column("employee_id", postgresql.UUID(), nullable=False),
        sa.Column("employee_name", sa.String(length=120), nullable=False),
        sa.Column("collect_date", sa.Date(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_collection",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("tracking_id", postgresql.UUID(), nullable=False),
        sa.Column("maThuNhan", sa.String(length=120), nullable=False),
        sa.Column("donViThuNhanMau_id", sa.Integer(), nullable=False),
        sa.Column("noiThuThapMau", sa.Integer(), nullable=False),
        sa.Column("ngayGioThuThapMau", sa.Date(), nullable=False),
        sa.Column("nhanVienLayMau_id", postgresql.UUID(), nullable=False),
        sa.Column("nhanVienGhiHoSo_id", postgresql.UUID(), nullable=False),
        sa.Column("nhanVienLuuMau_id", postgresql.UUID(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_delivery",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("tracking_id", postgresql.UUID(), nullable=False),
        sa.Column("maVanChuyen", sa.String(length=120), nullable=True),
        sa.Column("nhietDoChuyenGiao", sa.String(length=120), nullable=True),
        sa.Column("tinhTrangNiemPhong", sa.Boolean(), nullable=False),
        sa.Column("ngayGioChuyenGiao", sa.Date(), nullable=False),
        sa.Column("diaDiemChuyenGiao", sa.Integer(), nullable=False),
        sa.Column("donViBanGiao_id", sa.Integer(), nullable=False),
        sa.Column("donViVanChuyen_id", sa.Integer(), nullable=True),
        sa.Column("donViNhanMau_id", sa.Integer(), nullable=False),
        sa.Column("nhanVienBanGiao_id", postgresql.UUID(), nullable=False),
        sa.Column("nhanVienNhanMau_id", postgresql.UUID(), nullable=False),
        sa.Column("khac", sa.String(length=200), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_employee",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("hoTenNhanVien", sa.String(length=120), nullable=False),
        sa.Column("hocVi", sa.String(length=120), nullable=True),
        sa.Column("soDinhDanh", sa.String(length=120), nullable=False),
        sa.Column("chucVu", sa.String(length=120), nullable=False),
        sa.Column("unit_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_procedure",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("tracking_id", postgresql.UUID(), nullable=False),
        sa.Column("template_id", postgresql.UUID(), nullable=False),
        sa.Column("maXetNghiem", sa.String(length=120), nullable=False),
        sa.Column("nhietDoLuuTru", sa.String(length=120), nullable=True),
        sa.Column("gs_barcode", sa.String(length=120), nullable=False),
        sa.Column("gs_lid", sa.String(length=120), nullable=False),
        sa.Column("gs_currStep", sa.Integer(), nullable=False),
        sa.Column("gs_totalStep", sa.Integer(), nullable=False),
        sa.Column("gs_isComplete", sa.Boolean(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_procedure_step",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("procedure_id", postgresql.UUID(), nullable=False),
        sa.Column("employee_id", postgresql.UUID(), nullable=False),
        sa.Column("template_step_id", postgresql.UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_session_sample",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("session_id", postgresql.UUID(), nullable=False),
        sa.Column("samplecode", sa.String(length=36), nullable=True),
        sa.Column("subject_id", postgresql.UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_step",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("tenBuoc", sa.String(length=120), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_template",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("congNghe", sa.String(length=120), nullable=False),
        sa.Column("tenKit", sa.String(length=120), nullable=False),
        sa.Column("gs_template_name", sa.String(length=120), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_template_step",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("step_id", postgresql.UUID(), nullable=False),
        sa.Column("template_id", postgresql.UUID(), nullable=False),
        sa.Column("gs_step_number", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_unit",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("tenDonVi", sa.String(length=120), nullable=False),
        sa.Column("gs_area", sa.String(length=120), nullable=False),
        sa.Column("gs_area_code", sa.Integer(), nullable=False),
        sa.Column("gs_phone_number", sa.String(length=50), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracking_unit_location",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("unit_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "staff",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=50), nullable=False),
        sa.Column("email", sa.String(length=50), nullable=True),
        sa.Column("userid", sa.String(length=50), nullable=True),
        sa.Column("account_id", postgresql.UUID(), nullable=True),
        sa.Column("phone_number", sa.String(length=50), nullable=True),
        sa.Column("role", sa.String(length=50), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["account.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("staff")
    op.drop_table("tracking_unit_location")
    op.drop_table("tracking_unit")
    op.drop_table("tracking_template_step")
    op.drop_table("tracking_template")
    op.drop_table("tracking_step")
    op.drop_table("tracking_session_sample")
    op.drop_table("tracking_procedure_step")
    op.drop_table("tracking_procedure")
    op.drop_table("tracking_employee")
    op.drop_table("tracking_delivery")
    op.drop_table("tracking_collection")
    op.drop_table("tracking_collect_session")
    op.drop_table("tracking")
    op.drop_table("subject")
    op.drop_table("status")
    op.drop_table("source")
    op.drop_table("samplecode")
    op.drop_table("sample_meta")
    op.drop_table("sample_mapping")
    op.drop_table("sample")
    op.drop_table("sale_account_history")
    op.drop_table("request_adn_integration")
    op.drop_table("request")
    op.drop_table("promotion")
    op.drop_table("plate")
    op.drop_table("old_sample")
    op.drop_table("old_kit")
    op.drop_table("missing_sample")
    op.drop_table("lab_sample")
    op.drop_table("kit")
    op.drop_table("identity_card")
    op.drop_table("dna_extractions")
    op.drop_table("dna_box_mappings")
    op.drop_table("dna_box")
    op.drop_table("code")
    op.drop_table("chip")
    op.drop_table("card_product")
    op.drop_table("card")
    op.drop_table("billcode")
    op.drop_table("batch_mapping")
    op.drop_table("batch")
    op.drop_table("attempt")
    op.drop_table("agency")
    op.drop_table("adn_result")
    op.drop_table("adn_integration_type")
    op.drop_table("adn_integration")
    op.drop_table("account")
    # ### end Alembic commands ###
