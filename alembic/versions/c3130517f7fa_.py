"""empty message

Revision ID: c3130517f7fa
Revises: 8760d6bfc279
Create Date: 2025-03-20 09:56:59.326367

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "c3130517f7fa"
down_revision = "8760d6bfc279"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "gs_areas",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("area", sa.String(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sponsors",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=False),
        sa.Column("code", sa.String(length=200), nullable=False),
        sa.Column("description", sa.String(length=200), nullable=True),
        sa.Column("type", sa.String(length=50), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.UniqueConstraint("code"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sponsor_contracts",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=False),
        sa.Column("code", sa.String(length=200), nullable=False),
        sa.Column("signing_date", sa.DateTime(), nullable=False),
        sa.Column("sponsor_id", postgresql.UUID(), nullable=False),
        sa.Column("sponsored_party", sa.String(length=50), nullable=True),
        sa.Column("s3_key", sa.String(length=100), nullable=True),
        sa.Column("s3_bucket", sa.String(length=100), nullable=True),
        sa.Column("total_quantity", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["sponsor_id"],
            ["sponsors.id"],
        ),
        sa.UniqueConstraint("code"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "sponsor_campaigns",
        sa.Column("id", postgresql.UUID(), nullable=False),
        sa.Column("name", sa.String(length=200), nullable=False),
        sa.Column("start_date", sa.DateTime(), nullable=True),
        sa.Column("end_date", sa.DateTime(), nullable=True),
        sa.Column("quantity", sa.Integer(), nullable=True),
        sa.Column("contract_id", postgresql.UUID(), nullable=False),
        sa.Column("gs_area_code", sa.Integer(), nullable=True),
        sa.Column("description", sa.String(length=200), nullable=True),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(
            ["contract_id"],
            ["sponsor_contracts.id"],
        ),
        sa.ForeignKeyConstraint(
            ["gs_area_code"],
            ["gs_areas.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "sample", sa.Column("sponsor_contract_id", postgresql.UUID(), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("sample", "sponsor_contract_id")
    op.drop_table("sponsor_campaigns")
    op.drop_table("sponsor_contracts")
    op.drop_table("sponsors")
    op.drop_table("gs_areas")
    # ### end Alembic commands ###
