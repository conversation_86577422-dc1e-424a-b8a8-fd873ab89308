"""add manual input to identity_card

Revision ID: b7abb7caa3fc
Revises: 57ec10f3004e
Create Date: 2025-05-17 17:11:11.420765

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b7abb7caa3fc'
down_revision: Union[str, None] = '57ec10f3004e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('identity_card', sa.Column('manual_input', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('identity_card', 'manual_input')
    # ### end Alembic commands ###
