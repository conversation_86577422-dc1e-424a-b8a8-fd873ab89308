"""empty message

Revision ID: 57ec10f3004e
Revises: c3130517f7fa
Create Date: 2025-04-18 17:09:07.813132

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision: str = '57ec10f3004e'
down_revision: Union[str, None] = 'c3130517f7fa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('subject', sa.Column('martyr_name', sa.String(length=120)))
    op.add_column('subject', sa.Column('martyr_relationships', JSONB))
    pass


def downgrade() -> None:
    op.drop_column('subject', 'martyr_name')
    op.drop_column('subject', 'martyr_relationships')
    pass
