"""Add guardian information

Revision ID: fd8991385d66
Revises: 7e762d5ae561
Create Date: 2025-05-29 13:45:32.334932

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'fd8991385d66'
down_revision: Union[str, None] = '7e762d5ae561'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subject', sa.Column('guardian_name', sa.String(length=120), nullable=True))
    op.add_column('subject', sa.Column('guardian_gender', sa.String(length=50), nullable=True))
    op.add_column('subject', sa.Column('guardian_phone_number', sa.String(length=50), nullable=True))
    op.add_column('subject', sa.Column('guardian_identifier_code', sa.String(length=50), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subject', 'guardian_identifier_code')
    op.drop_column('subject', 'guardian_phone_number')
    op.drop_column('subject', 'guardian_gender')
    op.drop_column('subject', 'guardian_name')
    # ### end Alembic commands ###
