"""modify sample-sponsor_contract to sample-sponsor relation

Revision ID: 7e762d5ae561
Revises: b7abb7caa3fc
Create Date: 2025-05-18 09:47:07.633170

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '7e762d5ae561'
down_revision: Union[str, None] = 'b7abb7caa3fc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sample', sa.Column('sponsor_id', postgresql.UUID(), nullable=True))
    op.drop_column('sample', 'sponsor_contract_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sample', sa.Column('sponsor_contract_id', postgresql.UUID(), autoincrement=False, nullable=True))
    op.drop_column('sample', 'sponsor_id')
    # ### end Alembic commands ###
