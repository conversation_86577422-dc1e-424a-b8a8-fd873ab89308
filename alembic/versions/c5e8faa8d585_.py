"""add warehouse management

Revision ID: c5e8faa8d585
Revises: fd8991385d66
Create Date: 2025-07-14 16:33:03.797731

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c5e8faa8d585'
down_revision: Union[str, None] = 'fd8991385d66'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sample_rack',
    sa.Column('id', postgresql.UUID(), nullable=False),
    sa.Column('code', sa.String(length=120), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('sample_box',
    sa.Column('id', postgresql.UUID(), nullable=False),
    sa.Column('code', sa.String(length=120), nullable=False),
    sa.Column('sample_rack_id', postgresql.UUID(), nullable=True),
    sa.Column('created_by', sa.String(length=120), nullable=True),
    sa.Column('updated_by', sa.String(length=120), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['sample_rack_id'], ['sample_rack.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('sample_box_position',
    sa.Column('id', postgresql.UUID(), nullable=False),
    sa.Column('sample_box_id', postgresql.UUID(), nullable=False),
    sa.Column('barcode', sa.String(length=20), nullable=False),
    sa.Column('x_position', sa.String(length=12), nullable=False),
    sa.Column('y_position', sa.String(length=12), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['sample_box_id'], ['sample_box.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('sample_box_position')
    op.drop_table('sample_box')
    op.drop_table('sample_rack')
    # ### end Alembic commands ###
