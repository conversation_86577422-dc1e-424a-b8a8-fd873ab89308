# Operation

Operation exposes an API to manage access requests.

[View API Documentation](http://petstore.swagger.io/?url=https://raw.githubusercontent.com/uc-cdis/requestor/master/docs/openapi.yaml)

The server is built with [FastAPI](https://fastapi.tiangolo.com/) and packaged with [Poetry](https://poetry.eustace.io/).

## Local installation

### Install Operation

Install required software:

*   [PostgreSQL](PostgreSQL) 9.6 or above
*   [Python](https://www.python.org/downloads/) 3.7 or above
*   [Poetry](https://poetry.eustace.io/docs/#installation)

Then use `poetry install` to install the dependencies. Before that,
a [virtualenv](https://virtualenv.pypa.io/) is recommended.
If you don't manage your own, Poetry will create one for you
during `poetry install`, and you must activate it with `poetry shell`.

### Create configuration file

Operation requires a configuration file to run. We have a command line
utility to help you create one based on a default configuration.

The configuration file itself will live outside of this repo (to
prevent accidentally checking in sensitive information like database passwords).

To create a new configuration file from the default configuration:

```bash
python cfg_help.py create
``` 

This file will be placed in one of the default search directories for Operation.

To get the exact path where the new configuration file was created, use:

```bash
python cfg_help.py get
```

The file should have detailed information about each of the configuration
variables. **Remember to fill out the new configuration file!**

To use a configuration file in a custom location, you can set the `OPERATION_CONFIG_PATH` environment variable.

### Run Operation

Run database schema migration:

```bash
alembic upgrade head
```

Run the server with auto-reloading:

```bash
python run.py
OR
uvicorn operation.asgi:app --reload
```

Try out the API at: <http://localhost:8000/docs>.

```
docker start 91dc0cbb41a9
docker-compose -f docker-compose-local.yml up
docker-compose -f docker-compose-local.yml down
docker run -it --net=host -v $(pwd)/operation:/src -v $(pwd)/alembic:/alembic  tysud/operation:qa-2.0.5 sh
docker run -it --net=host -v $(pwd)/operation:/src -v $(pwd)/alembic:/alembic  tysud/operation:qa-2.0.5 sh

# Upgrade start server
docker run -it --net=host -v $(pwd)/operation:/src -v $(pwd)/alembic/operation/alembic:/alembic  tysud/operation:qa-2.0.5 sh
/env/bin/gunicorn operation.asgi:app -b 0.0.0.0:8000 -k uvicorn.workers.UvicornWorker --reload


. /env/bin/activate
docker build -t tysud/operation:qa-new-cccd-2 -f Dockerfile .
docker build -t tysud/operation:qa-2.0.5 -f Dockerfile .
```

tysud/operation:qa-v6-rollback-11

Add NEW Alembic Code

```
docker start 91dc0cbb41a9
docker run -it --net=host -v $(pwd)/operation:/src -v $(pwd)/alembic/operation/alembic:/alembic tysud/operation:qa-2.0.5 sh
. /env/bin/activate
alembic revision -m 'add_tables_for_limbs'
alembic current
alembic downgrade -1
alembic upgrade head

chmod +x /src/create_db.sh
chmod +x /src/create_extension.sh
chmod +x /src/drop_db.sh
pytest -sv tests/

```

RUN UNIT TESTS WITH LOCAL DB

```
PSQL

Note: Only schema & No data

pg_dump -h gt-qa-rds.cntsuccfkujx.ap-southeast-1.rds.amazonaws.com -U operation_user -d operation_v6_qa_rollback_5 -s > operation_qa_to_local.sql

docker run --name postgresql -p 8540:5432 -e POSTGRESQL_USERNAME=root -e POSTGRESQL_PASSWORD=secret -e POSTGRESQL_DATABASE=my_database bitnami/postgresql:latest

docker start 91dc0cbb41a9

docker run -it --net=host -v $(pwd)/operation:/src -v $(pwd)/alembic/operation/alembic:/alembic tysud/operation:qa-2.0.5 sh
. /env/bin/activate
apk add postgresql

pytest -sv tests/
pytest -v tests/tests_apis_2
pytest --capture=sys tests/tests_apis_2/test_detail_screen.py


```
docker run -it --net=host -v $(pwd)/operation:/src -v $(pwd)/alembic/operation/alembic:/alembic tysud/operation:qa-2.0.5 sh
docker run -it -p 8000:8000 -v $(pwd)/operation:/src tysud/operation:qa-new-cccd sh

/env/bin/gunicorn operation.asgi:app -b 0.0.0.0:8000 -k uvicorn.workers.UvicornWorker --reload

docker run -it -p 8000:8000 -p 5432:5432 -v $(pwd)/operation:/src -v $(pwd)/alembic/operation/alembic:/alembic tysud/operation:qa-2.0.5 sh

/env/bin/gunicorn operation.asgi:app -b 0.0.0.0:8000 -k uvicorn.workers.UvicornWorker --reload

# Database Test
74e4b428f158
74e4b428f158   bitnami/postgresql:latest      "/opt/bitnami/script…"   3 weeks ago     Up 13 days               0.0.0.0:8540->5432/tcp, :::8540->5432/tcp   postgresql

# Using venv & poetry

source venv/bin/activate
pip install poetry==1.1.15
venv/bin/poetry install --no-dev --no-interaction

(optional)
venv/bin/poetry export -f requirements.txt --without-hashes --output requirements.txt

pip list

lsof -i :8000
kill 1234