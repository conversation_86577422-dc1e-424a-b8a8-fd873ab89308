#!/bin/sh

sleep 5
update-ca-certificates

if [ ! -d "/alembic/migrations" ]
then
    echo "Directory /alembic/migrations does not exists. Creating ..."
    mkdir -p /alembic
    /env/bin/alembic init /alembic/migrations
    cp /src/alembic-env/env.py /alembic/migrations
else
    echo "Directory /alembic/migrations exists. Skip creating."
fi

cp /src/alembic-env/env.py /alembic/migrations
/env/bin/alembic revision --autogenerate
/env/bin/alembic upgrade head
